<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huafon.dao.mapper.WorkSafeMapper">

    <resultMap id = "workSafeMap" type ="com.huafon.models.entity.WorkSafe">
        <id column="id" property="id"/>
        <result column="push_id" property="pushId"/>
        <result column="special_fire_num" property="specialFireNum"/>
        <result column="first_fire_num" property="firstFireNum"/>
        <result column="second_fire_num" property="secondFireNum"/>
        <result column="space_num" property="spaceNum"/>
        <result column="other_num" property="otherNum"/>
    </resultMap>

    <sql id = "commonSql">
        id,
        push_id,
        special_fire_num,
        first_fire_num,
        second_fire_num,
        space_num,
        other_num
    </sql>

    <select id="queryByPage" resultType="com.huafon.models.vo.data.work.WorkSafeCommonVO">
        SELECT
        b.id,
        c.company_code,
        c.code,
        c.name,
        a.year,
        a.month,
        a.statistic_time,
        b.special_fire_num,
        b.first_fire_num,
        b.second_fire_num,
        b.space_num,
        b.other_num,
        b.num,
        b.blind_num,
        b.high_num,
        b.hoist_num,
        b.temporary_electricity_num,
        b.break_ground_num,
        b.break_road_num,
        b.energy_isolation_num
        FROM hf_base_data_push AS a
        LEFT JOIN hf_base_work_safe AS b ON a.id = b.push_id
        LEFT JOIN hf_base_tenant AS c ON a.code = c.code AND a.company_code = c.company_code
        WHERE a.is_del = 0
        AND b.is_del = 0
        AND c.is_del = 0
        AND a.current_version IS TRUE
        <if test="query.filterCodes != null and query.filterCodes.size() > 0">
            AND c.code IN
            <foreach collection="query.filterCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.filterCompanyCodes != null and query.filterCompanyCodes.size() > 0">
            AND c.company_code IN
            <foreach collection="query.filterCompanyCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.startYearAndMonthDate != null and query.endYearAndMonthDate != null">
            AND a.statistic_time BETWEEN #{query.startYearAndMonthDate} AND #{query.endYearAndMonthDate}
        </if>
        <choose>
            <when test="query.orders != null and query.orders.size() > 0">
                ORDER BY
                <foreach collection="query.orders" separator="," item="item">
                    ${item.column} <choose><when test="item.asc">ASC</when><otherwise>DESC</otherwise></choose>
                </foreach>
            </when>
            <otherwise>
                ORDER BY c.code ASC, a.year DESC, a.month ASC,b.id ASC
            </otherwise>
        </choose>
    </select>


</mapper>
