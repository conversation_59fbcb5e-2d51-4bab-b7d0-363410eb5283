package com.huafon.utils;


import com.alibaba.fastjson.JSONObject;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.huafon.models.dto.UserDTO;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 组件 jwt 处理类
 *
 * <AUTHOR>
 */
@Component
@Log4j2
public class JwtToken {

    @Value("${jwt.appkey:hf745612}")
    private String appKey;

    /**
     * 生成token
     *
     * @param userInfo
     * @return
     */
    public String encodeUserInfo(UserDTO userInfo) {
        try {
//            long time = System.currentTimeMillis() + length * 1000;
//            Date expiresDate = new Date(time);
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("alg", "HS256");
            map.put("typ", "JWT");
            return JWT.create()
                    .withHeader(map)
                    .withClaim("userInfoJson", JSONObject.toJSONString(userInfo))
                    //.withExpiresAt(expiresDate)
                    .sign(Algorithm.HMAC256(appKey));
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    public String encodeUserInfo(UserDTO userInfo, String key) {
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("alg", "HS256");
            map.put("typ", "JWT");
            return JWT.create()
                    .withHeader(map)
                    .withClaim("userInfoJson", JSONObject.toJSONString(userInfo))
                    .sign(Algorithm.HMAC256(key));
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    /**
     * 解析token
     *
     * @param hfToken
     * @return
     */
    public UserDTO decodeUserInfo(String hfToken) {
        try {
            JWTVerifier verifier = JWT.require(Algorithm.HMAC256(appKey)).build();
            DecodedJWT jwt = verifier.verify(hfToken);
            String userInfoJson = jwt.getClaim("userInfoJson").asString();
            if (null != userInfoJson) {
                return JSONObject.parseObject(userInfoJson, UserDTO.class);
            }
        } catch (Exception e) {

        }
        return null;
    }
}
