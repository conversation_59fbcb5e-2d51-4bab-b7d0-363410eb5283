package com.huafon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huafon.models.entity.HseCostDetail;
import com.huafon.models.query.HseCostDetailPageQuery;
import com.huafon.models.vo.data.cost.HseCostDetailCommonVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @since 2024-08-27 20:01
*/
@Mapper
public interface HseCostDetailMapper extends BaseMapper<HseCostDetail> {

    /**
    * 分页
    */
    IPage<HseCostDetailCommonVO> queryByPage(@Param("page") IPage<HseCostDetailCommonVO> page, @Param("query") HseCostDetailPageQuery query);

    HseCostDetailCommonVO queryTotalInfo(@Param("query") HseCostDetailPageQuery query);
}
