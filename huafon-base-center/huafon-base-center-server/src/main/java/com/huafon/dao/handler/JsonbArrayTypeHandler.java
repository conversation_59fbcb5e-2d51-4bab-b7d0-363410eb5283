package com.huafon.dao.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.postgresql.util.PGobject;

import java.io.IOException;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-05-16 14:58
 **/
@MappedTypes({List.class})
@MappedJdbcTypes({JdbcType.OTHER})
public abstract class JsonbArrayTypeHandler<T> extends BaseTypeHandler<List<T>> {

	private static final ObjectMapper objectMapper = new ObjectMapper();

	@Override
	public void setNonNullParameter(PreparedStatement ps, int i, List<T> parameter, JdbcType jdbcType)
			throws SQLException {
		try {
			String json = objectMapper.writeValueAsString(parameter);
			PGobject jsonObject = new PGobject();
			jsonObject.setType("jsonb");
			jsonObject.setValue(json);
			ps.setObject(i, jsonObject);
		} catch (JsonProcessingException e) {
			throw new SQLException("Error converting object array to JSON", e);
		}
	}

	@Override
	public List<T> getNullableResult(ResultSet rs, String columnName) throws SQLException {
		String json = rs.getString(columnName);
		return parseJson(json);
	}

	@Override
	public List<T> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
		String json = rs.getString(columnIndex);
		return parseJson(json);
	}

	@Override
	public List<T> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
		String json = cs.getString(columnIndex);
		return parseJson(json);
	}

	private List<T> parseJson(String json) throws SQLException {
		if (json != null && !json.isEmpty()) {
			try {
				return objectMapper.readValue(json, typeReference());
			} catch (IOException e) {
				throw new SQLException("Error converting JSON to object array", e);
			}
		}
		return null;
	}

	protected abstract TypeReference<List<T>> typeReference();
}
