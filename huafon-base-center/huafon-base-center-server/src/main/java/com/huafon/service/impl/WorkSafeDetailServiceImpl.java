package com.huafon.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huafon.common.utils.bean.BeanUtils;
import com.huafon.dao.mapper.WorkSafeMapper;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.models.dto.WorkSafeDownloadDTO;
import com.huafon.models.entity.BaseDataPush;
import com.huafon.models.entity.WorkSafe;
import com.huafon.models.enums.DataReportType;
import com.huafon.models.query.WorkSafeAnalyzeQuery;
import com.huafon.models.query.WorkSafePageQuery;
import com.huafon.models.vo.TenantVO;
import com.huafon.models.vo.data.DataReportBaseVO;
import com.huafon.models.vo.data.work.WorkSafeAnalyzeVO;
import com.huafon.models.vo.data.work.WorkSafeCommonVO;
import com.huafon.models.vo.data.work.WorkSafeDetailVO;
import com.huafon.models.vo.data.work.WorkSafeVO;
import com.huafon.service.BaseDataPushService;
import com.huafon.service.TenantService;
import com.huafon.service.WorkSafeDetailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-09-03 20:38
 **/
@Service
@Slf4j
@Transactional
public class WorkSafeDetailServiceImpl extends ServiceImpl<WorkSafeMapper, WorkSafe>
		implements WorkSafeDetailService {

	@Autowired
	private TenantService tenantService;

	@Autowired
	private BaseDataPushService dataPushService;

	@Override
	public DataReportType getReportType() {
		return DataReportType.WORK;
	}

	@Override
	public void handler(DataReportBaseVO source, Integer pushId) {
		this.delete(Collections.singletonList(pushId));

		WorkSafeVO workSafeInfo = (WorkSafeVO) source;
		Collection<WorkSafeDetailVO> detailList = workSafeInfo.getDetailList();
		if (!CollectionUtils.isEmpty(detailList)) {
			for (WorkSafeDetailVO item : detailList) {
				if (item == null) {
					continue;
				}
				WorkSafe target = BeanUtils.convert(item, WorkSafe.class);
				target.setPushId(pushId);
				target.setCreate(0L);
				this.save(target);
			}
		}
	}

	@Override
	public void delete(Collection<Integer> allPushIds) {
		if (CollectionUtils.isEmpty(allPushIds)) {
			return;
		}
		List<WorkSafe> allRelationInfos = this.lambdaQuery().in(WorkSafe::getPushId, allPushIds).list();
		if (!CollectionUtils.isEmpty(allRelationInfos)) {
			List<WorkSafe> allInfo = allRelationInfos.stream().peek(e -> e.setModify(0L)).collect(Collectors.toList());
			List<Integer> allTargetIds = allInfo.stream().map(WorkSafe::getId).distinct().collect(Collectors.toList());
			this.updateBatchById(allInfo);
			this.removeByIds(allTargetIds);
		}
	}

	@Override
	public CommonPage<WorkSafeCommonVO> commonPage(WorkSafePageQuery query) {
		query.setDefaultYearMonth();
		Page<WorkSafeCommonVO> page = new Page<>(query.getPageNo(), query.getPageSize());
		IPage<WorkSafeCommonVO> pageResult = this.baseMapper.queryByPage(page, query);
		return new CommonPage<>(pageResult);
	}

	@Override
	public WorkSafeAnalyzeVO analyze(WorkSafeAnalyzeQuery query) {
		query.setDefaultYearMonth();
		List<TenantVO> tenantInfos = tenantService.all();
		if (CollectionUtils.isEmpty(tenantInfos)) {
			return defaultValue(query, tenantInfos);
		}
		query.setReportType(this.getReportType());
		List<BaseDataPush> list = dataPushService.queryAllDataPushData(query);
		if (CollectionUtils.isEmpty(list)) {
			return defaultValue(query, tenantInfos);
		}

		Map<String, List<BaseDataPush>> dataPushMappings = list.stream()
				.filter(e -> Objects.nonNull(e.getCode()))
				.collect(Collectors.groupingBy(BaseDataPush::getCode, Collectors.toCollection(CopyOnWriteArrayList::new)));

		List<Integer> pushIds = list.stream().map(BaseDataPush::getId).collect(Collectors.toList());

		List<WorkSafe> allWorkDetails = this.lambdaQuery()
				.in(WorkSafe::getPushId, pushIds)
				.list();

		if (CollectionUtils.isEmpty(allWorkDetails)) {
			return defaultValue(query, tenantInfos);
		}

		Map<Integer, List<WorkSafe>> pushWorkSafeMappings = allWorkDetails.stream()
				.filter(e -> Objects.nonNull(e.getPushId()))
				.collect(Collectors.groupingByConcurrent(WorkSafe::getPushId,
						Collectors.toCollection(CopyOnWriteArrayList::new)));

		WorkSafeAnalyzeVO result = new WorkSafeAnalyzeVO();
		List<WorkSafeAnalyzeVO.WorkSafeDetailVO>  itemList = new ArrayList<>();
		for (TenantVO tenantInfo : tenantInfos) {
			if (!CollectionUtils.isEmpty(query.getFilterCodes()) && !query.getFilterCodes().contains(tenantInfo.getCode())) {
				continue;
			}
			List<BaseDataPush> pushInfos = dataPushMappings.getOrDefault(tenantInfo.getCode(), Collections.emptyList());
			List<WorkSafe> workSafeItemList = new ArrayList<>();
			if (!CollectionUtils.isEmpty(pushInfos)) {
				List<Integer> relationPushIds = pushInfos.stream().map(BaseDataPush::getId).distinct().collect(Collectors.toList());
				workSafeItemList.addAll(pushWorkSafeMappings.entrySet().stream()
						.filter(e -> relationPushIds.contains(e.getKey()))
						.map(Map.Entry::getValue)
						.flatMap(Collection::stream).collect(Collectors.toList()));
			}
			int num = 0;
			int specialFireNum = 0;
			int firstFireNum = 0;
			int secondFireNum = 0;
			int spaceNum = 0;
			int blindNum = 0;
			int highNum = 0;
			int hoistNum = 0;
			int temporaryElectricityNum = 0;
			int breakGroundNum = 0;
			int breakRoadNum = 0;
			int energyIsolationNum = 0;

			for (WorkSafe item : workSafeItemList) {
				num += Optional.ofNullable(item.getNum()).orElse(0);
				specialFireNum += Optional.ofNullable(item.getSpecialFireNum()).orElse(0);
				firstFireNum += Optional.ofNullable(item.getFirstFireNum()).orElse(0);
				secondFireNum += Optional.ofNullable(item.getSecondFireNum()).orElse(0);
				spaceNum += Optional.ofNullable(item.getSpaceNum()).orElse(0);
				blindNum += Optional.ofNullable(item.getBlindNum()).orElse(0);
				highNum += Optional.ofNullable(item.getHighNum()).orElse(0);
				hoistNum += Optional.ofNullable(item.getHoistNum()).orElse(0);
				temporaryElectricityNum += Optional.ofNullable(item.getTemporaryElectricityNum()).orElse(0);
				breakGroundNum += Optional.ofNullable(item.getBreakGroundNum()).orElse(0);
				breakRoadNum += Optional.ofNullable(item.getBreakRoadNum()).orElse(0);
				energyIsolationNum += Optional.ofNullable(item.getEnergyIsolationNum()).orElse(0);
			}

			WorkSafeAnalyzeVO.WorkSafeDetailVO workSafeAnalyze = new WorkSafeAnalyzeVO.WorkSafeDetailVO();
			workSafeAnalyze.setCode(tenantInfo.getCode());
			workSafeAnalyze.setCompanyCode(tenantInfo.getCompanyCode());
			workSafeAnalyze.setName(tenantInfo.getName());
			workSafeAnalyze.setNum(num);
			workSafeAnalyze.setSpecialFireNum(specialFireNum);
			workSafeAnalyze.setFirstFireNum(firstFireNum);
			workSafeAnalyze.setSecondFireNum(secondFireNum);
			workSafeAnalyze.setSpaceNum(spaceNum);
			workSafeAnalyze.setBlindNum(blindNum);
			workSafeAnalyze.setHighNum(highNum);
			workSafeAnalyze.setHoistNum(hoistNum);
			workSafeAnalyze.setTemporaryElectricityNum(temporaryElectricityNum);
			workSafeAnalyze.setBreakGroundNum(breakGroundNum);
			workSafeAnalyze.setBreakRoadNum(breakRoadNum);
			workSafeAnalyze.setEnergyIsolationNum(energyIsolationNum);

			itemList.add(workSafeAnalyze);
		}


		result.setCompanyAnalyze(itemList);

		return result;
	}

	private WorkSafeAnalyzeVO defaultValue(WorkSafeAnalyzeQuery query, List<TenantVO> tenantInfos) {
		WorkSafeAnalyzeVO result = new WorkSafeAnalyzeVO();
		List<WorkSafeAnalyzeVO.WorkSafeDetailVO> items = tenantInfos.stream()
				.filter(e -> {
					return CollectionUtils.isEmpty(query.getFilterCodes()) || query.getFilterCodes().contains(e.getCode());
				})
				.map(origin -> {
					WorkSafeAnalyzeVO.WorkSafeDetailVO item = new WorkSafeAnalyzeVO.WorkSafeDetailVO();
					item.setCompanyCode(origin.getCompanyCode());
					item.setName(origin.getName());
					item.setCode(origin.getCode());
					return item;
				}).collect(Collectors.toList());
		result.setCompanyAnalyze(items);
		return result;
	}

	@Override
	public List<WorkSafeDownloadDTO> download(WorkSafePageQuery query) {
		CommonPage<WorkSafeCommonVO> pageResult = this.commonPage(query);
		if (CollectionUtils.isEmpty(pageResult.getList())) {
			return new ArrayList<>();
		}

		List<WorkSafeDownloadDTO> result = new ArrayList<>();
		for (WorkSafeCommonVO origin : pageResult.getList()) {
			WorkSafeDownloadDTO target = BeanUtils.convert(origin, WorkSafeDownloadDTO.class);
			result.add(target);
		}
		return result;
	}
}
