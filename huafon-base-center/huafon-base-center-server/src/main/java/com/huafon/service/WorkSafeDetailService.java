package com.huafon.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.models.dto.WorkSafeDownloadDTO;
import com.huafon.models.entity.WorkSafe;
import com.huafon.models.query.WorkSafeAnalyzeQuery;
import com.huafon.models.query.WorkSafePageQuery;
import com.huafon.models.vo.data.work.WorkSafeAnalyzeVO;
import com.huafon.models.vo.data.work.WorkSafeCommonVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-09-03 20:38
 **/
public interface WorkSafeDetailService extends DataPushOptService, IService<WorkSafe> {

	/**
	 * 分页
	 */
	CommonPage<WorkSafeCommonVO> commonPage(WorkSafePageQuery query);

	/**
	 * 统计图
	 * @param query
	 * @return
	 */
	WorkSafeAnalyzeVO analyze(WorkSafeAnalyzeQuery query);


	List<WorkSafeDownloadDTO> download(WorkSafePageQuery query);
}
