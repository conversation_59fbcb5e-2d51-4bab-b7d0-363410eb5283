package com.huafon.models.vo.data.risk;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* <AUTHOR>
* @since 2024-09-03 19:53
*/
@Data
@ApiModel(value = "华峰集团数据上报-隐患列表")
public class RiskDetailCommonVO implements Serializable {

    @ApiModelProperty(value = "公司COde")
    private String companyCode;

    @ApiModelProperty(value = "所属企业COde")
    private String code;

    @ApiModelProperty(value = "所属企业")
    private String name;

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "月份")
    private Integer month;

    @ApiModelProperty(value = "统计的时间")
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    private Date statisticTime;

    @ApiModelProperty(value = "排查数量")
    private Integer inspectionNum;

    @ApiModelProperty(value = "整改数量")
    private Integer rectificationNum;

    @ApiModelProperty(value = "整改率")
    private BigDecimal rectificationRate;


}
