package com.huafon.models.vo.data.waste;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-08-28 20:07
 **/
@Data
@ApiModel(value = "废物统计")
public class WasteAnalyzeVO implements Serializable {

	@ApiModelProperty(value = "月度废物产生量统计")
	private Map<String, Float> monthProductAnalyze;

	@ApiModelProperty(value = "月度废物处置量统计")
	private Map<String, Float> monthProcessAnalyze;

	@ApiModelProperty(value = "月度废物处置量统计")
	private Map<String, Float> monthStorageAnalyze;
}
