package com.huafon.models.vo.data.resource;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 集团数据上报-温室气体情况明细VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-09
 */
@Data
@ApiModel(value = "ResourceDetailVO", description = "集团数据上报-资源情况明细VO")
public class ResourceDetailVO {

    @ApiModelProperty(value = "维度名称")
    private String name;

    @ApiModelProperty(value = "数量")
    private BigDecimal num;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "备注")
    private String remark;

}
