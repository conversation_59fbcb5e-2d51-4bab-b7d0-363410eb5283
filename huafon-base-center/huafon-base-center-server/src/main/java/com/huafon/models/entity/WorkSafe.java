package com.huafon.models.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import lombok.Data;

/**
* 华峰集团数据上报-特殊作业
* <AUTHOR>
* @since 2024-09-03 20:40
*/
@Data
@TableName("hf_base_work_safe")
public class WorkSafe extends BaseEntity{

    /**
    * 主键
    */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
    * 推送的记录ID
    */
    @TableField(value = "push_id")
    private Integer pushId;

    /**
     * 特级动火数量
     */
    @TableField(value = "special_fire_num")
    private Integer specialFireNum;

    /**
     * 一级动火数量
     */
    @TableField(value = "first_fire_num")
    private Integer firstFireNum;

    /**
     * 二级动火数量
     */
    @TableField(value = "second_fire_num")
    private Integer secondFireNum;

    /**
     * 受限空间数量
     */
    @TableField(value = "space_num")
    private Integer spaceNum;

    /**
     * 作业数量
     */
    @TableField(value = "num")
    private Integer num;

    /**
     * 盲板作业数量
     */
    @TableField(value = "blind_num")
    private Integer blindNum;

    /**
     * 高处作业数量
     */
    @TableField(value = "high_num")
    private Integer highNum;

    /**
     * 吊装作业数量
     */
    @TableField(value = "hoist_num")
    private Integer hoistNum;

    /**
     * 临时用电作业数量
     */
    @TableField(value = "temporary_electricity_num")
    private Integer temporaryElectricityNum;

    /**
     * 动土作业数量
     */
    @TableField(value = "break_ground_num")
    private Integer breakGroundNum;

    /**
     * 断路作业数量
     */
    @TableField(value = "break_road_num")
    private Integer breakRoadNum;

    /**
     * 能量隔离作业数量
     */
    @TableField(value = "energy_isolation_num")
    private Integer energyIsolationNum;


}
