package com.huafon.models.vo.data.risk;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-09-03 20:07
 **/
@Data
@ApiModel(value = "隐患统计")
public class RiskAnalyzeVO implements Serializable {

	List<RiskDetailVO> companyAnalyze;


	@Data
	public static class RiskDetailVO {
		@ApiModelProperty(value = "code")
		private String code;

		@ApiModelProperty(value = "所属企业")
		private String name;

		@ApiModelProperty("公司Code")
		private String companyCode;

		@ApiModelProperty(value = "排查数量")
		private Integer inspectionNum = 0;

		@ApiModelProperty(value = "整改数量")
		private Integer rectificationNum = 0;

		@ApiModelProperty(value = "整改率")
		private BigDecimal rectificationRate = BigDecimal.ZERO;
	}
}
