package com.huafon.models.vo.data.accident;

import com.huafon.models.enums.DataReportType;
import com.huafon.models.vo.data.DataReportBaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Collection;

/**
 * <AUTHOR>
 * @since 2024-08-23 15:11
 **/
@ToString
@Getter
@Setter
@ApiModel(value = "事故上报")
public class AccidentReportVO extends DataReportBaseVO {

	public AccidentReportVO() {
		super(DataReportType.ACCIDENT.toString());
	}

	@ApiModelProperty(value = "事故详情")
	private Collection<AccidentReportDetailVO> detailList;
}
