package com.huafon.models.entity;

import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * Table: hf_base_receive_config
 */
@Data
@TableName("hf_base_receive_config")
public class BaseReceiveConfig {
    /**
     * Column: id
     * Type: bigserial
     * Default value: nextval('base_center.hf_base_receive_config_id_seq'::regclass)
     * Remark: id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * Column: company_name
     * Type: varchar(255)
     * Remark: 基地名称
     */
    private String companyName;

    /**
     * Column: secret
     * Type: varchar(1000)
     * Remark: 秘钥
     */
    private String secret;

    /**
     * Column: create_time
     * Type: timestamp
     * Remark: 创建时间
     */
    private LocalDateTime createTime;

    /**
     * Column: modify_time
     * Type: timestamp
     * Remark: 更新时间
     */
    private LocalDateTime modifyTime;

    /**
     * Column: is_del
     * Type: int2
     * Default value: 0
     * Remark: 是否删除 0|否 1|是
     */
    private Integer isDel;

    /**
     * Column: domain_name
     * Type: varchar(255)
     * Remark: 域名
     */
    private String domainName;

    /**
     * Column: port
     * Type: varchar(255)
     * Remark: 端口
     */
    private String port;

    private String jwtKey;

    private String webDomainName;
}