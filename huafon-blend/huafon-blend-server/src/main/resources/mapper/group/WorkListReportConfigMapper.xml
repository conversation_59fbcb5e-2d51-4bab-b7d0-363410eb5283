<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huafon.dao.mapper.group.WorkListReportConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huafon.models.entity.group.WorkListReportConfig">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="work_model_id" property="workModelId" />
        <result column="work_model_name" property="workModelName" />
        <result column="company_work_model_name" property="companyWorkModelName" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="is_del" property="isDel" />
        <result column="modify_by" property="modifyBy" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, work_model_id, work_model_name, company_work_model_name, create_by, create_time, is_del, modify_by, modify_time
    </sql>

    <update id="deleteByTenantId">
        UPDATE
            hf_group_work_list_report_config
        SET
            is_del = 1,
            modify_by = #{userId},
            modify_time = now()
        WHERE
            tenant_id = #{tenantId}
        AND
            is_del = 0
    </update>

    <select id="findByTenantId" resultType="com.huafon.models.entity.group.WorkListReportConfig">
        SELECT
            t1."id",
            t1.tenant_id tenantId,
            t2.id workModelId,
            t2.work_model_name workModelName,
            t1.company_work_model_name companyWorkModelName
        FROM
            hf_group_work_list_report_config AS t1
                RIGHT JOIN hf_hse_work_safe_model t2 ON t1.work_model_id = t2.id AND t1.is_del = 0
        WHERE
            t2.is_del = 0
          AND
            t2.state = 1
          AND
            t2.tenant_id = #{tenantId}
    </select>

</mapper>
