<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huafon.dao.mapper.ScoreShopProductMapper">

    <resultMap id = "scoreShopProductCommonMap" type ="com.huafon.models.vo.ScoreShopProductCommonVo">
        <id column="id" property="id"/>
        <result column="type_id" property="typeId"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="single_redemption_score" property="singleRedemptionScore"/>
        <result column="inventory" property="inventory"/>
        <result column="picture" property="picture" typeHandler="com.huafon.models.dto.AttachmentDTO$AttachmentDTOHandler"/>
        <result column="state" property="state"/>
        <result column="sort" property="sort"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_by_name" property="createByName"/>
        <result column="modify_by_name" property="modifyByName"/>
    </resultMap>

    <sql id = "commonSql">
        id,
        type_id,
        code,
        name,
        single_redemption_score,
        inventory,
        picture,
        state,
        sort,
        tenant_id,
        create_by_name,
        modify_by_name
    </sql>

    <select id="queryByPage" resultMap="scoreShopProductCommonMap">
        SELECT
        <include refid="commonSql"/>
        FROM
            hf_score_shop_product
        WHERE is_del = 0
        <if test="query.excludeOutOfStock">
            AND (state = 0 AND inventory &gt; 0)
        </if>
        <if test="query.tenantId != null">
             AND tenant_id = #{query.tenantId}
        </if>
        <if test="query.searchKey != null and query.searchKey != ''">
             AND (
                    code LIKE CONCAT('%',#{query.searchKey},'%')
                    OR name LIKE CONCAT('%',#{query.searchKey},'%')
                 )
        </if>
        <if test="query.filterTypeIds != null and query.filterTypeIds.size() > 0">
           AND type_id IN
           <foreach collection="query.filterTypeIds" item="item" open="(" close=")" separator=",">
                #{item}
           </foreach>
        </if>
        <choose>
            <when test="query.orders != null and query.orders.size() > 0">
                ORDER BY
                <foreach collection="query.orders" separator="," item="item">
                    ${item.column} <choose><when test="item.asc">ASC</when><otherwise>DESC</otherwise></choose>
                </foreach>
            </when>
            <otherwise>
                 ORDER BY modify_time DESC
            </otherwise>
        </choose>
    </select>

</mapper>
