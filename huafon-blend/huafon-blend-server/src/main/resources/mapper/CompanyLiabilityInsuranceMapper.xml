<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huafon.dao.mapper.CompanyLiabilityInsuranceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huafon.models.entity.CompanyLiabilityInsurance">
        <id column="id" property="id" />
        <result column="company_info_id" property="companyInfoId" />
        <result column="insurance_company" property="insuranceCompany" />
        <result column="policy_sign_date" property="policySignDate" />
        <result column="effective_start_date" property="effectiveStartDate" />
        <result column="insured_persons" property="insuredPersons" />
        <result column="effective_end_date" property="effectiveEndDate" />
        <result column="liability_list" property="liabilityList" />
        <result column="is_del" property="isDel" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="modify_by" property="modifyBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, company_info_id, insurance_company, policy_sign_date, effective_start_date, insured_persons, effective_end_date, liability_list, is_del, create_by, create_time, modify_time, modify_by
    </sql>

    <select id="findList" resultType="com.huafon.models.entity.CompanyLiabilityInsurance">
        SELECT
            t1."id",
            t1.company_info_id,
            t1.insurance_company,
            t1.policy_sign_date,
            t1.effective_start_date,
            t1.insured_persons,
            t1.effective_end_date,
            t1.liability_list,
            t1.is_del,
            t1.create_by,
            t1.create_time,
            t1.modify_time,
            t1.modify_by,
            t2.tenant_id
        FROM
            hf_company_liability_insurance AS t1 LEFT JOIN hf_company_info t2 ON t1.company_info_id = t2.id
        WHERE
            1=1
        <if test="startTime != null ">
            AND t1.modify_time &gt;= #{startTime}
        </if>
        <if test="endTime != null ">
            AND t1.modify_time &lt;= #{endTime}
        </if>
        <if test="tenantId != null ">
            AND t2.tenant_id = #{tenantId}
        </if>

    </select>

</mapper>
