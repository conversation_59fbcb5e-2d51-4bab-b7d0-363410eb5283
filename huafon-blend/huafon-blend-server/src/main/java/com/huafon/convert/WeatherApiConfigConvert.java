package com.huafon.convert;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.huafon.common.utils.BaseUtils;
import com.huafon.models.entity.WeatherApiConfig;
import com.huafon.models.vo.WeatherApiConfigRespVO;
import com.huafon.models.vo.WeatherApiConfigSubmitVO;
import com.huafon.param.WeatherApiConfigSubmitParam;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @ClassName com.huafon.convert.WeatherApiConfigConvert
 * @Description
 * @createTime 2024年06月05日 17:19:00
 */
@Mapper(componentModel = "spring")
public interface WeatherApiConfigConvert {

    Logger LOGGER = LoggerFactory.getLogger(WeatherApiConfigConvert.class);

    ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    

    @Mapping(source = "apiLineList", target = "apiLineJsonArr", qualifiedByName = "list2ApiLineJsonArr")
    WeatherApiConfig req2Entity(WeatherApiConfigSubmitVO req);

    @Named("list2ApiLineJsonArr")
    default String list2ApiLineJsonArr(List<String> apiLineList) {
        String jsonArr = "[]";
        try {
            if (Objects.nonNull(apiLineList)) {
                jsonArr = OBJECT_MAPPER.writeValueAsString(apiLineList);
            }
        } catch (Exception e) {
            LOGGER.error("list2ApiLineJsonArr 实体转换异常", e);
        }
        return jsonArr;
    }

    @Mapping(source = "apiLineJsonArr", target = "apiLineList", qualifiedByName = "apiLineJsonArr2List")
    WeatherApiConfigRespVO entity2RespVO(WeatherApiConfig dbApiConfig);

    @Named("apiLineJsonArr2List")
    default List<String> apiLineJsonArr2List(String json) {
        List<String> list = null;
        try {
            if (BaseUtils.isNotEmpty(json)) {
                list = OBJECT_MAPPER.readValue(json, new TypeReference<List<String>>() {
                });
            }
        } catch (Exception e) {
            LOGGER.error("apiLineJsonArr2List 实体转换异常，json:{}", json, e);
        }
        return list;
    }

}
