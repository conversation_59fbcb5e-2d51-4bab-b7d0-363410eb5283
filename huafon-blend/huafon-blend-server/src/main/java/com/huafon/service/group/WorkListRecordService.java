package com.huafon.service.group;

import com.baomidou.mybatisplus.extension.service.IService;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.models.entity.group.WorkListRecord;
import com.huafon.models.vo.group.*;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-03
 */
public interface WorkListRecordService extends IService<WorkListRecord> {

    /**
     * 分页
     * @param reqVO
     * @return
     */
    CommonPage<WorkListPageRespVO> workListPage(WorkListPageReqVO reqVO);

    /**
     * 删除某一条数据
     */
    void delByIds(List<Integer> ids);

    /**
     * 根据上报id删除
     */
    void delByReportId(Integer reportId);

    /**
     * 同步作业票
     * @param reqVO
     */
    void syncWorkList(Integer newReportId,Integer oldReportId,WorkListReportVO reqVO);

    /**
     * 统计作业数量
     * @param reqVO
     * @return
     */
    WorkListReportStaticRespVO staticWorkNum(WorkListReportStaticReqVO reqVO);

    List<WorkListRecord> findByReportIds(List<Integer> reportIds);


}
