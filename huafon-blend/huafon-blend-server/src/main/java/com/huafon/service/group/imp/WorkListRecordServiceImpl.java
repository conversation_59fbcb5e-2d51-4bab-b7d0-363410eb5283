package com.huafon.service.group.imp;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huafon.common.config.TenantContext;
import com.huafon.common.utils.StreamUtils;
import com.huafon.dao.mapper.group.WorkListRecordMapper;
import com.huafon.framework.mybatis.enums.DelFlag;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.models.dto.group.WorkListRecordDTO;
import com.huafon.models.entity.group.WorkListRecord;
import com.huafon.models.entity.group.WorkListReportConfig;
import com.huafon.models.vo.group.*;
import com.huafon.service.group.WorkListRecordService;
import com.huafon.service.group.WorkListReportConfigService;
import com.huafon.support.config.UserContext;
import com.huafon.work_safe.api.dto.WorkListReqDTO;
import com.huafon.work_safe.api.dto.WorkListRespDTO;
import com.huafon.work_safe.api.service.IWorkListRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-03
 */
@Slf4j
@Service
public class WorkListRecordServiceImpl extends ServiceImpl<WorkListRecordMapper, WorkListRecord> implements WorkListRecordService {

    @DubboReference(check = false)
    private IWorkListRpcService iWorkListRpcService;

    @Autowired
    private WorkListReportConfigService reportConfigService;

    @Override
    public CommonPage<WorkListPageRespVO> workListPage(WorkListPageReqVO reqVO) {
        reqVO.setTenantId(TenantContext.getOne());
        Page<WorkListPageRespVO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        IPage<WorkListPageRespVO> workListPage = baseMapper.findWorkListPage(page, reqVO);
        return new CommonPage<>(workListPage);
    }

    @Override
    public void delByIds(List<Integer> ids) {
        Date date = new Date();
        baseMapper.updateDelFlagById(ids,date,UserContext.getId());
    }

    /**
     * 根据上报id删除
     */
    @Override
    @Transactional
    public void delByReportId(Integer id) {
        Date date = new Date();
        if (Objects.nonNull(id)) {
            baseMapper.updateDelFlagByReportId(id, date, UserContext.getId());
        }
    }

    @Override
    @Transactional
    public void syncWorkList(Integer newReportId,Integer oldReportId,WorkListReportVO reqVO) {
        log.info("=====WorkListReportVO====:{}",JSONObject.toJSONString(reqVO));
        List<WorkListRecord> workListRecords = new ArrayList<>();

        if (Objects.equals(reqVO.getIsNeedSync(),Boolean.TRUE)){
            Map<Integer,String> configMap = getConfigMap();

            WorkListReqDTO workListReqDTO = new WorkListReqDTO();
            workListReqDTO.setYear(reqVO.getYear());
            workListReqDTO.setMonth(reqVO.getMonth());
            workListReqDTO.setTotal(reqVO.getTotal());
            workListReqDTO.setExcludeIds(reqVO.getExcludeIds());
            workListReqDTO.setTenantId(TenantContext.getOne());
            List<WorkListRespDTO> list = iWorkListRpcService.findList(workListReqDTO);
            log.info("=====list====:{}",JSONObject.toJSONString(list));
            if (!CollectionUtils.isEmpty(list)){
                List<WorkListRecordDTO> workListRecordDTOS = JSONArray.parseArray(JSONObject.toJSONString(list), WorkListRecordDTO.class);
                if (!CollectionUtils.isEmpty(workListRecordDTOS)){
                    for (WorkListRecordDTO workListRecordDTO : workListRecordDTOS) {

                        String companyWorkModelName = configMap.get(workListRecordDTO.getWorkModelId());
                        if (StringUtils.isNotBlank(companyWorkModelName)) {
                            WorkListRecord workListRecord = new WorkListRecord();
                            workListRecord.setReportId(newReportId);
                            workListRecord.setTenantId(TenantContext.getOne());
                            workListRecord.setWorkModelId(workListRecordDTO.getWorkModelId());
                            workListRecord.setWorkModelName(workListRecordDTO.getWorkModelName());
                            workListRecord.setApplyTime(workListRecordDTO.getApplyTime());
                            workListRecord.setReceptionTime(workListRecordDTO.getReceptionTime());
                            workListRecord.setCreate(UserContext.getId());
                            workListRecord.setContent(workListRecordDTO);
                            workListRecord.setWorkId(workListRecordDTO.getWorkId());
                            workListRecord.setCompanyWorkModelName(companyWorkModelName);
                            workListRecords.add(workListRecord);
                        }

                    }
                }
            }

        }else if (Objects.equals(reqVO.getIsNeedSync(),Boolean.FALSE) && Objects.nonNull(oldReportId)){
            workListRecords = baseMapper.selectList(new LambdaQueryWrapper<WorkListRecord>()
                    .eq(WorkListRecord::getReportId, oldReportId)
                    .eq(WorkListRecord::getIsDel, DelFlag.SAVE.getValue())
                    .notIn(!CollectionUtils.isEmpty(reqVO.getExcludeIds()),WorkListRecord::getId,reqVO.getExcludeIds()));
        }

        if (!CollectionUtils.isEmpty(workListRecords)){
            for (WorkListRecord workListRecord : workListRecords) {
                workListRecord.setId(null);
                workListRecord.setReportId(newReportId);
                workListRecord.setCreate(UserContext.getId());
            }
            this.saveBatch(workListRecords);
        }
    }

    /**
     * 统计作业数量
     * @param reqVO
     * @return
     */
    @Override
    public WorkListReportStaticRespVO staticWorkNum(WorkListReportStaticReqVO reqVO) {
        setStaticParams(reqVO);
        List<WorkListRecordDTO> staticList = baseMapper.findStaticList(reqVO);
        Map<Integer,String> configMap = getConfigMap();
        WorkListReportStaticRespVO result = WorkListReportStaticRespVO.calculate(configMap, staticList);
        return result;
    }

    @Override
    public List<WorkListRecord> findByReportIds(List<Integer> reportIds) {
        List<WorkListRecord> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(reportIds)){
            return list;
        }
        return baseMapper.findByReportIds(reportIds);
    }

    private Map<Integer, String> getConfigMap() {
        Map<Integer, String> configMap = null;
        List<WorkListReportConfig> byTenantId = reportConfigService.findByTenantId(TenantContext.getOne());
        if (!CollectionUtils.isEmpty(byTenantId)){
            byTenantId = byTenantId.stream().filter(x->Objects.nonNull(x.getWorkModelId()) && StringUtils.isNotBlank(x.getCompanyWorkModelName())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(byTenantId)) {
                configMap = StreamUtils.propMap(byTenantId, WorkListReportConfig::getWorkModelId, WorkListReportConfig::getCompanyWorkModelName);
            }
        }
        return configMap;
    }

    private void setStaticParams(WorkListReportStaticReqVO reqVO) {
        reqVO.setTenantId(TenantContext.getOne());
        if (Objects.nonNull(reqVO.getYear()) && Objects.nonNull(reqVO.getMonth())){
            List<Date> firstAndLastDate = getFirstAndLastDate(reqVO.getYear(), reqVO.getMonth());
            reqVO.setStartTime(firstAndLastDate.get(0));
            reqVO.setEndTime(firstAndLastDate.get(1));
        }
    }

    private List<Date> getFirstAndLastDate(Integer year,Integer month){
        List<Date> list = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR,year);
        calendar.set(Calendar.MONTH,month-1);

        // 设置到当前月的第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date startTime = calendar.getTime(); // 开始时间

        // 设置到当前月的最后一天
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        Date endTime = calendar.getTime(); // 结束时间

        list.add(startTime);
        list.add(endTime);
        return list;
    }

}
