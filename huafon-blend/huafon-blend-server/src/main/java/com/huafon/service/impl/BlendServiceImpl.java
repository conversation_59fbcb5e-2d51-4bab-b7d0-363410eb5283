package com.huafon.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.huafon.api.dto.v2.StatisticsNumGroupDto;
import com.huafon.area.api.service.AreaRpcService;
import com.huafon.auditingImprovements.api.InternalAuditTaskRpcService;
import com.huafon.auditingImprovements.api.OutAuditTaskService;
import com.huafon.auditingImprovements.dto.InternalTaskCountDTO;
import com.huafon.auditingImprovements.dto.OutAuditTaskCountDTO;
import com.huafon.common.config.TenantContext;
import com.huafon.common.utils.bean.BeanUtils;
import com.huafon.dao.mapper.BlendMapper;
import com.huafon.models.constants.BlendConstant;
import com.huafon.models.constants.readConstant;
import com.huafon.models.dto.BlendDto;
import com.huafon.models.dto.GroupDto;
import com.huafon.models.enums.BacklogItemEnum;
import com.huafon.models.enums.BlendTypeEnum;
import com.huafon.models.enums.WorkflowKeyEnum;
import com.huafon.models.reqo.ReqBlend;
import com.huafon.models.vo.*;
import com.huafon.patrol.api.service.EquipmentRpcService;
import com.huafon.patrol.api.service.PatrolPlanRpcService;
import com.huafon.patrol.api.service.PatrolTaskRecordRpcService;
import com.huafon.patrol.api.service.PatrolTaskRpcService;
import com.huafon.portal.api.dto.ApprovalUserDTO;
import com.huafon.portal.api.service.ApprovalService;
import com.huafon.portal.api.service.UserRpcV2Service;
import com.huafon.service.BlendService;
import com.huafon.service.PatrolTaskExecutorService;
import com.huafon.service.PatrolTaskItemService;
import com.huafon.service.PatrolTaskService;
import com.huafon.support.config.UserContext;
import com.huafon.support.core.pojo.R;
import com.huafon.support.dto.UserInfoDto;
import com.huafon.support.exceptions.ServiceException;
import com.huafon.support.utils.DateUtils;
import com.huafon.threeSimultaneity.api.ThreeSimultaneityTaskService;
import com.huafon.urgent.dto.UrgentEventCheckTaskStatisticsDTO;
import com.huafon.urgent.service.UrgentStatisticsRpcService;
import com.huafon.utils.*;
import com.huafon.visitor.api.dto.TaskInfoDTO;
import com.huafon.visitor.api.service.ContractorRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.rpc.RpcContext;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.huafon.threeSimultaneity.api.ThreeSimultaneityTaskService.CONTEXT_TENANT_KEY;
import static com.huafon.threeSimultaneity.api.ThreeSimultaneityTaskService.CONTEXT_USER_KEY;

/**
 * <AUTHOR>
 * @Date 2022/6/29 14:33
 */
@Slf4j
@Service
public class BlendServiceImpl implements BlendService {

    @Resource
    private BlendMapper blendMapper;
    private final PatrolTaskService taskService;
    private final PatrolTaskItemService itemService;

    private final PatrolTaskExecutorService patrolTaskExecutorService;

    @DubboReference(check = false)
    private UserRpcV2Service userRpcV2Service;

    @DubboReference(check = false)
    private ApprovalService approvalService;

    @DubboReference(retries = 1, mock = "return null", check = false)
    private PatrolPlanRpcService patrolPlanService;

    @DubboReference(retries = 1, mock = "return null", check = false)
    private PatrolTaskRecordRpcService patrolTaskRecordService;

    @DubboReference(retries = 1, mock = "return null", check = false)
    private PatrolTaskRpcService patrolTaskService;

    @DubboReference(check = false)
    private ContractorRpcService contractorRpcService;


    @Resource
    private RiskRpcUtil riskRpcUtil;

    @Resource
    private HazardousChemicalRpcUtil hazardousChemicalRpcUtil;

    @Resource
    private SafetyAuditRpcUtil safetyAuditRpcUtil;

    @Resource
    private WorkflowRpcUtil workflowRpcUtil;

    @Resource
    private DutyRpcUtil dutyRpcUtil;
    @Resource
    private EpRpcUtil epRpcUtil;
    @Resource
    private TrainAndTestRpcUtil trainAndTestRpcUtil;

    @DubboReference(check = false)
    ThreeSimultaneityTaskService threeSimultaneityTaskService;

    @DubboReference(check = false)
    private OutAuditTaskService outAuditTaskService;

    @DubboReference(check = false)
    private InternalAuditTaskRpcService internalAuditTaskRpcService;

    @DubboReference(check = false)
    private AreaRpcService areaRpcService;

    @DubboReference(check = false)
    private EquipmentRpcService equipmentRpcService;

    @DubboReference(check = false)
    private UrgentStatisticsRpcService urgentStatisticsRpcService;


    public BlendServiceImpl(PatrolTaskService taskService, PatrolTaskItemService itemService, PatrolTaskExecutorService patrolTaskExecutorService) {
        this.taskService = taskService;
        this.itemService = itemService;
        this.patrolTaskExecutorService = patrolTaskExecutorService;
    }


    /**
     * @return
     */
    @Override
    public R backlog() {
        List<BlendDto> allList = new ArrayList<>();

        //contractorProject();承包商项目
        //allList.addAll(contractorStaff());//承包商人员
        //allList.addAll(riskRectification());//整改单
        //allList.addAll(hazard());//风险
        //allList.addAll(workflow());

        allList.addAll(riskTask());
        //
        allList.addAll(patrolTask());
        allList.addAll(patrolRecord());
        //
        allList.addAll(risk());

//        allList.addAll(riskTakePictures());
//        allList.addAll(accident());
        allList.addAll(workSafe());
        Long userId = UserContext.getId();
        //allList.addAll(duty(userId.intValue()));

        allList.addAll(healthyHarmInform());//危害告知书
        allList.addAll(healthyExamineHistory());//体检记录
        allList.addAll(healthyStaffReport());//健康档案
        allList.addAll(lawsCirculation());//法规传阅
        allList.addAll(trainAndTest());//教育培训
        allList.addAll(healthyLaborProvide());//劳防用品发放
        allList.addAll(dutyResponsibility());
        allList.addAll(dutyCommitment());
        allList.addAll(dutyTaskTracking());
        allList.addAll(safetyAuditTask());
        allList.addAll(dutyShiftSchedule());//领导干部带班

        allList.addAll(dutyActivityHandle());
        allList.addAll(dutyActivitySignature());
        allList.addAll(workSafeConstructionHandle());


        allList = allList.stream()
                .sorted(Comparator.comparing(BlendDto::getModifyTime).reversed())
                .collect(Collectors.toList());
        List<BlendListVo> blendListVo = new ArrayList<>();
        for (int i = 0; i < allList.size(); i++) {
            if (i <= 2) {
                BlendDto blendDto = allList.get(i);
                BlendListVo blend = BeanUtils.convert(blendDto, BlendListVo.class);
                blend.setWorkflow(blendDto.getWorkflow());
                if (blendDto.getType().equals(BlendTypeEnum.RISK_PLAN.getType())) {
                    Long taskId = blendDto.getId();
                    ExecuteVo executeVo1 = blendMapper.selectAndUserId(taskId, userId, 2);
                    if (null != executeVo1 && null != executeVo1.getEntrustUserId()) {
                        ExecuteVo executeVo = blendMapper.selectAndUserId(taskId, executeVo1.getEntrustUserId(), 1);
                        blend.setEntrustState(1);
                        blend.setEntrustUserName(executeVo.getName());
                        blend.setEntrustUserMobile(executeVo.getUserMobile());
                    }
                    String startTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, blendDto.getStartTime());
                    if (null != blendDto.getEndTime()) {
                        String endTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, blendDto.getEndTime());
                        blend.setName(blendDto.getName() + "(" + startTime + "至" + endTime + ")");
                    } else {
                        blend.setName(blendDto.getName() + "(" + startTime + ")");
                    }

                    ExecuteVo executeVo = blendMapper.selectEntrustAndUserId(taskId, userId);
                    if (null != executeVo) {
                        blend.setEntrustState(2);
                        blend.setEntrustUserName(executeVo.getName());
                        blend.setEntrustUserMobile(executeVo.getUserMobile());
                    }
                } else if (blendDto.getType().equals(BlendTypeEnum.PATROL_TASK.getType())) {
                    String startTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, blendDto.getStartTime());
                    if (null != blendDto.getEndTime()) {
                        String endTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, blendDto.getEndTime());
                        blend.setName(blendDto.getName() + "(" + startTime + "至" + endTime + ")");
                    } else {
                        blend.setName(blendDto.getName() + "(" + startTime + ")");
                    }
                } else if (blendDto.getType().equals(BlendTypeEnum.SAFETY_AUDIT_TASK.getType())) {
                    String startTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, blendDto.getStartTime());
                    if (null != blendDto.getEndTime()) {
                        String endTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, blendDto.getEndTime());
                        blend.setName(blendDto.getName() + "(" + startTime + "至" + endTime + ")");
                    } else {
                        blend.setName(blendDto.getName() + "(" + startTime + ")");
                    }
                }
                blendListVo.add(blend);
            } else {
                break;
            }
        }
        return R.ok(blendListVo);
    }

    private List<BlendDto> lawsCirculation() {
        ReqBlend reqBlend = new ReqBlend();
        List<BlendDto> blendDtoList = blendMapper.getMyNotHandleCirculation(reqBlend);
        if (!CollectionUtils.isEmpty(blendDtoList)) {
            Map<Long, Integer> handleMap = new HashMap<>();
            List<BlendDto> readState = blendMapper.findReadState(reqBlend);
            if (!CollectionUtils.isEmpty(readState)) {
                handleMap = readState.stream()
                        .collect(Collectors.toMap(BlendDto::getId, BlendDto::getReadState, (e1, e2) -> e1));
            }

            for (BlendDto blendDto : blendDtoList) {
                Integer integer = handleMap.get(blendDto.getId()) + 1;
                blendDto.setReadState(integer);
                blendDto.setName(blendDto.getName() + "的法规传阅任务");
                blendDto.setType(BlendTypeEnum.LAWS_CRICULATION.getType());
            }
        }

        return blendDtoList;
    }

    private List<BlendDto> trainAndTest() {
        ReqBlend reqBlend = new ReqBlend();
        List<BlendDto> blendDtoList = blendMapper.getMyTrainAndTestJob(reqBlend);
        if (!CollectionUtils.isEmpty(blendDtoList)) {
            for (BlendDto blendDto : blendDtoList) {
                if (Objects.nonNull(blendDto.getReadState())) {
                    blendDto.setReadState(blendDto.getReadState() + 1);
                }
                blendDto.setType(BlendTypeEnum.TRAIN_AND_TEST.getType());
            }
        }
        return blendDtoList;
    }

    private Integer trainAndTestCount() {
        List<BlendDto> list = trainAndTest();
        return CollectionUtils.isEmpty(list) ? 0 : list.size();
    }

//    private List<BlendDto> workflow() {
//        List<BlendDto> blendDtoList = new ArrayList<>();
//        List<WaitOperationListDto> waitOperationList = workflowRpcUtil.getWaitOperationList();
//        for (WaitOperationListDto waitOperationListDto : waitOperationList) {
//            Integer type = WorkflowKeyEnum.getType(waitOperationListDto.getKey());
//            if (null != type) {
//                BlendDto dto = new BlendDto();
//                dto.setType(type);
//                dto.setWorkflow(BeanUtils.convert(waitOperationListDto, WorkflowVo.class));
//                dto.setId(waitOperationListDto.getBusinessId().longValue());
//                dto.setReadState(waitOperationListDto.getReadState());
//                dto.setModifyTime(waitOperationListDto.getTime());
//                blendDtoList.add(dto);
//            }
//        }
//        return blendDtoList;
//    }

/*    private List<BlendDto> duty(Integer staffId) {
        List<DutyPlanDto> data = dutyPlanRpcService.getTodo(staffId);
        return data.stream().map(x -> {
            BlendDto tmp = new BlendDto();
            tmp.setId(x.getId().longValue());
            tmp.setDate(x.getTime());
            tmp.setModifyTime(x.getTime());
            tmp.setName(x.getName());
            tmp.setReadState(x.getReadState());
            tmp.setType(BlendTypeEnum.DUTY.getType());
            return tmp;
        }).collect(Collectors.toList());
    }*/

    private List<BlendDto> healthyLaborProvide() {
        ReqBlend reqBlend = new ReqBlend();
        List<BlendDto> blendDtos = blendMapper.healthyLaborProvide(reqBlend);
        for (BlendDto item : blendDtos) {
            String name = item.getName();
            item.setType(BlendTypeEnum.HEALTHY_LABOR_PROVIDE.getType());
            item.setName(Optional.ofNullable(UserContext.get()).map(UserInfoDto::getName).orElse(Strings.EMPTY) + name);
        }

        return blendDtos;
    }

/*    private Integer dutyCount(Integer userId) {
        return dutyPlanRpcService.getTodoCount(userId);
    }*/

    private Integer healthyLaborProvideCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.healthyLaborProvideCount(reqBlend);
    }

    private Collection<? extends BlendDto> patrolTask() {
        ReqBlend reqBlend = new ReqBlend();
        reqBlend.setUserId(UserContext.getId());
        List<BlendDto> blendDtoList = blendMapper.selectPatrolTask(reqBlend);
        return blendDtoList.stream()
                .peek(p -> p.setType(BlendTypeEnum.PATROL_TASK.getType()))
                .collect(Collectors.toList());
    }

    private Collection<? extends BlendDto> patrolRecord() {
        ReqBlend reqBlend = new ReqBlend();
        reqBlend.setUserId(UserContext.getId());
        List<BlendDto> blendDtoList = blendMapper.selectPatrolRecord(reqBlend);
        return blendDtoList.stream()
                .peek(p -> p.setType(BlendTypeEnum.PATROL_RECORD.getType()))
                .collect(Collectors.toList());
    }

    private int patrolTaskCount() {
        ReqBlend reqBlend = new ReqBlend();
        reqBlend.setUserId(UserContext.getId());
        reqBlend.setStartDate(new Date());
        return blendMapper.selectPatrolTaskCount(reqBlend);
    }

    //    @Override
//    public R<TaskStatisticsVo> taskStatistics() {
//        TaskStatisticsVo taskStatisticsVo = new TaskStatisticsVo();
//        StatisticsNumVo statisticsNumVo = workflowRpcUtil.statisticsNumExceptWaitOperation();
//        //待办数量
//        int waitOperationNum = 0;
//        waitOperationNum += backlogCountStatistics(new BlendCountVo());
//        taskStatisticsVo.setWaitOperationNum(waitOperationNum);
//        //已办数量
//        int finishedNum = 0;
//        finishedNum += statisticsNumVo.getFinishedNum();
//        taskStatisticsVo.setFinishedNum(finishedNum);
//        //我发起的数量
//        int sponsorNum = 0;
//        sponsorNum += statisticsNumVo.getSponsorNum();
//        taskStatisticsVo.setSponsorNum(sponsorNum);
//        //抄送数量
//        int copySendNum = 0;
//        copySendNum += statisticsNumVo.getCopySendNum();
//        taskStatisticsVo.setCopySendNum(copySendNum);
//        return R.ok(taskStatisticsVo);
//    }
    private int patrolRecordCount() {
        return blendMapper.selectPatrolRecordCount(new ReqBlend());
    }

    private int alarmUserNoticeCount() {
        List<Integer> integers = blendMapper.selectalarmUserNoticeCount(new ReqBlend());
        return CollectionUtils.isEmpty(integers) ? 0 : integers.size();
    }

    private int alarmNoticeDangersourceCount() {
        return blendMapper.selectalarmUserNoticeDangersourceCount(new ReqBlend());
    }

    private int pssrCount() {
        return blendMapper.selectPssrCount(new ReqBlend());
    }

    private int phaCount() {
        ReqBlend reqBlend = new ReqBlend();
        reqBlend.setUserId(UserContext.getId());
        return blendMapper.selectPhaCount(reqBlend);
    }

    private int preCommissioningCount() {
        ReqBlend reqBlend = new ReqBlend();
        reqBlend.setUserId(UserContext.getId());
        return blendMapper.selectPreCommissioningTaskCount(reqBlend);
    }

    private List<BlendDto> dutyShiftSchedule() {
        ReqBlend reqBlend = new ReqBlend();
        List<BlendDto> blendDtos = blendMapper.dutyShiftSchedule(reqBlend);
        for (BlendDto item : blendDtos) {
            item.setType(BlendTypeEnum.DUTY_SHIFT_SCHEDULE.getType());
        }
        return CollectionUtils.isEmpty(blendDtos) ? Collections.emptyList() : blendDtos;
    }

    private Integer dutyShiftScheduleCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.dutyShiftScheduleCount(reqBlend);
    }

    @Override
    public R backlogCount() {
        BlendCountVo blendCountVo = new BlendCountVo();
        backlogCountStatistics(blendCountVo);
        return R.ok(blendCountVo);
    }

    private void backlogCountStatistics(BlendCountVo blendCountVo) {
        int contractorProjectCount = 0;
        int contractorStaffCount = 0;
        int healthyLaborApplyCount = 0;
        int riskRectificationCount = 0;
        int hazardCount = 0;

        int bowtieHazardCount = 0;

        int lecHazardCount = 0;

        int sclHazardCount = 0;

        int annualTrainCount = 0;
        int hazardousChemicalCheckTaskCount = 0;
        int hazardousChemicalRegisterCount = 0;

        int hazardousChemicalAppraisalCount = 0;
        int hazardousChemicalCount = 0;
        int lawsIdentifyCount = 0;
        int examineInformCount = 0;//体检通知单
        int accidentReportCount = 0;
        int accidentCorrectiveCount = 0;

        Integer urgentDrillPlanCount = 0;
        Integer urgentDrillActivityCount = 0;
        Integer urgentDrillEvaluateCount = 0;
        Integer hseMeetingTaskCount = 0;


        Integer generalCalculateStorageCount = 0;
        int hazardousPutStorageCount = 0;
        int hazardousOutStorageCount = 0;
        int hazardousCalculateStorageCount = 0;
        Integer dutyManagementPlanCount = 0;
        int safetyAuditReportCount = 0;
        int dutyShiftPlanCount = 0;
        int dutyShiftScheduleChangeCount = 0;
        int sewageDischargeApplyCount = 0;//污水排放申请
        int sewageDistributionCount = 0;//配水表
        Integer dutyActivityPlanCount = 0;
        Integer dutyActivityAuditCount = 0;
        int workPlanDeclarationCount = 0;
        int lawsPeriodicEvaluation = 0;
        int personalScoreApplicationCount = 0; // 个人素养积分申请
        //外审计划
        Integer externalAuditPlanCount = 0;
        //外审报告
        Integer externalAuditReportCount = 0;
        //内审计划
        Integer internalAuditPlanCount = 0;
        //内审报告
        Integer internalAuditReportCount = 0;

        int techChange = 0;

        //物料审批
        Integer materialIdentify = 0;
        //仪器借用审批
        Integer instrumentBorrow = 0;
        //密封点审批
        Integer sealPointApprove = 0;

        Integer fileApplicabilityReviewNum = 0;
        // 环境因素评价
        Integer factorEvaluationCount = 0;

        //绩效申诉
        Integer assessAppealCount = 0;
        //公司级重要环境因素审核
        Integer companyLevelFactorReviewCount = 0;

        //安全自评
        Integer selfEvaluationSafeCount = 0;

        Integer visitorReservationCount = 0;

        //矩阵课程
        Integer matrixCourseCount = 0;

        //承包商违规处罚流程统计
        Integer contractorStaffViolationProcessCount = 0;

        //承包商项目资质审核项目
        Integer contractorProjectQualityExamineCount = 0;
        //培训活动审批
        Integer examCompleteActivityCount = 0;
        //培训活动申诉
        Integer examActivityAppealCount = 0;
        //承包商项目评价记录
        Integer contractorProjectEvaluate = 0;
        //承包商保证金退回
        Integer contractorSecurityDepositBack = 0;
        //班组研判
        Integer teamJudge = 0;
        //部门研判
        Integer deptJudge = 0;
        //公司研判
        Integer companyJudge = 0;
        //费用变更
        Integer costChangeCount = 0;
        //工作票申报
        Integer workTicketApplyCount = 0;
        //工作票审批
        Integer workTicketApproveCount = 0;
        //承包商人员批量审批
        Integer contractorStaffBatchCount = 0;
        //人员补卡申请数量
        Integer workflowReissuePersonCardCount = 0;
        //活动创建审批数量
        Integer activityCreateApprovalCount = 0;
        //承包商工器具审批
        Integer contractorToolCount = 0;

        Integer techInterlockCount = 0;

        Integer techAlarmCount = 0;

        Integer workAuthorizationCount = 0;

        Integer contractorApproveCount = 0;
        //作业停送电
        Integer stopSendCount = 0;

        Integer hazardApproveCount = 0;

        Integer equipmentOutageCount = 0;

        Integer annualTrainTaskChangeCount = 0;

        Integer contractorProjectChangeCount = 0;

        Integer carRegistrationCount = 0;
        Integer driverRegistrationCount = 0;

        List<StatisticsNumGroupDto> waitOperationNum = workflowRpcUtil.getWaitOperationNum();
        for (StatisticsNumGroupDto dto : waitOperationNum) {
            if (dto.getName().equals(WorkflowKeyEnum.CONTRACTOR.getKey())) {
                contractorProjectCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.CONTRACTOR_STAFF.getKey())) {
                contractorStaffCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.HEALTHY_LABOR_APPLY.getKey())) {
                healthyLaborApplyCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.RISK_RECTIFICATION.getKey())) {
                riskRectificationCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.ANNUAL_TRAIN.getKey())) {
                annualTrainCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.HAZARDOUS_CHEMICAL_REGISTER.getKey())) {
                hazardousChemicalRegisterCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.HAZARDOUS_CHEMICAL_APPRAISAL.getKey())) {
                hazardousChemicalAppraisalCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.HAZARDOUS_CHEMICAL.getKey())) {
                hazardousChemicalCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.LAWS_IDENTIFY.getKey())) {
                lawsIdentifyCount = dto.getNum();
            } else if (WorkflowKeyEnum.HEALTHY_EXAMINE_INFORM.getKey().equals(dto.getName())) {
                examineInformCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.ACCIDENT_REPORT.getKey())) {
                accidentReportCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.ACCIDENT_CORRECTIVE.getKey())) {
                accidentCorrectiveCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.URGENT_DRILL_PLAN.getKey())) {
                urgentDrillPlanCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.URGENT_DRILL_ACTIVITY.getKey())) {
                urgentDrillActivityCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.URGENT_DRILL_EVALUATE.getKey())) {
                urgentDrillEvaluateCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.DUTY_MANAGEMENT_PLAN.getKey())) {
                dutyManagementPlanCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.GENERAL_CALCULATE_STORAGE.getKey())) {
                generalCalculateStorageCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.HAZARDOUS_PUT_STORAGE.getKey())) {
                hazardousPutStorageCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.HAZARDOUS_OUT_STORAGE.getKey())) {
                hazardousOutStorageCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.HAZARDOUS_CALCULATE_STORAGE.getKey())) {
                hazardousCalculateStorageCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.SAFETY_AUDIT_REPORT.getKey())) {
                safetyAuditReportCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.SEWAGE_DISCHARGE_APPLY.getKey())) {
                sewageDischargeApplyCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.SEWAGE_DISTRIBUTION.getKey())) {
                sewageDistributionCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.DUTY_SHIFT_PLAN.getKey())) {
                dutyShiftPlanCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.DUTY_SHIFT_SCHEDULE_CHANGE.getKey())) {
                dutyShiftScheduleChangeCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.DUTY_ACTIVITY_PLAN.getKey())) {
                dutyActivityPlanCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.DUTY_ACTIVITY.getKey())) {
                dutyActivityAuditCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.HAZARDOUS_CHEMICAL_CHECK_TASK.getKey())) {
                hazardousChemicalCheckTaskCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.WORK_PLAN_DECLARATION.getKey())) {
                workPlanDeclarationCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.LAWS_PERIODIC_EVALUATION.getKey())) {
                lawsPeriodicEvaluation = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.PERSONAL_SCORE_APPLICATION.getKey())) {
                personalScoreApplicationCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.HSE_MEETING_TASK.getKey())) {
                hseMeetingTaskCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.EXTERNAL_AUDIT_PLAN.getKey())) {
                externalAuditPlanCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.EXTERNAL_AUDIT_REPORT.getKey())) {
                externalAuditReportCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.INTERNAL_AUDIT_PLAN.getKey())) {
                internalAuditPlanCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.INTERNAL_AUDIT_REPORT.getKey())) {
                internalAuditReportCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.HAZARD.getKey())) {
                hazardCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.BOWTIE_HAZARD_IDENTIFY.getKey())) {
                bowtieHazardCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.LEC_HAZARD_IDENTIFY.getKey())) {
                lecHazardCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.SCL_HAZARD_IDENTIFY.getKey())) {
                sclHazardCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.TECH_CHANGE.getKey())) {
                techChange = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.MATERIAL_IDENTIFY.getKey())) {
                materialIdentify = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.INSTRUMENT_BORROW.getKey())) {
                instrumentBorrow = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.SEAL_POINT_APPROVAL.getKey())) {
                sealPointApprove = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.FILE_APPLICABILITY_REVIEW.getKey())) {
                fileApplicabilityReviewNum = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.FACTOR_EVALUATION.getKey())) {
                factorEvaluationCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.APPEAL_RECODE_PROCESS_KEY.getKey())) {
                assessAppealCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.COMPANY_LEVEL_FACTOR_REVIEW.getKey())) {
                companyLevelFactorReviewCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.SELF_EVALUATION_SAFE_PROCESS_KEY.getKey())) {
                selfEvaluationSafeCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.VISITOR_RESERVATION.getKey())) {
                visitorReservationCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.MATRIX_COURSE.getKey())) {
                matrixCourseCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.CONTRACTOR_VIOLATION.getKey())) {
                contractorStaffViolationProcessCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.EXAM_COMPLETE_ACTIVITY.getKey())) {
                examCompleteActivityCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.EXAM_ACTIVITY_APPEAL.getKey())) {
                examActivityAppealCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.CONTRACTOR_PROJECT_EVALUATE.getKey())) {
                contractorProjectEvaluate = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.CONTRACTOR_SECURITY_DEPOSIT_BACK.getKey())) {
                contractorSecurityDepositBack = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.TEAM_JUDGE.getKey())) {
                teamJudge = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.DEPT_JUDGE.getKey())) {
                deptJudge = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.COMPANY_JUDGE.getKey())) {
                companyJudge = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.COST_CHANGE.getKey())) {
                costChangeCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.WORK_TICKET_APPLY.getKey())) {
                workTicketApplyCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.WORK_TICKET_APPROVE.getKey())) {
                workTicketApproveCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.CONTRACTOR_STAFF_BATCH.getKey())) {
                contractorStaffBatchCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.WORKFLOW_REISSUE_PERSON_CARD.getKey())) {
                workflowReissuePersonCardCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.EXAM_ACTIVITY_CREATE.getKey())) {
                activityCreateApprovalCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.CONTRACTOR_TOOL.getKey())) {
                contractorToolCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.TECH_INTERLOCK.getKey())) {
                techInterlockCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.TECH_ALARM.getKey())) {
                techAlarmCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.WORK_AUTHORIZATION.getKey())) {
                workAuthorizationCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.CONTRACTOR_APPROVE.getKey())) {
                contractorApproveCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.WORK_STOP_SEND.getKey())) {
                stopSendCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.HAZARD_CONTROL_APPROVE.getKey())) {
                hazardApproveCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.EQUIPMENT_OUTAGE.getKey())) {
                equipmentOutageCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.ANNUAL_TRAIN_TASK_CHANGE.getKey())) {
                annualTrainTaskChangeCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.CONTRACTOR_PROJECT_CHANGE.getKey())) {
                contractorProjectChangeCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.CAR_REGISTRATION.getKey())) {
                carRegistrationCount = dto.getNum();
            } else if (dto.getName().equals(WorkflowKeyEnum.DRIVER_REGISTRATION.getKey())) {
                driverRegistrationCount = dto.getNum();
            }
        }
        //int contractorProjectCount = contractorProjectCount();
        //int contractorStaffCount = contractorStaffCount(); //承包商人员
        //int healthyLaborCount = healthyLaborCount(staff);
        //int riskRectificationCount = riskRectificationCount();
        //int hazardCount = hazardCount();

        int riskTaskCount = riskRpcUtil.riskTaskCount(UserContext.getId());
        int riskCount = riskCount();
        //int dutyCount = dutyCount(UserContext.getId().intValue());

        int patrolTaskCount = patrolTaskCount();
        int patrolRecordCount = patrolRecordCount();
        int alarmUserNoticeCount = alarmUserNoticeCount();
        int alarmNoticeDangersourceCount = alarmNoticeDangersourceCount();
        int phaCount = phaCount();
        int preCommissioningCount = preCommissioningCount();
        Integer pssrCount = pssrCount();
        int workSafeCount = workSafeCount();
        int harmInformCount = healthyHarmInformCount();//危害告知书
        int examineHistoryCount = healthyExamineHistoryCount();//体检记录
        int staffReportCount = healthyStaffReportCount();//健康档案
        int lawsCirculationCount = lawsCirculationCount();
        int trainAndTestCount = trainAndTestCount();//教育培训
        int laborProvideCount = healthyLaborProvideCount();//劳防用品发放

        int dutyResponsibilityCount = dutyResponsibilityCount();
        int dutyCommitmentCount = dutyCommitmentCount();
        int dutyTaskTrackingCount = dutyTaskTrackingCount();

        int safetyAuditTaskCount = safetyAuditTaskCount();
        int dutyShiftScheduleCount = dutyShiftScheduleCount();
        int dutyActivityHandleCount = dutyActivityHandleCount();
        int dutyActivitySignatureCount = dutyActivitySignatureCount();
        int dutyActivityCheckerCount = dutyActivityCheckerCount();

        int workSafeConstructionHandleCount = workSafeConstructionHandleCount();
        int workSafeConstructionAcceptingCount = workSafeConstructionAcceptingCount();
        int threeSimultaneityUntreatedTaskCount = threeSimultaneityUntreatedTaskCount(); // 三同时待办任务
        int contractorProjectFinishedTaskCount = contractorProjectFinishedCount(); //承包商项目到期结束任务
        int contractorStaffViolationUndoCount = contractorStaffViolationUndoCount(); //承包商项目到期结束任务
        // 内审任务待办数量
        Integer internalAuditTaskCount = getInternalAuditTaskCount();
        //外审任务待办数量
        Integer externalAuditTaskCount = getExternalAuditTaskCount();

        int myCourseCount = myCourseCount();
        int myTestCount = myTestCount();

        int detectionTaskNum = detectionTaskNum();//环境检测任务

        int introspectionTaskNum = introspectionTaskNum();


        Integer sealPointResolveTaskCount = sealPointResolveCount();//密封点分解任务
        Integer sealPointExecutorTaskCount = sealPointExecutorCount();//密封点执行任务
        Integer drainageStatisticsTaskCount = drainageStatisticsTaskCount();//排水统计任务
        Integer annualTrainTaskCount = annualTrainTaskCount(); //年度培训计划任务
        Integer hseMeetingTaskWaitCount = hseMeetingTaskWaitCount();
        contractorProjectQualityExamineCount = contractorProjectQualityExamineCount();
        Integer urgentNotifyUnprocessedCount = urgentNotifyUnprocessedCount();
        Integer dailyTrainingTaskCount = dailyTrainingTaskCount();
        Integer lawsGatherTaskCount = lawsGatherTaskCount();
        UrgentEventCheckTaskStatisticsDTO urgentEventCheckTaskCount = getUrgentEventCheckTaskCount();

        Integer myEventCheckTaskCount = 0;
        Integer myDispatchCenterTaskCount = 0;
        Integer anonymousReportingCount = anonymousReportingCount();
        if (Objects.nonNull(urgentEventCheckTaskCount)) {
            myEventCheckTaskCount =
                    Objects.isNull(urgentEventCheckTaskCount.getMyEventCheckTaskCount()) ? 0 : urgentEventCheckTaskCount.getMyEventCheckTaskCount();

            myDispatchCenterTaskCount =
                    Objects.isNull(urgentEventCheckTaskCount.getMyDispatchCenterTaskCount()) ? 0 : urgentEventCheckTaskCount.getMyDispatchCenterTaskCount();
        }
//        int assessmentSignTaskCount = assessmentSignTaskCount();
//        blendCountVo.setAssessmentSignTaskCount(assessmentSignTaskCount);
        blendCountVo.setContractorProjectCount(contractorProjectCount);
        blendCountVo.setContractorStaffCount(contractorStaffCount);
        blendCountVo.setRiskTaskPlanCount(riskTaskPlanCount());
        blendCountVo.setRiskTaskCount(riskTaskCount);
        blendCountVo.setRiskTaskReviewCount(riskTaskReviewCount());
        blendCountVo.setRiskCount(riskCount);
        blendCountVo.setRiskRectificationCount(riskRectificationCount);
        blendCountVo.setHazardCount(hazardCount);
        blendCountVo.setBowtieHazardCount(bowtieHazardCount);
        blendCountVo.setLecHazardCount(lecHazardCount);
        blendCountVo.setSclHazardCount(sclHazardCount);
        blendCountVo.setLaborApplyCount(healthyLaborApplyCount);
        blendCountVo.setPatrolTaskCount(patrolTaskCount);
        blendCountVo.setPatrolRecordCount(patrolRecordCount);
        blendCountVo.setAlarmUserNoticeCount(alarmUserNoticeCount);
        blendCountVo.setPssrCount(pssrCount);
        blendCountVo.setPhaCount(phaCount);
        blendCountVo.setPssrCount(pssrCount);
        blendCountVo.setPreCommissioningCount(preCommissioningCount);
        blendCountVo.setWorkSafeCount(workSafeCount);
        blendCountVo.setAnnualTrainCount(annualTrainCount);
        blendCountVo.setHazardousChemicalCheckTaskCount(hazardousChemicalCheckTaskCount);
        blendCountVo.setHazardousChemicalRegisterCount(hazardousChemicalRegisterCount);
        blendCountVo.setHazardousChemicalAppraisalCount(hazardousChemicalAppraisalCount);
        blendCountVo.setHarmInformCount(harmInformCount);
        blendCountVo.setExamineHistoryCount(examineHistoryCount);
        blendCountVo.setHazardousChemicalCount(hazardousChemicalCount);
        blendCountVo.setStaffReportCount(staffReportCount);//健康档案
        blendCountVo.setExamineInformCount(examineInformCount);//体检通知单
        blendCountVo.setLawsIdentifyCount(lawsIdentifyCount);
        blendCountVo.setLawsCirculationCount(lawsCirculationCount);
        blendCountVo.setTrainAndTestCount(trainAndTestCount);
        blendCountVo.setLaborProvideCount(laborProvideCount);
        blendCountVo.setAccidentReportCount(accidentReportCount);
        blendCountVo.setAccidentCorrectiveCount(accidentCorrectiveCount);
        blendCountVo.setDutyResponsibilityCount(dutyResponsibilityCount);//责任书
        blendCountVo.setDutyCommitmentCount(dutyCommitmentCount);//承诺书
        blendCountVo.setDutyTaskTrackingCount(dutyTaskTrackingCount);//任务跟踪
        blendCountVo.setUrgentDrillPlanCount(urgentDrillPlanCount);
        blendCountVo.setUrgentDrillActivityCount(urgentDrillActivityCount);
        blendCountVo.setUrgentDrillEvaluateCount(urgentDrillEvaluateCount);
        blendCountVo.setDutyManagementPlanCount(dutyManagementPlanCount); //管理方案统计
        blendCountVo.setGeneralCalculateStorageCount(generalCalculateStorageCount);
        blendCountVo.setHazardousPutStorageCount(hazardousPutStorageCount);
        blendCountVo.setHazardousOutStorageCount(hazardousOutStorageCount);
        blendCountVo.setHazardousCalculateStorageCount(hazardousCalculateStorageCount);
        blendCountVo.setSafetyAuditTaskCount(safetyAuditTaskCount);
        blendCountVo.setSafetyAuditReportCount(safetyAuditReportCount);
        blendCountVo.setDutyShiftPlanCount(dutyShiftPlanCount);
        blendCountVo.setDutyShiftScheduleChangeCount(dutyShiftScheduleChangeCount);
        blendCountVo.setDutyShiftScheduleCount(dutyShiftScheduleCount);
        blendCountVo.setSewageDischargeApplyCount(sewageDischargeApplyCount);//污水排放申请
        blendCountVo.setSewageDistributionCount(sewageDistributionCount);//配水表
        blendCountVo.setDutyActivityPlanCount(dutyActivityPlanCount);
        blendCountVo.setDutyActivityAuditCount(dutyActivityAuditCount);
        blendCountVo.setDutyActivityHandleCount(dutyActivityHandleCount);
        blendCountVo.setDutyActivitySignatureCount(dutyActivitySignatureCount);
        blendCountVo.setDutyActivityCheckerCount(dutyActivityCheckerCount);
        blendCountVo.setWorkPlanDeclarationCount(workPlanDeclarationCount);
        blendCountVo.setWorkSafeConstructionHandleCount(workSafeConstructionHandleCount);
        blendCountVo.setWorkSafeConstructionAcceptingCount(workSafeConstructionAcceptingCount);
        blendCountVo.setThreeSimultaneityUntreatedTaskCount(threeSimultaneityUntreatedTaskCount);
        blendCountVo.setMyCourseCount(myCourseCount);
        blendCountVo.setMyTestCount(myTestCount);
        blendCountVo.setLawsPeriodicEvaluation(lawsPeriodicEvaluation);
        blendCountVo.setContractorProjectFinishedTaskCount(contractorProjectFinishedTaskCount);
        blendCountVo.setPersonalScoreApplicationCount(personalScoreApplicationCount);
        blendCountVo.setContractorStaffViolationUndoCount(contractorStaffViolationUndoCount);
        blendCountVo.setDetectionTaskNum(detectionTaskNum);
        blendCountVo.setHseMeetingTaskCount(hseMeetingTaskCount);
        blendCountVo.setExternalAuditTaskCount(externalAuditTaskCount);
        blendCountVo.setExternalAuditPlanCount(externalAuditPlanCount);
        blendCountVo.setExternalAuditReportCount(externalAuditReportCount);
        blendCountVo.setInternalAuditTaskCount(internalAuditTaskCount);
        blendCountVo.setInternalAuditPlanCount(internalAuditPlanCount);
        blendCountVo.setInternalAuditReportCount(internalAuditReportCount);
        blendCountVo.setAlarmNoticeDangersourceCount(alarmNoticeDangersourceCount);
        blendCountVo.setIntrospectionTaskNum(introspectionTaskNum);
        blendCountVo.setTechChange(techChange);
        blendCountVo.setMaterialIdentify(materialIdentify);
        blendCountVo.setInstrumentBorrow(instrumentBorrow);
        blendCountVo.setSealPointApprove(sealPointApprove);
        blendCountVo.setFileApplicabilityReviewNum(fileApplicabilityReviewNum);
        blendCountVo.setSealPointResolveTaskCount(sealPointResolveTaskCount);
        blendCountVo.setSealPointExecutorTaskCount(sealPointExecutorTaskCount);
        blendCountVo.setDrainageStatisticsTaskCount(drainageStatisticsTaskCount);
        blendCountVo.setFactorEvaluationCount(factorEvaluationCount);
        blendCountVo.setAssessAppealCount(assessAppealCount);
        blendCountVo.setAnnualTrainTaskCount(annualTrainTaskCount);
        blendCountVo.setCompanyLevelFactorReviewCount(companyLevelFactorReviewCount);
        blendCountVo.setSelfEvaluationSafeCount(selfEvaluationSafeCount);
        blendCountVo.setVisitorReservationCount(visitorReservationCount);
        blendCountVo.setHseMeetingTaskWaitCount(hseMeetingTaskWaitCount);
        blendCountVo.setMatrixCourseCount(matrixCourseCount);
        blendCountVo.setContractorStaffViolationProcessCount(contractorStaffViolationProcessCount);
        blendCountVo.setContractorProjectQualityExamineCount(contractorProjectQualityExamineCount);
        blendCountVo.setExamCompleteActivityCount(examCompleteActivityCount);
        blendCountVo.setExamActivityAppealCount(examActivityAppealCount);
        blendCountVo.setUrgentNotifyUnprocessedCount(urgentNotifyUnprocessedCount);
        blendCountVo.setContractorProjectEvaluate(contractorProjectEvaluate);
        blendCountVo.setContractorSecurityDepositBack(contractorSecurityDepositBack);
        blendCountVo.setTeamJudge(teamJudge);
        blendCountVo.setDeptJudge(deptJudge);
        blendCountVo.setCompanyJudge(companyJudge);
        blendCountVo.setCostChangeCount(costChangeCount);
        blendCountVo.setWorkTicketApplyCount(workTicketApplyCount);
        blendCountVo.setWorkTicketApproveCount(workTicketApproveCount);
        blendCountVo.setContractorStaffBatchCount(contractorStaffBatchCount);
        blendCountVo.setWorkflowReissuePersonCardCount(workflowReissuePersonCardCount);
        blendCountVo.setActivityCreateApprovalCount(activityCreateApprovalCount);
        blendCountVo.setContractorToolCount(contractorToolCount);
        blendCountVo.setContractorStaffBatchTaskCount(convertContractorStaffBatchTaskCount());
        blendCountVo.setTechInterlockCount(techInterlockCount);
        blendCountVo.setTechAlarmCount(techAlarmCount);
        blendCountVo.setDailyTrainingTaskCount(dailyTrainingTaskCount);
        blendCountVo.setWorkAuthorizationCount(workAuthorizationCount);
        blendCountVo.setContractorApproveCount(contractorApproveCount);
        blendCountVo.setLawsGatherTaskCount(lawsGatherTaskCount);
        blendCountVo.setStopSendCount(stopSendCount);
        blendCountVo.setHazardApproveCount(hazardApproveCount);
        blendCountVo.setEquipmentOutageCount(equipmentOutageCount);
        blendCountVo.setAnnualTrainTaskChangeCount(annualTrainTaskChangeCount);
        blendCountVo.setContractorProjectChangeCount(contractorProjectChangeCount);
        blendCountVo.setUrgentEventCheckTaskCount(myEventCheckTaskCount);
        blendCountVo.setDispatchCenterMyTaskCount(myDispatchCenterTaskCount);
        blendCountVo.setAnonymousReportingCount(anonymousReportingCount);
        blendCountVo.setCarRegistrationCount(carRegistrationCount);
        blendCountVo.setDriverRegistrationCount(driverRegistrationCount);
    }

    private int assessmentSignTaskCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.assessmentSignTaskCount(reqBlend);
    }

    private UrgentEventCheckTaskStatisticsDTO getUrgentEventCheckTaskCount() {
        try {
            UserInfoDto userInfoDto = UserContext.getOrElseThrow();
            Integer tenantId = TenantContext.getOneOrElseThrow();
            return urgentStatisticsRpcService.countEventCheckTask(userInfoDto.getUserId(), tenantId);
        } catch (Exception ex) {
            log.error("getUrgentEventCheckTaskCount error, ", ex);
        }

        return null;
    }

    private Integer convertContractorStaffBatchTaskCount() {
        try {
            TaskInfoDTO taskInfoDTO = contractorRpcService.queryTaskInfo(UserContext.getId(), TenantContext.getOne());
            if (Objects.nonNull(taskInfoDTO)) {
                return taskInfoDTO.getTodo();
            }
        } catch (Exception e) {
            log.error("RPC查询承包商人员批量审批待办任务 错误: {}, {}", e.getMessage(), Optional.ofNullable(e.getStackTrace()).map(x -> x[0]).orElse(null));
            e.printStackTrace();
        }
        return 0;
    }

    private Integer anonymousReportingCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.anonymousReportingCount(reqBlend);
    }


//    @Override
//    public R commonUseCount() {
//        BlendCountVo blendCountVo = new BlendCountVo();
//
//        int contractorProjectCount = 0;
//        int contractorStaffCount = 0;
//        int laborApplyCount = 0;
//        int riskRectificationCount = 0;
//        int hazardCount = 0;
//        int annualTrainCount = 0;
//        int hazardousChemicalCheckTaskCount = 0;
//        int hazardousChemicalRegisterCount = 0;
//        int hazardousChemicalCount = 0;
//        int lawsIdentifyCount = 0;
//        int lawsCirculationCount = lawsCirculationCount();
//        int examineInformCount = 0;//体检通知单
//        int accidentReportCount = 0;
//        int accidentCorrectivceCount = 0;
//
//        Integer urgentDrillPlanCount = 0;
//        Integer urgentDrillActivityCount = 0;
//        Integer urgentDrillEvaluateCount = 0;
//        Integer generalCalculateStorageCount = 0;
//        int hazardousPutStorageCount = 0;
//        int hazardousOutStorageCount = 0;
//        int hazardousCalculateStorageCount = 0;
//
//        List<StatisticsNumGroupDto> waitOperationNum = workflowRpcUtil.getWaitOperationNum();
//        for (StatisticsNumGroupDto dto : waitOperationNum) {
//            if (dto.getName().equals(WorkflowKeyEnum.CONTRACTOR.getKey())) {
//                contractorProjectCount = dto.getNum();
//            } else if (dto.getName().equals(WorkflowKeyEnum.CONTRACTOR_STAFF.getKey())) {
//                contractorStaffCount = dto.getNum();
//            } else if (dto.getName().equals(WorkflowKeyEnum.HEALTHY_LABOR_APPLY.getKey())) {
//                laborApplyCount = dto.getNum();
//            } else if (dto.getName().equals(WorkflowKeyEnum.RISK_RECTIFICATION.getKey())) {
//                riskRectificationCount = dto.getNum();
//            } else if (dto.getName().equals(WorkflowKeyEnum.ANNUAL_TRAIN.getKey())) {
//                annualTrainCount = dto.getNum();
//            } else if (dto.getName().equals("hazardous_chemical_check_task")) {
//                hazardousChemicalCheckTaskCount = dto.getNum();
//            } else if (dto.getName().equals(WorkflowKeyEnum.HAZARDOUS_CHEMICAL_REGISTER.getKey())) {
//                hazardousChemicalRegisterCount = dto.getNum();
//            } else if (dto.getName().equals(WorkflowKeyEnum.HAZARDOUS_CHEMICAL.getKey())) {
//                hazardousChemicalCount = dto.getNum();
//            } else if (WorkflowKeyEnum.HEALTHY_EXAMINE_INFORM.getKey().equals(dto.getName())) {
//                examineInformCount = dto.getNum();
//            } else if (dto.getName().equals(WorkflowKeyEnum.LAWS_IDENTIFY.getKey())) {
//                lawsIdentifyCount = dto.getNum();
//            } else if (dto.getName().equals(WorkflowKeyEnum.ACCIDENT_REPORT.getKey())) {
//                accidentReportCount = dto.getNum();
//            } else if (dto.getName().equals(WorkflowKeyEnum.ACCIDENT_CORRECTIVE.getKey())) {
//                accidentCorrectivceCount = dto.getNum();
//            } else if (dto.getName().equals(WorkflowKeyEnum.URGENT_DRILL_PLAN.getKey())){
//                urgentDrillPlanCount = dto.getNum();
//            } else if (dto.getName().equals(WorkflowKeyEnum.URGENT_DRILL_ACTIVITY.getKey())){
//                urgentDrillActivityCount = dto.getNum();
//            } else if (dto.getName().equals(WorkflowKeyEnum.URGENT_DRILL_EVALUATE.getKey())){
//                urgentDrillEvaluateCount = dto.getNum();
//            } else if (dto.getName().equals(WorkflowKeyEnum.GENERAL_CALCULATE_STORAGE.getKey())){
//                generalCalculateStorageCount = dto.getNum();
//            } else if (dto.getName().equals(WorkflowKeyEnum.HAZARDOUS_PUT_STORAGE.getKey())) {
//                hazardousPutStorageCount = dto.getNum();
//            } else if (dto.getName().equals(WorkflowKeyEnum.HAZARDOUS_OUT_STORAGE.getKey())) {
//                hazardousOutStorageCount = dto.getNum();
//            } else if (dto.getName().equals(WorkflowKeyEnum.HAZARDOUS_CALCULATE_STORAGE.getKey())) {
//                hazardousCalculateStorageCount = dto.getNum();
//            }
////            else if (dto.getName().equals(WorkflowKeyEnum.HAZARD.getKey())) {
////                hazardCount = dto.getNum();
////            }
//        }
//
//        blendCountVo.setContractorProjectCount(contractorProjectCount + Optional.ofNullable(workflowRpcUtil.getCopySendNum()).orElse(0));
//        blendCountVo.setContractorStaffCount(contractorStaffCount);
//        blendCountVo.setLaborApplyCount(laborApplyCount);
//        blendCountVo.setRiskRectificationCount(riskRectificationCount);
//        blendCountVo.setHazardCount(hazardCount);
//
//        blendCountVo.setRiskTaskCount(riskTaskCount());
//        blendCountVo.setRiskCount(riskCount());
////        blendCountVo.setRiskTakePicturesCount(riskTakePicturesCount());
////        blendCountVo.setAccidentCount(accidentCount());
//        blendCountVo.setWorkSafeCount(workSafeAllCount());
//        blendCountVo.setVisitorCount(visitorCount());
//        blendCountVo.setLicenceCount(licenceCount());
//        blendCountVo.setDutyCount(dutyCount(UserContext.getId().intValue()));
//        blendCountVo.setPatrolTaskCount(patrolTaskCount());
//        blendCountVo.setAnnualTrainCount(annualTrainCount);
//        blendCountVo.setHazardousChemicalCheckTaskCount(hazardousChemicalCheckTaskCount);
//        blendCountVo.setHazardousChemicalRegisterCount(hazardousChemicalRegisterCount);
//        blendCountVo.setHarmInformCount(healthyHarmInformCount());//危害告知书
//        blendCountVo.setExamineHistoryCount(healthyExamineHistoryCount());//体检记录
//        blendCountVo.setHazardousChemicalCount(hazardousChemicalCount);
//        blendCountVo.setStaffReportCount(healthyStaffReportCount());//健康档案
//        blendCountVo.setExamineInformCount(examineInformCount);//体检通知单
//        blendCountVo.setLawsCirculationCount(lawsCirculationCount);
//        blendCountVo.setLawsIdentifyCount(lawsIdentifyCount);
//        blendCountVo.setTrainAndTestCount(trainAndTestCount());
//        blendCountVo.setLaborProvideCount(healthyLaborProvideCount());
//        blendCountVo.setAccidentReportCount(accidentReportCount);
//        blendCountVo.setAccidentCorrectiveCount(accidentCorrectivceCount);
//
//        blendCountVo.setUrgentDrillPlanCount(urgentDrillPlanCount);
//        blendCountVo.setUrgentDrillActivityCount(urgentDrillActivityCount);
//        blendCountVo.setUrgentDrillEvaluateCount(urgentDrillEvaluateCount);
//        blendCountVo.setGeneralCalculateStorageCount(generalCalculateStorageCount);
//        blendCountVo.setHazardousPutStorageCount(hazardousPutStorageCount);
//        blendCountVo.setHazardousOutStorageCount(hazardousOutStorageCount);
//        blendCountVo.setHazardousCalculateStorageCount(hazardousCalculateStorageCount);
//        return R.ok(blendCountVo);
//    }


    private int lawsCirculationCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.lawsCirculationCount(reqBlend);
    }

    @Override
    public R securityOverview() {
        SecurityOverviewVo securityOverviewVo = new SecurityOverviewVo();
        ReqBlend reqBlend = new ReqBlend(1);
        securityOverviewVo.setRiskCount(blendMapper.selectRiskCountInState(reqBlend));
        securityOverviewVo.setRiskTaskCount(blendMapper.selectRiskTaskCount(reqBlend));
        int num = 0;
        List<GroupDto> groupDtoList = blendMapper.selectWorkSafeGroupByType(reqBlend);
        for (GroupDto groupDto : groupDtoList) {
            num += groupDto.getCount();
        }
        securityOverviewVo.setWorkSafeCount(num);
        return R.ok(securityOverviewVo);
    }

    /**
     * 获取用户待办任务和待办计划
     *
     * @param reqVO
     * @return
     */
    @Override
    public BacklogRespVO findUserBacklog(BacklogReqVO reqVO) {
        List<BacklogItemRespVO> itemList = new ArrayList<>();
        try {
            //1.去 BacklogItemEnum 定义各自模块的枚举变量
            //2.统计各个模块的 数量，如果不为0，则加入itemList
            //3.各个模块自行catch异常
            Integer userId = reqVO.getUserId();
            Integer tenantId = TenantContext.getOne();
            //巡检
            itemList.add(new BacklogItemRespVO(BacklogItemEnum.PATROL_PLAN, patrolPlanService.countByUserId(userId, tenantId)));
            itemList.add(new BacklogItemRespVO(BacklogItemEnum.PATROL_TASK, patrolTaskService.countByUserId(userId, tenantId)));
            itemList.add(new BacklogItemRespVO(BacklogItemEnum.PATROL_TASK_RECORD, patrolTaskRecordService.countByUserId(userId, tenantId)));
            //环境检测任务
            itemList.add(new BacklogItemRespVO(BacklogItemEnum.DETECTION_TASK, epRpcUtil.getDetectionTaskNum(userId, tenantId)));
            //排水统计任务
            itemList.add(new BacklogItemRespVO(BacklogItemEnum.DRAINAGE_STATISTICS_TASK, epRpcUtil.getDrainageStatisticsTaskNum(userId, tenantId)));
            //领导干部带班任务
            itemList.add(new BacklogItemRespVO(BacklogItemEnum.DUTY_SHIFT_SCHEDULE, dutyRpcUtil.getBacklogNum(userId, tenantId)));
            //环境检测计划
            itemList.add(new BacklogItemRespVO(BacklogItemEnum.DETECTION_PLAN, epRpcUtil.getDetectionPlanNum(userId, tenantId)));
            //排水统计计划
            itemList.add(new BacklogItemRespVO(BacklogItemEnum.DRAINAGE_STATISTICS_PLAN, epRpcUtil.getDrainageStatisticsPlanNum(userId, tenantId)));
            //密封点检测计划
            itemList.add(new BacklogItemRespVO(BacklogItemEnum.SEAL_POINT_DETECTION_PLAN, epRpcUtil.sealPointDetectionPlanCountByUserId(userId, tenantId)));
            //密封点检测任务
            itemList.add(new BacklogItemRespVO(BacklogItemEnum.SEAL_POINT_DETECTION_TASK, epRpcUtil.sealPointDetectionTaskCountByUserId(userId, tenantId)));
            //隐患任务
            itemList.add(new BacklogItemRespVO(BacklogItemEnum.RISK_TASK, riskRpcUtil.riskTaskCount(userId.longValue())));
            //隐患计划
            Map<Integer, Long> riskMap = riskRpcUtil.riskPlanCount(userId);
            //JSA排查计划
            itemList.add(new BacklogItemRespVO(BacklogItemEnum.RISK_JSA_PLAN, riskMap));
            //BOWTIE排查计划
            itemList.add(new BacklogItemRespVO(BacklogItemEnum.RISK_BOWTIE_PLAN, riskMap));
            //lec
            itemList.add(new BacklogItemRespVO(BacklogItemEnum.RISK_LEC_PLAN, riskMap));
            //scl
            itemList.add(new BacklogItemRespVO(BacklogItemEnum.RISK_SCL_PLAN, riskMap));
            //zc
            itemList.add(new BacklogItemRespVO(BacklogItemEnum.RISK_ZC_PLAN, riskMap));
            //dc
            itemList.add(new BacklogItemRespVO(BacklogItemEnum.RISK_DC_PLAN, riskMap));
            //危化品计划
            itemList.add(new BacklogItemRespVO(BacklogItemEnum.CHEMICAL_PLAN, hazardousChemicalRpcUtil.planCount(userId)));
            //安全稽核计划
            itemList.add(new BacklogItemRespVO(BacklogItemEnum.SAFETY_AUDIT_PLAN, safetyAuditRpcUtil.planCount(userId)));
            //年度培训计划任务
            itemList.add(new BacklogItemRespVO(BacklogItemEnum.ANNUAL_TRAIN_PLAN_TASK, trainAndTestRpcUtil.getAnnualTrainPlanTaskNum(userId, tenantId)));
            //三级培训活动任务
            itemList.add(new BacklogItemRespVO(BacklogItemEnum.THREE_LEVEL_ACTIVITY_TASK, trainAndTestRpcUtil.getThreeLevelActivityTaskNum(userId, tenantId)));
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }
        BacklogRespVO backlogRespVO = BacklogRespVO.countTotalNum(itemList);
        return backlogRespVO;
    }

    /**
     * 处理用户待办任务和待办计划
     *
     * @param reqVO
     */
    @Override
    public void handleUserBacklog(HandleBacklogReqVO reqVO) {
        //通过moduleCodeList 比对 BacklogItemEnum中的 moduleCode 去区分各个业务模块进行相应操作
        //各个模块自行catch异常
        Integer tenantId = TenantContext.getOne();
        try {

            for (String moduleCode : reqVO.getModuleCodeList()) {
                BacklogItemEnum module = BacklogItemEnum.getByCode(moduleCode);
                if (Objects.isNull(module)) {
                    throw new ServiceException("该模块未接入，与对应开发人员确认后重试。");
                }
                switch (module) {
                    case RISK_TASK:
                        riskRpcUtil.taskEntrust(reqVO.getOriginalUserId(), reqVO.getTargetUserId(), reqVO.getRemark());
                        break;
                    case PATROL_TASK:
                        patrolTaskService.entrust(reqVO.getOriginalUserId(), reqVO.getTargetUserId(), reqVO.getRemark(), tenantId);
                        break;
                    case PATROL_TASK_RECORD:
                        patrolTaskRecordService.entrust(reqVO.getOriginalUserId(), reqVO.getTargetUserId(), reqVO.getRemark(), tenantId);
                        break;
                    case RISK_JSA_PLAN:
                        riskRpcUtil.planEditExecute(reqVO.getOriginalUserId(), reqVO.getTargetUserId(), BacklogItemEnum.RISK_JSA_PLAN);
                        break;
                    case RISK_BOWTIE_PLAN:
                        riskRpcUtil.planEditExecute(reqVO.getOriginalUserId(), reqVO.getTargetUserId(), BacklogItemEnum.RISK_BOWTIE_PLAN);
                        break;
                    case RISK_LEC_PLAN:
                        riskRpcUtil.planEditExecute(reqVO.getOriginalUserId(), reqVO.getTargetUserId(), BacklogItemEnum.RISK_LEC_PLAN);
                        break;
                    case RISK_SCL_PLAN:
                        riskRpcUtil.planEditExecute(reqVO.getOriginalUserId(), reqVO.getTargetUserId(), BacklogItemEnum.RISK_SCL_PLAN);
                        break;
                    case RISK_ZC_PLAN:
                        riskRpcUtil.planEditExecute(reqVO.getOriginalUserId(), reqVO.getTargetUserId(), BacklogItemEnum.RISK_ZC_PLAN);
                        break;
                    case RISK_DC_PLAN:
                        riskRpcUtil.planEditExecute(reqVO.getOriginalUserId(), reqVO.getTargetUserId(), BacklogItemEnum.RISK_DC_PLAN);
                        break;
                    case CHEMICAL_PLAN:
                        hazardousChemicalRpcUtil.planEditExecute(reqVO.getOriginalUserId(), reqVO.getTargetUserId());
                        break;
                    case SAFETY_AUDIT_PLAN:
                        safetyAuditRpcUtil.planEditExecute(reqVO.getOriginalUserId(), reqVO.getTargetUserId());
                        break;

                    case PATROL_PLAN:
                        patrolPlanService.changeExecutor(reqVO.getOriginalUserId(), reqVO.getTargetUserId(), tenantId);
                        break;
                    case DETECTION_TASK:
                        epRpcUtil.batchEntrustDetectionTask(reqVO);
                        break;
                    case DRAINAGE_STATISTICS_TASK:
                        epRpcUtil.batchEntrustDrainageStatisticsTask(reqVO);
                        break;
                    case DUTY_SHIFT_SCHEDULE:
                        dutyRpcUtil.batchEntrust(reqVO.getOriginalUserId(), reqVO.getTargetUserId(), tenantId);
                        break;
                    case DETECTION_PLAN:
                        epRpcUtil.batchEntrustDetectionPlan(reqVO);
                        break;
                    case DRAINAGE_STATISTICS_PLAN:
                        epRpcUtil.batchEntrustDrainageStatisticsPlan(reqVO);
                        break;
                    case SEAL_POINT_DETECTION_PLAN:
                        epRpcUtil.sealPointDetectionPlanChangeExecutor(reqVO.getOriginalUserId(), reqVO.getTargetUserId(), UserContext.getId().intValue(), tenantId);
                        break;
                    case SEAL_POINT_DETECTION_TASK:
                        epRpcUtil.sealPointDetectionTaskEntrust(reqVO.getOriginalUserId(), reqVO.getTargetUserId(), tenantId, reqVO.getRemark());
                        break;
                    case ANNUAL_TRAIN_PLAN_TASK:
                        trainAndTestRpcUtil.entrustAnnualTrainPlanTask(reqVO.getOriginalUserId(), reqVO.getTargetUserId(), tenantId);
                        break;
                    case THREE_LEVEL_ACTIVITY_TASK:
                        trainAndTestRpcUtil.entrustThreeLevelActivityTask(reqVO.getOriginalUserId(), reqVO.getTargetUserId(), tenantId);
                    default:
                        throw new ServiceException("该模块未接入，与对应开发人员确认后重试。");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }
    }

    /**
     * 清空用户待办任务和待办计划
     *
     * @param reqVO
     */
    @Override
    public void clearUserBacklog(HandleBacklogReqVO reqVO) {
        try {
            Integer originalUserId = reqVO.getOriginalUserId();
            Integer tenantId = TenantContext.getOne();
            for (String moduleCode : reqVO.getModuleCodeList()) {
                BacklogItemEnum module = BacklogItemEnum.getByCode(moduleCode);
                if (Objects.isNull(module)) {
                    throw new ServiceException("该模块未接入，与对应开发人员确认后重试。");
                }
                switch (module) {
                    case PATROL_PLAN:
                        patrolPlanService.clearByUser(reqVO.getOriginalUserId(), tenantId);
                        break;
                    case DETECTION_PLAN:
                        epRpcUtil.batchClearDetectionPlan(originalUserId, tenantId);
                        break;
                    case DRAINAGE_STATISTICS_PLAN:
                        epRpcUtil.batchClearDrainageStatisticsPlan(originalUserId, tenantId);
                        break;
                    case SEAL_POINT_DETECTION_PLAN:
                        epRpcUtil.sealPointDetectionPlanClearByUser(originalUserId, UserContext.getId().intValue(), tenantId);
                        break;

                    case RISK_JSA_PLAN:
                        riskRpcUtil.planDelExecute(originalUserId, BacklogItemEnum.RISK_JSA_PLAN);
                        break;
                    case RISK_BOWTIE_PLAN:
                        riskRpcUtil.planDelExecute(originalUserId, BacklogItemEnum.RISK_BOWTIE_PLAN);
                        break;
                    case RISK_LEC_PLAN:
                        riskRpcUtil.planDelExecute(originalUserId, BacklogItemEnum.RISK_LEC_PLAN);
                        break;
                    case RISK_SCL_PLAN:
                        riskRpcUtil.planDelExecute(originalUserId, BacklogItemEnum.RISK_SCL_PLAN);
                        break;
                    case RISK_ZC_PLAN:
                        riskRpcUtil.planDelExecute(originalUserId, BacklogItemEnum.RISK_ZC_PLAN);
                        break;
                    case RISK_DC_PLAN:
                        riskRpcUtil.planDelExecute(originalUserId, BacklogItemEnum.RISK_DC_PLAN);
                        break;
                    case CHEMICAL_PLAN:
                        hazardousChemicalRpcUtil.planDelExecute(originalUserId);
                        break;
                    case SAFETY_AUDIT_PLAN:
                        safetyAuditRpcUtil.planDelExecute(originalUserId);
                        break;
                    default:
                        throw new ServiceException("该模块未接入，与对应开发人员确认后重试。");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }
    }

    /**
     * 获取风险区域档案关联的各table下的统计数据
     *
     * @param reqVO
     * @return
     */
    @Override
    public HazardAreaRecordStaticRespVO findHazardAreaRecordStatic(HazardAreaRecordStaticReqVO reqVO) {
        HazardAreaRecordStaticRespVO respVO = HazardAreaRecordStaticRespVO.init();
        setAreaRecordParams(reqVO);
        if (!CollectionUtils.isEmpty(reqVO.getAreaIdList())) {
            respVO.setEnvironmentCheckNum(blendMapper.getEnvironmentCheckNum(reqVO));
            respVO.setOccupationalHazardNum(blendMapper.getHarmPlaceNum(reqVO));//危害场所
            respVO.setInvolvedPersonNum(processInvolvedPersonNum(reqVO));//涉及人员
            respVO.setChemicalNum(blendMapper.getChemicalCountByEffective(reqVO));
            respVO.setEquipNum(equipmentRpcService.cmk(reqVO.getAreaId(), reqVO.getAreaIdList()));
        }
        return respVO;
    }

    private void setAreaRecordParams(HazardAreaRecordStaticReqVO reqVO) {
        if (Objects.nonNull(reqVO.getAreaId())) {
            List<Long> selfAndChildId = areaRpcService.getSelfAndChildId(reqVO.getAreaId());
            reqVO.setAreaIdList(selfAndChildId);
        }
        reqVO.setTenantId(TenantContext.getOne());
    }

    private int processInvolvedPersonNum(HazardAreaRecordStaticReqVO query) {
        List<Integer> allPostIds = blendMapper.getInvolvedPerson(query);
        if (CollectionUtils.isEmpty(allPostIds)) {
            return 0;
        }

        return userRpcV2Service.getByPostIds(allPostIds, query.getTenantId()).size();
    }

    /**
     * 承包商项目待办前3条
     */
    private List<BlendDto> contractorProject() {
        ReqBlend reqBlend = new ReqBlend();
        List<BlendDto> blendDtoList = blendMapper.selectContractorProject(reqBlend);
        return blendDtoList.stream()
                .peek(p -> p.setType(BlendTypeEnum.CONTRACTOR.getType()))
                .collect(Collectors.toList());
    }

    /**
     * 承包商人员待办前3条
     */
    private List<BlendDto> contractorStaff() {
        ReqBlend reqBlend = new ReqBlend();
        List<BlendDto> blendDtoList = blendMapper.selectContractorStaff(reqBlend);
        return blendDtoList.stream()
                .peek(p -> p.setType(BlendTypeEnum.CONTRACTOR_STAFF.getType()))
                .collect(Collectors.toList());
    }

    /**
     * 承包商项目待办数量
     */
    private int contractorProjectCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.selectContractorProjectCountAndCheck(reqBlend);
    }

    /**
     * 承包商人员待办数量
     */
    private int contractorStaffCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.selectContractorStaffCountAndCheck(reqBlend);
    }

    /**
     * 承包商项目抄送数量
     */
    private int contractorProjectCarbonCopyCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.contractorProjectCarbonCopyCount(reqBlend);
    }

    /**
     * 隐患任务待办
     */
    private List<BlendDto> riskTask() {
        ReqBlend reqBlend = new ReqBlend();
        List<BlendDto> blendDtoList = blendMapper.selectRiskTask(reqBlend);
        return blendDtoList.stream()
                .peek(p -> p.setType(BlendTypeEnum.RISK_PLAN.getType()))
                .collect(Collectors.toList());
    }

    /**
     *
     */
    private List<BlendDto> safetyAuditTask() {
        ReqBlend reqBlend = new ReqBlend();
        List<BlendDto> blendDtoList = blendMapper.safetyAuditTask(reqBlend);
        return blendDtoList.stream()
                .peek(p -> p.setType(BlendTypeEnum.SAFETY_AUDIT_TASK.getType()))
                .collect(Collectors.toList());
    }

    /**
     * 隐患排查计划待办数量
     */
    private int riskTaskPlanCount() {
        ReqBlend reqBlend = new ReqBlend();
        List<Integer> dutyDepartmentIds = getDutyDepartmentIds(approvalService.getDepartmentWithUserIds(), UserContext.getId().intValue());
        return blendMapper.selectRiskTaskPlanCount(dutyDepartmentIds, reqBlend);
    }

    private List<Integer> getDutyDepartmentIds(List<ApprovalUserDTO> approvalDepartmentWithUserIds, Integer userId) {
        List<Integer> dutyDepartmentIds = new ArrayList<>();
        dutyDepartmentIds.add(0);
        approvalDepartmentWithUserIds = approvalDepartmentWithUserIds.stream()
                .filter(p -> (null != p.getHsePrincipalUserIds() && p.getHsePrincipalUserIds().contains(userId)) ||
                        (null != p.getDeviceSectionPrincipalUserIds() && p.getDeviceSectionPrincipalUserIds().contains(userId)))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(approvalDepartmentWithUserIds)) {
            approvalDepartmentWithUserIds.forEach(approvalDepartmentWithUserId -> {
                dutyDepartmentIds.add(approvalDepartmentWithUserId.getDepartmentId());
            });
        }
        return dutyDepartmentIds;
    }

    /**
     * 隐患任务评审待办数量
     */
    private int riskTaskReviewCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.selectRiskTaskReviewCount(reqBlend);
    }


    /**
     * 隐患清单待办前3条
     */
    private List<BlendDto> risk() {
        ReqBlend reqBlend = new ReqBlend();
        List<BlendDto> blendDtoList = blendMapper.selectRisk(reqBlend);
        return blendDtoList.stream().peek(p -> p.setType(BlendTypeEnum.RISK.getType())).collect(Collectors.toList());
    }

    /**
     * 隐患清单待办数量
     */
    private int riskCount() {
        ReqBlend reqBlend = new ReqBlend();
        reqBlend.setState(BlendConstant.RISK_STATE_ONE);
        return blendMapper.selectRiskCount(reqBlend);
    }

    /**
     * 隐患整改单待办前3条
     */
    private List<BlendDto> riskRectification() {
        ReqBlend reqBlend = new ReqBlend();
        Integer userId = UserContext.getId().intValue();
        List<BlendDto> blendDtoList = blendMapper.selectRiskRectification(reqBlend);
        for (BlendDto blendDto : blendDtoList) {
            if (null != blendDto.getAreaPrincipalUserId() && blendDto.getAreaPrincipalUserId().equals(userId)) {
                blendDto.setReadState(blendDto.getAreaPrincipalReadState());
            }
        }
        return blendDtoList.stream()
                .peek(p -> p.setType(BlendTypeEnum.RISK_RECTIFICATION.getType()))
                .collect(Collectors.toList());
    }

    /**
     * 隐患整改单待办数量
     */
    private int riskRectificationCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.selectRiskRectificationCount(reqBlend);
    }


    /**
     * 隐患随手拍待办前3条(废弃)
     */
    private List<BlendDto> riskTakePictures() {
        ReqBlend reqBlend = new ReqBlend();
        List<BlendDto> blendDtoList = blendMapper.selectRiskTakePictures(reqBlend);
        return blendDtoList.stream()
                .peek(p -> p.setType(BlendTypeEnum.RISK_TAKE_PICTURES.getType()))
                .collect(Collectors.toList());
    }

    /**
     * 隐患随手拍待办数量(废弃)
     */
    private int riskTakePicturesCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.selectRiskTakePicturesCount(reqBlend);
    }

    /**
     * 风险待办前3条
     */
    private List<BlendDto> hazard() {
        ReqBlend reqBlend = new ReqBlend();
        List<BlendDto> blendDtoList = blendMapper.selectHazard(reqBlend);
        return blendDtoList.stream().peek(p -> p.setType(BlendTypeEnum.HAZARD.getType())).collect(Collectors.toList());
    }

    /**
     * 风险待办数量
     */
    private int hazardCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.selectHazardCount(reqBlend);
    }

    /**
     * 作业安全待办前3条(办证人、签字人、作业人/监护人)
     */
    private List<BlendDto> workSafe() {
        List<BlendDto> blendDtoList = new ArrayList<>();
        UserInfoDto userInfoDto = UserContext.get();
        Long userId = userInfoDto.getUserId();
        if (null != userId && userId > 0) {
            List<Long> workIdList = blendMapper.selectWorkId(userId);
            if (workIdList.size() > 0) {
                ReqBlend req = new ReqBlend();
                req.setWorkIdList(workIdList);
                blendDtoList = blendMapper.selectWorkSafe(req);
            }
        }

        return blendDtoList.stream().peek(p -> {
            p.setType(BlendTypeEnum.WORK_SAFE.getType());
            p.setReadState(readConstant.READ_NO);
        }).collect(Collectors.toList());
    }

    /**
     * 作业安全待办数量(办证人、签字人、作业人/监护人)
     */
    private int workSafeCount() {
        int count = 0;
        Long userId = UserContext.get().getUserId();
        if (null != userId && userId > 0) {
            ReqBlend req = new ReqBlend();
            count = blendMapper.selectWorkSafeCount(req);
        }
        return count;
    }

    private int safetyAuditTaskCount() {
        return safetyAuditRpcUtil.taskCount();
    }

    /**
     * 事故待办前3条
     */
    private List<BlendDto> accident() {
        ReqBlend reqBlend = new ReqBlend();
        List<BlendDto> blendDtoList = blendMapper.selectAccident(reqBlend);
        List<Long> ids = blendMapper.selectAccidentRead(reqBlend);

        return blendDtoList.stream().peek(p -> {
            p.setType(BlendTypeEnum.ACCIDENT.getType());
            if (ids.contains(p.getId())) {
                p.setReadState(readConstant.READ_YES);
            } else {
                p.setReadState(readConstant.READ_NO);
            }
        }).collect(Collectors.toList());
    }

    /**
     * 事故待办数量
     */
    private int accidentCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.selectAccidentCount(reqBlend);
    }

    /**
     * 体检记录待办前3条
     */
    private List<BlendDto> healthyExamineHistory() {
        ReqBlend reqBlend = new ReqBlend();
        List<BlendDto> blendDtos = blendMapper.healthyExamineHistory(reqBlend);
        for (BlendDto item : blendDtos) {
            String name = item.getName();
            item.setType(BlendTypeEnum.HEALTHY_EXAMINE_HISTORY.getType());
            item.setName(Optional.ofNullable(UserContext.get()).map(UserInfoDto::getName).orElse(Strings.EMPTY) + name);
        }

        return blendDtos;
    }

    /**
     * 体检记录待办数量
     */
    private int healthyExamineHistoryCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.healthyExamineHistoryCount(reqBlend);
    }

    /**
     * 危害告知书待办前3条
     */
    private List<BlendDto> healthyHarmInform() {
        ReqBlend reqBlend = new ReqBlend();
        List<BlendDto> blendDtos = blendMapper.healthyHarmInform(reqBlend);
        for (BlendDto item : blendDtos) {
            item.setType(BlendTypeEnum.HEALTHY_HARM_INFORM.getType());
        }

        return blendDtos;
    }

    /**
     * 责任书待办前3条
     */
    private List<BlendDto> dutyResponsibility() {
        ReqBlend reqBlend = new ReqBlend();
        List<BlendDto> blendDtos = blendMapper.dutyResponsibility(reqBlend);
        for (BlendDto item : blendDtos) {
            item.setType(BlendTypeEnum.DUTY_RESPONSIBILITY.getType());
        }

        return blendDtos;
    }


    /**
     * 承诺书待办前3条
     */
    private List<BlendDto> dutyCommitment() {
        ReqBlend reqBlend = new ReqBlend();
        List<BlendDto> blendDtos = blendMapper.dutyCommitment(reqBlend);
        for (BlendDto item : blendDtos) {
            item.setType(BlendTypeEnum.DUTY_COMMITMENT.getType());
        }

        return blendDtos;
    }

    /**
     * 任务跟踪待办前3条
     */
    private List<BlendDto> dutyTaskTracking() {
        ReqBlend reqBlend = new ReqBlend();
        List<BlendDto> blendDtos = blendMapper.dutyTaskTracking(reqBlend);
        for (BlendDto item : blendDtos) {
            item.setType(BlendTypeEnum.DUTY_TASK_TRACKING.getType());
        }

        return blendDtos;
    }


    /**
     * 危害告知书待办数量
     */
    private int healthyHarmInformCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.healthyHarmInformCount(reqBlend);
    }

    /**
     * 健康档案待办前3条
     */
    private List<BlendDto> healthyStaffReport() {
        ReqBlend reqBlend = new ReqBlend();
        List<BlendDto> blendDtos = blendMapper.healthyStaffReport(reqBlend);
        for (BlendDto item : blendDtos) {
            String name = item.getName();
            item.setType(BlendTypeEnum.HEALTHY_STAFF_REPORT.getType());
            item.setName(Optional.ofNullable(UserContext.get())
                    .map(o -> o.getName() + "健康档案")
                    .orElse(Strings.EMPTY) + name);
        }

        return blendDtos;
    }

    /**
     * 健康档案待办数量
     */
    private int healthyStaffReportCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.healthyStaffReportCount(reqBlend);
    }

    /**
     * 责任书待办数量
     */
    private int dutyResponsibilityCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.dutyResponsibilityCount(reqBlend);
    }

    /**
     * 承诺书待办数量
     */
    private int dutyCommitmentCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.dutyCommitmentCount(reqBlend);
    }

    /**
     * 任务跟踪待办数量
     */
    private int dutyTaskTrackingCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.dutyTaskTrackingCount(reqBlend);
    }

    /**
     * 今日或未来里的未接待和接待中的邀约单数量
     */
    private int visitorCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.selectVisitorCount(reqBlend);
    }

    /**
     * 证书已到期、即将到期状态下的证书数量
     *
     * @return
     */
    private int licenceCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.selectLicenceCount(reqBlend);
    }

    private int dutyActivityHandleCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.dutyActivityHandleCount(reqBlend);
    }

    private int dutyActivitySignatureCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.dutyActivitySignatureCount(reqBlend);
    }

    private int dutyActivityCheckerCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.dutyActivityCheckerCount(reqBlend);
    }

    private Integer workSafeConstructionHandleCount() {
        ReqBlend reqBlend = new ReqBlend();
        Boolean isManager = this.judgeIsWorkSafeConstructionManager(reqBlend);
        return blendMapper.workSafeConstructionHandleCount(reqBlend, isManager);
    }

    private int workSafeConstructionAcceptingCount() {
        ReqBlend reqBlend = new ReqBlend();
        Boolean isManager = this.judgeIsWorkSafeConstructionManager(reqBlend);
        return blendMapper.workSafeConstructionAcceptingCount(reqBlend, isManager);
    }

    private int myCourseCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.myCourseCount(reqBlend);
    }

    private int myTestCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.myTestCount(reqBlend);
    }

    private Boolean judgeIsWorkSafeConstructionManager(ReqBlend reqBlend) {
        List<Integer> principalList = blendMapper.queryConstructionPrincipal(reqBlend);
        Boolean isManager = Boolean.FALSE;
        if (CollUtil.isNotEmpty(principalList)) {
            if (CollUtil.contains(principalList, UserContext.getId().intValue())) {
                isManager = Boolean.TRUE;
            }
        }
        return isManager;
    }

    /**
     * 任务跟踪待办前3条
     */
    private List<BlendDto> dutyActivityHandle() {
        ReqBlend reqBlend = new ReqBlend();
        List<BlendDto> blendDtos = blendMapper.dutyActivityHandle(reqBlend);
        for (BlendDto item : blendDtos) {
            item.setType(BlendTypeEnum.DUTY_ACTIVITY_HANDLE.getType());
        }

        return blendDtos;
    }


    /**
     * 任务跟踪待办前3条
     */
    private List<BlendDto> dutyActivitySignature() {
        ReqBlend reqBlend = new ReqBlend();
        List<BlendDto> blendDtos = blendMapper.dutyActivitySignature(reqBlend);
        for (BlendDto item : blendDtos) {
            item.setType(BlendTypeEnum.DUTY_ACTIVITY_SIGNATURE.getType());
        }

        return blendDtos;
    }


    /**
     * 零星土建维修实施待办前3条
     */
    private List<BlendDto> workSafeConstructionHandle() {
        ReqBlend reqBlend = new ReqBlend();
        Boolean isManager = this.judgeIsWorkSafeConstructionManager(reqBlend);
        List<BlendDto> blendDtos = blendMapper.workSafeConstructionHandle(reqBlend, isManager);
        for (BlendDto item : blendDtos) {
            item.setType(BlendTypeEnum.WORK_SAFE_CONSTRUCTION.getType());
        }
        return blendDtos;
    }

    /**
     * 获取三同时待办任务
     *
     * @return
     */
    private int threeSimultaneityUntreatedTaskCount() {
        int count = 0;
        Integer tenantId = TenantContext.getOne();
        Long userId = UserContext.getId();
        try {
            RpcContext context = RpcContext.getContext();
            context.setAttachment(CONTEXT_TENANT_KEY, tenantId);
            context.setAttachment(CONTEXT_USER_KEY, userId);
            count = threeSimultaneityTaskService.queryTaskCount().getUnderWayCount();
            log.info("threeSimultaneityTaskService.queryTaskCount 调用成功,tenantId:{}, userId:{}, res:{}", tenantId, userId, count);
        } catch (Exception e) {
            log.error("threeSimultaneityTaskService.queryTaskCount 调用失败,tenantId:{}, userId:{}", tenantId, userId, e);
        }
        return count;
    }

    /**
     * 获取内审任务待办数据
     *
     * @return
     */
    private Integer getInternalAuditTaskCount() {
        Integer count = 0;
        Integer tenantId = TenantContext.getOne();
        Long userId = UserContext.getId();
        try {
            RpcContext context = RpcContext.getContext();
            context.setAttachment(InternalAuditTaskRpcService.CONTEXT_TENANT_KEY, tenantId);
            context.setAttachment(InternalAuditTaskRpcService.CONTEXT_USER_KEY, userId);
            InternalTaskCountDTO taskCountDTO = internalAuditTaskRpcService.getInternalAuditTaskCount();
            log.info("internalAuditTaskRpcService.getInternalAuditTaskCount() 调用成功,tenantId:{}, userId:{}, res:{}", tenantId, userId, JSONObject.toJSONString(taskCountDTO));
            if (Objects.nonNull(taskCountDTO) && Objects.nonNull(taskCountDTO.getCompleteCount())) {
                count = taskCountDTO.getUnCompleteCount();
            }
        } catch (Exception e) {
            log.error("internalAuditTaskRpcService.getInternalAuditTaskCount(),tenantId:{}, userId:{}", tenantId, userId, e);
        }
        return count;
    }

    /**
     * 获取外审任务待办数据
     *
     * @return
     */
    private Integer getExternalAuditTaskCount() {
        Integer count = 0;
        Integer tenantId = TenantContext.getOne();
        Long userId = UserContext.getId();
        try {
            RpcContext context = RpcContext.getContext();
            context.setAttachment(OutAuditTaskService.CONTEXT_TENANT_KEY, tenantId);
            context.setAttachment(OutAuditTaskService.CONTEXT_USER_KEY, userId);
            OutAuditTaskCountDTO outAuditTaskCount = outAuditTaskService.getOutAuditTaskCount();
            if (Objects.nonNull(outAuditTaskCount) && Objects.nonNull(outAuditTaskCount.getUnCompleteCount())) {
                count = outAuditTaskCount.getUnCompleteCount();
            }
            log.info("outAuditTaskService.getOutAuditTaskCount 调用成功,tenantId:{}, userId:{}, res:{}", tenantId, userId, count);
        } catch (Exception e) {
            log.error("outAuditTaskService.getOutAuditTaskCount 调用失败,tenantId:{}, userId:{}", tenantId, userId, e);
        }
        return count;
    }


    private int contractorProjectFinishedCount() {
        try {
            TaskInfoDTO result = contractorRpcService.queryProject(UserContext.getId(), TenantContext.getOne());
            if (Objects.nonNull(result)) {
                return result.getTodo();
            }
        } catch (Exception e) {
            log.error("RPC查询承包商项目清代待办任务: {}, {}", e.getMessage(), Optional.ofNullable(e.getStackTrace()).map(x -> x[0]).orElse(null));
            e.printStackTrace();
        }
        return 0;
    }

    private int contractorStaffViolationUndoCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.contractorStaffViolationUndoCount(reqBlend);
    }

    private Integer detectionTaskNum() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.detectionTaskNum(reqBlend);
    }

    private int introspectionTaskNum() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.introspectionTaskNum(reqBlend);
    }

    private int sealPointResolveCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.sealPointResolveCount(reqBlend);
    }

    private int sealPointExecutorCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.sealPointExecutorCount(reqBlend);
    }

    private Integer drainageStatisticsTaskCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.drainageStatisticsTaskCount(reqBlend);
    }

    private int annualTrainTaskCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.annualTrainTaskCount(reqBlend);
    }

    private int hseMeetingTaskWaitCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.hseMeetingTaskWaitCount(reqBlend);
    }

    private int contractorProjectQualityExamineCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.contractorProjectQualityExamineCount(reqBlend);
    }


    private Integer urgentNotifyUnprocessedCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.urgentNotifyUnprocessedCount(reqBlend);
    }

    private Integer dailyTrainingTaskCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.dailyTrainingTaskCount(reqBlend);
    }

    private Integer lawsGatherTaskCount() {
        ReqBlend reqBlend = new ReqBlend();
        return blendMapper.lawsGatherTaskCount(reqBlend);
    }
}
