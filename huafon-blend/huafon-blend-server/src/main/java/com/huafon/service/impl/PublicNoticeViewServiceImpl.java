package com.huafon.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huafon.dao.mapper.PublicNoticeViewMapper;
import com.huafon.models.entity.PublicNoticeView;
import com.huafon.service.PublicNoticeViewService;
import com.huafon.support.config.UserContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 公告通知查阅 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@Service
public class PublicNoticeViewServiceImpl extends ServiceImpl<PublicNoticeViewMapper, PublicNoticeView> implements PublicNoticeViewService {

    @Override
    public void increment(Integer publicNoticeId, Integer userId) {
        PublicNoticeView one = this.getOne(new LambdaQueryWrapper<PublicNoticeView>()
                .eq(PublicNoticeView::getPublicNoticeId, publicNoticeId)
                .eq(PublicNoticeView::getUserId, userId));
        if (one == null) {
            PublicNoticeView publicNoticeView = new PublicNoticeView();
            publicNoticeView.setPublicNoticeId(publicNoticeId);
            publicNoticeView.setUserId(userId);
            publicNoticeView.setCreate(UserContext.getId());
            save(publicNoticeView);
        }
    }

    @Override
    public int getCount(Integer publicNoticeId) {
        return this.count(new LambdaQueryWrapper<PublicNoticeView>()
                .eq(PublicNoticeView::getPublicNoticeId, publicNoticeId));
    }

    @Override
    public Map<Integer, List<PublicNoticeView>> groupByPublicNoticeId(List<Integer> publicNoticeIds) {
        if (!CollectionUtils.isEmpty(publicNoticeIds)) {
            List<PublicNoticeView> list = this.list(new LambdaQueryWrapper<PublicNoticeView>()
                    .in(PublicNoticeView::getPublicNoticeId, publicNoticeIds));
            return list.stream().collect(Collectors.groupingBy(PublicNoticeView::getPublicNoticeId));
        }
        return Collections.emptyMap();
    }

    @Override
    public List<PublicNoticeView> getByPublicNoticeId(Integer publicNoticeId) {
        return this.list(new LambdaQueryWrapper<PublicNoticeView>()
                .eq(PublicNoticeView::getPublicNoticeId, publicNoticeId)
                .orderByDesc(PublicNoticeView::getCreateTime));
    }

}
