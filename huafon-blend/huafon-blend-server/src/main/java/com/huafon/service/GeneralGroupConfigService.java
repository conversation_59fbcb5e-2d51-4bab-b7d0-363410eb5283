package com.huafon.service;

import com.huafon.framework.mybatis.service.MybatisIService;
import com.huafon.models.entity.GeneralGroupConfig;
import com.huafon.models.reqo.ReqGeneralGroupConfig;
import com.huafon.models.vo.GeneralGroupConfigVo;

import java.util.List;

/**
* 通用分组配置
* <AUTHOR>
* @since 2023-07-28 17:13
*/
public interface GeneralGroupConfigService extends MybatisIService<GeneralGroupConfig> {

	void addProperty(String groupKey, Integer limit, boolean useTenant, GeneralGroupConfigVo source);

	void editProperty(String groupKey, boolean useTenant, GeneralGroupConfigVo source);

	void saveOrUpdateProperty(String groupKey, Integer limit, boolean useTenant, GeneralGroupConfigVo source);

	void batchSubmitProperty(String groupKey, Integer limit, boolean useTenant, List<GeneralGroupConfigVo> sources);

	void deleteProperty(ReqGeneralGroupConfig query);

	List<GeneralGroupConfigVo> getProperties(ReqGeneralGroupConfig query);

}
