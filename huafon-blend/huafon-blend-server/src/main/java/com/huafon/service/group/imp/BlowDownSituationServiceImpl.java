package com.huafon.service.group.imp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huafon.common.config.TenantContext;
import com.huafon.common.utils.uuid.UUID;
import com.huafon.dao.mapper.group.BlowDownSituationMapper;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.models.dto.group.GroupDataPushDTO;
import com.huafon.models.entity.group.BlowDownSituation;
import com.huafon.models.vo.group.AccidentReportVO;
import com.huafon.models.vo.group.BlowDownSituationPageReqVO;
import com.huafon.models.vo.group.BlowDownSituationPageRespVO;
import com.huafon.models.vo.group.BlowDownSituationVO;
import com.huafon.service.group.BlowDownSituationDetailService;
import com.huafon.service.group.BlowDownSituationService;
import com.huafon.support.core.pojo.SystemCode;
import com.huafon.support.exceptions.ServiceException;
import com.huafon.utils.GroupPushUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Objects;

/**
 * <p>
 * 集团数据上报-排污情况 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-09
 */
@Service
public class BlowDownSituationServiceImpl extends ServiceImpl<BlowDownSituationMapper, BlowDownSituation> implements BlowDownSituationService {

    @Autowired
    private BlowDownSituationDetailService blowDownSituationDetailService;
    @Autowired
    private GroupPushUtil groupPushUtil;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(BlowDownSituationVO reqVO) {
        reqVO.setId(null);
        BlowDownSituation entity = BlowDownSituationVO.convert(reqVO);
        entity.setVersion(1);
        entity.setIsLastVersion(true);
        entity.setUuid(UUID.randomUUID().toString());
        baseMapper.insert(entity);
        blowDownSituationDetailService.save(entity.getId(),reqVO.getDetailList());
        groupPushUtil.pushData(convertPushData(entity.getId()));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void modify(BlowDownSituationVO reqVO) {
        BlowDownSituation old = baseMapper.selectById(reqVO.getId());
        if (Objects.isNull(old)) {
            throw new ServiceException(SystemCode.DATA_NOT_EXIST);
        }
        old.setIsLastVersion(false);
        old.setRemark(reqVO.getRemark());
        baseMapper.updateById(old);
        reqVO.setId(null);
        reqVO.setRemark(null);
        BlowDownSituation entity = BlowDownSituationVO.convert(reqVO);
        entity.setVersion(old.getVersion()+1);
        entity.setIsLastVersion(true);
        entity.setUuid(old.getUuid());
        baseMapper.insert(entity);
        blowDownSituationDetailService.save(entity.getId(),reqVO.getDetailList());
        groupPushUtil.pushData(convertPushData(entity.getId()));
    }

    @Override
    public BlowDownSituationVO info(Integer id) {
        BlowDownSituation entity = baseMapper.selectById(id);
        if (Objects.isNull(entity)) {
            throw new ServiceException(SystemCode.DATA_NOT_EXIST);
        }
        BlowDownSituationVO vo = BlowDownSituationVO.convert(entity);
        vo.setDetailList(blowDownSituationDetailService.findByParentId(id));
        return vo;
    }

    @Override
    public CommonPage<BlowDownSituationPageRespVO> page(BlowDownSituationPageReqVO reqVO) {
        reqVO.setTenantId(TenantContext.getOne());
        IPage<BlowDownSituationPageRespVO> iPage = baseMapper.page(new Page<>(reqVO.getPageNo(),reqVO.getPageSize()),reqVO);
        return new CommonPage<>(iPage);
    }

    @Override
    public void delete(Integer id) {
        BlowDownSituationVO vo = info(id);
        baseMapper.delete(new LambdaQueryWrapper<BlowDownSituation>().eq(BlowDownSituation::getUuid,vo.getUuid()).eq(BlowDownSituation::getTenantId,TenantContext.getOne()));
        groupPushUtil.deleteData(convert(vo));
    }

    private GroupDataPushDTO convertPushData(Integer id) {
        BlowDownSituationVO vo = info(id);
        return convert(vo);
    }

    private GroupDataPushDTO convert(BlowDownSituationVO vo) {
        GroupDataPushDTO dto = new GroupDataPushDTO();
        dto.setReportType("POLLUTION");
        dto.setYear(vo.getYear());
        dto.setVersion(vo.getVersion());
        dto.setTenantId(vo.getTenantId());
        dto.setUniqueKey(vo.getUuid());
        dto.setDetailList(vo.getDetailList());
        return dto;
    }

}
