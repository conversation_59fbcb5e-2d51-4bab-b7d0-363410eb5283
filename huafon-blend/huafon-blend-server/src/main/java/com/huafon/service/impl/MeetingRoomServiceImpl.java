package com.huafon.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huafon.area.api.dto.NodeStructureDto;
import com.huafon.area.api.service.AreaRpcService;
import com.huafon.common.config.TenantContext;
import com.huafon.common.utils.bean.BeanUtils;
import com.huafon.dao.mapper.*;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.models.entity.meeting.*;
import com.huafon.models.enums.MeetingEnumDef;

import com.huafon.models.reqo.meeting.MeetingRoomDeviceReq;
import com.huafon.models.reqo.meeting.MeetingRoomPageReq;
import com.huafon.models.reqo.meeting.MeetingRoomSubmitReq;
import com.huafon.models.vo.meeting.MeetingRoomDeviceResp;
import com.huafon.models.vo.meeting.MeetingRoomResp;
import com.huafon.service.EntityVerifyService;
import com.huafon.service.MeetingReservationService;
import com.huafon.service.MeetingRoomLockService;
import com.huafon.service.MeetingRoomService;
import com.huafon.support.exceptions.ServiceException;
import com.huafon.utils.DistributedLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.huafon.models.enums.MeetingEnumDef.MeetingRoomLockTypeEnum.manual;
import static com.huafon.models.enums.MeetingEnumDef.MeetingRoomStatusEnum.*;
import static com.huafon.utils.CommonUtil.*;
import static com.huafon.utils.CommonUtil.OptionalField.*;

/**
 * <AUTHOR>
 * @ClassName com.huafon.service.impl.MeetingRoomServiceImpl
 * @Description
 * @createTime 2023年09月20日 14:06:00
 */
@Service
@Slf4j
public class MeetingRoomServiceImpl implements MeetingRoomService {

    /**
     * 培训室编码前缀
     */
    private static final String MEETING_ROOM_CODE_PREFIX = "HYS-";


    @Autowired
    private MeetingRoomMapper meetingRoomMapper;

    @Autowired
    private MeetingReservationMapper meetingReservationMapper;

    @Autowired
    private MeetingParticipantMapper meetingParticipantMapper;

    @Autowired
    private MeetingRoomDeviceMapper meetingRoomDeviceMapper;

    @Autowired
    private MeetingRoomLockRecordMapper meetingRoomLockRecordMapper;

    @Autowired
    private EntityVerifyService entityVerifyService;

    @Autowired
    RedisTemplate redisTemplate;

    @DubboReference(check = false)
    AreaRpcService areaRpcService;

    @Autowired
    DistributedLock distributedLock;

    @Autowired
    MeetingRoomLockService meetingRoomLockService;

    @Autowired
    MeetingReservationService meetingReservationService;

    @Resource(name = "blendThreadPoolTaskExecutor")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Override
    @Transactional
    public void createMeetingRoom(MeetingRoomSubmitReq req) {
        MeetingRoom meetingRoom = BeanUtils.convert(req, MeetingRoom.class);
        // 生成培训室编码，redis的key值保证唯一
        String meetingRoomCode = generateCode(MEETING_ROOM_CODE_PREFIX, redisTemplate.opsForValue().increment(MEETING_ROOM_CODE_PREFIX + this.getClass().getName()));
        meetingRoom.setCode(meetingRoomCode);
        // 通用字段填充
        autoFillCommonFields(meetingRoom, createBy, createName, createTime, modifyBy, modifyName, modifyTime, tenantId);
        meetingRoomMapper.insert(meetingRoom);
        // 执行会议室锁定配置
        Integer tenant = TenantContext.getOne();
        CompletableFuture.runAsync(() -> meetingRoomLockService.executeLockConfiguration(tenant), threadPoolTaskExecutor);
        // 保存培训室设备
        List<MeetingRoomDeviceReq> meetingRoomDeviceReqList = req.getDeviceList();
        if (CollectionUtils.isEmpty(meetingRoomDeviceReqList)) {
            return;
        }
        List<MeetingRoomDevice> deviceList = new ArrayList<>();
        meetingRoomDeviceReqList.forEach(deviceReq -> {
            MeetingRoomDevice device = BeanUtils.convert(deviceReq, MeetingRoomDevice.class);
            device.setMeetingRoomId(meetingRoom.getId());
            autoFillCommonFields(device, createTime, createBy, modifyTime, modifyBy);
            deviceList.add(device);
        });
        meetingRoomDeviceMapper.batchInsert(deviceList);
    }

    @Override
    @Transactional
    public void editMeetingRoom(MeetingRoomSubmitReq req) {
        Long meetingRoomId = req.getMeetingRoomId();
        if (Objects.isNull(meetingRoomId)) {
            throw new ServiceException("培训室id缺失");
        }
        if (Objects.isNull(req.getVersion())) {
            throw new ServiceException("版本号缺失");
        }
        meetingRoomBookedCheck(meetingRoomId, "不可编辑");
        MeetingRoom meetingRoom = BeanUtils.convert(req, MeetingRoom.class);
        meetingRoom.setId(meetingRoomId);
        autoFillCommonFields(meetingRoom, modifyBy, modifyTime, modifyName);
        // 检查更新
        entityVerifyService.doCheck(param -> meetingRoomMapper.updateById((MeetingRoom) param), meetingRoom);

        // 更新设备
        List<MeetingRoomDeviceReq> deviceList = req.getDeviceList();
        if (Objects.isNull(deviceList)) {
            return;
        }
        List<Long> handledList = new ArrayList<>();
        List<MeetingRoomDevice> toUpdateList = new ArrayList<>();
        List<MeetingRoomDevice> toInsertList = new ArrayList<>();
        for (MeetingRoomDeviceReq meetingRoomDeviceReq : deviceList) {
            MeetingRoomDevice device = BeanUtils.convert(meetingRoomDeviceReq, MeetingRoomDevice.class);
            device.setMeetingRoomId(meetingRoomId);
            if (Objects.isNull(meetingRoomDeviceReq.getDeviceId())) {
                toInsertList.add(device);
                continue;
            }
            device.setId(meetingRoomDeviceReq.getDeviceId());
            toUpdateList.add(device);
        }
        // 逐个创建，需要回显id
        if (!CollectionUtils.isEmpty(toInsertList)) {
            for (MeetingRoomDevice meetingRoomDevice : toInsertList) {
                meetingRoomDeviceMapper.insert(meetingRoomDevice);
                autoFillCommonFields(meetingRoomDevice, createBy, createTime, modifyTime, modifyBy);
                handledList.add(meetingRoomDevice.getId());
            }
        }
        // 逐个更新
        if (!CollectionUtils.isEmpty(toUpdateList)) {
            for (MeetingRoomDevice meetingRoomDevice : toUpdateList) {
                autoFillCommonFields(meetingRoomDevice, modifyTime, modifyBy);
                meetingRoomDeviceMapper.updateById(meetingRoomDevice);
                handledList.add(meetingRoomDevice.getId());
            }
        }
        // 减法删除
        if (!CollectionUtils.isEmpty(handledList)) {
            meetingRoomDeviceMapper.delete(new LambdaQueryWrapper<MeetingRoomDevice>()
                    .eq(MeetingRoomDevice::getMeetingRoomId, meetingRoomId)
                    .notIn(MeetingRoomDevice::getId, handledList)
            );
        }
    }

    @Override
    public MeetingRoomResp detailMeetingRoom(Long meetingRoomId) {
        MeetingRoom meetingRoom = meetingRoomMapper.selectById(meetingRoomId);
        if (Objects.isNull(meetingRoom)) {
            throw new ServiceException("当前页面数据已更新，请刷新重试");
        }
        if (!Objects.equals(meetingRoom.getTenantId(), TenantContext.getOne())) {
            throw new ServiceException("无权查看其他租户会议信息");
        }
        MeetingRoomResp resp = BeanUtils.convert(meetingRoom, MeetingRoomResp.class);
        resp.setMeetingRoomId(meetingRoom.getId());
        resp.setMeetingRoomCode(meetingRoom.getCode());
        int currentHeadCount = 0;
        List<MeetingReservation> reservationList = meetingReservationMapper.selectList(new LambdaQueryWrapper<MeetingReservation>()
                .eq(MeetingReservation::getMeetingRoomId, meetingRoomId));
        if (!CollectionUtils.isEmpty(reservationList)) {
            for (MeetingReservation reservation : reservationList) {
                Date now = Calendar.getInstance().getTime();
                // 当前时间该培训室正在发生会议
                if (now.before(reservation.getEndTime()) && now.after(reservation.getStartTime())) {
                    Integer count = meetingParticipantMapper.selectCount(new LambdaQueryWrapper<MeetingParticipant>()
                            .eq(MeetingParticipant::getReservationId, reservation.getId()));
                    currentHeadCount += count;
                }
            }
        }
        resp.setCurrentHeadCount(currentHeadCount);
        // 培训室是否有会议
        resp.setIsBooked(this.meetingRoomIsBooked(meetingRoomId));
        // 培训室设备信息
        List<MeetingRoomDeviceResp> respList = new ArrayList<>();
        List<MeetingRoomDevice> deviceList = meetingRoomDeviceMapper.selectList(new LambdaQueryWrapper<MeetingRoomDevice>().eq(MeetingRoomDevice::getMeetingRoomId, meetingRoomId));
        if (!CollectionUtils.isEmpty(deviceList)) {
            deviceList.forEach(device -> {
                MeetingRoomDeviceResp deviceResp = BeanUtils.convert(device, MeetingRoomDeviceResp.class);
                deviceResp.setDeviceId(device.getId());
                respList.add(deviceResp);
            });
        }
        resp.setDeviceList(respList);
        return resp;
    }

    @Override
    public CommonPage<MeetingRoomResp> pageMeetingRoom(MeetingRoomPageReq req) {
        if (Objects.isNull(req.getPageInfo())) {
            throw new ServiceException("分页信息缺失");
        }
        Date todayStart = getTodayStart();
        Date todayEnd = getTodayEnd();
        if (Objects.isNull(req.getStartTime())) {
            req.setStartTime(todayStart);
        }
        if (Objects.isNull(req.getEndTime())) {
            req.setEndTime(todayEnd);
        }
        // 培训室id条件集合，用于通过详细状态构建培训室查询条件
        Set<Long> deviceConditionSet = new HashSet<>();
        Set<Long> lockConditionSet = new HashSet<>();
        Set<Long> inMeetingConditionSet = new HashSet<>();
        Set<Long> inReservationConditionSet = new HashSet<>();
        Set<Long> idleConditionSet = new HashSet<>();

        // 查询所有满足培训室设备条件的回忆室
        if (StringUtils.isNotBlank(req.getDeviceName())) {
            deviceConditionSet = meetingRoomDeviceMapper.selectList(new LambdaQueryWrapper<MeetingRoomDevice>()
                            .like(MeetingRoomDevice::getName, req.getDeviceName())).stream()
                    .map(MeetingRoomDevice::getMeetingRoomId)
                    .collect(Collectors.toSet());

        }
        // 查询今天所有的会议预定
        List<MeetingReservation> reservationListToday = meetingReservationMapper.selectList(new LambdaQueryWrapper<MeetingReservation>()
                .eq(MeetingReservation::getTenantId, TenantContext.getOne())
                .between(MeetingReservation::getStartTime, todayStart, todayEnd)
                .between(MeetingReservation::getEndTime, todayStart, todayEnd)
        );
        Map<Long, List<MeetingReservation>> reservationTodayMap = reservationListToday.stream().collect(Collectors.groupingBy(MeetingReservation::getMeetingRoomId));
        // 查询今天所有的会议锁定记录
        Map<Long, List<MeetingRoomLockRecord>> lockTodayMap = meetingRoomLockRecordMapper.selectList(new LambdaQueryWrapper<MeetingRoomLockRecord>()
                .eq(MeetingRoomLockRecord::getTenantId, TenantContext.getOne())
                .between(MeetingRoomLockRecord::getLockStartTime, getTodayStart(), getTodayEnd())
                .between(MeetingRoomLockRecord::getLockEndTime, getTodayStart(), getTodayEnd())
        ).stream().collect(Collectors.groupingBy(MeetingRoomLockRecord::getMeetingRoomId));
        Set<Long> lockedMeetingRoomSet = lockTodayMap.keySet();
        // 状态字段，因为有的状态并非数据库直接状态，而是派生状态，所以需要根据派生状态提前构建查询条件
        Integer status = req.getStatus();
        if (Objects.nonNull(status) && getDescendentStatus().contains(parseByStatus(status))) {
            MeetingEnumDef.MeetingRoomStatusEnum meetingRoomStatusEnum = parseByStatus(status);
            Date now = Calendar.getInstance().getTime();
            switch (meetingRoomStatusEnum) {
                case lock:
                    // 通过查询锁定记录构建查询条件
                    if (!CollectionUtils.isEmpty(lockedMeetingRoomSet)) {
                        lockConditionSet.addAll(lockedMeetingRoomSet);
                        // 为空就不构建状态条件。相反，有会议的话，派生状态查数据库能保证不会查到数据，下同
                        status = null;
                    }
                    break;
                case in_a_meeting:
                    // 过滤出会议中的培训室
                    Set<Long> inMeetingSet = reservationListToday.stream()
                            .filter(reservation -> now.before(reservation.getEndTime()) && now.after(reservation.getStartTime()))
                            .map(MeetingReservation::getMeetingRoomId).collect(Collectors.toSet());
                    // 去掉被锁定的培训室
                    inMeetingSet.removeAll(lockedMeetingRoomSet);
                    if (!CollectionUtils.isEmpty(inMeetingSet)) {
                        inMeetingConditionSet.addAll(inMeetingSet);
                        status = null;
                    }
                    break;
                case idle:
                    // 过滤未被锁定的培训室
                    Set<Long> reservationSet = reservationListToday.stream()
                            .filter(reservation -> now.before(reservation.getEndTime()))
                            .map(MeetingReservation::getMeetingRoomId)
                            .collect(Collectors.toSet());
                    // 所有normal的培训室
                    Set<Long> normalSet = meetingRoomMapper.selectList(new LambdaQueryWrapper<MeetingRoom>()
                                    .eq(MeetingRoom::getTenantId, TenantContext.getOne())
                                    .eq(MeetingRoom::getStatus, normal.getStatus())).stream()
                            .map(MeetingRoom::getId)
                            .collect(Collectors.toSet());
                    // 去掉预约掉的培训室
                    normalSet.removeAll(reservationSet);
                    // 去掉被锁定的培训室
                    normalSet.removeAll(lockedMeetingRoomSet);
                    if (!CollectionUtils.isEmpty(normalSet)) {
                        idleConditionSet.addAll(normalSet);
                        status = null;
                    }
                    break;
                case in_reservation:
                    // 过滤出今天仍有预定的培训室
                    Set<Long> inReservationSet = reservationListToday.stream()
                            .filter(reservation -> now.before(reservation.getStartTime()))
                            .map(MeetingReservation::getMeetingRoomId).collect(Collectors.toSet());
                    // 去掉被锁定的培训室
                    inReservationSet.removeAll(lockedMeetingRoomSet);
                    if (!CollectionUtils.isEmpty(inReservationSet)) {
                        inReservationConditionSet.addAll(inReservationSet);
                        status = null;
                        break;
                    }
            }
        }

        Page page = meetingRoomMapper.selectPage(req.getPageInfo(), new LambdaQueryWrapper<MeetingRoom>()
                .eq(MeetingRoom::getTenantId, TenantContext.getOne())
                .eq(Objects.nonNull(req.getAreaId()), MeetingRoom::getAreaId, req.getAreaId())
                .eq(Objects.nonNull(status), MeetingRoom::getStatus, status)
                .eq(Objects.nonNull(req.getType()), MeetingRoom::getType, req.getType())
                .in(!CollectionUtils.isEmpty(deviceConditionSet), MeetingRoom::getId, deviceConditionSet)
                .in(!CollectionUtils.isEmpty(lockConditionSet), MeetingRoom::getId, lockConditionSet)
                .in(!CollectionUtils.isEmpty(inMeetingConditionSet), MeetingRoom::getId, inMeetingConditionSet)
                .in(!CollectionUtils.isEmpty(inReservationConditionSet), MeetingRoom::getId, inReservationConditionSet)
                .in(!CollectionUtils.isEmpty(idleConditionSet), MeetingRoom::getId, idleConditionSet)
                .like(StringUtils.isNotBlank(req.getMeetingRoomName()), MeetingRoom::getName, req.getMeetingRoomName())
                .and(StringUtils.isNotBlank(req.getFuzzyField()),
                        wrapper -> wrapper.like(MeetingRoom::getCode, req.getFuzzyField())
                                .or()
                                .like(MeetingRoom::getName, req.getFuzzyField()))
                .orderByDesc(MeetingRoom::getCreateTime)
        );

        List<MeetingRoom> meetingRoomList = page.getRecords();
        List<Long> meetingRoomIdList = meetingRoomList.stream().map(MeetingRoom::getId).collect(Collectors.toList());
        List<Long> areaIdList = meetingRoomList.stream().map(MeetingRoom::getAreaId).distinct().collect(Collectors.toList());
        Map<Long, List<MeetingRoomDevice>> meetingRoomDeviceMap = new HashMap<>();
        Map<Long, String> cascadedAreaNameMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(meetingRoomIdList)) {
            // 查询培训室设备
            meetingRoomDeviceMap = meetingRoomDeviceMapper.selectList(new LambdaQueryWrapper<MeetingRoomDevice>()
                            .in(MeetingRoomDevice::getMeetingRoomId, meetingRoomIdList))
                    .stream().collect(Collectors.groupingBy(MeetingRoomDevice::getMeetingRoomId));
            reservationTodayMap = meetingReservationMapper.selectList(new LambdaQueryWrapper<MeetingReservation>().in(MeetingReservation::getMeetingRoomId, meetingRoomIdList))
                    .stream().collect(Collectors.groupingBy(MeetingReservation::getMeetingRoomId));
        }
        if (!CollectionUtils.isEmpty(areaIdList)) {
            List<NodeStructureDto> nodeList = new ArrayList<>();
            try {
                nodeList = areaRpcService.getNodeByIds(areaIdList);
            } catch (Exception e) {
                log.error("获取区域信息异常", e);
            }
            for (NodeStructureDto nodeStructureDto : nodeList) {
                cascadedAreaNameMap.put(nodeStructureDto.getId(), nodeStructureDto.getStructureName());
            }
        }
        List<MeetingRoomResp> meetingRoomRespList = new ArrayList<>();
        for (MeetingRoom meetingRoom : meetingRoomList) {
            MeetingRoomResp meetingRoomResp = BeanUtils.convert(meetingRoom, MeetingRoomResp.class);
            meetingRoomResp.setMeetingRoomCode(meetingRoom.getCode());
            meetingRoomResp.setMeetingRoomId(meetingRoom.getId());
            meetingRoomResp.setStructureAreaNames(cascadedAreaNameMap.get(meetingRoom.getAreaId()));
            int currentHeadCount = 0;
            List<MeetingReservation> reservationList = reservationTodayMap.get(meetingRoom.getId());
            List<MeetingRoomLockRecord> lockList = lockTodayMap.get(meetingRoom.getId());
            // 原始会议状态如果是normal，需要进行详细划分
            Integer originalStatus = meetingRoom.getStatus();
            meetingRoomResp.setStatus(originalStatus);
            if (Objects.equals(normal.getStatus(), originalStatus)) {
                // 当天有培训室锁定，优先显示锁定状态
                if (!CollectionUtils.isEmpty(lockList)) {
                    meetingRoomResp.setStatus(lock.getStatus());
                } else {
                    // 培训室状态初始化为空闲状态
                    Integer reservationStatus = idle.getStatus();
                    if (!CollectionUtils.isEmpty(reservationList)) {
                        for (MeetingReservation reservation : reservationList) {
                            Date now = Calendar.getInstance().getTime();
                            // 会议中：此刻时间点在会议时间区间内
                            if (now.before(reservation.getEndTime()) && now.after(reservation.getStartTime())) {
                                Integer count = meetingParticipantMapper.selectCount(new LambdaQueryWrapper<MeetingParticipant>()
                                        .eq(MeetingParticipant::getReservationId, reservation.getId()));
                                currentHeadCount += count;
                                reservationStatus = in_a_meeting.getStatus();
                            } else if (now.before(reservation.getStartTime()) && !Objects.equals(in_a_meeting.getStatus(), reservationStatus)) {
                                // 预定中：当天有预定未开始的会议
                                reservationStatus = in_reservation.getStatus();
                            }
                        }
                    }
                    meetingRoomResp.setStatus(reservationStatus);
                }
            }
            // 如果是按"日查"，需要返回详细培训室状态，否则默认还是原始状态
            if (!isDateToday(req.getStartTime()) || !isDateToday(req.getEndTime())) {
                meetingRoomResp.setStatus(originalStatus);
            }
            meetingRoomResp.setCurrentHeadCount(currentHeadCount);
            // 培训室是否有会议
            meetingRoomResp.setIsBooked(this.meetingRoomIsBooked(meetingRoom.getId()));
            // 设备情况
            List<MeetingRoomDeviceResp> deviceRespList = new ArrayList<>();
            List<MeetingRoomDevice> meetingRoomDevices = meetingRoomDeviceMap.get(meetingRoom.getId());
            if (!CollectionUtils.isEmpty(meetingRoomDevices)) {
                for (MeetingRoomDevice meetingRoomDevice : meetingRoomDevices) {
                    MeetingRoomDeviceResp deviceResp = BeanUtils.convert(meetingRoomDevice, MeetingRoomDeviceResp.class);
                    deviceResp.setDeviceId(meetingRoomDevice.getId());
                    deviceRespList.add(deviceResp);
                }
            }
            meetingRoomResp.setDeviceList(deviceRespList);
            meetingRoomRespList.add(meetingRoomResp);
        }
        page.setRecords(meetingRoomRespList);
        return new CommonPage<>(page);
    }

    @Override
    @Transactional
    public void deleteMeetingRoom(Long meetingRoomId) {
        meetingRoomBookedCheck(meetingRoomId, "不可删除");
        // 检查培训室情况
        MeetingRoom meetingRoom = new MeetingRoom();
        meetingRoom.setId(meetingRoomId);
        entityVerifyService.doCheck(param -> meetingRoomMapper.deleteById(param.getId()), meetingRoom);
        // 删除设备
        meetingRoomDeviceMapper.delete(new LambdaQueryWrapper<MeetingRoomDevice>().eq(MeetingRoomDevice::getMeetingRoomId, meetingRoomId));
        // 执行会议室锁定配置
        Integer tenant = TenantContext.getOne();
        CompletableFuture.runAsync(() -> meetingRoomLockService.executeLockConfiguration(tenant), threadPoolTaskExecutor);
    }

    @Override
    public void disableMeetingRoom(Long meetingRoomId) {
        MeetingRoom dbMeetingRoom = meetingRoomMapper.selectById(meetingRoomId);
        if (Objects.isNull(dbMeetingRoom)) {
            throw new ServiceException("当前页面数据已更新，请刷新后重试");
        }
        // 锁定的培训室，不可置为不可用
        Date now = Calendar.getInstance().getTime();
        if (this.meetingRoomIsLocked(meetingRoomId, now, now)) {
            throw new ServiceException("当前培训室已锁定，不可置为不可用");
        }
        // 有预定的培训室，不可置为不可用
        meetingRoomBookedCheck(meetingRoomId, "不可置为不可用");
        MeetingRoom meetingRoom = new MeetingRoom();
        meetingRoom.setId(meetingRoomId);
        meetingRoom.setStatus(repair.getStatus());
        autoFillCommonFields(meetingRoom, modifyBy, modifyTime, modifyName);
        entityVerifyService.doCheck(param -> meetingRoomMapper.updateById((MeetingRoom) param), meetingRoom);
        // 执行会议室锁定配置
        Integer tenant = TenantContext.getOne();
        CompletableFuture.runAsync(() -> meetingRoomLockService.executeLockConfiguration(tenant), threadPoolTaskExecutor);
    }

    @Override
    public void enableMeetingRoom(Long meetingRoomId) {
        MeetingRoom dbMeetingRoom = meetingRoomMapper.selectById(meetingRoomId);
        if (Objects.isNull(dbMeetingRoom)) {
            throw new ServiceException("当前页面数据已更新，请刷新后重试");
        }
        // 只能操作不可用的培训室，防止越权
        if (!Objects.equals(dbMeetingRoom.getStatus(), MeetingEnumDef.MeetingRoomStatusEnum.repair.getStatus())) {
            throw new ServiceException("当前培训室非不可用，不可置为可用");
        }
        MeetingRoom meetingRoom = new MeetingRoom();
        meetingRoom.setId(meetingRoomId);
        meetingRoom.setStatus(MeetingEnumDef.MeetingRoomStatusEnum.normal.getStatus());
        autoFillCommonFields(meetingRoom, modifyBy, modifyTime, modifyName);
        entityVerifyService.doCheck(param -> meetingRoomMapper.updateById((MeetingRoom) param), meetingRoom);
        // 执行会议室锁定配置
        Integer tenant = TenantContext.getOne();
        CompletableFuture.runAsync(() -> meetingRoomLockService.executeLockConfiguration(tenant), threadPoolTaskExecutor);
    }

    @Override
    public void lockMeetingRoom(Long meetingRoomId) {
        MeetingRoom dbMeetingRoom = meetingRoomMapper.selectById(meetingRoomId);
        if (Objects.isNull(dbMeetingRoom)) {
            throw new ServiceException("当前页面数据已更新，请刷新后重试");
        }
        // 不可用的培训室，不可锁定
        if (Objects.equals(dbMeetingRoom.getStatus(), MeetingEnumDef.MeetingRoomStatusEnum.repair.getStatus())) {
            throw new ServiceException("当前培训室不可用，不可锁定");
        }
        // 更新培训室表，目的是为了记录编辑信息和租户校验
        MeetingRoom meetingRoom = new MeetingRoom();
        meetingRoom.setId(meetingRoomId);
        // meetingRoom.setStatus(MeetingEnumDef.MeetingRoomStatusEnum.lock.getStatus());
        autoFillCommonFields(meetingRoom, modifyBy, modifyTime, modifyName);
        entityVerifyService.doCheck(param -> meetingRoomMapper.updateById((MeetingRoom) param), meetingRoom);
        // 查看当天培训室锁定记录
        Integer tenant = TenantContext.getOne();
        List<MeetingRoomLockRecord> lockRecords = meetingRoomLockRecordMapper.selectList(new LambdaQueryWrapper<MeetingRoomLockRecord>()
                .eq(MeetingRoomLockRecord::getMeetingRoomId, meetingRoomId)
                .eq(MeetingRoomLockRecord::getTenantId, TenantContext.getOne())
                .between(MeetingRoomLockRecord::getLockStartTime, getTodayStart(), getTodayEnd())
                .between(MeetingRoomLockRecord::getLockEndTime, getTodayStart(), getTodayEnd())
        );
        if (!CollectionUtils.isEmpty(lockRecords)) {
            throw new ServiceException("当前培训室已锁定");
        }
        MeetingRoomLockRecord lockRecord = new MeetingRoomLockRecord(null, meetingRoomId, getTodayStart(), getTodayEnd(), manual.getType(), TenantContext.getOne());
        meetingRoomLockRecordMapper.insert(lockRecord);
        // 执行会议室锁定配置
        CompletableFuture.runAsync(() -> meetingRoomLockService.executeLockConfiguration(tenant), threadPoolTaskExecutor);
    }

    @Override
    public void unlockMeetingRoom(Long meetingRoomId) {
        MeetingRoom dbMeetingRoom = meetingRoomMapper.selectById(meetingRoomId);
        if (Objects.isNull(dbMeetingRoom)) {
            throw new ServiceException("当前页面数据已更新，请刷新后重试");
        }
        Integer tenant = TenantContext.getOne();
        List<MeetingRoomLockRecord> lockRecords = meetingRoomLockRecordMapper.selectList(new LambdaQueryWrapper<MeetingRoomLockRecord>()
                .eq(MeetingRoomLockRecord::getMeetingRoomId, meetingRoomId)
                .eq(MeetingRoomLockRecord::getTenantId, tenant)
                .between(MeetingRoomLockRecord::getLockStartTime, getTodayStart(), getTodayEnd())
                .between(MeetingRoomLockRecord::getLockEndTime, getTodayStart(), getTodayEnd())
        );
        if (CollectionUtils.isEmpty(lockRecords)) {
            throw new ServiceException("当前培训室未锁定，无需解锁");
        }
        meetingRoomLockRecordMapper.delete(new LambdaQueryWrapper<MeetingRoomLockRecord>()
                .eq(MeetingRoomLockRecord::getMeetingRoomId, meetingRoomId)
                .eq(MeetingRoomLockRecord::getTenantId, tenant)
                .between(MeetingRoomLockRecord::getLockStartTime, getTodayStart(), getTodayEnd())
                .between(MeetingRoomLockRecord::getLockEndTime, getTodayStart(), getTodayEnd())
        );
        // 执行会议室锁定配置
        CompletableFuture.runAsync(() -> meetingRoomLockService.executeLockConfiguration(tenant), threadPoolTaskExecutor);
    }


    @Override
    public boolean meetingRoomIsBooked(Long meetingRoomId) {
        List<MeetingReservation> reservationList = meetingReservationMapper.selectList(new LambdaQueryWrapper<MeetingReservation>()
                .eq(MeetingReservation::getMeetingRoomId, meetingRoomId));
        for (MeetingReservation reservation : reservationList) {
            Date now = Calendar.getInstance().getTime();
            // 已经有预定
            if (now.before(reservation.getEndTime())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean meetingRoomIsBooked(Long meetingRoomId, Long currentReservationId, Date startTime, Date endTime) {
        List<MeetingReservation> reservationList = meetingReservationMapper.selectList(new LambdaQueryWrapper<MeetingReservation>()
                .eq(MeetingReservation::getMeetingRoomId, meetingRoomId));
        for (MeetingReservation reservation : reservationList) {
            // 如果是当前会议，直接跳过比较
            if (Objects.equals(currentReservationId, reservation.getId())) {
                continue;
            }
            // 时间是否产生冲突
            if (reservation.getEndTime().after(startTime) && reservation.getStartTime().before(endTime)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean meetingRoomIsLocked(Long meetingRoomId, Date startTime, Date endTime) {
        List<MeetingRoomLockRecord> lockRecordList = meetingRoomLockRecordMapper.selectList(new LambdaQueryWrapper<MeetingRoomLockRecord>()
                .eq(MeetingRoomLockRecord::getMeetingRoomId, meetingRoomId));

        if (CollectionUtils.isEmpty(lockRecordList)) {
            return false;
        }
        MeetingRoomLockRecord lockRecord = lockRecordList.get(0);
        // 时间是否产生冲突
        return startTime.before(lockRecord.getLockEndTime()) && endTime.after(lockRecord.getLockStartTime());
    }

    private void meetingRoomBookedCheck(Long meetingRoomId, String msg) {
        List<MeetingReservation> reservationList = meetingReservationMapper.selectList(new LambdaQueryWrapper<MeetingReservation>()
                .eq(MeetingReservation::getMeetingRoomId, meetingRoomId));
        for (MeetingReservation reservation : reservationList) {
            Date now = Calendar.getInstance().getTime();
            if (now.before(reservation.getEndTime()) && now.after(reservation.getStartTime())) {
                throw new ServiceException("培训室已被使用，" + msg);
            }
            if (now.before(reservation.getStartTime())) {
                throw new ServiceException("培训室已被预定，" + msg);
            }
        }
    }

    private static boolean isDateToday(Date date) {
        // 将 Date 转换为 LocalDate
        LocalDate localDateToCheck = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 比较日期部分是否相同
        return localDateToCheck.isEqual(currentDate);
    }
}
