package com.huafon.service;

import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.models.reqo.meeting.MeetingRoomPageReq;
import com.huafon.models.reqo.meeting.MeetingRoomSubmitReq;
import com.huafon.models.vo.meeting.MeetingRoomResp;

import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName com.huafon.service.MeetingRoomService
 * @Description
 * @createTime 2023年09月20日 14:03:00
 */
public interface MeetingRoomService {
    /**
     * 创建会议室
     * @param req
     */
    void createMeetingRoom(MeetingRoomSubmitReq req);
    /**
     * 编辑会议室
     * @param req
     */
    void editMeetingRoom(MeetingRoomSubmitReq req);
    /**
     * 查看会议室详情
     * @param meetingRoomId
     * @return
     */
    MeetingRoomResp detailMeetingRoom(Long meetingRoomId);
    /**
     * 会议室分页列表
     * @param req
     * @return
     */
    CommonPage<MeetingRoomResp> pageMeetingRoom(MeetingRoomPageReq req);
    /**
     * 删除会议室
     * @param meetingRoomId
     */
    void deleteMeetingRoom(Long meetingRoomId);
    /**
     * 设置会议室不可用
     * @param meetingRoomId
     */
    void disableMeetingRoom(Long meetingRoomId);
    /**
     * 设置会议室可用
     * @param meetingRoomId
     */
    void enableMeetingRoom(Long meetingRoomId);

    /**
     * 锁定会议室
     * @param meetingRoomId
     */
    void lockMeetingRoom(Long meetingRoomId);

    /**
     * 解锁会议室
     * @param meetingRoomId
     */
    void unlockMeetingRoom(Long meetingRoomId);

    /**
     * 会议室是否当前有预定
     * @param meetingRoomId
     * @return
     */
    boolean meetingRoomIsBooked(Long meetingRoomId);

    /**
     * 会议室是否在指定时间区间内有预定其他会议
     *
     * @param meetingRoomId  会议室id
     * @param currentReservationId  当前会议id
     * @param startTime  预定开始时间
     * @param endTime  预定结束时间
     * @return
     */
    boolean meetingRoomIsBooked(Long meetingRoomId, Long currentReservationId, Date startTime, Date endTime);

    /**
     * 当前给定时间段，会议室是否被锁定
     * @param meetingRoomId
     * @param startTime
     * @param endTime
     * @return
     */
    boolean meetingRoomIsLocked(Long meetingRoomId, Date startTime, Date endTime);
}
