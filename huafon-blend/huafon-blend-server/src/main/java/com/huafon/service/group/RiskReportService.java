package com.huafon.service.group;

import com.baomidou.mybatisplus.extension.service.IService;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.models.entity.group.RiskGroup;
import com.huafon.models.vo.group.*;

/**
 * <p>
 * 集团数据上报-隐患排查 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-03
 */
public interface RiskReportService extends IService<RiskGroup> {

    /**
     * 判断当前月份是否有数据
     *
     * @param reqVO
     */
    void ifExist(YearAndMonthVO reqVO);

    /**
     * 保存
     *
     * @param reqVO
     */
    void save(RiskReportVO reqVO);

    /**
     * 编辑
     *
     * @param reqVO
     */
    void modify(RiskReportVO reqVO);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    RiskReportVO info(Integer id);

    /**
     * 分页
     *
     * @param reqVO
     */
    CommonPage<RiskReportVO> page(BaseGroupPageReqVO reqVO);

    /**
     * 删除
     *
     * @param reqVO
     */
    void delete(YearAndMonthVO reqVO);

    void syncData(int year, int month);
}
