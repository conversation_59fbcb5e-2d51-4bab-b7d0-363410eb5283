package com.huafon.service.remote;

import com.huafon.portal.api.dto.UserDto;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-10-08 13:41
 **/
public interface UserRemoteService {

	/**
	 * 通过UserID获取账户基础信息
	 * @param userId
	 * @return 只返回基础信息：不包含和租户关联的信息
	 */
	UserDto getById(Integer userId);

	/**
	 * 查询租户下用户信息
	 * @param userId
	 * @param tenantId
	 * @return
	 */
	UserDto getById(Integer userId, Integer tenantId);

	/**
	 * 批量查询用户信息
	 * @param userIds
	 * @param tenantId
	 * @return
	 */
	List<UserDto> getByIds(List<Integer> userIds, Integer tenantId);
}
