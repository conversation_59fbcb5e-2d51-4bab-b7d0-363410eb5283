package com.huafon.service.remote;

import com.huafon.api.dto.blackboard.BlackboardOptDTO;

/**
 * <AUTHOR>
 * @since 2024-08-20 16:13
 **/
public interface BlackboardRemoteService {
	/**
	 * 创建公告
	 * @return 公告的ID
	 */
	String createBlackboard(BlackboardOptDTO source);

	/**
	 * 更新公告
	 * @return 公告ID
	 */
	boolean updateBlackboard(BlackboardOptDTO source);

	/**
	 * 删除公告
	 * @return
	 */
	boolean deleteBlackboard(BlackboardOptDTO source);

}
