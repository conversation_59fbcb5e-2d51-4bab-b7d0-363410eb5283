package com.huafon.service.group.imp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.huafon.dao.mapper.group.GreenhouseGasesSituationDetailMapper;
import com.huafon.models.entity.group.GreenhouseGasesSituationDetail;
import com.huafon.models.vo.group.GreenhouseGasesSituationDetailVO;
import com.huafon.service.group.GreenhouseGasesSituationDetailService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 集团数据上报-温室气体情况明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-09
 */
@Service
public class GreenhouseGasesSituationDetailServiceImpl extends ServiceImpl<GreenhouseGasesSituationDetailMapper, GreenhouseGasesSituationDetail> implements GreenhouseGasesSituationDetailService {

    @Override
    public void save(Integer parentId, List<GreenhouseGasesSituationDetailVO> detailList) {
        if (Objects.nonNull(parentId) && !CollectionUtils.isEmpty(detailList)) {
            List<GreenhouseGasesSituationDetail> entityList = new ArrayList<>();
            for (GreenhouseGasesSituationDetailVO vo: detailList) {
                vo.setId(null);
                GreenhouseGasesSituationDetail entity = GreenhouseGasesSituationDetailVO.convert(vo);
                entity.setParentId(parentId);
                entityList.add(entity);
            }
            saveBatch(entityList);
        }
    }

    @Override
    public List<GreenhouseGasesSituationDetailVO> findByParentId(Integer parentId) {
        List<GreenhouseGasesSituationDetail> entityList = baseMapper.selectList(new LambdaQueryWrapper<GreenhouseGasesSituationDetail>().eq(GreenhouseGasesSituationDetail::getParentId,parentId).orderByAsc(GreenhouseGasesSituationDetail::getId));
        return Lists.transform(entityList,entity->{
            return GreenhouseGasesSituationDetailVO.convert(entity);
        });
    }

}
