package com.huafon.service.group;

import com.baomidou.mybatisplus.extension.service.IService;
import com.huafon.models.entity.group.BaseCenterPushConfig;
import com.huafon.models.reqo.ReqCompanyCommon;
import com.huafon.models.vo.group.CompanyVO;

/**
 * <AUTHOR>
 * @Date 2024/12/2 17:03
 */
public interface BaseCenterCompanyService extends IService<BaseCenterPushConfig> {

    void submitConfig(ReqCompanyCommon req);

    CompanyVO getConfig(Boolean push, Integer tenantId);
}
