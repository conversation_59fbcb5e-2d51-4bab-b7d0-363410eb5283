package com.huafon.service;

import com.huafon.framework.mybatis.service.MybatisIService;
import com.huafon.models.entity.Holiday;
import com.huafon.models.vo.HolidayV2Vo;
import com.huafon.models.vo.HolidayVo;

import java.util.Date;
import java.util.List;


/**
 * 节假日
 * <AUTHOR>
 */
public interface HolidayService extends MybatisIService<Holiday> {
    HolidayV2Vo list(String year);

    void saveOrUpdate(HolidayV2Vo reqVo);

    Boolean include(Date beginDate, Date endDate);

    List<HolidayVo> listByYearAndMonth(String date);
}