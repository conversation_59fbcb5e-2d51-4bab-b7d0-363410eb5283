package com.huafon.service.support;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;

/**
 * <AUTHOR>
 * @since 2023-11-14 14:43
 **/
public final class CustomSheetUtils {

	public static final String LINE_SEPARATOR = "\r\n";

	public static String getCellValueString(Cell cell) {
		CellType cellType = cell.getCellType();
		if (cellType == null) {
			return null;
		}

		String cellValue = "";
		switch (cellType) {
			case STRING:
				cellValue = cell.getStringCellValue();
				break;
			case BOOLEAN:
				cellValue = String.valueOf(cell.getBooleanCellValue());
				break;
			case NUMERIC:
				cellValue = String.valueOf(cell.getNumericCellValue());
				break;
			case BLANK:
				cell.setCellValue(cellValue);//空白的设置空字符
				break;
			default:
		}
		return cellValue;
	}
}
