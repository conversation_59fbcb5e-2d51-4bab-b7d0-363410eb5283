package com.huafon.service.impl;

import com.huafon.api.service.MarkRpcService;
import com.huafon.api.service.dto.MarkDTO;
import com.huafon.config.MinioConfig;
import com.huafon.dao.mapper.MarkMapper;
import com.huafon.dao.mapper.MarkTypeMapper;
import com.huafon.models.entity.Mark;
import com.huafon.models.entity.MarkType;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-10-31 20:23
 **/
@DubboService
@Slf4j
public class MarkRpcServiceImpl implements MarkRpcService {

	@Autowired
	private MarkMapper markMapper;
	@Autowired
	private MarkTypeMapper markTypeMapper;
	@Autowired
	private MinioConfig minioConfig;

	@Override
	public MarkDTO getById(Integer id) {
		if (Objects.isNull(id)) {
			return null;
		}

		Mark mark = markMapper.selectById(id);
		MarkType type = null;
		if (Objects.nonNull(mark)) {
			type = markTypeMapper.selectById(mark.getTypeId());
		}

		return convert(mark, type);
	}

	private MarkDTO convert(Mark source, MarkType type) {
		if (source == null) {
			return null;
		}
		MarkDTO markDTO = new MarkDTO();
		markDTO.setId(source.getId());
		markDTO.setTypeId(source.getTypeId());
		if (Objects.nonNull(type)) {
			markDTO.setTypeName(type.getName());
		}
		markDTO.setName(source.getName());
		markDTO.setUrl(minioConfig.concatFilePrefix(source.getUrl()));
		markDTO.setRemark(source.getRemark());
		markDTO.setSort(source.getSort());
		markDTO.setIsDefault(source.getIsDefault());
		return markDTO;
	}

	@Override
	public List<MarkDTO> getByIds(Collection<Integer> ids) {
		if (CollectionUtils.isEmpty(ids)) {
			return Collections.emptyList();
		}

		List<Mark> marks = markMapper.selectBatchIds(ids);
		List<Integer> typeIds = marks.stream().filter(Objects::nonNull).map(Mark::getTypeId).filter(Objects::nonNull).collect(Collectors.toList());
		Map<Integer, MarkType> typeMappings = new HashMap<>();
		if (!CollectionUtils.isEmpty(typeIds)) {
			List<MarkType> markTypes = markTypeMapper.selectBatchIds(typeIds);
			typeMappings.putAll(markTypes.stream().collect(Collectors.toMap(MarkType::getId, Function.identity(), (a, b) -> b)));
		}

		return marks.stream().map(e -> convert(e, typeMappings.get(e.getTypeId()))).collect(Collectors.toList());
	}
}
