package com.huafon.service.support;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-10-20 14:53
 **/
@Data
public class ExcelValidationResult<T> {
	private List<T> successData;
	private List<ExcelValidationError<T>> errorData;

	public ExcelValidationResult(List<T> successData, List<ExcelValidationError<T>> errorData) {
		this.successData = successData;
		this.errorData = errorData;
	}

	public ExcelValidationResult(List<ExcelValidationError<T>> errorData) {
		this(new ArrayList<>(), errorData);
	}
}
