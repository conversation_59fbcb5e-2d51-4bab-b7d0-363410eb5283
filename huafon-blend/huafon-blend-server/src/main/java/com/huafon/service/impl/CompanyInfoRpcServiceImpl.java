package com.huafon.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huafon.api.service.CompanyInfoRpcService;
import com.huafon.api.service.dto.CompanyInfoDTO;
import com.huafon.common.utils.bean.BeanUtils;
import com.huafon.dao.mapper.CompanyInfoMapper;
import com.huafon.models.entity.CompanyInfo;
import com.huafon.portal.api.dto.TenantDto;
import com.huafon.portal.api.service.TenantRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import javax.annotation.Resource;
import java.util.Objects;
/**
 * @Description 企业信息rpc接口
 * <AUTHOR>
 * @Date 2023/8/28 19:45
 * @Version 1.0
 */
@Slf4j
@DubboService
public class CompanyInfoRpcServiceImpl implements CompanyInfoRpcService {
    @Resource
    private CompanyInfoMapper companyInfoMapper;

    @DubboReference(check = false)
    private TenantRpcService tenantRpcService;


    @Override
    public CompanyInfoDTO getContactInfo(Integer tenantId) {
        CompanyInfoDTO dto = new CompanyInfoDTO();
        LambdaQueryWrapper<CompanyInfo> companyInfoWrapper = new QueryWrapper<CompanyInfo>().lambda()
                .eq(Objects.nonNull(tenantId),CompanyInfo::getTenantId, tenantId)
                .last("LIMIT 1");
        CompanyInfo companyInfo = companyInfoMapper.selectOne(companyInfoWrapper);
        log.info("查询当前租户的企业基本信息 入参：{},结果：{}",tenantId, JSON.toJSONString(companyInfo));
        if(Objects.nonNull(companyInfo)){
            BeanUtils.copyProperties(companyInfo,dto);
        }
        String companyCode = getCompanyCode(tenantId);
        dto.setCompanyCode(companyCode);
        return dto;
    }

    public String getCompanyCode(Integer tenantId) {
        String companyCode = null;
        if (!Objects.isNull(tenantId)) {
            TenantDto tenantById = tenantRpcService.findTenantById(tenantId);
            if (!Objects.isNull(tenantById)) {
                companyCode = tenantById.getCompanyCode();
            }
        }
        return companyCode;
    }
}