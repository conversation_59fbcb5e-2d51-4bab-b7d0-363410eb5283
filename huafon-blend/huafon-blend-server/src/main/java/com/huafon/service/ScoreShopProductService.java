package com.huafon.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.models.dto.ProductChangeInventoryDTO;
import com.huafon.models.dto.ScoreShopProductPostDTO;
import com.huafon.models.entity.ScoreShopProduct;
import com.huafon.models.query.ScoreShopProductCommonQuery;
import com.huafon.models.query.ScoreShopProductPageQuery;
import com.huafon.models.vo.ScoreShopProductCommonVo;
import com.huafon.models.vo.ScoreShopProductVo;
import com.huafon.models.vo.TreeStatisticalVo;

import java.util.List;

/**
* 积分商城：商品
* <AUTHOR>
* @since 2023-12-22 14:28
*/
public interface ScoreShopProductService extends IService<ScoreShopProduct> {

    String LOCK_PREFIX = "SHOP:PRODUCT:";

    /**
    * 创建积分商城：商品
    * @param create
    */
    void createScoreShopProduct(ScoreShopProductPostDTO create);

    /**
    * 更新积分商城：商品
    * @param update
    */
    void updateScoreShopProduct(ScoreShopProductPostDTO update);

    /**
    * 删除积分商城：商品
    * @param id
    */
    void deleteScoreShopProductById(Integer id);

    /**
    * 查询详情
    * @param id
    * @return
    */
    ScoreShopProductVo findScoreShopProductById(Integer id);

    /**
    * 分页
    */
    CommonPage<ScoreShopProductCommonVo> commonPage(ScoreShopProductPageQuery query);

    /**
    * 通用查询
    * @param query
    * @return
    */
    List<ScoreShopProduct> commonQuery(ScoreShopProductCommonQuery query);


    /**
     * 移到其他类型
     * @param typeId
     * @param otherId
     */
    void moveToOther(Integer typeId, Integer otherId);

    /**
     * 下架
     * @param id
     */
    void downProduct(Integer id);

    /**
     * 上架
     * @param id
     */
    void upProduct(Integer id);

    /**
     * 入库/出库
     * @param origin
     */
    void changeInventory(ProductChangeInventoryDTO origin);

    /**
     * 统计信息
     * @return
     */
    TreeStatisticalVo statistic(boolean excludeOutOfStock);

}
