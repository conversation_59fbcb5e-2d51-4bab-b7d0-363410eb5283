package com.huafon.models.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huafon.models.dto.AttachmentDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @since 2023-12-22 14:28
*/
@Data
@ApiModel(value = "积分商城：商品")
public class ScoreShopProductVo implements Serializable {

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "类型ID")
    private Integer typeId;

    @ApiModelProperty(value = "类型名称")
    private String typeName;

    @ApiModelProperty(value = "商品名称编码")
    private String code;

    @ApiModelProperty(value = "商品名称")
    private String name;

    @ApiModelProperty(value = "单个兑换积分")
    private BigDecimal singleRedemptionScore;

    @ApiModelProperty(value = "库存")
    private Integer inventory;

    @ApiModelProperty(value = "商品图片")
    private List<AttachmentDTO> picture;

    @ApiModelProperty(value = "状态：0上架状态，-1下架状态")
    private Integer state;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "租户ID")
    private Integer tenantId;

    @ApiModelProperty(value = "创建人名称")
    private String createByName;

    @ApiModelProperty(value = "编辑人名称")
    private String modifyByName;

    @ApiModelProperty(value = "编辑时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

}
