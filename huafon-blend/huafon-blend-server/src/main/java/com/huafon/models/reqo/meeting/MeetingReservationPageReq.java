package com.huafon.models.reqo.meeting;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huafon.framework.mybatis.pojo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName com.huafon.models.vo.req.meeting.MeetingReservationPageQuery
 * @Description
 * @createTime 2023年09月21日 16:12:00
 */
@Data
@ApiModel(description = "会议分页查询实体")
public class MeetingReservationPageReq extends PageRequest {


    @ApiModelProperty(value = "会议室名称")
    private String meetingRoomName;

    @ApiModelProperty(value = "会议室类型：1-大、2-中、3-小")
    private Integer type;

    @ApiModelProperty(value = "区域id")
    private Long areaId;

    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @ApiModelProperty(value = "会议开始时间，字符串格式为：yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "开始时间缺失")
    private Date startTime;

    @ApiModelProperty(value = "会议结束时间，字符串格式为：yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "结束时间缺失")
    private Date endTime;


}
