package com.huafon.models.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: TODO
 * @Date: 2023/12/20 10:23 AM
 * @Author: zyf
 **/
@Data
public class DepartmentWithUserTreeVO {
    @ApiModelProperty(value = "组织机构id")
    private Integer id;

    @ApiModelProperty(value = "部门描述")
    private String departmentDesc;

    @ApiModelProperty(value = "父id")
    private Integer parentId;

    @ApiModelProperty(value = "排序值")
    private BigDecimal sortOrder;

    @ApiModelProperty(value = "父节点名称")
    private String parentName;

    @ApiModelProperty(value = "组织机构编号")
    private String departmentCode;

    @ApiModelProperty(value = "组织机构名称")
    private String departmentName;

    @ApiModelProperty(value = "icon图标")
    private String icon;

    @ApiModelProperty(value = "数据来源 SELF,EHR")
    private String dataFrom;

    @ApiModelProperty(value = "子节点")
    private List<DepartmentWithUserTreeVO> children;

    @ApiModelProperty(value = "用户列表")
    private List<UserForDepartmentTreeVo> userList;

    @ApiModelProperty(value = "当前部门下及所有子部门关联的用户集合")
    private List<Integer> userIds;

    @ApiModelProperty(value = "当前部门下及所有子部门关联的用户数量")
    private Integer userNum;

    @ApiModelProperty(value = "当前部门下及所有子部门关联的在线用户集合")
    private List<Integer> onlineUserIds;

    @ApiModelProperty(value = "当前部门下及所有子部门关联的在线用户数量")
    private Integer onlineUserNum;
}