package com.huafon.models.vo.group;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.huafon.common.config.TenantContext;
import com.huafon.models.dto.group.GroupDataPushDTO;
import com.huafon.models.entity.group.WorkListReport;
import com.huafon.support.config.UserContext;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 集团数据上报-作业上报VO
 * @Date: 2024/9/3 14:03
 * @Author: zyf
 **/
@Slf4j
@Data
@ApiModel(value = "WorkListReportVO", description = "集团数据上报-作业上报VO")
public class WorkListReportVO extends BaseGroupVO {

    @ApiModelProperty("是否需要同步")
    private Boolean isNeedSync;

    @ApiModelProperty("作业票的总记录数")
    private Integer total;

    @ApiModelProperty("移出统计的作业票id列表")
    private List<Integer> excludeIds;

    @ApiModelProperty(value = "特级动火数量")
    private Integer specialFireNum;

    @ApiModelProperty(value = "一级动火数量")
    private Integer firstFireNum;

    @ApiModelProperty(value = "二级动火数量")
    private Integer secondFireNum;

    @ApiModelProperty(value = "受限空间数量")
    private Integer spaceNum;

    @ApiModelProperty(value = "盲板作业数量")
    private Integer blindNum;

    @ApiModelProperty(value = "高处作业数量")
    private Integer highNum;

    @ApiModelProperty(value = "吊装作业数量")
    private Integer hoistNum;

    @ApiModelProperty(value = "临时用电数量")
    private Integer temporaryElectricityNum;

    @ApiModelProperty(value = "动土作业数量")
    private Integer breakGroundNum;

    @ApiModelProperty(value = "断路作业数量")
    private Integer breakRoadNum;

    @ApiModelProperty(value = "能量隔离数量")
    private Integer energyIsolationNum;

    @ApiModelProperty(value = "小计数量")
    private Integer num;

    @ApiModelProperty(value = "唯一uuid")
    private String uniqueKey;

    @ApiModelProperty(value = "统计年月")
    @JsonFormat(pattern = "yyyy-MM")
    @DateTimeFormat("yyyy-MM")
    private Date time;

    public static WorkListReport convert(WorkListReportVO item) {
        if (item == null) {
            return null;
        }
        WorkListReport entity = new WorkListReport();
        BeanUtils.copyProperties(item,entity);
        if (Objects.isNull(item.getId())) {
            entity.setCreate(UserContext.getId());
            entity.setCreateByName(UserContext.get().getName());
            entity.setTenantId(TenantContext.getOne());
        }
        entity.setModify(UserContext.getId());
        entity.setModifyByName(UserContext.get().getName());
        return entity;
    }

    public static WorkListReportVO convert(WorkListReport item) {
        if (item == null) {
            return null;
        }
        WorkListReportVO reportVO = new WorkListReportVO();
        BeanUtils.copyProperties(item,reportVO);
        return reportVO;
    }

    public static GroupDataPushDTO convertPushData(WorkListReportVO item) {
        if (item == null) {
            return null;
        }
        GroupDataPushDTO groupDataPushDTO = new GroupDataPushDTO();
        groupDataPushDTO.setReportType("WORK");
        groupDataPushDTO.setYear(item.getYear());
        groupDataPushDTO.setMonth(item.getMonth());
        groupDataPushDTO.setVersion(item.getVersion());
        groupDataPushDTO.setTenantId(TenantContext.getOne());
        groupDataPushDTO.setUniqueKey(item.getUniqueKey());

        groupDataPushDTO.setDetailList(Arrays.asList(item));
        log.info("======作业推送======:{}", JSONObject.toJSONString(groupDataPushDTO));
        return groupDataPushDTO;
    }

    public static GroupDataPushDTO convertPushData(WorkListReport item) {
        if (item == null) {
            return null;
        }
        GroupDataPushDTO groupDataPushDTO = new GroupDataPushDTO();
        groupDataPushDTO.setReportType("WORK");
        groupDataPushDTO.setYear(item.getYear());
        groupDataPushDTO.setMonth(item.getMonth());
        groupDataPushDTO.setVersion(item.getVersion());
        groupDataPushDTO.setTenantId(item.getTenantId());
        groupDataPushDTO.setUniqueKey(item.getUniqueKey());

        groupDataPushDTO.setDetailList(Arrays.asList(item));
        log.info("======作业推送======:{}", JSONObject.toJSONString(groupDataPushDTO));
        return groupDataPushDTO;
    }

}
