package com.huafon.models.dto.download;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.huafon.models.enums.ProductOperateTypeEnum;
import com.huafon.service.support.ExpandAll;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023-12-24 17:31
 **/
@Data
@ContentRowHeight(18)
@ContentStyle(wrapped = BooleanEnum.TRUE)
public class ProductOperateDownloadDTO {

	@ExcelProperty(value = "商品操作编码")
	@ExpandAll(minWidth = 22)
	private String code;

	@ExcelProperty(value = "商品名称")
	private String productName;

	@ExcelProperty(value = "商品名称编码")
	@ExpandAll(minWidth = 22)
	private String productCode;

	@ExcelProperty(value = "商品类型")
	private String productTypeName;

	@ExcelProperty(value = "单个兑换积分")
	private BigDecimal singleRedemptionScore;

	@ExcelProperty(value = "操作类型")
	private String type;//UP(上架)、DOWN(下架)、INCREASE(增加库存)、DECREASE(减少库存)

	public void setType(ProductOperateTypeEnum type) {
		if (Objects.nonNull(type)) {
			this.type = type.getDescription();
		}
	}

	@ExcelProperty(value = "原有库存")
	private Integer originInventory;

	@ExcelProperty(value = "现有库存")
	private Integer targetInventory;

	@ExcelProperty(value = "操作人名称")
	private String operateUserName;

	@ExcelProperty(value = "操作时间")
	@DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
	private Date operateTime;
}
