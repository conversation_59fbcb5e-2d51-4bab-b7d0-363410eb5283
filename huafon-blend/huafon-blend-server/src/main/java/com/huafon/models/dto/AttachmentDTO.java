package com.huafon.models.dto;

import com.fasterxml.jackson.core.type.TypeReference;
import com.huafon.dao.handler.JsonbArrayTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 附件
 *
 * <AUTHOR>
 * @since 2023-09-26 10:44
 **/
@Data
@ApiModel(value = "附件信息")
public class AttachmentDTO {

	@ApiModelProperty(value = "附件名称")
	private String name;

	@ApiModelProperty(value = "附件URL")
	private String url;


	public static class AttachmentDTOHandler extends JsonbArrayTypeHandler<AttachmentDTO> {
		@Override
		protected TypeReference<List<AttachmentDTO>> typeReference() {
			return new TypeReference<List<AttachmentDTO>>() {};
		}
	}
}
