package com.huafon.models.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;
import java.io.Serializable;

import com.huafon.framework.mybatis.pojo.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 增加企业经营情况
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hf_company_operation_status")
@ApiModel(value="CompanyOperationStatus对象", description="")
public class CompanyOperationStatus extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "hf_company_info 表id")
    private Integer companyInfoId;

    @ApiModelProperty(value = "年度信息")
    @TableField(value = "year",updateStrategy = FieldStrategy.IGNORED)
    private String year;

    @ApiModelProperty(value = "上一年度营业收入")
    @TableField(value = "previous_year_revenue",updateStrategy = FieldStrategy.IGNORED)
    private String previousYearRevenue;

    @ApiModelProperty(value = "应提取安全生产费用")
    @TableField(value = "should_extract_funds",updateStrategy = FieldStrategy.IGNORED)
    private String shouldExtractFunds;

    @ApiModelProperty(value = "使用安全生产费用")
    @TableField(value = "actual_used_funds",updateStrategy = FieldStrategy.IGNORED)
    private String actualUsedFunds;

    @ApiModelProperty(value = "实际提取安全生产费用")
    @TableField(value = "real_funds",updateStrategy = FieldStrategy.IGNORED)
    private String realFunds;

    @ApiModelProperty(value = "安全生产费用支出清单")
    @TableField(value = "safe_list",updateStrategy = FieldStrategy.IGNORED)
    private String safeList;

    @ApiModelProperty(value = "安全费用提取年 度计划/方案")
    @TableField(value = "safe_plan",updateStrategy = FieldStrategy.IGNORED)
    private String safePlan;

    @TableField(exist = false)
    private Integer tenantId;

    @TableField(exist = false)
    private String communityCreditCode;

}
