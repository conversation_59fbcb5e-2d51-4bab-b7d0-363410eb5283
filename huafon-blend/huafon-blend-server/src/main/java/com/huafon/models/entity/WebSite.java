package com.huafon.models.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * @Description 网站资源
 * <AUTHOR>
 * @Date 2023/12/5 13:35
 * @Version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("hf_web_site")
public class WebSite extends BaseEntity {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 网站名称
     */
    private String webSiteName;

    /**
     * 网站链接
     */

    private String webSiteUrl;
    /**
     * 网站LOGO
     */

    private String webSiteLogo;

    /**
     * 租户id
     */
    private Integer tenantId;

}
