package com.huafon.models.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* <AUTHOR>
* @since 2023-12-24 16:57
*/
@Data
@ApiModel(value = "积分商城：兑换记录")
public class ScoreShopProductExchangeVo implements Serializable {

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "兑换记录编码")
    private String code;

    @ApiModelProperty(value = "操作人UserId")
    private Integer operateUserId;

    @ApiModelProperty(value = "操作人名称")
    private String operateUserName;

    @ApiModelProperty(value = "操作时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operateTime;

    @ApiModelProperty(value = "兑换人UserId")
    private Integer exchangeUserId;

    @ApiModelProperty(value = "兑换人姓名")
    private String exchangeUserName;

    @ApiModelProperty(value = "兑换日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date exchangeDate;

    @ApiModelProperty(value = "兑换数量")
    private Integer exchangeNum;

    @ApiModelProperty(value = "商品ID")
    private Integer productId;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "商品编码")
    private String productCode;

    @ApiModelProperty(value = "单个兑换积分")
    private BigDecimal singleRedemptionScore;

    @ApiModelProperty(value = "完成兑换前有多少积分")
    private BigDecimal originScore;

    @ApiModelProperty(value = "扣减积分")
    private BigDecimal deductionScore;

    @ApiModelProperty(value = "扣减后的积分")
    private BigDecimal targetScore;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "附件")
    private Object attachment;

    @ApiModelProperty(value = "租户ID")
    private Integer tenantId;

    @ApiModelProperty(value = "创建人名称")
    private String createByName;

    @ApiModelProperty(value = "编辑人名称")
    private String modifyByName;

    @ApiModelProperty(value = "编辑时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

}
