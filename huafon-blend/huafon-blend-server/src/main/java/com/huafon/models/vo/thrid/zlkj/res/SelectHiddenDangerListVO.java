package com.huafon.models.vo.thrid.zlkj.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2023-04-17 16:56:07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "SelectHiddenDangerListVo", description = "查询隐患列表")
public class SelectHiddenDangerListVO {

    @ApiModelProperty(value = "当前页数")
    private Integer current;

    @ApiModelProperty(value = "排序")
    private List<Orders> orders;

    @ApiModelProperty(value = "页数")
    private Integer pages;

    @ApiModelProperty(value = "记录")
    private List<Records> records;

    @ApiModelProperty(value = "查询总数")
    private boolean searchCount;

    @ApiModelProperty(value = "跨度")
    private Integer size;

    @ApiModelProperty(value = "总数")
    private Integer total;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel(value = "Orders", description = "Orders")
    public static class Orders {
        private boolean asc;
        private String column;
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel(value = "Records", description = "记录")
    public static class Records {
        @ApiModelProperty(value = "任务分配人")
        private String allotor;

        @ApiModelProperty(value = "任务分配人id")
        private Integer allotorid;

        @ApiModelProperty(value = "分配人电话")
        private String allotorphone;

        @ApiModelProperty(value = "分配时间")
        private String allottime;

        @ApiModelProperty(value = "隐患描述")
        private String checkdescription;

        @ApiModelProperty(value = "排查人")
        private String checker;

        @ApiModelProperty(value = "排查人id")
        private Integer checkerid;

        @ApiModelProperty(value = "排查人电话")
        private String checkerphone;

        @ApiModelProperty(value = "隐患上报图片地址，最多3张，以##作为分隔符")
        private String checkpicture;

        @ApiModelProperty(value = "排查人部门")
        private String checksection;

        @ApiModelProperty(value = "排查人部门编号")
        private String checksectioncode;

        @ApiModelProperty(value = "排查时间")
        private String checktime;

        @ApiModelProperty(value = "'排查类别:1日查2周查3月查4专项排查5临时检查6高级审核7督办8群众举报'")
        private Integer checktype;

        @ApiModelProperty(value = "语音文件地址")
        private String checkvoice;

        @ApiModelProperty(value = "公司id")
        private Integer companyid;

        @ApiModelProperty(value = "公司名称")
        private String companyname;

        @ApiModelProperty(value = "整改措施及应急预案")
        private String dealmeasure;

        @ApiModelProperty(value = "整改部门")
        private String dealsection;

        @ApiModelProperty(value = "整改部门编号")
        private String dealsectioncode;

        @ApiModelProperty(value = "延期原因")
        private String delayreason;

        @ApiModelProperty(value = "延期时间")
        private String delaytime;

        @ApiModelProperty(value = "整改后图片地址，最多3张，以##作为分隔符")
        private String donepicture;

        @ApiModelProperty(value = "整改完成日期")
        private String donetime;

        @ApiModelProperty(value = "验收情况")
        private String examinecondition;

        @ApiModelProperty(value = "验收人")
        private String examiner;

        @ApiModelProperty(value = "验收人id")
        private Integer examinerid;

        @ApiModelProperty(value = "验收人员手机号")
        private String examinerphone;

        @ApiModelProperty(value = "验收时间")
        private String examinetime;

        @ApiModelProperty(value = "隐患类别一级id")
        private Integer firsttype;

        @ApiModelProperty(value = "整改资金")
        private double funds;

        @ApiModelProperty(value = "整改要求")
        private String handdemand;

        @ApiModelProperty(value = "整改人电话")
        private String handlephone;

        @ApiModelProperty(value = "整改人")
        private String handler;

        @ApiModelProperty(value = "整改人id")
        private Integer handlerid;

        @ApiModelProperty(value = "来源部门")
        private String handoverdep;

        @ApiModelProperty(value = "来源部门编号")
        private String handoverdepcode;

        @ApiModelProperty(value = "来源人")
        private String handoverperson;

        @ApiModelProperty(value = "来源人id")
        private Integer handoverpersonid;

        @ApiModelProperty(value = "来源人电话")
        private String handoverpersonphone;

        @ApiModelProperty(value = "id")
        private Integer id;

        @ApiModelProperty(value = "隐患等级1一般，2重大")
        private Integer level;

        @ApiModelProperty(value = "整改期限")
        private String limittime;

        @ApiModelProperty(value = "采取措施1就地整改,2上报领导")
        private Integer measure;

        @ApiModelProperty(value = "0是未逾期1是已逾期")
        private Integer overdue;

        @ApiModelProperty(value = "督办级别1中央级2市级3区县级4街道镇乡级5其他")
        private Integer overseelevel;

        @ApiModelProperty(value = "督办文书号")
        private String overseenum;

        @ApiModelProperty(value = "所属工序")
        private String process;

        @ApiModelProperty(value = "工序编码")
        private String processcode;

        @ApiModelProperty(value = "报警次数")
        private Integer property;

        @ApiModelProperty(value = "排查项id")
        private Integer qldid;

        @ApiModelProperty(value = "举报类别(1生产隐患2-灾害事故)")
        private Integer reporttype;

        @ApiModelProperty(value = "复查人")
        private String reviewperson;

        @ApiModelProperty(value = "复查人id")
        private Integer reviewpersonid;

        @ApiModelProperty(value = "复查结果(0-合格1-不合格)")
        private String reviewresult;

        @ApiModelProperty(value = "复查情况")
        private String reviewsituation;

        @ApiModelProperty(value = "复查时间")
        private String reviewtime;

        @ApiModelProperty(value = "隐患类别二级id")
        private Integer secondtype;

        @ApiModelProperty(value = "整改后完整图片路径")
        private String showdonepicture;

        @ApiModelProperty(value = "隐患上报完整图片地址")
        private String showpicture;

        @ApiModelProperty(value = "隐患上报完整语音地址")
        private String showvoice;

        @ApiModelProperty(value = "隐患来源1企业自查，2行业主管部门检查，3中介机构检查，4群众举报")
        private Integer source;

        @ApiModelProperty(value = "移交部门")
        private String sourcedep;

        @ApiModelProperty(value = "移交部门编号")
        private String sourcedepcode;

        @ApiModelProperty(value = "移交人")
        private String sourceperson;

        @ApiModelProperty(value = "移交人id")
        private Integer sourcepersonid;

        @ApiModelProperty(value = "移交人电话")
        private String sourcepersonphone;

        @ApiModelProperty(value = "隐患状态：0已完成1待分配2待整改3待验收4延期申请中5移交申请中6重新上报待分配7整改逾期8停业停产整顿9已忽略")
        private Integer state;

        @ApiModelProperty(value = "隐患类别三级id")
        private Integer thirdtype;

        @ApiModelProperty(value = "隐患类别。1级类别##2级类别##3级类别")
        private String type;

        @ApiModelProperty(value = "政府督办单位id")
        private Integer unitid;

        @ApiModelProperty(value = "政府督办单位名称")
        private String unitname;

        @ApiModelProperty(value = "唯一uuid")
        private String uuid;

        @ApiModelProperty(value = "语音时长")
        private String voicelength;

        @ApiModelProperty(value = "区域")
        private String zone;

        @ApiModelProperty(value = "区域code")
        private String zonecode;
    }


}