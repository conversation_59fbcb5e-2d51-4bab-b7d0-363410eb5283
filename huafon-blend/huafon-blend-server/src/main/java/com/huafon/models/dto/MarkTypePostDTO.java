package com.huafon.models.dto;

import com.huafon.models.entity.MarkType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
* <AUTHOR>
* @since 2023-10-08 13:49
*/
@Data
@ApiModel(value = "标志库提交")
public class MarkTypePostDTO implements Serializable {

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "父节点")
    private Integer parentId;

    @ApiModelProperty(value = "类型名称")
    @NotEmpty(message = "类型名称不能为空")
    private String name;

    @ApiModelProperty(value = "图标")
    private String icon;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    public MarkType transferToEntity(MarkType target) {
        if (target == null) {
            target = new MarkType();
        }
        target.setId(this.getId());
        target.setParentId(this.getParentId());//父节点
        target.setName(this.getName());//类型名称
        target.setIcon(this.getIcon());//图标
        target.setRemark(this.getRemark());//备注
        target.setSort(this.getSort());//排序
        return target;
    }
}
