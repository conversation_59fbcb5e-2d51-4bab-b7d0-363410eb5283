package com.huafon.models.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @Description 公告通知entity
 * <AUTHOR>
 * @Date 2023/11/13 09:48
 * @Version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("hf_public_notice")
public class PublicNotice extends BaseEntity {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    @ApiModelProperty(value = "公告标题")
    private String publicTitle;

    @ApiModelProperty(value = "是否限期0：否，1：是")
    private Integer ifLimit;

    @ApiModelProperty(value = "截止时间")
    private Date limitTime;
    @ApiModelProperty(value = "附件")
    private String attach;

    @ApiModelProperty(value = "公告通知内容")
    private String noticeContent;


    @ApiModelProperty(value = "发布时间")
    private Date publishTime;

    @ApiModelProperty(value = "发布状态1：已发布 2:草稿箱 3：历史公告")
    private Integer publishState;

    @ApiModelProperty(value = "发布人")
    private String publishName;

    @ApiModelProperty(value = "发布人id")
    private Long publishId;

    @ApiModelProperty(value = "创建人姓名")
    private String createName;

    @ApiModelProperty(value = "租户id")
    private Integer tenantId;

    @ApiModelProperty(value = "最后编辑人")
    private String modifyUserName;

    @ApiModelProperty(value = "封面")
    private String coverImage;

    @ApiModelProperty(value = "接收对象类型 1:全部成员 2:部门 3:用户")
    private Integer receiveTargetType;

    @ApiModelProperty(value = "接收对象json")
    private String receiveTarget;

    @ApiModelProperty(value = "接收对象ids")
    private String receiveTargetIds;

}
