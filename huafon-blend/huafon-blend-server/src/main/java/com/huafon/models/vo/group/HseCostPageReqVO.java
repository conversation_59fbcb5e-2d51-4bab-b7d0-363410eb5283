package com.huafon.models.vo.group;

import com.huafon.framework.mybatis.pojo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 集团数据上报-HSE费用分页请求VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-11
 */
@Data
@ApiModel(value = "HseCostPageReqVO", description = "集团数据上报-HSE费用分页请求VO")
public class HseCostPageReqVO extends PageRequest {

    @ApiModelProperty("uuid")
    private String uuid;

    @ApiModelProperty(value = "租户id", hidden = true)
    private Integer tenantId;

}
