package com.huafon.models.vo.group;

import com.huafon.common.config.TenantContext;
import com.huafon.models.entity.group.ResourceDetail;
import com.huafon.support.config.UserContext;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <p>
 * 集团数据上报-温室气体情况明细VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-09
 */
@Data
@ApiModel(value = "ResourceDetailVO", description = "集团数据上报-资源情况明细VO")
public class ResourceDetailVO {

    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty(value = "维度名称")
    private String name;

    @ApiModelProperty(value = "数量")
    private BigDecimal num;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "备注")
    private String remark;

    public static ResourceDetail convert(ResourceDetailVO item) {
        if (item == null) {
            return null;
        }
        ResourceDetail target = new ResourceDetail();
        target.setId(item.getId());
        target.setName(item.getName());
        target.setUnit(item.getUnit());
        target.setNum(item.getNum());
        target.setRemark(item.getRemark());
        if (Objects.isNull(item.getId())) {
            target.setCreate(UserContext.getId());
            target.setTenantId(TenantContext.getOne());
        }
        target.setModify(UserContext.getId());
        return target;
    }

    public static ResourceDetailVO convert(ResourceDetail item) {
        if (item == null) {
            return null;
        }
        ResourceDetailVO target = new ResourceDetailVO();
        target.setId(item.getId());
        target.setId(item.getId());
        target.setName(item.getName());
        target.setUnit(item.getUnit());
        target.setNum(item.getNum());
        target.setRemark(item.getRemark());
        return target;
    }

}
