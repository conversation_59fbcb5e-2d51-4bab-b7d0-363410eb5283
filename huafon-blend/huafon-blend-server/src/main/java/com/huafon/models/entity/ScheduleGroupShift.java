package com.huafon.models.entity;

import lombok.Data;

import java.time.LocalDate;

/**
 * Table: hf_schedule_group_shift
 */
@Data
public class ScheduleGroupShift {
    /**
     * Column: id
     * Type: bigserial
     * Default value: nextval('security_environment.hf_schedule_group_shift_id_seq'::regclass)
     * Remark: id
     */
    private Integer id;

    /**
     * Column: group_id
     * Type: int8
     * Remark: 班组id
     */
    private Integer groupId;

    /**
     * Column: classes_id
     * Type: int8
     * Remark: 班次id
     */
    private Integer classesId;

    /**
     * Column: is_start
     * Type: bool
     * Default value: false
     * Remark: 是否是开始班次
     */
    private Boolean isStart;

    /**
     * Column: is_del
     * Type: int2
     * Default value: 0
     * Remark: 是否删除 0|否 1|是
     */
    private Integer isDel;

    /**
     * is_last_start
     */
    private Boolean isLastStart;

    /**
     * start_date
     */
    private LocalDate startDate;
}