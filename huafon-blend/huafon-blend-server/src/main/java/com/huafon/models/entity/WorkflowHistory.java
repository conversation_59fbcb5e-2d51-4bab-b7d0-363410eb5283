package com.huafon.models.entity;

import java.util.Date;
import lombok.Data;

/**
 * Table: hf_workflow_history
 */
@Data
public class WorkflowHistory {
    /**
     * Column: id
     * Type: bigserial
     * Default value: nextval('security_environment.hf_workflow_history_id_seq'::regclass)
     * Remark: id
     */
    private Long id;

    /**
     * Column: workflow_id
     * Type: varbit
     * Remark: 流程id
     */
    private String workflowId;

    /**
     * Column: user_id
     * Type: int8
     * Remark: 所属人user_id
     */
    private Integer userId;

    /**
     * Column: create_time
     * Type: timestamp
     * Remark: 创建时间
     */
    private Date createTime;

    /**
     * Column: modify_time
     * Type: timestamp
     * Remark: 更新时间
     */
    private Date modifyTime;


    private String businessKey;
}