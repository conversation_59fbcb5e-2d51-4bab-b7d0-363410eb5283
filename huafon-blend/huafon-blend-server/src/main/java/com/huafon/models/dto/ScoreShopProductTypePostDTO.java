package com.huafon.models.dto;

import com.huafon.models.entity.ScoreShopProductType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
* <AUTHOR>
* @since 2023-12-22 13:41
*/
@Data
@ApiModel(value = "积分商城：商品类型提交")
public class ScoreShopProductTypePostDTO implements Serializable {

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "父节点")
    private Integer parentId;

    @ApiModelProperty(value = "类型名称")
    @NotNull(message = "商品类型名称不能为空")
    private String name;

    @ApiModelProperty(value = "图标")
    private String icon;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "排序", hidden = true)
    private Integer sort;

    @ApiModelProperty(value = "是否默认：true默认，不可删除", hidden = true)
    private Boolean isDefault;

    @ApiModelProperty(value = "节点类型：TOP(顶级节点)、OTHER(其他)", hidden = true)
    private String type;

    @ApiModelProperty(value = "租户ID", hidden = true)
    private Integer tenantId;

    @ApiModelProperty(value = "创建人名称", hidden = true)
    private String createByName;

    @ApiModelProperty(value = "编辑人名称", hidden = true)
    private String modifyByName;


    public ScoreShopProductType transferToEntity(ScoreShopProductType target) {
        if (target == null) {
            target = new ScoreShopProductType();
        }
        target.setId(this.getId());
        target.setParentId(this.getParentId());//父节点
        target.setName(this.getName());//类型名称
        target.setIcon(this.getIcon());//图标
        target.setRemark(this.getRemark());//备注
        target.setSort(this.getSort());//排序
        target.setIsDefault(this.getIsDefault());//是否默认：true默认，不可删除
        target.setType(this.getType());//节点类型：TOP(顶级节点)、OTHER(其他)
        target.setTenantId(this.getTenantId());//租户ID
        target.setCreateByName(this.getCreateByName());//创建人名称
        target.setModifyByName(this.getModifyByName());//编辑人名称
        return target;
    }
}
