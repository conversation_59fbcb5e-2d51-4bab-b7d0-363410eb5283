
package com.huafon.models.vo.meeting;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName com.huafon.models.entity.meeting.MeetingParticipant
 * @Description
 * @createTime 2023年09月20日 10:33:00
 */
@Data
@ApiModel(description = "与会人员返回实体")
public class MeetingParticipantResp{

    @ApiModelProperty(value = "与会人员表主键")
    private Long id;

    @ApiModelProperty(value = "预定会议id")
    private Long reservationId;

    @ApiModelProperty(value = "与会人员id")
    private Long participantId;

    @ApiModelProperty(value = "与会人员姓名")
    private String participantName;

    @ApiModelProperty(value = "与会人员类型")
    private String participantType;

    @ApiModelProperty(value = "与会人员电话")
    private String mobile;
}
