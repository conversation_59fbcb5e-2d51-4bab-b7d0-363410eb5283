package com.huafon.models.entity.group;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <p>
 * 集团数据上报-温室气体情况
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-09
 */
@Data
@ApiModel(value = "GreenhouseGasesSituation", description = "集团数据上报-温室气体情况")
@TableName(value = "hf_group_greenhouse_gases_situation")
public class GreenhouseGasesSituation extends BaseEntity {

    private static final long serialVersionUID = 579178997555077956L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * uuid
     */
    private String uuid;

    /**
     * 所属企业
     */
    private String companyName;

    /**
     * 所属企业ID
     */
    private Integer companyId;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 版次
     */
    private Integer version;

    /**
     * 是否最新班次
     */
    private Boolean isLastVersion;

    /**
     * 备注
     */
    private String remark;

    /**
     * 修改人
     */
    private String modifyByName;

    /**
     * 租户ID
     */
    private Integer tenantId;

}
