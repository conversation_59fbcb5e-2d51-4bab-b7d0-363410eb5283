package com.huafon.models.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @ClassName com.huafon.models.enums.MeetingEnumDef
 * @Description
 * @createTime 2023年09月20日 15:30:00
 */
public interface MeetingEnumDef {
    enum MeetingRoomStatusEnum {
        normal(0, "正常"),
        repair(1, "维修"),
        /****************************上面状态是基础状态，记录与数据库****************************/

        /**************************下面的状态是派生状态，根据基础状态做细分，如果出现状态重叠，展示优先级：锁定 > 会议中 > 预定中 > 空闲************************/
        /**
         * 由状态0派生，根据是否有锁定记录查询所得
         */
        lock(2, "锁定"),
        /**
         * 由状态0派生，根据当前时间与会议时间计算所得
         */
        in_a_meeting(3, "会议中"),
        /**
         * 由状态0派生，根据当前时间与会议时间计算所得，状态生命周期为当天
         */
        idle(4, "空闲"),
        /**
         * 由状态0派生，根据当前时间与会议时间计算所得，状态生命周期为当天
         */
        in_reservation(5, "预定中"),
        /**
         * 兜底状态
         */
        others(9999, "其他");

        private final int status;

        private final String desc;

        MeetingRoomStatusEnum(int status, String desc) {
            this.status = status;
            this.desc = desc;
        }

        public int getStatus() {
            return status;
        }

        public String getDesc() {
            return desc;
        }

        public static MeetingRoomStatusEnum parseByStatus(int status) {
            for (MeetingRoomStatusEnum value : values()) {
                if (Objects.equals(value.getStatus(), status)) {
                    return value;
                }
            }
            return others;
        }

        /**
         * 会议室派生状态：由normal状态下细分的详细状态（非数据库状态一对多）
         * @return
         */
        public static List<MeetingRoomStatusEnum> getDescendentStatus(){
            return Arrays.asList(lock, in_a_meeting, idle, in_reservation);
        }

        /**
         * 会议室根本状态，数据库直接状态
         * @return
         */
        public static List<MeetingRoomStatusEnum> getBasicStatus(){
            return Arrays.asList(normal, repair);
        }
    }

    enum MeetingRoomTypeEnum {

        big(1, "大"),
        medium(2, "中"),
        small(3, "小"),
        others(9999, "其他");

        private final int type;
        private final String desc;

        MeetingRoomTypeEnum(int type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public int getType() {
            return type;
        }

        public String getDesc() {
            return desc;
        }

        public static MeetingRoomTypeEnum parseByType(int type) {
            for (MeetingRoomTypeEnum value : values()) {
                if (Objects.equals(type, value.getType())) {
                    return value;
                }
            }
            return others;
        }
    }

    enum MeetingStatusEnum {
        not_start(0, "未开始"),
        under_way(1, "进行中"),
        finish(2, "已结束"),
        others(9999, "其他");

        private final int status;
        private final String desc;

        MeetingStatusEnum(int status, String desc) {
            this.status = status;
            this.desc = desc;
        }

        public String getDesc() {
            return desc;
        }

        public int getStatus() {
            return status;
        }
    }

    enum MeetingPeriodEnum{
        day(1,"日"),
        week(2,"周"),
        month(3,"月"),
        others(9999, "年"),;
        private final Integer code;
        private final String desc;

        MeetingPeriodEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
        public static MeetingPeriodEnum parseByCode(Integer code){
            for (MeetingPeriodEnum value : values()) {
                if (Objects.equals(value.getCode(), code)) {
                    return value;
                }
            }
            return others;
        }
    }

    enum MeetingRoomLockTypeEnum{
        config(0,"配置锁定"),
        manual(1,"手动锁定"),
        others(9999,"其他");
        private final Integer type;
        private final String desc;

        MeetingRoomLockTypeEnum(Integer type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public Integer getType() {
            return type;
        }

        public String getDesc() {
            return desc;
        }
    }
}
