package com.huafon.models.vo.group;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 集团数据上报-HSE费用列表返回VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-11
 */
@Data
@ApiModel(value = "HseCostListRespVO", description = "集团数据上报-HSE费用列表返回VO")
public class HseCostListRespVO {

    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("uuid")
    private String uuid;

    @ApiModelProperty("所属企业")
    private String companyName;

    @ApiModelProperty("集团费用科目")
    private String subject;

    @ApiModelProperty("统计年月")
    private Date time;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("版次")
    private Integer version;

    @ApiModelProperty("修改人id")
    private Long modifyBy;

    @ApiModelProperty("修改人")
    private String modifyByName;

    @ApiModelProperty("修改时间")
    private String modifyTime;

    @ApiModelProperty("数据来源 0:手动录入 1:系统数据")
    private Integer source;

}
