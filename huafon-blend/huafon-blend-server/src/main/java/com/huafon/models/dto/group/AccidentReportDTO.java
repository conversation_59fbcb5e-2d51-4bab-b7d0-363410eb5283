package com.huafon.models.dto.group;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.huafon.service.support.CustomRow;
import com.huafon.service.support.ExpandAll;
import com.huafon.service.support.ManualGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Data
public class AccidentReportDTO extends CustomRow {

    @ExcelProperty(index = 0, value = "事故类别")
    @NotBlank(message = "事故类别不能为空", groups = ManualGroup.class)
    private String type;

    @ExcelProperty(index = 1, value = "事故等级")
    @NotBlank(message = "事故等级不能为空", groups = ManualGroup.class)
    private String gradeName;

    @ExcelProperty(index = 2, value = "责任单位")
    @NotBlank(message = "责任单位不能为空", groups = ManualGroup.class)
    private String reportDeptName;

    @ExcelProperty(index = 3, value = "事故标题")
    @NotBlank(message = "事故标题不能为空", groups = ManualGroup.class)
    private String name;

    @ExcelProperty(index = 4, value = "发生时间")
    @NotNull(message = "发生时间不能为空", groups = ManualGroup.class)
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm")
    @ExpandAll(minWidth = 18)
    private Date happenTime;

    @ExcelProperty(index = 5, value = "简要经过")
    @NotBlank(message = "简要经过不能为空", groups = ManualGroup.class)
    private String specificDesc;

    @ExcelProperty(index = 6, value = "直接经济损失（元）")
    @NotNull(message = "直接经济损失（元）不能为空", groups = ManualGroup.class)
    private BigDecimal lossAmount;

    @ExcelProperty(index = 7, value = "伤者姓名")
    private String personName;

    @ExcelProperty(index = 8, value = "工种")
    private String workType;

    @ExcelProperty(index = 9, value = "年龄")
    private String age;

    @ExcelProperty(index = 10, value = "工龄")
    private String workAge;

    @ExcelProperty(index = 11, value = "受伤部位")
    private String injuredPart;

    @ExcelProperty(index = 12, value = "伤害类型")
    private String injuredType;

    @ExcelProperty(index = 13, value = "伤情")
    private String injuredSituation;

    @ExcelProperty(index = 14, value = "损失工作日")
    private BigDecimal lostWorkDays;

    @ApiModelProperty("人员伤害信息")
    private List<AccidentReportPersonDTO> personList;

    public boolean merge() {
        return Objects.isNull(type)
                && Objects.isNull(gradeName)
                && Objects.isNull(reportDeptName)
                && Objects.isNull(name)
                && Objects.isNull(happenTime)
                && Objects.isNull(specificDesc)
                && Objects.isNull(lossAmount)
                && haveValue();
    }

    public boolean haveValue() {
        return Objects.nonNull(personName)
                || Objects.nonNull(workType)
                || Objects.nonNull(age)
                || Objects.nonNull(workAge)
                || Objects.nonNull(injuredPart)
                || Objects.nonNull(injuredType)
                || Objects.nonNull(injuredSituation);
    }

}
