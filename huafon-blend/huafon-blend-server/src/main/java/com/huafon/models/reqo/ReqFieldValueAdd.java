package com.huafon.models.reqo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/28 13:15
 */
@Data
public class ReqFieldValueAdd {

    @ApiModelProperty(value = "每个业务对应唯一的key", required = true)
    @NotBlank(message = "key不能为空")
    private String businessKey;

    @ApiModelProperty(value = "字段列表")
    private List<FieldDTO> fieldList;

    @Data
    public static class FieldDTO {

        @ApiModelProperty(value = "字段名", required = true)
        @NotBlank(message = "字段名不能为空")
        private String fieldName;

        @ApiModelProperty(value = "字段code", required = true)
        @NotBlank(message = "字段code不能为空")
        private String fieldCode;

        @ApiModelProperty(value = "值")
        private String fieldValue;
    }
}
