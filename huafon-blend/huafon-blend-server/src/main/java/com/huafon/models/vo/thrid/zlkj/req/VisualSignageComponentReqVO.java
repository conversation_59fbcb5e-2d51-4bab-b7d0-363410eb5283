package com.huafon.models.vo.thrid.zlkj.req;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName com.huafon.models.vo.thrid.zlkj.req.VisualSignageComponentReqVO
 * @Description
 * @createTime 2023年08月21日 14:43:00
 */
@Data
@ApiModel(description = "可视化看板组件请求实体")
public class VisualSignageComponentReqVO {
    /**
     * 组件类型
     */
    private String componentType;
    /**
     * 组件其他属性
     */
    @ApiModelProperty(hidden = true)
    private Map<String, Object> additionalProperties = new HashMap<>();

    @JsonAnySetter
    public void setAdditionalProperty(String key, Object value) {
        additionalProperties.put(key, value);
    }
}
