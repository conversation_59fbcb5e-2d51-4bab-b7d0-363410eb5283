package com.huafon.models.vo;

import com.huafon.models.enums.BacklogItemAndRiskPlanTypeEnum;
import com.huafon.models.enums.BacklogItemEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

import java.util.Map;
import java.util.Optional;

/**
 * @Description: 待办项
 * @Date: 2024/7/18 15:34
 * @Author: zyf
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "各待办项数量VO")
public class BacklogItemRespVO {

    @ApiModelProperty(value = "1:任务|2:计划")
    private Integer type;

    @ApiModelProperty(value = "各个模块的唯一Code")
    private String moduleCode;

    @ApiModelProperty(value = "各个模块名称")
    private String moduleName;

    @ApiModelProperty(value = "各个模块的数量")
    private Integer num;


    public BacklogItemRespVO(BacklogItemEnum type, Integer num) {
        this.type = type.getType();
        this.moduleCode = type.getCode();
        this.moduleName = type.getName();
        this.num = Objects.isNull(num) ? 0 : num;
    }


    public BacklogItemRespVO(BacklogItemEnum type, Map<Integer, Long> riskMap) {
        this.type = type.getType();
        this.moduleCode = type.getCode();
        this.moduleName = type.getName();
        Long numLong = Optional.ofNullable(riskMap.get(BacklogItemAndRiskPlanTypeEnum.getType(type))).orElse(0L);
        this.num = numLong.intValue();
    }
}
