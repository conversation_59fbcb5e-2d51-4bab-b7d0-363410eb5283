package com.huafon.models.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @Description 公告通知新增/编辑 vo
 * <AUTHOR>
 * @Date 2023/11/13 15:33
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class PublishNoticeAddVO {
    @ApiModelProperty(value = "id 编辑传入")
    private Integer id;
    @ApiModelProperty(value = "公告标题")
    @NotBlank(message = "公告标题不可为空")
    @Length(max = 50,message ="公告标题最大50字符" )
    private String publicTitle;

    @ApiModelProperty(value = "是否限期0：否，1：是")
    @NotNull(message = "是否限期不可为空")
    private Integer ifLimit;

    @ApiModelProperty(value = "截止时间")
    private Date limitTime;
    @ApiModelProperty(value = "附件")
    private String attach;

    @ApiModelProperty(value = "公告通知内容")
    @NotBlank(message = "公告通知内容不可为空")
    private String noticeContent;

    @ApiModelProperty(value = "发布状态1：已发布 2:草稿箱")
    @NotNull(message = "发布状态不可为空")
    private Integer publishState;

    @ApiModelProperty(value = "封面")
    private String coverImage;

    @ApiModelProperty(value = "接收对象类型 1:全部成员 2:部门 3:用户")
    private Integer receiveTargetType;

    @ApiModelProperty(value = "接收对象")
    private List<PublicNoticeReceiveTargetVO> receiveTargetList;

}
