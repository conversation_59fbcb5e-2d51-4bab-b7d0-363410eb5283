package com.huafon.models.vo.thrid.zlkj.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2023-04-17 14:34:18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "countHiddenDangerVo", description = "统计华峰风险管控清单数据")
public class CountHiddenDangerVO {

    @ApiModelProperty(value = "隐患整改完成率")
    private String investigationRate;

    @ApiModelProperty(value = "已验收")
    private Integer numAcceptance;

    @ApiModelProperty(value = "一般隐患数量")
    private Integer numGeneral;

    @ApiModelProperty(value = "重大隐患数量")
    private Integer numGreat;

    @ApiModelProperty(value = "本月新增数量")
    private Integer numNew;

    @ApiModelProperty(value = "逾期未整改数量")
    private Integer numOverdue;

    @ApiModelProperty(value = "待整改数量")
    private Integer numStayRectification;

    @ApiModelProperty(value = "待整改重大隐患数量")
    private Integer numStayRectificationGreat;

    @ApiModelProperty(value = "隐患总数")
    private Integer totalHiddenDanger;
}