package com.huafon.models.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 系统升级日志
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="SysUpdateLog对象", description="系统升级日志")
public class SysUpdateLog extends BaseEntity {

    private static final long serialVersionUID = 7384240548196656564L;
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "更新内容")
    private String content;

    @ApiModelProperty(value = "1：pc，2：移动端 3：数字孪生")
    private Integer type;

    @ApiModelProperty(value = "发布时间")
    private Date releaseTime;

    @ApiModelProperty(value = "最后编辑人")
    private String modifyUserName;

    @ApiModelProperty(value = "附件")
    @TableField(value = "files", updateStrategy = FieldStrategy.IGNORED)
    private String files;

    @ApiModelProperty(value = "地图项目id")
    private Integer mapProjectId;

    @ApiModelProperty(value = "基于版本")
    private String baseVersion;

    @ApiModelProperty(value = "依赖版本id")
    @TableField(value = "dependent_version_id", updateStrategy = FieldStrategy.IGNORED)
    private Integer dependentVersionId;

    @ApiModelProperty(value = "依赖版本")
    @TableField(value = "dependent_version", updateStrategy = FieldStrategy.IGNORED)
    private String dependentVersion;

    @ApiModelProperty(value = "客户端")
    private String client;

}
