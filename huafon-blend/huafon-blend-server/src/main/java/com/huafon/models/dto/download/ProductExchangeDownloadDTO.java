package com.huafon.models.dto.download;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.huafon.service.support.ExpandAll;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-12-24 17:45
 **/
@Data
@ContentRowHeight(18)
public class ProductExchangeDownloadDTO {

	@ExcelProperty(value = "兑换记录编码")
	@ExpandAll(minWidth = 22)
	private String code;

	@ExcelProperty(value = "兑换人")
	private String exchangeUserName;

	@ExcelProperty(value = "工号")
	private String workNum;

	@ExcelProperty(value = "部门岗位")
	private String departmentPostInfo;

	@ExcelProperty(value = "兑换日期")
	@DateTimeFormat(value = "yyyy-MM-dd")
	private Date exchangeDate;

	@ExcelProperty(value = "兑换商品")
	private String productName;

	@ExcelProperty(value = "商品名称编码")
	@ExpandAll(minWidth = 22)
	private String productCode;

	@ExcelProperty(value = "兑换数量")
	private Integer exchangeNum;

	@ExcelProperty(value = "扣减积分")
	private BigDecimal deductionScore;

//	@ExcelProperty(value = "余额积分")
//	private BigDecimal targetScore;

	@ExcelProperty(value = "备注说明")
	private String remark;

	@ExcelProperty(value = "操作人")
	private String operateUserName;

	@ExcelProperty(value = "操作时间")
	@DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
	@ExpandAll(minWidth = 22)
	private Date operateTime;
}
