package com.huafon.models.vo.group;

import com.huafon.common.config.TenantContext;
import com.huafon.models.entity.group.AccidentReport;
import com.huafon.support.config.UserContext;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 集团数据上报-事故报告VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@Data
@ApiModel(value = "AccidentReportVO", description = "集团数据上报-事故报告VO")
public class AccidentReportVO {

    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("所属企业")
    private String companyName;

    @ApiModelProperty("所属企业id")
    private Integer companyId;

    @ApiModelProperty("事故类别")
    private String type;

    @ApiModelProperty("事故等级")
    private String gradeName;

    @ApiModelProperty("责任单位")
    private String reportDeptName;

    @ApiModelProperty("事故标题")
    private String name;

    @ApiModelProperty("发生时间")
    private Date happenTime;

    @ApiModelProperty("简要经过")
    private String specificDesc;

    @ApiModelProperty("直接经济损失")
    private BigDecimal lossAmount;

    @ApiModelProperty("最终事故报告")
    private String reportAndAttachment;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("人员伤害信息")
    private List<AccidentReportPersonVO> personList;

    @ApiModelProperty("修改人id")
    private Long modifyBy;

    @ApiModelProperty("修改人")
    private String modifyByName;

    @ApiModelProperty("修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "uuid", hidden = true)
    private String uuid;

    @ApiModelProperty(value = "租户id", hidden = true)
    private Integer version;

    @ApiModelProperty(value = "租户id", hidden = true)
    private Integer tenantId;

    public static AccidentReport convert(AccidentReportVO item) {
        if (item == null) {
            return null;
        }
        AccidentReport accidentReport = new AccidentReport();
        accidentReport.setId(item.getId());
        accidentReport.setCompanyName(item.getCompanyName());
        accidentReport.setCompanyId(item.getCompanyId());
        accidentReport.setType(item.getType());
        accidentReport.setGradeName(item.getGradeName());
        accidentReport.setReportDeptName(item.getReportDeptName());
        accidentReport.setName(item.getName());
        accidentReport.setHappenTime(item.getHappenTime());
        accidentReport.setSpecificDesc(item.getSpecificDesc());
        accidentReport.setLossAmount(item.getLossAmount());
        accidentReport.setReportAndAttachment(item.getReportAndAttachment());
        if (Objects.isNull(item.getId())) {
            accidentReport.setCreate(UserContext.getId());
            accidentReport.setTenantId(TenantContext.getOne());
        }
        accidentReport.setModify(UserContext.getId());
        accidentReport.setModifyByName(UserContext.get().getName());
        return accidentReport;
    }

    public static AccidentReportVO convert(AccidentReport item) {
        if (item == null) {
            return null;
        }
        AccidentReportVO accidentReportVO = new AccidentReportVO();
        accidentReportVO.setId(item.getId());
        accidentReportVO.setCompanyName(item.getCompanyName());
        accidentReportVO.setCompanyId(item.getCompanyId());
        accidentReportVO.setType(item.getType());
        accidentReportVO.setGradeName(item.getGradeName());
        accidentReportVO.setReportDeptName(item.getReportDeptName());
        accidentReportVO.setName(item.getName());
        accidentReportVO.setHappenTime(item.getHappenTime());
        accidentReportVO.setSpecificDesc(item.getSpecificDesc());
        accidentReportVO.setLossAmount(item.getLossAmount());
        accidentReportVO.setReportAndAttachment(item.getReportAndAttachment());
        accidentReportVO.setModifyBy(item.getModifyBy());
        accidentReportVO.setModifyByName(item.getModifyByName());
        accidentReportVO.setModifyTime(item.getModifyTime());
        accidentReportVO.setUuid(item.getUuid());
        accidentReportVO.setVersion(item.getVersion());
        accidentReportVO.setUuid(item.getUuid());
        accidentReportVO.setTenantId(item.getTenantId());
        return accidentReportVO;
    }

}
