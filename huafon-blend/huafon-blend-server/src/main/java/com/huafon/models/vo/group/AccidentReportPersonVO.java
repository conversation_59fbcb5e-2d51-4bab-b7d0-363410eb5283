package com.huafon.models.vo.group;

import com.huafon.common.config.TenantContext;
import com.huafon.models.entity.group.AccidentReportPerson;
import com.huafon.support.config.UserContext;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <p>
 * 集团数据上报-事故报告人员伤害信息VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@Data
@ApiModel(value = "AccidentReportPersonVO", description = "集团数据上报-事故报告人员伤害信息VO")
public class AccidentReportPersonVO {

    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("父id")
    private Integer parentId;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("工种")
    private String workType;

    @ApiModelProperty("年龄")
    private String age;

    @ApiModelProperty("工龄")
    private String workAge;

    @ApiModelProperty("受伤部位")
    private String injuredPart;

    @ApiModelProperty("伤害类型")
    private String injuredType;

    @ApiModelProperty("伤情")
    private String injuredSituation;

    @ApiModelProperty("损失工作日")
    private BigDecimal lostWorkDays;

    public static AccidentReportPerson convert(AccidentReportPersonVO item) {
        if (item == null) {
            return null;
        }
        AccidentReportPerson accidentReportPerson = new AccidentReportPerson();
        accidentReportPerson.setId(item.getId());
        accidentReportPerson.setName(item.getName());
        accidentReportPerson.setWorkType(item.getWorkType());
        accidentReportPerson.setAge(item.getAge());
        accidentReportPerson.setWorkAge(item.getWorkAge());
        accidentReportPerson.setInjuredPart(item.getInjuredPart());
        accidentReportPerson.setInjuredType(item.getInjuredType());
        accidentReportPerson.setInjuredSituation(item.getInjuredSituation());
        accidentReportPerson.setLostWorkDays(item.getLostWorkDays());
        if (Objects.isNull(item.getId())) {
            accidentReportPerson.setCreate(UserContext.getId());
            accidentReportPerson.setTenantId(TenantContext.getOne());
        }
        accidentReportPerson.setModify(UserContext.getId());
        return accidentReportPerson;
    }

    public static AccidentReportPersonVO convert(AccidentReportPerson item) {
        if (item == null) {
            return null;
        }
        AccidentReportPersonVO accidentReportPersonVO = new AccidentReportPersonVO();
        accidentReportPersonVO.setId(item.getId());
        accidentReportPersonVO.setParentId(item.getParentId());
        accidentReportPersonVO.setName(item.getName());
        accidentReportPersonVO.setWorkType(item.getWorkType());
        accidentReportPersonVO.setAge(item.getAge());
        accidentReportPersonVO.setWorkAge(item.getWorkAge());
        accidentReportPersonVO.setInjuredPart(item.getInjuredPart());
        accidentReportPersonVO.setInjuredType(item.getInjuredType());
        accidentReportPersonVO.setInjuredSituation(item.getInjuredSituation());
        accidentReportPersonVO.setLostWorkDays(item.getLostWorkDays());
        return accidentReportPersonVO;
    }

}
