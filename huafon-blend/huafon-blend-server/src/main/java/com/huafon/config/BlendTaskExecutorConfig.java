package com.huafon.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @ClassName com.huafon.config.BlendTaskExecutorConfig
 * @Description  blend-server的全局线程池，核心及最大线程数采用默认，可根据实际业务处理场景及服务器核心数等配置做相应配置调整
 * @createTime 2023年11月10日 11:05:00
 */
@Configuration
public class BlendTaskExecutorConfig {

    @Value("${task.corePoolSize:10}")
    private Integer corePoolSize;

    @Value("${task.maxPoolSize:20}")
    private Integer maxPoolSize;

    @Bean("blendThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor threadPoolTaskExecutor() {
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        //线程池所使用的缓冲队列
        taskExecutor.setQueueCapacity(Integer.MAX_VALUE);
        // 线程池维护线程的最少数量(核心线程数)
        taskExecutor.setCorePoolSize(corePoolSize);
        // 线程池维护线程的最大数量
        taskExecutor.setMaxPoolSize(maxPoolSize);
        // 线程池维护线程所允许的空闲时间
        taskExecutor.setKeepAliveSeconds(300);
        taskExecutor.setThreadNamePrefix("blend-server-thread-pool-");
        taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        taskExecutor.initialize();
        return taskExecutor;
    }
}
