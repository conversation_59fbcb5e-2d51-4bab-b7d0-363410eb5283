package com.huafon.controller;

import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.models.reqo.PublicNoticeQuery;
import com.huafon.models.vo.*;
import com.huafon.service.PublicNoticeConfigService;
import com.huafon.service.PublicNoticeService;
import com.huafon.support.core.pojo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @Description 公告通知
 * <AUTHOR>
 * @Date 2023/11/13 09:46
 * @Version 1.0
 */
@Api(tags = "公告通知模块")
@RestController
@RequestMapping("/publicNotice")
public class PublicNoticeController {
    @Resource
    private PublicNoticeService publicNoticeService;
    @Resource
    private PublicNoticeConfigService publicNoticeConfigService;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询/其他公告")
    public CommonPage<PublicNoticeVO> pageInfo(@Valid @RequestBody PublicNoticeQuery query) {
        return publicNoticeService.pageInfo(query);
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation(value = "新增/编辑")
    public R<Void> saveOrUpdate(@Valid @RequestBody PublishNoticeAddVO publishNoticeAddVO) {
        publicNoticeService.create(publishNoticeAddVO);
        return R.ok();
    }

    @PostMapping("/batchDelete")
    @ApiOperation(value = "批量删除")
    public R<Void> batchDelete(@Valid @RequestBody CommonIdsVO publishNoticeIds) {
        publicNoticeService.batchDelete(publishNoticeIds);
        return R.ok();
    }

    @PostMapping("/batchPublish")
    @ApiOperation(value = "批量发布")
    public R<Void> batchPublish(@Valid @RequestBody CommonIdsVO publishNoticeIds) {
        publicNoticeService.batchPublish(publishNoticeIds);
        return R.ok();
    }

    @PostMapping("/info")
    @ApiOperation(value = "详情")
    public R<PublicNoticeDetailVO> info(@Valid @RequestBody CommonIdVO vo) {
        return R.ok(publicNoticeService.info(vo.getId()));
    }

    @PostMapping("/homeInfo")
    @ApiOperation(value = "首页详情")
    public R<PublicNoticeDetailVO> homeInfo(@Valid @RequestBody CommonIdVO vo) {
        return R.ok(publicNoticeService.homeInfo(vo.getId()));
    }

    @PostMapping("/homeInfo/h5")
    @ApiOperation(value = "首页详情")
    public R<PublicNoticeDetailVO> homeInfoH5(@Valid @RequestBody CommonIdVO vo) {
        return R.ok(publicNoticeService.homeInfoH5(vo.getId()));
    }

    @PostMapping("/publicNoticeLimit")
    @ApiOperation(value = "测试定时任务")
    public R<Void> publicNoticeLimit() {
        publicNoticeService.publicNoticeLimit();
        return R.ok();
    }

    @PostMapping("/statistics")
    @ApiOperation(value = "各状态数量统计")
    public R<PublishNoticeCountVO> statistics() {
        return R.ok(publicNoticeService.statistics());
    }

    @PostMapping("/setConfig")
    @ApiOperation(value = "设置配置")
    public void setConfig(@RequestBody PublicNoticeConfigVO reqVO) {
        publicNoticeConfigService.set(reqVO);
    }

    @GetMapping("/getConfig")
    @ApiOperation(value = "获取配置")
    public R<PublicNoticeConfigVO> getConfig() {
        return R.ok(publicNoticeConfigService.get());
    }

}
