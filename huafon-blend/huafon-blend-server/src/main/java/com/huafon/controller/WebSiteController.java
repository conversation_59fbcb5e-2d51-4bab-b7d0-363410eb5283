package com.huafon.controller;

import com.huafon.models.vo.CommonIdLongVO;
import com.huafon.models.vo.WebSiteReqVO;
import com.huafon.models.vo.WebSiteVO;
import com.huafon.service.WebSiteService;
import com.huafon.support.core.pojo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @Description 网站资源
 * <AUTHOR>
 * @Date 2023/12/5 13:42
 * @Version 1.0
 */
@Api(tags = "网站资源模块")
@RestController
@RequestMapping("/webSite")
public class WebSiteController {

    @Resource
    private WebSiteService webSiteService;
    @PostMapping("/saveOrUpdate")
    @ApiOperation(value = "新增/编辑")
    public R<Void> saveOrUpdate(@Valid @RequestBody WebSiteVO webSiteVO) {
        webSiteService.create(webSiteVO);
        return R.ok();
    }

    @PostMapping("/info")
    @ApiOperation(value = "详情")
    public R<WebSiteVO> info(@Valid @RequestBody CommonIdLongVO vo) {
        return R.ok(webSiteService.info(vo.getId()));
    }

    @PostMapping("/list")
    @ApiOperation(value = "列表")
    public R<List<WebSiteVO>> pageMeetingRoom(@Valid @RequestBody WebSiteReqVO req) {
        return R.ok(webSiteService.getList(req));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除")
    public R<Void> batchDelete(@Valid @RequestBody CommonIdLongVO vo) {
        webSiteService.delete(vo.getId());
        return R.ok();
    }

    @PostMapping("/init")
    @ApiOperation(value = "初始化")
    public R<Void> init(@RequestBody List<Integer> tenantIds) {
        webSiteService.initTenantInfo(tenantIds);
        return R.ok();
    }
}
