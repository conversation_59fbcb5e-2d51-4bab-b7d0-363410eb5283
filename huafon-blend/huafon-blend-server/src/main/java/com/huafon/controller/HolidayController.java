package com.huafon.controller;

import com.huafon.models.vo.HolidayNightVO;
import com.huafon.models.vo.HolidayV2Vo;
import com.huafon.models.vo.HolidayVo;
import com.huafon.models.vo.NightTimeVO;
import com.huafon.service.HolidayService;
import com.huafon.service.NightTimeService;
import com.huafon.support.core.pojo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/1 10:14:59
 * @description 节假日管理
 */
@Api(tags = "节假日管理")
@RestController
@RequestMapping("/holiday")
public class HolidayController {

    @Autowired
    private HolidayService holidayService;
    @Autowired
    private NightTimeService nightTimeService;

    @GetMapping("/list")
    @ApiOperation(value = "根据年份获取节假日")
    public R<HolidayV2Vo> list(@RequestParam("year") String year) {
        return R.ok(holidayService.list(year));
    }

    @GetMapping("/listByYearAndMonth")
    @ApiOperation(value = "根据年份月份获取节假日，格式（yyyy-MM-dd）")
    public R<List<HolidayVo>> listByYearAndMonth(@RequestParam("date") String date) {
        return R.ok(holidayService.listByYearAndMonth(date));
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation(value = "批量新增或编辑节假日")
    public void saveOrUpdate(@RequestBody HolidayV2Vo reqVo) {
        holidayService.saveOrUpdate(reqVo);
    }

    @GetMapping("/include")
    @ApiOperation(value = "判断日期范围内是否存在节假日")
    public R<Boolean> include(@Parameter(name = "beginDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date beginDate, @Parameter(name = "endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        return R.ok(holidayService.include(beginDate,endDate));
    }

    @PostMapping("/setNightTime")
    @ApiOperation(value = "设置夜间时间段")
    public void setNightTime(@Valid @RequestBody NightTimeVO reqVO) {
        nightTimeService.set(reqVO);
    }

    @GetMapping("/getNightTime")
    @ApiOperation(value = "获取夜间时间段")
    public R<NightTimeVO> getNightTime() {
        return R.ok(nightTimeService.get());
    }

    @GetMapping("/isHolidayNight")
    @ApiOperation(value = "判断日期范围内是否存在节假日和夜间")
    public R<HolidayNightVO> isHolidayNight(@Parameter(name = "beginDate") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date beginDate,
                                            @Parameter(name = "endDate") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endDate) {
        HolidayNightVO vo = new HolidayNightVO();
        vo.setIsHoliday(holidayService.include(beginDate,endDate));
        vo.setIsNight(nightTimeService.include(beginDate,endDate));
        return R.ok(vo);
    }

}
