package com.huafon.controller.group;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.huafon.common.utils.bean.BeanUtils;
import com.huafon.models.dto.UploadResultDTO;
import com.huafon.models.dto.group.AccidentReportDTO;
import com.huafon.models.dto.group.AccidentReportPersonDTO;
import com.huafon.models.dto.group.ExcelDownloadDTO;
import com.huafon.models.dto.group.HseCostDTO;
import com.huafon.models.dto.group.WasteReportDetailDTO;
import com.huafon.models.dto.group.WasteReportHandleDetailDTO;
import com.huafon.service.ExcelService;
import com.huafon.service.group.AccidentReportExcelService;
import com.huafon.service.group.HseCostExcelService;
import com.huafon.service.group.WasteReportDetailExcelService;
import com.huafon.service.support.CustomRow;
import com.huafon.service.support.EasyExcelListener;
import com.huafon.support.core.pojo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 集团数据上报-Excel处理
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Slf4j
@Api(tags = "集团数据上报-Excel处理")
@RestController
@RequestMapping("/group/excel")
public class ExcelController {

    @Autowired
    private ExcelService excelService;
    @Autowired
    private AccidentReportExcelService accidentReportExcelService;
    @Autowired
    private HseCostExcelService hseCostExcelService;
    @Autowired
    private WasteReportDetailExcelService wasteReportDetailExcelService;

    @PostMapping("/accidentReport/import")
    @ApiOperation(value = "集团数据上报-事故报告导入")
    public R<UploadResultDTO<AccidentReportDTO>> accidentReportImport(@RequestPart(value = "file") MultipartFile file,
                                                                      @RequestParam("companyName") String companyName,
                                                                      @RequestParam("companyId") Integer companyId) throws Exception {
        LocalDateTime startTime = LocalDateTime.now();
        log.info("---------------------------------------集团数据上报-事故报告导入-------------------------------------------");
        log.info("文件名：{}, 大小: {}", file.getOriginalFilename(), FileUtils.byteCountToDisplaySize(file.getSize()));
        log.info("---------------------------------------集团数据上报-事故报告导入-------------------------------------------");
        EasyExcelListener<AccidentReportDTO> easyExcelListener = new EasyExcelListener<>(accidentReportExcelService, AccidentReportDTO.class);
        EasyExcel.read(file.getInputStream(), AccidentReportDTO.class, easyExcelListener).sheet().doRead();
        if (!CollectionUtils.isEmpty(easyExcelListener.getSuccessData())) {
            accidentReportExcelService.importIntoDatabase(companyName,companyId, easyExcelListener.getSuccessData());
        }
        LocalDateTime endTime = LocalDateTime.now();
        log.info("[集团数据上报-事故报告导入]:开始于：{}, 结束于：{}, 时间：{}", startTime, endTime, Duration.between(startTime, endTime).getSeconds());
        return R.ok(new UploadResultDTO<>(easyExcelListener.getSuccessData(), easyExcelListener.getErrorData()));
    }

    @PostMapping("/hseCost/import")
    @ApiOperation(value = "集团数据上报-HSE费用导入")
    public R<UploadResultDTO<HseCostDTO>> hseCostImport(@RequestPart(value = "file") MultipartFile file,
                                                        @RequestParam("companyName") String companyName,
                                                        @RequestParam("companyId") Integer companyId) throws Exception {
        LocalDateTime startTime = LocalDateTime.now();
        log.info("---------------------------------------集团数据上报-HSE费用导入-------------------------------------------");
        log.info("文件名：{}, 大小: {}", file.getOriginalFilename(), FileUtils.byteCountToDisplaySize(file.getSize()));
        log.info("---------------------------------------集团数据上报-HSE费用导入-------------------------------------------");
        EasyExcelListener<HseCostDTO> easyExcelListener = new EasyExcelListener<>(hseCostExcelService, HseCostDTO.class);
        EasyExcel.read(file.getInputStream(), HseCostDTO.class, easyExcelListener).sheet().doRead();
        if (!CollectionUtils.isEmpty(easyExcelListener.getSuccessData())) {
            hseCostExcelService.importIntoDatabase(companyName,companyId, easyExcelListener.getSuccessData());
        }
        LocalDateTime endTime = LocalDateTime.now();
        log.info("[集团数据上报-HSE费用导入]:开始于：{}, 结束于：{}, 时间：{}", startTime, endTime, Duration.between(startTime, endTime).getSeconds());
        return R.ok(new UploadResultDTO<>(easyExcelListener.getSuccessData(), easyExcelListener.getErrorData()));
    }

    @PostMapping("/wasteReport/import")
    @ApiOperation(value = "集团数据上报-固废导入")
    public R<UploadResultDTO<WasteReportDetailDTO>> wasteReportImport(@RequestPart(value = "file") MultipartFile file) throws Exception {
        LocalDateTime startTime = LocalDateTime.now();
        log.info("---------------------------------------集团数据上报-固废导入-------------------------------------------");
        log.info("文件名：{}, 大小: {}", file.getOriginalFilename(), FileUtils.byteCountToDisplaySize(file.getSize()));
        log.info("---------------------------------------集团数据上报-固废导入-------------------------------------------");
        EasyExcelListener<WasteReportDetailDTO> easyExcelListener = new EasyExcelListener<>(wasteReportDetailExcelService, WasteReportDetailDTO.class);
        EasyExcel.read(file.getInputStream(), WasteReportDetailDTO.class, easyExcelListener).sheet().doRead();
        LocalDateTime endTime = LocalDateTime.now();
        log.info("[集团数据上报-固废导入]:开始于：{}, 结束于：{}, 时间：{}", startTime, endTime, Duration.between(startTime, endTime).getSeconds());
        return R.ok(new UploadResultDTO<>(easyExcelListener.getSuccessData(), easyExcelListener.getErrorData()));
    }

    @PostMapping("/upload/error/download")
    @ApiOperation(value = "上传错误信息导出")
    public void errorDownload(HttpServletResponse response, @RequestBody List<? extends CustomRow> errorInfos) throws IOException {
        if (CollectionUtils.isEmpty(errorInfos)) {
            return;
        }
        CustomRow excelBaseRow = errorInfos.get(0);
        Class<? extends CustomRow> clazz = excelBaseRow.getClass();
        Set<String> headList = Arrays.stream(clazz.getDeclaredFields())
                .filter(Objects::nonNull)
                .filter(field -> field.getAnnotation(ExcelProperty.class) != null)
                .map(Field::getName)
                .collect(Collectors.toSet());
        headList.add("errorMessage");
        if (clazz == AccidentReportDTO.class) {
            ExcelDownloadDTO dto = accidentReportErrorDownload(BeanUtils.convert(AccidentReportDTO.class,errorInfos));
            excelService.processDownloadExcel(response, "错误信息", clazz, headList, dto.getDataList(), dto.getCellRangeAddresses());
        } else if (clazz == WasteReportDetailDTO.class) {
            ExcelDownloadDTO dto = wasteReportErrorDownload(BeanUtils.convert(WasteReportDetailDTO.class,errorInfos));
            excelService.processDownloadExcel(response, "错误信息", clazz, headList, dto.getDataList(), dto.getCellRangeAddresses());
        } else {
            excelService.processDownloadExcel(response, "错误信息", clazz, headList, errorInfos, null);
        }
    }

    private ExcelDownloadDTO accidentReportErrorDownload(List<AccidentReportDTO> errorInfos) {
        List<AccidentReportDTO> dataList = new ArrayList<>();
        List<CellRangeAddress> cellRangeAddresses = new ArrayList<>();
        int row = 1;
        for (int i=0;i<errorInfos.size();i++) {
            AccidentReportDTO data = errorInfos.get(i);
            List<AccidentReportPersonDTO> personList = data.getPersonList();
            if (CollectionUtils.isEmpty(personList)) {
                dataList.add(data);
                row++;
            } else {
                int size = personList.size();
                for(int j=0;j<size;j++) {
                    AccidentReportDTO mergeData = BeanUtils.convert(data, AccidentReportDTO.class);
                    AccidentReportPersonDTO person = personList.get(j);
                    mergeData.setPersonName(person.getName());
                    mergeData.setWorkType(person.getWorkType());
                    mergeData.setAge(person.getAge());
                    mergeData.setWorkAge(person.getWorkAge());
                    mergeData.setInjuredPart(person.getInjuredPart());
                    mergeData.setInjuredType(person.getInjuredType());
                    mergeData.setLostWorkDays(person.getLostWorkDays());
                    mergeData.setErrorMessage(data.getErrorMessage());
                    dataList.add(mergeData);
                }
                if (size>1) {
                    for (int k=0;k<=6;k++) {
                        cellRangeAddresses.add(new CellRangeAddress(row,row+size-1,k,k));
                    }
                    cellRangeAddresses.add(new CellRangeAddress(row,row+size-1,15,15));
                    row += size;
                } else {
                    row++;
                }
            }
        }
        ExcelDownloadDTO dto = new ExcelDownloadDTO();
        dto.setDataList(dataList);
        dto.setCellRangeAddresses(cellRangeAddresses);
        return dto;
    }

    private ExcelDownloadDTO wasteReportErrorDownload(List<WasteReportDetailDTO> errorInfos) {
        List<WasteReportDetailDTO> dataList = new ArrayList<>();
        List<CellRangeAddress> cellRangeAddresses = new ArrayList<>();
        int row = 1;
        for (int i=0;i<errorInfos.size();i++) {
            WasteReportDetailDTO data = errorInfos.get(i);
            List<WasteReportHandleDetailDTO> handleDetailList = data.getHandleDetailList();
            if (CollectionUtils.isEmpty(handleDetailList)) {
                dataList.add(data);
                row++;
            } else {
                int size = handleDetailList.size();
                for(int j=0;j<size;j++) {
                    WasteReportDetailDTO mergeData = BeanUtils.convert(data, WasteReportDetailDTO.class);
                    WasteReportHandleDetailDTO handleDetail = handleDetailList.get(j);
                    mergeData.setFlowDirection(handleDetail.getFlowDirection());
                    mergeData.setOutStorage(handleDetail.getOutStorage());
                    mergeData.setErrorMessage(data.getErrorMessage());
                    dataList.add(mergeData);
                }
                if (size>1) {
                    for (int k=0;k<=5;k++) {
                        cellRangeAddresses.add(new CellRangeAddress(row,row+size-1,k,k));
                    }
                    cellRangeAddresses.add(new CellRangeAddress(row,row+size-1,7,7));
                    for (int k=9;k<=13;k++) {
                        cellRangeAddresses.add(new CellRangeAddress(row,row+size-1,k,k));
                    }
                    row += size;
                } else {
                    row++;
                }
            }
        }
        ExcelDownloadDTO dto = new ExcelDownloadDTO();
        dto.setDataList(dataList);
        dto.setCellRangeAddresses(cellRangeAddresses);
        return dto;
    }


}
