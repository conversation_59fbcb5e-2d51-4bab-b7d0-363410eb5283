package com.huafon.utils;

import com.alibaba.fastjson.JSONObject;
import com.huafon.models.dto.group.GroupDataPushDTO;
import com.huafon.models.vo.group.CompanyVO;
import com.huafon.service.group.BaseCenterCompanyService;
import com.huafon.service.group.GroupApiService;
import com.huafon.support.core.pojo.R;
import com.huafon.support.exceptions.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-29
 */
@Slf4j
@Component
public class GroupPushUtil {

//    @Value("${base-center-url:localhost}")
//    private String baseUrl;
//
//    @Value("${company-code:HUAFON}")
//    private String companyCode;

    @Autowired
    private GroupApiService groupApiService;

    @Autowired
    private BaseCenterCompanyService baseCenterCompanyService;

    public void pushData(GroupDataPushDTO dto) {
        log.info("======数据推送======:{}", JSONObject.toJSONString(dto));
        CompanyVO config = baseCenterCompanyService.getConfig(false, dto.getTenantId());
        if (Objects.nonNull(config)) {
            dto.setCompanyCode(config.getSecret());
            String url = config.getUrl() + "/data/report/upload";
            log.info("======数据推送地址======:{}", url);
            String response = HttpClientUtil.postJson(url, JSONObject.toJSONString(dto));
            //R response = groupApiService.dataPush(dto);
            log.info("response:{}", response);
//            if (!response.isSuccess()) {
//                throw new ServiceException(response.getMsg());
//            }
        }
    }

    public void deleteData(GroupDataPushDTO dto) {
        CompanyVO config = baseCenterCompanyService.getConfig(false, dto.getTenantId());
        if (Objects.nonNull(config)) {
            dto.setCompanyCode(config.getSecret());
            String url = config.getUrl() + "/data/report/delete";
            log.info("======数据推送地址======:{}", url);
            String response = HttpClientUtil.postJson(url, JSONObject.toJSONString(dto));
            //R response = groupApiService.dataDelete(dto);
            log.info("response:{}", response);
//            if (!response.isSuccess()) {
//                throw new ServiceException(response.getMsg());
//            }
        }
    }

}
