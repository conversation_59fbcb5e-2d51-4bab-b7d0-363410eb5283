package com.huafon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.huafon.models.entity.GeneralGroupConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @since 2023-07-28 17:13
*/
@Mapper
public interface GeneralGroupConfigMapper extends BaseMapper<GeneralGroupConfig> {

    /**
    * 批量生成
    */
    void batchInsert(@Param("source") List<GeneralGroupConfig> source);

}
