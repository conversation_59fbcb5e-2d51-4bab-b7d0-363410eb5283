package com.huafon.dao.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huafon.common.config.TenantContext;
import com.huafon.models.entity.Mark;
import com.huafon.models.reqo.MarkPageQuery;
import com.huafon.models.vo.MarkCommonVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collections;
import java.util.List;

/**
* <AUTHOR>
* @since 2023-10-08 13:34
*/
@Mapper
public interface MarkMapper extends BaseMapper<Mark> {

    /**
    * 分页
    */
    IPage<MarkCommonVo> queryByPage(@Param("page") IPage<MarkCommonVo> page, @Param("query") MarkPageQuery query);

    /**
    * 批量生成
    */
    void batchInsert(@Param("source") List<Mark> source);


    default LambdaQueryWrapper<Mark> lambdaQuery() {
        return new LambdaQueryWrapper<Mark>().in(Mark::getTenantId, Collections.singletonList(TenantContext.getOne()));
    }

}
