package com.huafon.service;

import com.alibaba.fastjson.JSON;
import com.huafon.BlendApplication;
import com.huafon.common.config.TenantContext;
import com.huafon.models.reqo.ReqConsole;
import com.huafon.models.vo.ConsoleVo;
import com.huafon.support.config.UserContext;
import com.huafon.support.core.pojo.R;
import com.huafon.support.dto.UserInfoDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.Collections;

/**
 * <AUTHOR>
 * @since 2022-10-09 17:22
 **/
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = BlendApplication.class)
public class ConsoleServiceTest {

	@Autowired
	ConsoleService consoleService;

	@BeforeEach
	public void beforeAll() {
		UserInfoDto user = new UserInfoDto();
		user.setUserId(47L);
		user.setUsername("wjz");
		user.setName("魏家政");
		UserContext.bind(user);

		TenantContext.bind(Collections.singletonList(1));
	}

	@Test
	public void testStatistic() {
		ReqConsole reqConsole = new ReqConsole();
		reqConsole.setAccidentVo(true);
//		reqConsole.setWorkSafeVo(true);
//		reqConsole.setStaffVo(true);
//		reqConsole.setRiskVo(true);
//		reqConsole.setLicenceVo(true);
		R statistics = consoleService.statistics(reqConsole, false);
		System.out.println(JSON.toJSONString(((ConsoleVo) statistics.getData()).getAccidentVo()));
	}
}
