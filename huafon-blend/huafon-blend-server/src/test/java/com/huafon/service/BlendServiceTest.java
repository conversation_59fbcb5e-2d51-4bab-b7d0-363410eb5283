package com.huafon.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.huafon.BlendApplication;
import com.huafon.common.config.TenantContext;
import com.huafon.support.config.UserContext;
import com.huafon.support.core.pojo.R;
import com.huafon.support.dto.UserInfoDto;
import com.huafon.threeSimultaneity.api.ThreeSimultaneityTaskService;
import com.huafon.threeSimultaneity.dto.ThreeSimultaneityTaskCountDTO;
import org.apache.dubbo.rpc.RpcContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.Collections;

import static com.huafon.threeSimultaneity.api.ThreeSimultaneityTaskService.CONTEXT_TENANT_KEY;
import static com.huafon.threeSimultaneity.api.ThreeSimultaneityTaskService.CONTEXT_USER_KEY;

/**
 * <AUTHOR>
 * @since 2022-08-22 18:39
 **/
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = BlendApplication.class)
public class BlendServiceTest {

	@Autowired
	BlendService blendService;

	@Autowired
	ThreeSimultaneityTaskService threeSimultaneityTaskService;

	@BeforeEach
	public void beforeAll() {
		UserInfoDto user = new UserInfoDto();
		user.setUserId(2755L);
		user.setUsername("wjz");
		user.setName("魏家政");
		UserContext.bind(user);
		TenantContext.bind(Collections.singletonList(1));
	}

	@Test
	public void testBacklog(){
		R backlog = blendService.backlog();
		System.out.println(JSON.toJSONString(backlog));
	}


	@Test
	public void testBacklogCount() {
		R r = blendService.backlogCount();
		System.out.println(JSON.toJSONString(r));
	}

	@Test
	public void testThreeSimultaneityTaskService(){
		RpcContext context = RpcContext.getContext();
		context.setAttachment(CONTEXT_TENANT_KEY, 1);
		context.setAttachment(CONTEXT_USER_KEY, 3164L);
		ThreeSimultaneityTaskCountDTO res = threeSimultaneityTaskService.queryTaskCount();
		System.out.println(JSONObject.toJSONString(res));
	}
}
