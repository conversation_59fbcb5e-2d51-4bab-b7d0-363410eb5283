package com.huafon.api.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/12/4 14:07
 */

@Getter
@AllArgsConstructor
public enum StatisticsAccidentEnum {

    ACCIDENT_NUM("ACCIDENT_NUM", "事故数量"),
    ACCIDENT_INJURED_RATE("ACCIDENT_INJURED_RATE", "千人负伤率"),
    ACCIDENT_INJURED_NUM("ACCIDENT_INJURED_NUM","受伤人次"),
    ACCIDENT_STAFF_NUM("ACCIDENT_STAFF_NUM","公司人数"),

    ACCIDENT_MONTH_CHART("ACCIDENT_MONTH_CHART", "月度事故数量统计"),
    ACCIDENT_DEPT_CHART("ACCIDENT_DEPT_CHART", "部门事故数量统计"),
    ACCIDENT_TYPE_CHART("ACCIDENT_TYPE_CHART", "事故类型统计"),
    ACCIDENT_INJURED_PART_CHART("ACCIDENT_INJURED_PART_CHART", "受伤部位统计"),
    ACCIDENT_INJURED_RATE_CHART("ACCIDENT_INJURED_RATE_CHART", "千人负伤率统计"),
    ;

    //code
    private final String code;

    //名称
    private final String name;
}
