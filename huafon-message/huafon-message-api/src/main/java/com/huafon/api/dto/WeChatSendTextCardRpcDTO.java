package com.huafon.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description: 发送企业微信文本卡片模式消息
 * @date 2023-08-24 16:10:44
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WeChatSendTextCardRpcDTO implements Serializable {

    private static final long serialVersionUID = 6703859351084111604L;
    /**
     * 账号（手机号）
     */
    private String account;
    /**
     * 标题
     */
    private String title;
    /**
     * 正文
     */
    private String description;
    /**
     * 详情调整链接
     */
    private String url;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        WeChatSendTextCardRpcDTO that = (WeChatSendTextCardRpcDTO) o;

        return new EqualsBuilder().append(account, that.account).append(title, that.title)
                .append(description, that.description).append(url, that.url).isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37).append(account).append(title).append(description).append(url).toHashCode();
    }
}