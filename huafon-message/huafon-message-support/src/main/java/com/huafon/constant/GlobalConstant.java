package com.huafon.constant;

/**
 * <AUTHOR> zhang
 * @program: huafon-message
 * @description: 全局变量
 * @date 2022-03-21 11:41:09
 */
public class GlobalConstant {
    /**
     * <AUTHOR>
     * @describe: redis订阅的topic 通过redis pub/sub 发布
     * @date 2022/3/21 11:59
     */
    public static final String REDIS_TOPIC_CHANNEL = "server-change";

    /**
     * <AUTHOR>
     * @describe: hash环
     * @date 2022/3/21 11:59
     */
    public static final String HASH_RING_REDIS = "ring";

    /**
     * <AUTHOR>
     * @describe: websocket实例上线
     * @date 2022/3/21 12:00
     */
    public static final String SERVER_UP_MESSAGE = "UP";

    /**
     * <AUTHOR>
     * @describe: websocket实例下线
     * @date 2022/3/21 12:00
     */
    public static final String SERVER_DOWN_MESSAGE = "DOWN";

    /**
     * <AUTHOR>
     * @describe: websocket连接id对应的websocket服务
     * @date 2022/3/21 12:00
     */
    public static final String CONNECT_ID_TO_SERVER = "websocket-userId";

    /**
     * <AUTHOR>
     * @describe: rabbitmq 广播的交换器
     * @date 2022/3/21 12:00
     */
    public static final String FANOUT_EXCHANGE_NAME = "websocket-cluster-exchange";

    /**
     * <AUTHOR>
     * @describe: websocket连接器
     * @date 2022/3/21 12:01
     */
    public static final String WEBSOCKET_ENDPOINT_PATH = "/connect";

    /**
     * <AUTHOR>
     * @describe: websocket controller层前缀
     * @date 2022/3/23 16:46
     */
    public static final String WEBSOCKET_CONTROLLER_PATH = "/ws";

    /**
     * <AUTHOR>
     * @describe: 发送给谁的消息参数userId
     * @date 2022/3/23 16:00
     */
    public static final String QUERY_USER_ID = "userId";

    private GlobalConstant() {
    }
}