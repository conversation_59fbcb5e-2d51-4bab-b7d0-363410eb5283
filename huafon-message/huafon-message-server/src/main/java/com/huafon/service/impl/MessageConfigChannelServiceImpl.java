package com.huafon.service.impl;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.huafon.api.enums.MessageConfigChannelType;
import com.huafon.api.vo.MessageConfigChannelRequest;
import com.huafon.api.vo.MessageConfigChannelResponse;
import com.huafon.common.utils.bean.BeanUtils;
import com.huafon.framework.mybatis.service.MybatisServiceImpl;
import com.huafon.mapper.MessageConfigChannelMapper;
import com.huafon.models.entity.MessageConfigChannel;
import com.huafon.service.MessageConfigChannelService;
import com.huafon.support.config.UserContext;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * date  : 2022/10/26 9:55
 * description:
 */
@Service
public class MessageConfigChannelServiceImpl extends MybatisServiceImpl<MessageConfigChannelMapper, MessageConfigChannel> implements MessageConfigChannelService {

    private final MessageSendConfigServiceImpl messageSendConfigService;

    public MessageConfigChannelServiceImpl(MessageSendConfigServiceImpl messageSendConfigService) {
        this.messageSendConfigService = messageSendConfigService;
    }

    @Override
    public void cleanAneSave(Integer configId, List<MessageConfigChannelResponse.InfoVo> channelVos) {
        List<MessageConfigChannel> channels = new ArrayList<>();

        Map<MessageConfigChannelType, Boolean> channelVoMap = channelVos.stream().collect(Collectors.toMap(MessageConfigChannelRequest.BaseVo::getChannel, MessageConfigChannelRequest.BaseVo::getEnable));

        List<MessageConfigChannel> channelDbs = this.lambdaQuery().eq(MessageConfigChannel::getConfigId, configId).list();

        for (int i = 0; i < channelDbs.size(); i++) {
            MessageConfigChannel channel = channelDbs.get(i);
            if (channelVoMap.containsKey(channel.getChannel())) {
                channel.setEnable(channelVoMap.getOrDefault(channel.getChannel(), Boolean.FALSE));
                channel.setModify(UserContext.getId());
                channels.add(channel);
            }
        }
        this.updateBatchById(channels);

    }

    @Override
    public List<MessageConfigChannelResponse.InfoVo> getByConfigId(Integer configId) {
        List<MessageConfigChannel> channels = this.lambdaQuery().eq(MessageConfigChannel::getConfigId, configId).list();
        return channels.stream().map(channel -> BeanUtils.convert(channel, MessageConfigChannelResponse.InfoVo.class)).collect(Collectors.toList());
    }

    @Override
    public List<MessageConfigChannelResponse.InfoVo> getOpenChannelByConfigId(Integer configId) {
        List<MessageConfigChannel> channels = this.lambdaQuery().eq(MessageConfigChannel::getConfigId, configId).list();
        channels = channels.stream().filter(channel -> messageSendConfigService.isOpenChannel(channel.getChannel().getValue()).equals(Boolean.TRUE)).collect(Collectors.toList());
        return channels.stream().map(channel -> BeanUtils.convert(channel, MessageConfigChannelResponse.InfoVo.class)).collect(Collectors.toList());
    }

    @Override
    public void init(Integer configId, List<MessageConfigChannelResponse.InfoVo> channelVos) {
        List<MessageConfigChannel> channels = new ArrayList<>();
        Map<MessageConfigChannelType, Boolean> channelVoMap = channelVos.stream().collect(Collectors.toMap(MessageConfigChannelRequest.BaseVo::getChannel, MessageConfigChannelRequest.BaseVo::getEnable));
        MessageConfigChannelType[] channelTypes = MessageConfigChannelType.values();
        for (int i = 0; i < channelTypes.length; i++) {
            MessageConfigChannelType channel = channelTypes[i];
            MessageConfigChannel tmp = MessageConfigChannel.builder()
                    .configId(configId)
                    .channel(channel)
                    .enable(channelVoMap.getOrDefault(channel, Boolean.FALSE))
                    .build();
            tmp.setCreate(UserContext.getId());
            channels.add(tmp);
        }
        this.saveBatch(channels);
    }
}