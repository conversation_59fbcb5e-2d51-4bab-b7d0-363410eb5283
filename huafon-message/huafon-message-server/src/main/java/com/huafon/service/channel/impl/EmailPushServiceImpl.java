package com.huafon.service.channel.impl;

import com.huafon.models.entity.MessageSendConfig;
import com.huafon.models.entity.MessageSendLog;
import com.huafon.models.enums.ChannelEnum;
import com.huafon.models.enums.SendMsgStatus;
import com.huafon.service.MessageSendConfigService;
import com.huafon.service.MessageSendLogService;
import com.huafon.service.channel.ChannelPushService;
import com.huafon.service.channel.ChannelPushServiceStrategyFactory;
import com.huafon.utils.EmailUtil;
import com.huafon.utils.MsgUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2023-08-22 10:07:19
 */
@Service
@Slf4j
public class EmailPushServiceImpl implements ChannelPushService, InitializingBean {

    private final MessageSendLogService messageSendLogService;
    private final EmailUtil emailUtil;
    private final MessageSendConfigService messageSendConfigService;

    public EmailPushServiceImpl(MessageSendLogService messageSendLogService,
                                EmailUtil emailUtil,
                                MessageSendConfigService messageSendConfigService) {
        this.messageSendLogService = messageSendLogService;
        this.emailUtil = emailUtil;
        this.messageSendConfigService = messageSendConfigService;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ChannelPushServiceStrategyFactory.register(ChannelEnum.EMAIL.getCode(), this);
    }

    @Override
    public void sendMessage(String account, String msg) {

        msg = MsgUtil.replace(msg);
        MessageSendConfig messageSendConfig = messageSendConfigService.getChannel(ChannelEnum.EMAIL.getCode());
        if (messageSendConfig.getIsOpen().equals(SendMsgStatus.SEND_OPEN.getType())) {
            try {
                emailUtil.send(msg, account);
                messageSendLogService.saveLog(MessageSendLog.builder()
                        .account(account)
                        .channel(ChannelEnum.EMAIL.getCode())
                        .channelName(ChannelEnum.EMAIL.getDesc())
                        .content(msg)
                        .createTime(new Date())
                        .build());
            } catch (Exception ex) {
                log.error("发送异常");
                messageSendLogService.saveLog(MessageSendLog.builder()
                        .account(account)
                        .channel(ChannelEnum.EMAIL.getCode())
                        .channelName(ChannelEnum.EMAIL.getDesc())
                        .content("邮件发送异常：" + ex.getMessage() + "")
                        .createTime(new Date())
                        .build());
            }
        } else {
            log.error("发送异常：未开启邮件通道");
        }
    }

    @Override
    public void sendImageMessage(String account, String context, String title, String cardImageUrl,String messageUrl) {

    }

    @Override
    public void sendTextCardMessage(String account, String title, String description, String url) {

    }
}