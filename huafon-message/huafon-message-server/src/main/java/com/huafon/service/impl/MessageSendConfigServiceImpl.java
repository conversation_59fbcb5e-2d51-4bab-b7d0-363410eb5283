package com.huafon.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.huafon.framework.mybatis.enums.DelFlag;
import com.huafon.models.enums.ChannelEnum;
import com.huafon.models.enums.PushConfigType;
import com.huafon.models.enums.SendMsgStatus;
import com.huafon.models.vo.OpenChannelListRspVO;
import com.huafon.models.vo.PushListRspVO;
import com.huafon.service.MessageSendConfigService;
import com.huafon.mapper.MessageSendConfigMapper;
import com.huafon.models.entity.MessageSendConfig;
import com.huafon.support.exceptions.ServiceException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <p>
 * 消息发送类型表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Service
public class MessageSendConfigServiceImpl extends ServiceImpl<MessageSendConfigMapper, MessageSendConfig> implements MessageSendConfigService {

    private final MessageSendConfigMapper messageSendConfigMapper;

    public MessageSendConfigServiceImpl(MessageSendConfigMapper messageSendConfigMapper) {
        this.messageSendConfigMapper = messageSendConfigMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void switchOpen(String channel) {

        MessageSendConfig messageSendConfig = findOneByType(channel);
        if (PushConfigType.PUSH_SMS.getType().equals(messageSendConfig.getType().trim())) {
            //短信推送类型

            //将短信推送类型的全部关闭
            messageSendConfigMapper.update(MessageSendConfig.builder()
                            .isOpen(SendMsgStatus.SEND_CLOSE.getType())
                            .modifyTime(new Date())
                            .build(),
                    new LambdaQueryWrapper<MessageSendConfig>()
                            .eq(MessageSendConfig::getType, PushConfigType.PUSH_SMS.getType()));

            //将短信推送类型的相关渠道全部打开
            messageSendConfigMapper.update(MessageSendConfig.builder()
                            .isOpen(SendMsgStatus.SEND_OPEN.getType())
                            .modifyTime(new Date())
                            .build(),
                    new LambdaQueryWrapper<MessageSendConfig>()
                            .eq(MessageSendConfig::getChannel, channel));


        } else if (PushConfigType.PUSH_OTHER.getType().equals(messageSendConfig.getType().trim())) {
            //其他推送渠道
            messageSendConfigMapper.update(MessageSendConfig.builder()
                            .isOpen(SendMsgStatus.SEND_OPEN.getType())
                            .modifyTime(new Date())
                            .build(),
                    new LambdaQueryWrapper<MessageSendConfig>()
                            .eq(MessageSendConfig::getChannel, channel));
        }else if (PushConfigType.PUSH_EMAIL.getType().equals(messageSendConfig.getType().trim())) {
            //邮箱
            messageSendConfigMapper.update(MessageSendConfig.builder()
                            .isOpen(SendMsgStatus.SEND_OPEN.getType())
                            .modifyTime(new Date())
                            .build(),
                    new LambdaQueryWrapper<MessageSendConfig>()
                            .eq(MessageSendConfig::getChannel, channel));
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void switchClose(String channel) {

        findOneByType(channel);
        messageSendConfigMapper.update(MessageSendConfig.builder()
                        .isOpen(SendMsgStatus.SEND_CLOSE.getType())
                        .modifyTime(new Date())
                        .build(),
                new LambdaQueryWrapper<MessageSendConfig>()
                        .eq(MessageSendConfig::getChannel, channel));
    }

    @Override
    public List<PushListRspVO> getPushConfigList() {

        List<PushListRspVO> pushLists = new ArrayList<>();

        PushConfigType[] pushConfigTypes = PushConfigType.values();
        for (PushConfigType o : pushConfigTypes) {

            List<MessageSendConfig> messageSendConfigs = findItemsByType(o.getType());

            pushLists.add(PushListRspVO.builder()
                    .type(o.getType())
                    .channelLists(Lists.transform(messageSendConfigs, (MessageSendConfig) -> PushListRspVO.ChannelList.builder()
                            .channel(MessageSendConfig.getChannel())
                            .remark(MessageSendConfig.getRemark())
                            .isOpen(MessageSendConfig.getIsOpen()).build()))
                    .build());
        }
        return pushLists;
    }

    @Override
    public List<OpenChannelListRspVO> getOpenChannelList() {

        List<MessageSendConfig> messageSendConfigs = new LambdaQueryChainWrapper<>(messageSendConfigMapper)
                .eq(MessageSendConfig::getIsDel, DelFlag.SAVE.getValue())
                .eq(MessageSendConfig::getIsOpen, SendMsgStatus.SEND_OPEN.getType())
                .list();

        List<OpenChannelListRspVO> list = Lists.transform(messageSendConfigs, (MessageSendConfig) ->
                OpenChannelListRspVO.builder()
                        .channel(PushConfigType.PUSH_SMS.getType().equals(MessageSendConfig.getType().trim()) ? PushConfigType.PUSH_SMS.getType() : MessageSendConfig.getChannel())
                        .channelName(PushConfigType.PUSH_SMS.getType().equals(MessageSendConfig.getType().trim()) ? "短信" : MessageSendConfig.getRemark())
                        .build()

        );

        return list;
    }

    @Override
    public MessageSendConfig getOpenSms() {
        return this.lambdaQuery().eq(MessageSendConfig::getIsOpen, SendMsgStatus.SEND_OPEN.getType())
                .eq(MessageSendConfig::getType, ChannelEnum.SMS.getCode())
                .eq(MessageSendConfig::getIsDel, DelFlag.SAVE.getValue())
                .one();
    }

    @Override
    public MessageSendConfig getChannel(String channel) {

        return this.lambdaQuery()
                .eq(MessageSendConfig::getChannel, channel)
                .eq(MessageSendConfig::getIsDel, DelFlag.SAVE.getValue())
                .one();
    }

    @Override
    public Boolean isOpenChannel(String channel) {
        //短信有多个通道。只要有一个通道打开就认为，短信通道SMS是打开的
        if (ChannelEnum.SMS.getCode().equals(channel)) {
            if (Objects.nonNull(this.getOpenSms())) {
                return Boolean.TRUE;
            }
        } else {
            return this.getChannel(channel).getIsOpen().equals(SendMsgStatus.SEND_OPEN.getType()) ? Boolean.TRUE : Boolean.FALSE;
        }
        return Boolean.FALSE;
    }

    /**
     * @param type 类型
     * @return java.util.List<com.huafon.models.entity.MessageSendConfig>
     * <AUTHOR>
     * @describe: 通过类型渠道列表
     * @date 2023/8/21 14:44
     */
    private List<MessageSendConfig> findItemsByType(String type) {
        return Optional.ofNullable(new LambdaQueryChainWrapper<>(messageSendConfigMapper)
                .eq(MessageSendConfig::getIsDel, DelFlag.SAVE.getValue())
                .eq(MessageSendConfig::getType, type)
                .list()).orElse(new ArrayList<>());
    }

    /**
     * @param channel 渠道号
     * @return com.huafon.models.entity.MessageSendConfig
     * <AUTHOR>
     * @describe: 通过渠道找到数据
     * @date 2023/8/21 14:00
     */
    private MessageSendConfig findOneByType(String channel) {
        return Optional.ofNullable(this.lambdaQuery().eq(MessageSendConfig::getChannel, channel).one()).orElseThrow(() -> new ServiceException("不存在当前的渠道"));
    }


}
