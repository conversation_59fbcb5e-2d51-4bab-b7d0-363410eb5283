package com.huafon.service.channel;

import org.springframework.util.Assert;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2023-08-22 09:36:40
 */
public class ChannelPushServiceStrategyFactory {

    private static Map<String, ChannelPushService> services = new ConcurrentHashMap<String, ChannelPushService>();

    public static ChannelPushService getByChannel(String channel) {
        return services.get(channel);
    }

    public static void register(String type, ChannelPushService channelPushService) {
        Assert.notNull(type, "type can't be null");
        services.put(type, channelPushService);
    }

}