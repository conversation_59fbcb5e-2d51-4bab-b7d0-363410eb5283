package com.huafon.blend.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.huafon.blend.model.vo.resp.MsgListRespVO;
import com.huafon.blend.model.vo.resp.MsgRemindRespVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


/**
 * <AUTHOR> zhang
 * @program: huafon-base
 * @description:
 * @date 2022-08-17 15:45:16
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MsgListDTO {

    @ApiModelProperty(value = "主键id，消息id")
    private Long id;

    @ApiModelProperty(value = "消息内容（json格式:[{\"content\":\"内容1\"},{\"content\":\"内容2\"},{\"content\":\"内容3\"}]）")
    @JsonIgnore
    private String contentJson;

    @ApiModelProperty(value = "消息类型（来源）")
    private String type;

    @ApiModelProperty(value = "消息主题")
    private String subject;

    @ApiModelProperty(value = "给个模块的内容id，来源于各个模块的主键id，前端需要拿主键id去读取详情内容")
    private Long contentId;

    @ApiModelProperty(value = "是否已读 0 未读 1已读")
    private Integer isRead;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;


    public static MsgListRespVO convertToMsgListRespVO(MsgListDTO item) {
        if (item == null) {
            return null;
        }
        MsgListRespVO msgListRespVO = new MsgListRespVO();
        msgListRespVO.setId(item.getId());
        msgListRespVO.setMsg(item.getContentJson());
        msgListRespVO.setType(item.getType());
        msgListRespVO.setSubject(item.getSubject());
        msgListRespVO.setContentId(item.getContentId());
        msgListRespVO.setIsRead(item.getIsRead());
        msgListRespVO.setCreateTime(item.getCreateTime());
        return msgListRespVO;
    }
}