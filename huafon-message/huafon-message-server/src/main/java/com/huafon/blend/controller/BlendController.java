package com.huafon.blend.controller;

import com.huafon.api.dto.blend.MsgAddRpcDTO;
import com.huafon.api.service.blend.MsgRpcService;
import com.huafon.blend.model.vo.req.MsgListReqVO;
import com.huafon.blend.model.vo.req.MsgPushReqVO;
import com.huafon.blend.model.vo.req.MsgStatReqVO;
import com.huafon.blend.model.vo.req.MsgUnreadReqVO;
import com.huafon.blend.model.vo.resp.MsgListRespVO;
import com.huafon.blend.model.vo.resp.MsgPushRespVO;
import com.huafon.blend.model.vo.resp.MsgRemindRespVO;
import com.huafon.blend.model.vo.resp.MsgTypeRespVO;
import com.huafon.blend.service.MessageService;
import com.huafon.blend.service.MessageTypeService;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.support.core.pojo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR> zhang
 * @program: huafon-base
 * @description: 聚合消息中心
 * @date 2022-08-15 13:15:49
 */
@RestController
@RequestMapping("/blend")
@Api(value = "聚合消息中心", tags = {"聚合消息配置管理接口"})
@Slf4j
public class BlendController {

    private final MessageService messageService;

    private final MessageTypeService messageTypeService;

    @DubboReference
    private MsgRpcService msgRpcService;

    @Autowired
    public BlendController(MessageService messageService,
                           MessageTypeService messageTypeService) {
        this.messageService = messageService;
        this.messageTypeService = messageTypeService;
    }

    @PostMapping("/msg/list")
    @ApiOperation(value = "消息列表")
    public R<CommonPage<MsgListRespVO>> getMessages(@RequestBody MsgListReqVO msgListReq) {
        return R.ok(messageService.getMessages(msgListReq));
    }

    @GetMapping("/msg/types")
    @ApiOperation(value = "返回全部消息类型")
    public R<List<MsgTypeRespVO>> getMessages() {
        return R.ok(messageTypeService.getMsgTypes());
    }

    @PostMapping("/no/read/msg/list")
    @ApiOperation(value = "未读消息列表")
    public R<CommonPage<MsgListRespVO>> getNoReadMessages(@RequestBody MsgListReqVO msgListReq) {
        return R.ok(messageService.getNoReadMessages(msgListReq));
    }

    @PostMapping("/group/msg/list")
    @ApiOperation(value = "分组消息列表")
    public R<List<MsgRemindRespVO>> getGroupMessages(@RequestBody MsgListReqVO msgListReq) {
        return R.ok(messageService.getGroupMessages(msgListReq));
    }

    @PutMapping("/read/msg/{msgId}")
    @ApiOperation(value = "读取消息，点击我知道了按钮，触发该接口,将消息设置为已经读取")
    public R readMessage(@PathVariable Long msgId) {
        messageService.setMessageIsRead(msgId);
        return R.ok();
    }

    @PostMapping("/msg/stat")
    @ApiOperation(value = "返回消息统计数据")
    public R getMsgStat(@RequestBody MsgStatReqVO req) {
        return R.ok(messageService.readMsgStat(req));
    }

    @PutMapping("/msg/one/click/read/{userId}")
    @ApiOperation(value = "消息一键已读")
    public R oneClickMsg(@PathVariable Integer userId) {
        messageService.oneClickMsg(userId);
        return R.ok();
    }

    @PostMapping("/unread/msg/cnt")
    @ApiOperation(value = "统计未读消息")
    @Deprecated
    public R getUnreadMsgCnt(@RequestBody MsgUnreadReqVO msgUnreadReq) {
        return R.ok(messageService.readUnreadMsgCnt(msgUnreadReq));
    }

    @PostMapping("/push/app/msg")
    @ApiOperation(value = "app推送消息")
    public R<MsgPushRespVO> pushAppMsg(@RequestBody MsgPushReqVO msgPushReq) {
        return R.ok(messageService.pushAppMsg(msgPushReq));
    }

    @PostMapping("/msg")
    @ApiOperation(value = "新增消息")
    @Deprecated
    public R addMessage(@RequestBody MsgAddRpcDTO msgAddRpc) {
        //msgRpcService.addMessage(msgAddRpc);
        return R.ok();
    }
}