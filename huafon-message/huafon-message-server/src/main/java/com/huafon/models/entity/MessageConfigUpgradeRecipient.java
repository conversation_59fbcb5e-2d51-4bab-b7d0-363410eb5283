package com.huafon.models.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.huafon.api.enums.MessageConfigRecipientLevel;
import com.huafon.api.enums.MessageConfigRecipientType;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import lombok.Data;

/**
* 消息通知-升级提醒
* <AUTHOR>
* @since 2024-09-04 13:27
*/
@Data
@TableName("hf_message_config_upgrade_recipient")
public class MessageConfigUpgradeRecipient extends BaseEntity{

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
    * 消息通知升级ID
    */
    @TableField(value = "upgrade_id")
    private Integer upgradeId;

    /**
    * 用户id
    */
    @TableField(value = "user_id")
    private Integer userId;

    /**
    * 渠道类型目前，USER、CUSTOMIZE
    */
    @TableField(value = "type")
    private MessageConfigRecipientType type;

    /**
    * 等级，SELF、LEVEL1、LEVEL2、LEVEL3、LEVEL4、LEVEL5
    */
    @TableField(value = "level")
    private MessageConfigRecipientLevel level;

    /**
    * 角色ID
    */
    @TableField(value = "role_id")
    private Integer roleId;


}
