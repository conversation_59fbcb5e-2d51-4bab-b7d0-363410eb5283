package com.huafon.models.vo;

import com.huafon.framework.mybatis.pojo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2023-08-21 20:15:01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "MessageLogPageReqVO", description = "消息日志请求")
public class MessageLogPageReqVO extends PageRequest {

    @ApiModelProperty(value = "发送内容")
    private String content;

    @ApiModelProperty(value = "渠道")
    private String channel;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

}