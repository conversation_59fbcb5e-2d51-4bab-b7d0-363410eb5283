package com.huafon.models.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import lombok.Data;

/**
* 消息通知-升级提醒
* <AUTHOR>
* @since 2024-09-04 13:23
*/
@Data
@TableName("hf_message_config_upgrade")
public class MessageConfigUpgrade extends BaseEntity{

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
    * 消息通知配置ID
    */
    @TableField(value = "config_id")
    private Integer configId;

    /**
    * LEVEL1、LEVEL2、LEVEL3、LEVEL4、LEVEL5
    */
    @TableField(value = "level")
    private String level;

    /**
    * 天
    */
    @TableField(value = "day")
    private Integer day;

    /**
    * 小时
    */
    @TableField(value = "hour")
    private Integer hour;

    /**
    * 分钟
    */
    @TableField(value = "minute")
    private Integer minute;


}
