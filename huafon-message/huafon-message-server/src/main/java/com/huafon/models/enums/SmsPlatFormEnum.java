package com.huafon.models.enums;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2023-08-22 10:12:52
 */
public enum SmsPlatFormEnum {

//    HUAFON_SMS	华峰自有短信
//    UNI_SMS	合一短信
//    CLOOPEN	容联云短信
//    CTYUN	天翼云短信
//    NETEASE	网易云短信
//    EMAY	亿美软通短信
//    YUNPIAN	云片短信
//    HUAWEI	华为云短信
//    TENCENT	腾讯云短信
//    JD_CLOUD	京东云短信
//    ALIBABA	阿里云短信

    HUAFON_SMS("HUAFON_SMS", "华峰自有短信"),
    UNI_SMS("UNI_SMS", "合一短信"),
    CLOOPEN("CLOOPEN", "容联云短信"),
    CTYUN("CTYUN", "天翼云短信"),
    NETEASE("NETEASE", "网易云短信"),
    EMAY("EMAY", "亿美软通短信"),
    YUNPIAN("YUNPIAN", "云片短信"),
    HUAWEI("HUAWEI", "华为云短信"),
    TENCENT("TENCENT", "腾讯云短信"),
    JD_CLOUD("JD_CLOUD", "京东云短信"),
    ALIBABA("ALIBABA", "阿里云短信");


    SmsPlatFormEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;
    private String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}