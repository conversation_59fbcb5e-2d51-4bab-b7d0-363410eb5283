package com.huafon.models.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-08-14 16:23
 **/
@Data
public class DingTalkBlackboardReqVO implements Serializable {

	@Data
	@Builder
	@AllArgsConstructor
	@NoArgsConstructor
	public static class CreateRequestVO implements Serializable {

		private CreateRequestBody create_request;
	}

	@Data
	@Builder
	@AllArgsConstructor
	@NoArgsConstructor
	public static class UpdateRequestVO implements Serializable {

		private UpdateRequestBody update_request;
	}

	@Data
	@Builder
	@AllArgsConstructor
	@NoArgsConstructor
	public static class DeleteRequestVO implements Serializable {

		private String blackboard_id;
		private String operation_userid;
	}

	@Data
	@Builder
	@AllArgsConstructor
	@NoArgsConstructor
	public static class QueryIdRequestVO implements Serializable {
		private QueryIdRequestBody query_request;
	}


	@Data
	@Builder
	@AllArgsConstructor
	@NoArgsConstructor
	public static class CreateRequestBody implements Serializable {

		/**
		 * 操作人的userId，必须是公告管理员 (必须)
		 */
		private String operation_userid;

		private String author;

		private String title;

		private Long private_level = 0L;

		private BlackboardReceiver blackboard_receiver;

		private boolean push_top;

		private String content;

		private String category_id;//公告分了ID

		private String coverpic_mediaid;//封面图
	}

	@Data
	@Builder
	@AllArgsConstructor
	@NoArgsConstructor
	public static class BlackboardReceiver implements Serializable{
		private List<Long> deptid_list;
		private List<String> userid_list;
	}

	@Data
	@Builder
	@AllArgsConstructor
	@NoArgsConstructor
	public static class UpdateRequestBody implements Serializable {

		/**
		 * 操作人的userId，必须是公告管理员 (必须)
		 */
		private String operation_userid;

		/**
		 * 公告ID(必须)
		 */
		private String blackboard_id;

		private String author;

		private String title;

		/**
		 * 修改后是否再次通知接收人:true通知
		 */
		private boolean notify = false;

		private String content;

		private String category_id;//公告分了ID

		private String coverpic_mediaid;//封面图
	}

	@Data
	@Builder
	@AllArgsConstructor
	@NoArgsConstructor
	public static class QueryIdRequestBody implements Serializable {

		/**
		 * 操作人的userId，必须是公告管理员 (必须)
		 */
		private String operation_userid;

		private Integer page_size = 10;

		private Integer page = 1;

		private String category_id;

		@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//		@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
		private Date start_time;
		@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//		@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
		private Date end_time;
	}
}


