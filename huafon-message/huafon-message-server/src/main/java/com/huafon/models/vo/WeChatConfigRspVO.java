package com.huafon.models.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Description 企业微信配置表返回对象
 * <AUTHOR>
 * @Date 2023/8/23 15:22
 * @Version 1.0
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
@ApiModel(value = "WeChatConfigRspVO", description = "企业微信返回对象")
public class WeChatConfigRspVO {

    @ApiModelProperty(value = " 企业微信应用AgentID")
    private Long agentId;
    @ApiModelProperty(value = "企业微信应用secret")
    private String secret;

    @ApiModelProperty(value = "企业微信企业id")
    private String cropId;
}