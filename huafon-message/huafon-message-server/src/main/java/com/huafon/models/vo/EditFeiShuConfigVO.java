package com.huafon.models.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2023-08-14 16:56:11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "EditFeiShuConfigVO", description = "编辑飞书对象")
public class EditFeiShuConfigVO {

    @ApiModelProperty(value = "飞书应用的appId")
    private String appId;

    @ApiModelProperty(value = "飞书应用的appSecret")
    private String appSecret;
}