package com.huafon.thrd.config;

import com.huafon.thrd.FeiShuApiClient;
import feign.*;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import feign.okhttp.OkHttpClient;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2023-04-25 16:01:29
 */
@Configuration
@RefreshScope
public class FeiShuFeignConfig {

    private String FEISHU_URL = "https://open.feishu.cn/open-apis/";

    @Bean(name = "FeiShuApiClient")
    public FeiShuApiClient feishuApiClient() {
        return Feign.builder()
                .options(new Request.Options(1000, 3500))
                .retryer(new Retryer.Default(5000, 5000, 3))
                .logLevel(Logger.Level.BASIC)
                .encoder(new JacksonEncoder())
                .decoder(new JacksonDecoder())
                .client(new OkHttpClient())
                .target(FeiShuApiClient.class, FEISHU_URL);
    }

}