package com.huafon.thrd;

import com.huafon.models.vo.DingTalkBlackboardReqVO;
import com.huafon.models.vo.DingTalkSendMessageReqVO;
import com.huafon.thrd.dto.DingTalkCommonRsp;
import com.huafon.thrd.dto.DingTalkMobileQuery;
import com.huafon.thrd.dto.DingTalkTenantAccessTokenReq;
import com.huafon.thrd.dto.DingTalkTenantAccessTokenRsp;
import feign.Headers;
import feign.Param;
import feign.QueryMap;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description 钉钉api对接
 * <AUTHOR>
 * @Date 2023/8/23 15:05
 * @Version 1.0
 */

public interface DingTalkApiClient {
    /**
     * 获取钉钉token
     * @param dingTalkTenantAccessTokenReq 应用配置信息
     * @return DingTalkTenantAccessTokenRsp
     */
    @Headers({"Content-Type: application/json; charset=utf-8"})
    @RequestLine("GET /gettoken")
    DingTalkTenantAccessTokenRsp gettoken(@QueryMap DingTalkTenantAccessTokenReq dingTalkTenantAccessTokenReq);

    /**
     * 获取钉钉用户id
     * @param dingTalkMobileQuery 手机号
     * @param tenantAccessToken token
     * @return DingTalkCommonRsp
     */
    @Headers({"Content-Type: application/json"})
    @RequestLine("POST /topapi/v2/user/getbymobile?access_token={tenant_access_token}")
    DingTalkCommonRsp getDingTalkUserId(@RequestBody DingTalkMobileQuery dingTalkMobileQuery, @Param("tenant_access_token") String tenantAccessToken);

    /**
     * 发送钉钉消息
     * @param dingTalkSendMessageReqVO 消息体
     * @param tenantAccessToken token
     * @return DingTalkCommonRsp
     */
    @Headers({"Content-Type: application/json"})
    @RequestLine("POST /topapi/message/corpconversation/asyncsend_v2?access_token={tenant_access_token}")
    DingTalkCommonRsp send(@RequestBody DingTalkSendMessageReqVO  dingTalkSendMessageReqVO, @Param("tenant_access_token") String tenantAccessToken);


    /**
     * 创建公告
     * <a href="https://open.dingtalk.com/document/orgapp/create-an-enterprise-announcement"/>
     * @param body 消息体
     * @param tenantAccessToken token
     * @return DingTalkCommonRsp
     */
    @Headers({"Content-Type: application/json"})
    @RequestLine("POST /topapi/blackboard/create?access_token={tenant_access_token}")
    DingTalkCommonRsp createBlackboard(@RequestBody DingTalkBlackboardReqVO.CreateRequestVO body, @Param("tenant_access_token") String tenantAccessToken);


    /**
     * 更新公告
     * <a href="https://open.dingtalk.com/document/orgapp/modify-the-announcement-according-to-the-announcement-id"/>
     * @param body 消息体
     * @param tenantAccessToken token
     * @return DingTalkCommonRsp
     */
    @Headers({"Content-Type: application/json"})
    @RequestLine("POST /topapi/blackboard/update?access_token={tenant_access_token}")
    DingTalkCommonRsp updateBlackboard(@RequestBody DingTalkBlackboardReqVO.UpdateRequestVO body, @Param("tenant_access_token") String tenantAccessToken);

    /**
     * 删除公告
     * <a href="https://open.dingtalk.com/document/orgapp/delete-announcements-based-on-the-announcement-id"/>
     * @param body 消息体
     * @param tenantAccessToken token
     * @return DingTalkCommonRsp
     */
    @Headers({"Content-Type: application/json"})
    @RequestLine("POST /topapi/blackboard/delete?access_token={tenant_access_token}")
    DingTalkCommonRsp deleteBlackboard(@RequestBody DingTalkBlackboardReqVO.DeleteRequestVO body, @Param("tenant_access_token") String tenantAccessToken);

    /**
     * 获取公告列表
     * <a href="https://open.dingtalk.com/document/orgapp/obtains-the-id-list-of-announcements-that-are-not-deleted"/>
     * @param body 消息体
     * @param tenantAccessToken token
     * @return DingTalkCommonRsp
     */
    @Headers({"Content-Type: application/json"})
    @RequestLine("POST /topapi/blackboard/listids?access_token={tenant_access_token}")
    DingTalkCommonRsp queryBlackboardIds(@RequestBody DingTalkBlackboardReqVO.QueryIdRequestVO body, @Param("tenant_access_token") String tenantAccessToken);


}
