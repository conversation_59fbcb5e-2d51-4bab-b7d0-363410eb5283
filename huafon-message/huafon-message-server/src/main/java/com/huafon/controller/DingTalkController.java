package com.huafon.controller;

import com.huafon.models.enums.ChannelEnum;
import com.huafon.models.vo.DingTalkConfigRspVO;
import com.huafon.models.vo.EditDingTalkConfigVO;
import com.huafon.service.MessageDingTalkConfigService;
import com.huafon.service.channel.ChannelPushService;
import com.huafon.service.channel.ChannelPushServiceStrategyFactory;
import com.huafon.support.core.pojo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;

/**
 * @Description 钉钉消息配置管理接口
 * <AUTHOR>
 * @Date 2023/8/23 15:47
 * @Version 1.0
 */
@Slf4j
@Api(value = "钉钉消息配置", tags = {"钉钉消息配置管理接口"})
@RestController
@RequestMapping("/dingTalk")
public class DingTalkController {
    @Resource
    private MessageDingTalkConfigService messageDingTalkConfigService;

    @GetMapping("/config")
    @ApiOperation(value = "返回钉钉配置", notes = "返回钉钉配置")
    public R<DingTalkConfigRspVO> getDingTalkConfig() {

        return R.ok(messageDingTalkConfigService.getDingTalkConfig());
    }

    @PostMapping("/editConfig")
    @ApiOperation(value = "配置钉钉的参数", notes = "配置钉钉的参数")
    public R editDingTalkConfig(@RequestBody EditDingTalkConfigVO config) {
        messageDingTalkConfigService.editDingTalkConfig(config);
        return R.ok();
    }

    @PostMapping("/test/send/{mobile}")
    @ApiOperation(value = "钉钉发送测试", notes = "钉钉发送测试(同一个人 同一天收不到同一消息)")
    public R test(@PathVariable String mobile) {
        ChannelPushService channelPushService = ChannelPushServiceStrategyFactory.getByChannel(ChannelEnum.DINGTALK.getCode());
        channelPushService.sendMessage(mobile, "钉钉发送测试 "+System.currentTimeMillis());
        return R.ok();
    }

}
