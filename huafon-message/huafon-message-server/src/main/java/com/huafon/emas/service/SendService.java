package com.huafon.emas.service;

import com.huafon.emas.model.EmasPushMsgDTO;
import com.huafon.emas.model.entity.EmasUserDevice;

import java.util.List;

/**
 * @Author:
 * @Date: 2023-08-16 18:37:22
 * @Desc:
 */
public interface SendService {

    void emasPush(EmasPushMsgDTO emasPushMsgDTO, List<EmasUserDevice> deviceList);

    void huaweiPush(EmasPushMsgDTO emasPushMsgDTO, List<EmasUserDevice> huaweiMsgList);

    void xiaomiPush(EmasPushMsgDTO emasPushMsgDTO, List<EmasUserDevice> xiaomiMsgList);


    void oppoPush(EmasPushMsgDTO emasPushMsgDTO, List<EmasUserDevice> oppoMsgList);


    void vivoPush(EmasPushMsgDTO emasPushMsgDTO, List<EmasUserDevice> vivoMsgList);

    void honorPush(EmasPushMsgDTO emasPushMsgDTO, List<EmasUserDevice> honorMsgList);

}
