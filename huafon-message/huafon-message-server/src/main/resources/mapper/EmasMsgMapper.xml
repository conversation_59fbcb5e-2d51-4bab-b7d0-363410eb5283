<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huafon.emas.mapper.EmasMsgMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huafon.emas.model.entity.EmasMsg">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="is_del" property="isDel"/>
        <result column="msg_id" property="msgId"/>
        <result column="request_id" property="requestId"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
        <result column="app_key" property="appKey"/>
        <result column="error_info" property="errorInfo"/>


    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, modify_time, is_del, msg_id, request_id, title,content,app_key,error_info
    </sql>

</mapper>
