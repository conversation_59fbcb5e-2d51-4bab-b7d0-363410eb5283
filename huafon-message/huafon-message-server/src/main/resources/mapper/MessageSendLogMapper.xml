<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huafon.mapper.MessageSendLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huafon.models.entity.MessageSendLog">
        <id column="id" property="id"/>
        <result column="channel" property="channel"/>
        <result column="channel_name" property="channelName"/>
        <result column="account" property="account"/>
        <result column="content" property="content"/>
        <result column="is_del" property="isDel"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , channel, channel_name, account, content, is_del, create_time, modify_time
    </sql>
    <select id="pageQuery" resultType="com.huafon.models.entity.MessageSendLog">
        select
        <include refid="Base_Column_List"/>
        from hf_message_send_log a
        where is_del=0
        <if test="req.content != null and req.content != ''">
            and (
            a.content like concat('%',#{req.content},'%')
            or
            a.account like concat('%',#{req.content},'%')
            )
        </if>
        <if test="req.startTime != null and req.endTime != null">
            and a.create_time between #{req.startTime} and #{req.endTime}
        </if>
        <if test="req.channel != null">
            and a.channel = #{req.channel}
        </if>
        order by create_time desc
    </select>

</mapper>
