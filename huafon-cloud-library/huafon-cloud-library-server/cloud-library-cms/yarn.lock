# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ant-design/colors@3.2.2":
  "integrity" "sha512-YKgNbG2dlzqMhA9NtI3/pbY16m3Yl/EeWBRa+lB1X1YaYxHrxNexiQYCLTWO/uDvAjLFMEDU+zR901waBtMtjQ=="
  "resolved" "https://registry.npmjs.org/@ant-design/colors/-/colors-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "tinycolor2" "1.4.1"

"@ant-design/create-react-context@0.2.5":
  "integrity" "sha512-1rMAa4qgP2lfl/QBH9i78+Gjxtj9FTMpMyDGZsEBW5Kih72EuUo9958mV8PgpRkh4uwPSQ7vVZWXeyNZXVAFDg=="
  "resolved" "https://registry.npmjs.org/@ant-design/create-react-context/-/create-react-context-0.2.5.tgz"
  "version" "0.2.5"
  dependencies:
    "gud" "1.0.0"
    "warning" "4.0.3"

"@ant-design/icons-react@2.0.1":
  "integrity" "sha512-r1QfoltMuruJZqdiKcbPim3d8LNsVPB733U0gZEUSxBLuqilwsW28K2rCTWSMTjmFX7Mfpf+v/wdiFe/XCqThw=="
  "resolved" "https://registry.npmjs.org/@ant-design/icons-react/-/icons-react-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "@ant-design/colors" "3.2.2"
    "babel-runtime" "6.26.0"

"@ant-design/icons-svg@4.0.0":
  "integrity" "sha512-Nai+cd3XUrv/z50gSk1FI08j6rENZ1e93rhKeLTBGwa5WrmHvhn2vowa5+voZW2qkXJn1btS6tdvTEDB90M0Pw=="
  "resolved" "https://registry.npmjs.org/@ant-design/icons-svg/-/icons-svg-4.0.0.tgz"
  "version" "4.0.0"

"@ant-design/icons@2.1.1":
  "integrity" "sha512-jCH+k2Vjlno4YWl6g535nHR09PwCEmTBKAG6VqF+rhkrSPRLfgpU2maagwbZPLjaHuU5Jd1DFQ2KJpQuI6uG8w=="
  "resolved" "https://registry.npmjs.org/@ant-design/icons/-/icons-2.1.1.tgz"
  "version" "2.1.1"

"@ant-design/icons@latest":
  "integrity" "sha512-vWzmt1QsWpnmOfT/wtAIeKTheN61Mo8KtaLm0yosd6vVUEVdc5E/pmcrd8lIp2CmuRT7qCU6e9x/RMffv0hOJg=="
  "resolved" "https://registry.npmjs.org/@ant-design/icons/-/icons-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "@ant-design/colors" "3.2.2"
    "@ant-design/icons-svg" "4.0.0"
    "classnames" "2.2.6"
    "insert-css" "2.0.0"
    "rc-util" "4.20.0"

"@babel/code-frame@7.0.0":
  "integrity" "sha512-OfC2uemaknXr87bdLUkWog7nYuliM9Ij5HUcajsVcMCpQrcLmtxRbVFTIqmcSkSeYRBFBRxs2FiUqFJDLdiebA=="
  "resolved" "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "@babel/highlight" "7.8.3"

"@babel/code-frame@7.8.3":
  "integrity" "sha512-a9gxpmdXtZEInkCSHUJDLHZVBgb1QS0jhss4cPP93EW7s+uC5bikET2twEF3KV+7rDblJcmNvTR7VJejqd2C2g=="
  "resolved" "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/highlight" "7.8.3"

"@babel/compat-data@7.8.6":
  "integrity" "sha512-CurCIKPTkS25Mb8mz267vU95vy+TyUpnctEX2lV33xWNmHAfjruztgiPBbXZRh3xZZy1CYvGx6XfxyTVS+sk7Q=="
  "resolved" "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.8.6.tgz"
  "version" "7.8.6"
  dependencies:
    "browserslist" "4.9.1"
    "invariant" "2.2.4"
    "semver" "5.7.1"

"@babel/core@7.2.2":
  "integrity" "sha512-59vB0RWt09cAct5EIe58+NzGP4TFSD3Bz//2/ELy3ZeTeKF6VTD1AXlH8BGGbCX0PuobZBsIzO7IAI9PH67eKw=="
  "resolved" "https://registry.npmjs.org/@babel/core/-/core-7.2.2.tgz"
  "version" "7.2.2"
  dependencies:
    "@babel/code-frame" "7.8.3"
    "@babel/generator" "7.8.7"
    "@babel/helpers" "7.8.4"
    "@babel/parser" "7.8.7"
    "@babel/template" "7.8.6"
    "@babel/traverse" "7.8.6"
    "@babel/types" "7.8.7"
    "convert-source-map" "1.7.0"
    "debug" "4.1.1"
    "json5" "2.1.1"
    "lodash" "4.17.15"
    "resolve" "1.10.0"
    "semver" "5.7.1"
    "source-map" "0.5.7"

"@babel/core@7.8.7":
  "integrity" "sha512-rBlqF3Yko9cynC5CCFy6+K/w2N+Sq/ff2BPy+Krp7rHlABIr5epbA7OxVeKoMHB39LZOp1UY5SuLjy6uWi35yA=="
  "resolved" "https://registry.npmjs.org/@babel/core/-/core-7.8.7.tgz"
  "version" "7.8.7"
  dependencies:
    "@babel/code-frame" "7.8.3"
    "@babel/generator" "7.8.7"
    "@babel/helpers" "7.8.4"
    "@babel/parser" "7.8.7"
    "@babel/template" "7.8.6"
    "@babel/traverse" "7.8.6"
    "@babel/types" "7.8.7"
    "convert-source-map" "1.7.0"
    "debug" "4.1.1"
    "gensync" "1.0.0-beta.1"
    "json5" "2.1.1"
    "lodash" "4.17.15"
    "resolve" "1.10.0"
    "semver" "5.7.1"
    "source-map" "0.5.7"

"@babel/generator@7.8.7":
  "integrity" "sha512-DQwjiKJqH4C3qGiyQCAExJHoZssn49JTMJgZ8SANGgVFdkupcUhLOdkAeoC6kmHZCPfoDG5M0b6cFlSN5wW7Ew=="
  "resolved" "https://registry.npmjs.org/@babel/generator/-/generator-7.8.7.tgz"
  "version" "7.8.7"
  dependencies:
    "@babel/types" "7.8.7"
    "jsesc" "2.5.2"
    "lodash" "4.17.15"
    "source-map" "0.5.7"

"@babel/helper-annotate-as-pure@7.8.3":
  "integrity" "sha512-6o+mJrZBxOoEX77Ezv9zwW7WV8DdluouRKNY/IR5u/YTMuKHgugHOzYWlYvYLpLA9nPsQCAAASpCIbjI9Mv+Uw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/types" "7.8.7"

"@babel/helper-builder-binary-assignment-operator-visitor@7.8.3":
  "integrity" "sha512-5eFOm2SyFPK4Rh3XMMRDjN7lBH0orh3ss0g3rTYZnBQ+r6YPj7lgDyCvPphynHvUrobJmeMignBr6Acw9mAPlw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-explode-assignable-expression" "7.8.3"
    "@babel/types" "7.8.7"

"@babel/helper-builder-react-jsx@7.8.3":
  "integrity" "sha512-JT8mfnpTkKNCboTqZsQTdGo3l3Ik3l7QIt9hh0O9DYiwVel37VoJpILKM4YFbP2euF32nkQSb+F9cUk9b7DDXQ=="
  "resolved" "https://registry.npmjs.org/@babel/helper-builder-react-jsx/-/helper-builder-react-jsx-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/types" "7.8.7"
    "esutils" "2.0.3"

"@babel/helper-call-delegate@7.8.7":
  "integrity" "sha512-doAA5LAKhsFCR0LAFIf+r2RSMmC+m8f/oQ+URnUET/rWeEzC0yTRmAGyWkD4sSu3xwbS7MYQ2u+xlt1V5R56KQ=="
  "resolved" "https://registry.npmjs.org/@babel/helper-call-delegate/-/helper-call-delegate-7.8.7.tgz"
  "version" "7.8.7"
  dependencies:
    "@babel/helper-hoist-variables" "7.8.3"
    "@babel/traverse" "7.8.6"
    "@babel/types" "7.8.7"

"@babel/helper-compilation-targets@7.8.7":
  "integrity" "sha512-4mWm8DCK2LugIS+p1yArqvG1Pf162upsIsjE7cNBjez+NjliQpVhj20obE520nao0o14DaTnFJv+Fw5a0JpoUw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.8.7.tgz"
  "version" "7.8.7"
  dependencies:
    "@babel/compat-data" "7.8.6"
    "browserslist" "4.9.1"
    "invariant" "2.2.4"
    "levenary" "1.1.1"
    "semver" "5.7.1"

"@babel/helper-create-class-features-plugin@7.8.6":
  "integrity" "sha512-klTBDdsr+VFFqaDHm5rR69OpEQtO2Qv8ECxHS1mNhJJvaHArR6a1xTf5K/eZW7eZpJbhCx3NW1Yt/sKsLXLblg=="
  "resolved" "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.8.6.tgz"
  "version" "7.8.6"
  dependencies:
    "@babel/helper-function-name" "7.8.3"
    "@babel/helper-member-expression-to-functions" "7.8.3"
    "@babel/helper-optimise-call-expression" "7.8.3"
    "@babel/helper-plugin-utils" "7.8.3"
    "@babel/helper-replace-supers" "7.8.6"
    "@babel/helper-split-export-declaration" "7.8.3"

"@babel/helper-create-regexp-features-plugin@7.8.6":
  "integrity" "sha512-bPyujWfsHhV/ztUkwGHz/RPV1T1TDEsSZDsN42JPehndA+p1KKTh3npvTadux0ZhCrytx9tvjpWNowKby3tM6A=="
  "resolved" "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.8.6.tgz"
  "version" "7.8.6"
  dependencies:
    "@babel/helper-annotate-as-pure" "7.8.3"
    "@babel/helper-regex" "7.8.3"
    "regexpu-core" "4.6.0"

"@babel/helper-define-map@7.8.3":
  "integrity" "sha512-PoeBYtxoZGtct3md6xZOCWPcKuMuk3IHhgxsRRNtnNShebf4C8YonTSblsK4tvDbm+eJAw2HAPOfCr+Q/YRG/g=="
  "resolved" "https://registry.npmjs.org/@babel/helper-define-map/-/helper-define-map-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-function-name" "7.8.3"
    "@babel/types" "7.8.7"
    "lodash" "4.17.15"

"@babel/helper-explode-assignable-expression@7.8.3":
  "integrity" "sha512-N+8eW86/Kj147bO9G2uclsg5pwfs/fqqY5rwgIL7eTBklgXjcOJ3btzS5iM6AitJcftnY7pm2lGsrJVYLGjzIw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-explode-assignable-expression/-/helper-explode-assignable-expression-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/traverse" "7.8.6"
    "@babel/types" "7.8.7"

"@babel/helper-function-name@7.8.3":
  "integrity" "sha512-BCxgX1BC2hD/oBlIFUgOCQDOPV8nSINxCwM3o93xP4P9Fq6aV5sgv2cOOITDMtCfQ+3PvHp3l689XZvAM9QyOA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-get-function-arity" "7.8.3"
    "@babel/template" "7.8.6"
    "@babel/types" "7.8.7"

"@babel/helper-get-function-arity@7.8.3":
  "integrity" "sha512-FVDR+Gd9iLjUMY1fzE2SR0IuaJToR4RkCDARVfsBBPSP53GEqSFjD8gNyxg246VUyc/ALRxFaAK8rVG7UT7xRA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-get-function-arity/-/helper-get-function-arity-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/types" "7.8.7"

"@babel/helper-hoist-variables@7.8.3":
  "integrity" "sha512-ky1JLOjcDUtSc+xkt0xhYff7Z6ILTAHKmZLHPxAhOP0Nd77O+3nCsd6uSVYur6nJnCI029CrNbYlc0LoPfAPQg=="
  "resolved" "https://registry.npmjs.org/@babel/helper-hoist-variables/-/helper-hoist-variables-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/types" "7.8.7"

"@babel/helper-member-expression-to-functions@7.8.3":
  "integrity" "sha512-fO4Egq88utkQFjbPrSHGmGLFqmrshs11d46WI+WZDESt7Wu7wN2G2Iu+NMMZJFDOVRHAMIkB5SNh30NtwCA7RA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/types" "7.8.7"

"@babel/helper-module-imports@7.8.3":
  "integrity" "sha512-R0Bx3jippsbAEtzkpZ/6FIiuzOURPcMjHp+Z6xPe6DtApDJx+w7UYyOLanZqO8+wKR9G10s/FmHXvxaMd9s6Kg=="
  "resolved" "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/types" "7.8.7"

"@babel/helper-module-transforms@7.8.6":
  "integrity" "sha512-RDnGJSR5EFBJjG3deY0NiL0K9TO8SXxS9n/MPsbPK/s9LbQymuLNtlzvDiNS7IpecuL45cMeLVkA+HfmlrnkRg=="
  "resolved" "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.8.6.tgz"
  "version" "7.8.6"
  dependencies:
    "@babel/helper-module-imports" "7.8.3"
    "@babel/helper-replace-supers" "7.8.6"
    "@babel/helper-simple-access" "7.8.3"
    "@babel/helper-split-export-declaration" "7.8.3"
    "@babel/template" "7.8.6"
    "@babel/types" "7.8.7"
    "lodash" "4.17.15"

"@babel/helper-optimise-call-expression@7.8.3":
  "integrity" "sha512-Kag20n86cbO2AvHca6EJsvqAd82gc6VMGule4HwebwMlwkpXuVqrNRj6CkCV2sKxgi9MyAUnZVnZ6lJ1/vKhHQ=="
  "resolved" "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/types" "7.8.7"

"@babel/helper-plugin-utils@7.8.3":
  "integrity" "sha512-j+fq49Xds2smCUNYmEHF9kGNkhbet6yVIBp4e6oeQpH1RUs/Ir06xUKzDjDkGcaaokPiTNs2JBWHjaE4csUkZQ=="
  "resolved" "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.8.3.tgz"
  "version" "7.8.3"

"@babel/helper-regex@7.8.3":
  "integrity" "sha512-BWt0QtYv/cg/NecOAZMdcn/waj/5P26DR4mVLXfFtDokSR6fyuG0Pj+e2FqtSME+MqED1khnSMulkmGl8qWiUQ=="
  "resolved" "https://registry.npmjs.org/@babel/helper-regex/-/helper-regex-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "lodash" "4.17.15"

"@babel/helper-remap-async-to-generator@7.8.3":
  "integrity" "sha512-kgwDmw4fCg7AVgS4DukQR/roGp+jP+XluJE5hsRZwxCYGg+Rv9wSGErDWhlI90FODdYfd4xG4AQRiMDjjN0GzA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-annotate-as-pure" "7.8.3"
    "@babel/helper-wrap-function" "7.8.3"
    "@babel/template" "7.8.6"
    "@babel/traverse" "7.8.6"
    "@babel/types" "7.8.7"

"@babel/helper-replace-supers@7.8.6":
  "integrity" "sha512-PeMArdA4Sv/Wf4zXwBKPqVj7n9UF/xg6slNRtZW84FM7JpE1CbG8B612FyM4cxrf4fMAMGO0kR7voy1ForHHFA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.8.6.tgz"
  "version" "7.8.6"
  dependencies:
    "@babel/helper-member-expression-to-functions" "7.8.3"
    "@babel/helper-optimise-call-expression" "7.8.3"
    "@babel/traverse" "7.8.6"
    "@babel/types" "7.8.7"

"@babel/helper-simple-access@7.8.3":
  "integrity" "sha512-VNGUDjx5cCWg4vvCTR8qQ7YJYZ+HBjxOgXEl7ounz+4Sn7+LMD3CFrCTEU6/qXKbA2nKg21CwhhBzO0RpRbdCw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-simple-access/-/helper-simple-access-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/template" "7.8.6"
    "@babel/types" "7.8.7"

"@babel/helper-split-export-declaration@7.8.3":
  "integrity" "sha512-3x3yOeyBhW851hroze7ElzdkeRXQYQbFIb7gLK1WQYsw2GWDay5gAJNw1sWJ0VFP6z5J1whqeXH/WCdCjZv6dA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/types" "7.8.7"

"@babel/helper-wrap-function@7.8.3":
  "integrity" "sha512-LACJrbUET9cQDzb6kG7EeD7+7doC3JNvUgTEQOx2qaO1fKlzE/Bf05qs9w1oXQMmXlPO65lC3Tq9S6gZpTErEQ=="
  "resolved" "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-function-name" "7.8.3"
    "@babel/template" "7.8.6"
    "@babel/traverse" "7.8.6"
    "@babel/types" "7.8.7"

"@babel/helpers@7.8.4":
  "integrity" "sha512-VPbe7wcQ4chu4TDQjimHv/5tj73qz88o12EPkO2ValS2QiQS/1F2SsjyIGNnAD0vF/nZS6Cf9i+vW6HIlnaR8w=="
  "resolved" "https://registry.npmjs.org/@babel/helpers/-/helpers-7.8.4.tgz"
  "version" "7.8.4"
  dependencies:
    "@babel/template" "7.8.6"
    "@babel/traverse" "7.8.6"
    "@babel/types" "7.8.7"

"@babel/highlight@7.8.3":
  "integrity" "sha512-PX4y5xQUvy0fnEVHrYOarRPXVWafSjTW9T0Hab8gVIawpl2Sj0ORyrygANq+KjcNlSSTw0YCLSNA8OyZ1I4yEg=="
  "resolved" "https://registry.npmjs.org/@babel/highlight/-/highlight-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "chalk" "2.4.2"
    "esutils" "2.0.3"
    "js-tokens" "4.0.0"

"@babel/parser@7.8.7":
  "integrity" "sha512-9JWls8WilDXFGxs0phaXAZgpxTZhSk/yOYH2hTHC0X1yC7Z78IJfvR1vJ+rmJKq3I35td2XzXzN6ZLYlna+r/A=="
  "resolved" "https://registry.npmjs.org/@babel/parser/-/parser-7.8.7.tgz"
  "version" "7.8.7"

"@babel/plugin-proposal-async-generator-functions@7.8.3":
  "integrity" "sha512-NZ9zLv848JsV3hs8ryEh7Uaz/0KsmPLqv0+PdkDJL1cJy0K4kOCFa8zc1E3mp+RHPQcpdfb/6GovEsW4VDrOMw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"
    "@babel/helper-remap-async-to-generator" "7.8.3"
    "@babel/plugin-syntax-async-generators" "7.8.4"

"@babel/plugin-proposal-class-properties@7.3.0":
  "integrity" "sha512-wNHxLkEKTQ2ay0tnsam2z7fGZUi+05ziDJflEt3AZTP3oXLKHJp9HqhfroB/vdMvt3sda9fAbq7FsG8QPDrZBg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.3.0.tgz"
  "version" "7.3.0"
  dependencies:
    "@babel/helper-create-class-features-plugin" "7.8.6"
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-proposal-decorators@7.3.0":
  "integrity" "sha512-3W/oCUmsO43FmZIqermmq6TKaRSYhmh/vybPfVFwQWdSb8xwki38uAIvknCRzuyHRuYfCYmJzL9or1v0AffPjg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.3.0.tgz"
  "version" "7.3.0"
  dependencies:
    "@babel/helper-create-class-features-plugin" "7.8.6"
    "@babel/helper-plugin-utils" "7.8.3"
    "@babel/plugin-syntax-decorators" "7.8.3"

"@babel/plugin-proposal-dynamic-import@7.8.3":
  "integrity" "sha512-NyaBbyLFXFLT9FP+zk0kYlUlA8XtCUbehs67F0nnEg7KICgMc2mNkIeu9TYhKzyXMkrapZFwAhXLdnt4IYHy1w=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-dynamic-import/-/plugin-proposal-dynamic-import-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"
    "@babel/plugin-syntax-dynamic-import" "7.8.3"

"@babel/plugin-proposal-json-strings@7.8.3":
  "integrity" "sha512-KGhQNZ3TVCQG/MjRbAUwuH+14y9q0tpxs1nWWs3pbSleRdDro9SAMMDyye8HhY1gqZ7/NqIc8SKhya0wRDgP1Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-json-strings/-/plugin-proposal-json-strings-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"
    "@babel/plugin-syntax-json-strings" "7.8.3"

"@babel/plugin-proposal-nullish-coalescing-operator@7.8.3":
  "integrity" "sha512-TS9MlfzXpXKt6YYomudb/KU7nQI6/xnapG6in1uZxoxDghuSMZsPb6D2fyUwNYSAp4l1iR7QtFOjkqcRYcUsfw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"
    "@babel/plugin-syntax-nullish-coalescing-operator" "7.8.3"

"@babel/plugin-proposal-object-rest-spread@7.3.2":
  "integrity" "sha512-DjeMS+J2+lpANkYLLO+m6GjoTMygYglKmRe6cDTbFv3L9i6mmiE8fe6B8MtCSLZpVXscD5kn7s6SgtHrDoBWoA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.3.2.tgz"
  "version" "7.3.2"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"
    "@babel/plugin-syntax-object-rest-spread" "7.8.3"

"@babel/plugin-proposal-object-rest-spread@7.8.3":
  "integrity" "sha512-8qvuPwU/xxUCt78HocNlv0mXXo0wdh9VT1R04WU8HGOfaOob26pF+9P5/lYjN/q7DHOX1bvX60hnhOvuQUJdbA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"
    "@babel/plugin-syntax-object-rest-spread" "7.8.3"

"@babel/plugin-proposal-optional-catch-binding@7.8.3":
  "integrity" "sha512-0gkX7J7E+AtAw9fcwlVQj8peP61qhdg/89D5swOkjYbkboA2CVckn3kiyum1DE0wskGb7KJJxBdyEBApDLLVdw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "7.8.3"

"@babel/plugin-proposal-optional-chaining@7.8.3":
  "integrity" "sha512-QIoIR9abkVn+seDE3OjA08jWcs3eZ9+wJCKSRgo3WdEU2csFYgdScb+8qHB3+WXsGJD55u+5hWCISI7ejXS+kg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"
    "@babel/plugin-syntax-optional-chaining" "7.8.3"

"@babel/plugin-proposal-unicode-property-regex@7.8.3":
  "integrity" "sha512-1/1/rEZv2XGweRwwSkLpY+s60za9OZ1hJs4YDqFHCw0kYWYwL5IFljVY1MYBL+weT1l9pokDO2uhSTLVxzoHkQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "7.8.6"
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-syntax-async-generators@7.8.4":
  "integrity" "sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz"
  "version" "7.8.4"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-syntax-decorators@7.8.3":
  "integrity" "sha512-8Hg4dNNT9/LcA1zQlfwuKR8BUc/if7Q7NkTam9sGTcJphLwpf2g4S42uhspQrIrR+dpzE0dtTqBVFoHl8GtnnQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-syntax-dynamic-import@7.2.0":
  "integrity" "sha512-mVxuJ0YroI/h/tbFTPGZR8cv6ai+STMKNBq0f8hFxsxWjl94qqhsb+wXbpNMDPU3cfR1TIsVFzU3nXyZMqyK4w=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-syntax-dynamic-import@7.8.3":
  "integrity" "sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-syntax-flow@7.8.3":
  "integrity" "sha512-innAx3bUbA0KSYj2E2MNFSn9hiCeowOFLxlsuhXzw8hMQnzkDomUr9QCD7E9VF60NmnG1sNTuuv6Qf4f8INYsg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-flow/-/plugin-syntax-flow-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-syntax-json-strings@7.8.3":
  "integrity" "sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-syntax-jsx@7.8.3":
  "integrity" "sha512-WxdW9xyLgBdefoo0Ynn3MRSkhe5tFVxxKNVdnZSh318WrG2e2jH+E9wd/++JsqcLJZPfz87njQJ8j2Upjm0M0A=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-syntax-nullish-coalescing-operator@7.8.3":
  "integrity" "sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-syntax-object-rest-spread@7.8.3":
  "integrity" "sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-syntax-optional-catch-binding@7.8.3":
  "integrity" "sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-syntax-optional-chaining@7.8.3":
  "integrity" "sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-syntax-top-level-await@7.8.3":
  "integrity" "sha512-kwj1j9lL/6Wd0hROD3b/OZZ7MSrZLqqn9RAZ5+cYYsflQ9HZBIKCUkr3+uL1MEJ1NePiUbf98jjiMQSv0NMR9g=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-syntax-typescript@7.8.3":
  "integrity" "sha512-GO1MQ/SGGGoiEXY0e0bSpHimJvxqB7lktLLIq2pv8xG7WZ8IMEle74jIe1FhprHBWjwjZtXHkycDLZXIWM5Wfg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-transform-arrow-functions@7.8.3":
  "integrity" "sha512-0MRF+KC8EqH4dbuITCWwPSzsyO3HIWWlm30v8BbbpOrS1B++isGxPnnuq/IZvOX5J2D/p7DQalQm+/2PnlKGxg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-transform-async-to-generator@7.8.3":
  "integrity" "sha512-imt9tFLD9ogt56Dd5CI/6XgpukMwd/fLGSrix2httihVe7LOGVPhyhMh1BU5kDM7iHD08i8uUtmV2sWaBFlHVQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-module-imports" "7.8.3"
    "@babel/helper-plugin-utils" "7.8.3"
    "@babel/helper-remap-async-to-generator" "7.8.3"

"@babel/plugin-transform-block-scoped-functions@7.8.3":
  "integrity" "sha512-vo4F2OewqjbB1+yaJ7k2EJFHlTP3jR634Z9Cj9itpqNjuLXvhlVxgnjsHsdRgASR8xYDrx6onw4vW5H6We0Jmg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-transform-block-scoping@7.8.3":
  "integrity" "sha512-pGnYfm7RNRgYRi7bids5bHluENHqJhrV4bCZRwc5GamaWIIs07N4rZECcmJL6ZClwjDz1GbdMZFtPs27hTB06w=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"
    "lodash" "4.17.15"

"@babel/plugin-transform-classes@7.2.2":
  "integrity" "sha512-gEZvgTy1VtcDOaQty1l10T3jQmJKlNVxLDCs+3rCVPr6nMkODLELxViq5X9l+rfxbie3XrfrMCYYY6eX3aOcOQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.2.2.tgz"
  "version" "7.2.2"
  dependencies:
    "@babel/helper-annotate-as-pure" "7.8.3"
    "@babel/helper-define-map" "7.8.3"
    "@babel/helper-function-name" "7.8.3"
    "@babel/helper-optimise-call-expression" "7.8.3"
    "@babel/helper-plugin-utils" "7.8.3"
    "@babel/helper-replace-supers" "7.8.6"
    "@babel/helper-split-export-declaration" "7.8.3"
    "globals" "11.12.0"

"@babel/plugin-transform-classes@7.8.6":
  "integrity" "sha512-k9r8qRay/R6v5aWZkrEclEhKO6mc1CCQr2dLsVHBmOQiMpN6I2bpjX3vgnldUWeEI1GHVNByULVxZ4BdP4Hmdg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.8.6.tgz"
  "version" "7.8.6"
  dependencies:
    "@babel/helper-annotate-as-pure" "7.8.3"
    "@babel/helper-define-map" "7.8.3"
    "@babel/helper-function-name" "7.8.3"
    "@babel/helper-optimise-call-expression" "7.8.3"
    "@babel/helper-plugin-utils" "7.8.3"
    "@babel/helper-replace-supers" "7.8.6"
    "@babel/helper-split-export-declaration" "7.8.3"
    "globals" "11.12.0"

"@babel/plugin-transform-computed-properties@7.8.3":
  "integrity" "sha512-O5hiIpSyOGdrQZRQ2ccwtTVkgUDBBiCuK//4RJ6UfePllUTCENOzKxfh6ulckXKc0DixTFLCfb2HVkNA7aDpzA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-transform-destructuring@7.3.2":
  "integrity" "sha512-Lrj/u53Ufqxl/sGxyjsJ2XNtNuEjDyjpqdhMNh5aZ+XFOdThL46KBj27Uem4ggoezSYBxKWAil6Hu8HtwqesYw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.3.2.tgz"
  "version" "7.3.2"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-transform-destructuring@7.8.3":
  "integrity" "sha512-H4X646nCkiEcHZUZaRkhE2XVsoz0J/1x3VVujnn96pSoGCtKPA99ZZA+va+gK+92Zycd6OBKCD8tDb/731bhgQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-transform-dotall-regex@7.8.3":
  "integrity" "sha512-kLs1j9Nn4MQoBYdRXH6AeaXMbEJFaFu/v1nQkvib6QzTj8MZI5OQzqmD83/2jEM1z0DLilra5aWO5YpyC0ALIw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "7.8.6"
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-transform-duplicate-keys@7.8.3":
  "integrity" "sha512-s8dHiBUbcbSgipS4SMFuWGqCvyge5V2ZeAWzR6INTVC3Ltjig/Vw1G2Gztv0vU/hRG9X8IvKvYdoksnUfgXOEQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-transform-exponentiation-operator@7.8.3":
  "integrity" "sha512-zwIpuIymb3ACcInbksHaNcR12S++0MDLKkiqXHl3AzpgdKlFNhog+z/K0+TGW+b0w5pgTq4H6IwV/WhxbGYSjQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "7.8.3"
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-transform-flow-strip-types@7.2.3":
  "integrity" "sha512-xnt7UIk9GYZRitqCnsVMjQK1O2eKZwFB3CvvHjf5SGx6K6vr/MScCKQDnf1DxRaj501e3pXjti+inbSXX2ZUoQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-flow-strip-types/-/plugin-transform-flow-strip-types-7.2.3.tgz"
  "version" "7.2.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"
    "@babel/plugin-syntax-flow" "7.8.3"

"@babel/plugin-transform-for-of@7.8.6":
  "integrity" "sha512-M0pw4/1/KI5WAxPsdcUL/w2LJ7o89YHN3yLkzNjg7Yl15GlVGgzHyCU+FMeAxevHGsLVmUqbirlUIKTafPmzdw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.8.6.tgz"
  "version" "7.8.6"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-transform-function-name@7.8.3":
  "integrity" "sha512-rO/OnDS78Eifbjn5Py9v8y0aR+aSYhDhqAwVfsTl0ERuMZyr05L1aFSCJnbv2mmsLkit/4ReeQ9N2BgLnOcPCQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-function-name" "7.8.3"
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-transform-literals@7.8.3":
  "integrity" "sha512-3Tqf8JJ/qB7TeldGl+TT55+uQei9JfYaregDcEAyBZ7akutriFrt6C/wLYIer6OYhleVQvH/ntEhjE/xMmy10A=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-transform-member-expression-literals@7.8.3":
  "integrity" "sha512-3Wk2EXhnw+rP+IDkK6BdtPKsUE5IeZ6QOGrPYvw52NwBStw9V1ZVzxgK6fSKSxqUvH9eQPR3tm3cOq79HlsKYA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-transform-modules-amd@7.8.3":
  "integrity" "sha512-MadJiU3rLKclzT5kBH4yxdry96odTUwuqrZM+GllFI/VhxfPz+k9MshJM+MwhfkCdxxclSbSBbUGciBngR+kEQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-module-transforms" "7.8.6"
    "@babel/helper-plugin-utils" "7.8.3"
    "babel-plugin-dynamic-import-node" "2.3.0"

"@babel/plugin-transform-modules-commonjs@7.8.3":
  "integrity" "sha512-JpdMEfA15HZ/1gNuB9XEDlZM1h/gF/YOH7zaZzQu2xCFRfwc01NXBMHHSTT6hRjlXJJs5x/bfODM3LiCk94Sxg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-module-transforms" "7.8.6"
    "@babel/helper-plugin-utils" "7.8.3"
    "@babel/helper-simple-access" "7.8.3"
    "babel-plugin-dynamic-import-node" "2.3.0"

"@babel/plugin-transform-modules-systemjs@7.8.3":
  "integrity" "sha512-8cESMCJjmArMYqa9AO5YuMEkE4ds28tMpZcGZB/jl3n0ZzlsxOAi3mC+SKypTfT8gjMupCnd3YiXCkMjj2jfOg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-hoist-variables" "7.8.3"
    "@babel/helper-module-transforms" "7.8.6"
    "@babel/helper-plugin-utils" "7.8.3"
    "babel-plugin-dynamic-import-node" "2.3.0"

"@babel/plugin-transform-modules-umd@7.8.3":
  "integrity" "sha512-evhTyWhbwbI3/U6dZAnx/ePoV7H6OUG+OjiJFHmhr9FPn0VShjwC2kdxqIuQ/+1P50TMrneGzMeyMTFOjKSnAw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-module-transforms" "7.8.6"
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-transform-named-capturing-groups-regex@7.8.3":
  "integrity" "sha512-f+tF/8UVPU86TrCb06JoPWIdDpTNSGGcAtaD9mLP0aYGA0OS0j7j7DHJR0GTFrUZPUU6loZhbsVZgTh0N+Qdnw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "7.8.6"

"@babel/plugin-transform-new-target@7.8.3":
  "integrity" "sha512-QuSGysibQpyxexRyui2vca+Cmbljo8bcRckgzYV4kRIsHpVeyeC3JDO63pY+xFZ6bWOBn7pfKZTqV4o/ix9sFw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-transform-object-super@7.8.3":
  "integrity" "sha512-57FXk+gItG/GejofIyLIgBKTas4+pEU47IXKDBWFTxdPd7F80H8zybyAY7UoblVfBhBGs2EKM+bJUu2+iUYPDQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"
    "@babel/helper-replace-supers" "7.8.6"

"@babel/plugin-transform-parameters@7.8.7":
  "integrity" "sha512-brYWaEPTRimOctz2NDA3jnBbDi7SVN2T4wYuu0aqSzxC3nozFZngGaw29CJ9ZPweB7k+iFmZuoG3IVPIcXmD2g=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.8.7.tgz"
  "version" "7.8.7"
  dependencies:
    "@babel/helper-call-delegate" "7.8.7"
    "@babel/helper-get-function-arity" "7.8.3"
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-transform-property-literals@7.8.3":
  "integrity" "sha512-uGiiXAZMqEoQhRWMK17VospMZh5sXWg+dlh2soffpkAl96KAm+WZuJfa6lcELotSRmooLqg0MWdH6UUq85nmmg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-transform-react-constant-elements@7.2.0":
  "integrity" "sha512-YYQFg6giRFMsZPKUM9v+VcHOdfSQdz9jHCx3akAi3UYgyjndmdYGSXylQ/V+HswQt4fL8IklchD9HTsaOCrWQQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-react-constant-elements/-/plugin-transform-react-constant-elements-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "@babel/helper-annotate-as-pure" "7.8.3"
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-transform-react-constant-elements@7.8.3":
  "integrity" "sha512-glrzN2U+egwRfkNFtL34xIBYTxbbUF2qJTP8HD3qETBBqzAWSeNB821X0GjU06+dNpq/UyCIjI72FmGE5NNkQQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-react-constant-elements/-/plugin-transform-react-constant-elements-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-annotate-as-pure" "7.8.3"
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-transform-react-display-name@7.2.0":
  "integrity" "sha512-Htf/tPa5haZvRMiNSQSFifK12gtr/8vwfr+A9y69uF0QcU77AVu4K7MiHEkTxF7lQoHOL0F9ErqgfNEAKgXj7A=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-transform-react-display-name@7.8.3":
  "integrity" "sha512-3Jy/PCw8Fe6uBKtEgz3M82ljt+lTg+xJaM4og+eyu83qLT87ZUSckn0wy7r31jflURWLO83TW6Ylf7lyXj3m5A=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-transform-react-jsx-self@7.8.3":
  "integrity" "sha512-01OT7s5oa0XTLf2I8XGsL8+KqV9lx3EZV+jxn/L2LQ97CGKila2YMroTkCEIE0HV/FF7CMSRsIAybopdN9NTdg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"
    "@babel/plugin-syntax-jsx" "7.8.3"

"@babel/plugin-transform-react-jsx-source@7.8.3":
  "integrity" "sha512-PLMgdMGuVDtRS/SzjNEQYUT8f4z1xb2BAT54vM1X5efkVuYBf5WyGUMbpmARcfq3NaglIwz08UVQK4HHHbC6ag=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"
    "@babel/plugin-syntax-jsx" "7.8.3"

"@babel/plugin-transform-react-jsx@7.8.3":
  "integrity" "sha512-r0h+mUiyL595ikykci+fbwm9YzmuOrUBi0b+FDIKmi3fPQyFokWVEMJnRWHJPPQEjyFJyna9WZC6Viv6UHSv1g=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-builder-react-jsx" "7.8.3"
    "@babel/helper-plugin-utils" "7.8.3"
    "@babel/plugin-syntax-jsx" "7.8.3"

"@babel/plugin-transform-regenerator@7.8.7":
  "integrity" "sha512-TIg+gAl4Z0a3WmD3mbYSk+J9ZUH6n/Yc57rtKRnlA/7rcCvpekHXe0CMZHP1gYp7/KLe9GHTuIba0vXmls6drA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.8.7.tgz"
  "version" "7.8.7"
  dependencies:
    "regenerator-transform" "0.14.2"

"@babel/plugin-transform-reserved-words@7.8.3":
  "integrity" "sha512-mwMxcycN3omKFDjDQUl+8zyMsBfjRFr0Zn/64I41pmjv4NJuqcYlEtezwYtw9TFd9WR1vN5kiM+O0gMZzO6L0A=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-transform-runtime@7.2.0":
  "integrity" "sha512-jIgkljDdq4RYDnJyQsiWbdvGeei/0MOTtSHKO/rfbd/mXBxNpdlulMx49L0HQ4pug1fXannxoqCI+fYSle9eSw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "@babel/helper-module-imports" "7.8.3"
    "@babel/helper-plugin-utils" "7.8.3"
    "resolve" "1.10.0"
    "semver" "5.7.1"

"@babel/plugin-transform-shorthand-properties@7.8.3":
  "integrity" "sha512-I9DI6Odg0JJwxCHzbzW08ggMdCezoWcuQRz3ptdudgwaHxTjxw5HgdFJmZIkIMlRymL6YiZcped4TTCB0JcC8w=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-transform-spread@7.8.3":
  "integrity" "sha512-CkuTU9mbmAoFOI1tklFWYYbzX5qCIZVXPVy0jpXgGwkplCndQAa58s2jr66fTeQnA64bDox0HL4U56CFYoyC7g=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-transform-sticky-regex@7.8.3":
  "integrity" "sha512-9Spq0vGCD5Bb4Z/ZXXSK5wbbLFMG085qd2vhL1JYu1WcQ5bXqZBAYRzU1d+p79GcHs2szYv5pVQCX13QgldaWw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"
    "@babel/helper-regex" "7.8.3"

"@babel/plugin-transform-template-literals@7.8.3":
  "integrity" "sha512-820QBtykIQOLFT8NZOcTRJ1UNuztIELe4p9DCgvj4NK+PwluSJ49we7s9FB1HIGNIYT7wFUJ0ar2QpCDj0escQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-annotate-as-pure" "7.8.3"
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-transform-typeof-symbol@7.8.4":
  "integrity" "sha512-2QKyfjGdvuNfHsb7qnBBlKclbD4CfshH2KvDabiijLMGXPHJXGxtDzwIF7bQP+T0ysw8fYTtxPafgfs/c1Lrqg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.8.4.tgz"
  "version" "7.8.4"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/plugin-transform-typescript@7.8.7":
  "integrity" "sha512-7O0UsPQVNKqpHeHLpfvOG4uXmlw+MOxYvUv6Otc9uH5SYMIxvF6eBdjkWvC3f9G+VXe0RsNExyAQBeTRug/wqQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.8.7.tgz"
  "version" "7.8.7"
  dependencies:
    "@babel/helper-create-class-features-plugin" "7.8.6"
    "@babel/helper-plugin-utils" "7.8.3"
    "@babel/plugin-syntax-typescript" "7.8.3"

"@babel/plugin-transform-unicode-regex@7.8.3":
  "integrity" "sha512-+ufgJjYdmWfSQ+6NS9VGUR2ns8cjJjYbrbi11mZBTaWm+Fui/ncTLFF28Ei1okavY+xkojGr1eJxNsWYeA5aZw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "7.8.6"
    "@babel/helper-plugin-utils" "7.8.3"

"@babel/preset-env@7.3.1":
  "integrity" "sha512-FHKrD6Dxf30e8xgHQO0zJZpUPfVZg+Xwgz5/RdSWCbza9QLNk4Qbp40ctRoqDxml3O8RMzB1DU55SXeDG6PqHQ=="
  "resolved" "https://registry.npmjs.org/@babel/preset-env/-/preset-env-7.3.1.tgz"
  "version" "7.3.1"
  dependencies:
    "@babel/helper-module-imports" "7.8.3"
    "@babel/helper-plugin-utils" "7.8.3"
    "@babel/plugin-proposal-async-generator-functions" "7.8.3"
    "@babel/plugin-proposal-json-strings" "7.8.3"
    "@babel/plugin-proposal-object-rest-spread" "7.3.2"
    "@babel/plugin-proposal-optional-catch-binding" "7.8.3"
    "@babel/plugin-proposal-unicode-property-regex" "7.8.3"
    "@babel/plugin-syntax-async-generators" "7.8.4"
    "@babel/plugin-syntax-json-strings" "7.8.3"
    "@babel/plugin-syntax-object-rest-spread" "7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "7.8.3"
    "@babel/plugin-transform-arrow-functions" "7.8.3"
    "@babel/plugin-transform-async-to-generator" "7.8.3"
    "@babel/plugin-transform-block-scoped-functions" "7.8.3"
    "@babel/plugin-transform-block-scoping" "7.8.3"
    "@babel/plugin-transform-classes" "7.2.2"
    "@babel/plugin-transform-computed-properties" "7.8.3"
    "@babel/plugin-transform-destructuring" "7.3.2"
    "@babel/plugin-transform-dotall-regex" "7.8.3"
    "@babel/plugin-transform-duplicate-keys" "7.8.3"
    "@babel/plugin-transform-exponentiation-operator" "7.8.3"
    "@babel/plugin-transform-for-of" "7.8.6"
    "@babel/plugin-transform-function-name" "7.8.3"
    "@babel/plugin-transform-literals" "7.8.3"
    "@babel/plugin-transform-modules-amd" "7.8.3"
    "@babel/plugin-transform-modules-commonjs" "7.8.3"
    "@babel/plugin-transform-modules-systemjs" "7.8.3"
    "@babel/plugin-transform-modules-umd" "7.8.3"
    "@babel/plugin-transform-named-capturing-groups-regex" "7.8.3"
    "@babel/plugin-transform-new-target" "7.8.3"
    "@babel/plugin-transform-object-super" "7.8.3"
    "@babel/plugin-transform-parameters" "7.8.7"
    "@babel/plugin-transform-regenerator" "7.8.7"
    "@babel/plugin-transform-shorthand-properties" "7.8.3"
    "@babel/plugin-transform-spread" "7.8.3"
    "@babel/plugin-transform-sticky-regex" "7.8.3"
    "@babel/plugin-transform-template-literals" "7.8.3"
    "@babel/plugin-transform-typeof-symbol" "7.8.4"
    "@babel/plugin-transform-unicode-regex" "7.8.3"
    "browserslist" "4.9.1"
    "invariant" "2.2.4"
    "js-levenshtein" "1.1.6"
    "semver" "5.7.1"

"@babel/preset-env@7.8.7":
  "integrity" "sha512-BYftCVOdAYJk5ASsznKAUl53EMhfBbr8CJ1X+AJLfGPscQkwJFiaV/Wn9DPH/7fzm2v6iRYJKYHSqyynTGw0nw=="
  "resolved" "https://registry.npmjs.org/@babel/preset-env/-/preset-env-7.8.7.tgz"
  "version" "7.8.7"
  dependencies:
    "@babel/compat-data" "7.8.6"
    "@babel/helper-compilation-targets" "7.8.7"
    "@babel/helper-module-imports" "7.8.3"
    "@babel/helper-plugin-utils" "7.8.3"
    "@babel/plugin-proposal-async-generator-functions" "7.8.3"
    "@babel/plugin-proposal-dynamic-import" "7.8.3"
    "@babel/plugin-proposal-json-strings" "7.8.3"
    "@babel/plugin-proposal-nullish-coalescing-operator" "7.8.3"
    "@babel/plugin-proposal-object-rest-spread" "7.8.3"
    "@babel/plugin-proposal-optional-catch-binding" "7.8.3"
    "@babel/plugin-proposal-optional-chaining" "7.8.3"
    "@babel/plugin-proposal-unicode-property-regex" "7.8.3"
    "@babel/plugin-syntax-async-generators" "7.8.4"
    "@babel/plugin-syntax-dynamic-import" "7.8.3"
    "@babel/plugin-syntax-json-strings" "7.8.3"
    "@babel/plugin-syntax-nullish-coalescing-operator" "7.8.3"
    "@babel/plugin-syntax-object-rest-spread" "7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "7.8.3"
    "@babel/plugin-syntax-optional-chaining" "7.8.3"
    "@babel/plugin-syntax-top-level-await" "7.8.3"
    "@babel/plugin-transform-arrow-functions" "7.8.3"
    "@babel/plugin-transform-async-to-generator" "7.8.3"
    "@babel/plugin-transform-block-scoped-functions" "7.8.3"
    "@babel/plugin-transform-block-scoping" "7.8.3"
    "@babel/plugin-transform-classes" "7.8.6"
    "@babel/plugin-transform-computed-properties" "7.8.3"
    "@babel/plugin-transform-destructuring" "7.8.3"
    "@babel/plugin-transform-dotall-regex" "7.8.3"
    "@babel/plugin-transform-duplicate-keys" "7.8.3"
    "@babel/plugin-transform-exponentiation-operator" "7.8.3"
    "@babel/plugin-transform-for-of" "7.8.6"
    "@babel/plugin-transform-function-name" "7.8.3"
    "@babel/plugin-transform-literals" "7.8.3"
    "@babel/plugin-transform-member-expression-literals" "7.8.3"
    "@babel/plugin-transform-modules-amd" "7.8.3"
    "@babel/plugin-transform-modules-commonjs" "7.8.3"
    "@babel/plugin-transform-modules-systemjs" "7.8.3"
    "@babel/plugin-transform-modules-umd" "7.8.3"
    "@babel/plugin-transform-named-capturing-groups-regex" "7.8.3"
    "@babel/plugin-transform-new-target" "7.8.3"
    "@babel/plugin-transform-object-super" "7.8.3"
    "@babel/plugin-transform-parameters" "7.8.7"
    "@babel/plugin-transform-property-literals" "7.8.3"
    "@babel/plugin-transform-regenerator" "7.8.7"
    "@babel/plugin-transform-reserved-words" "7.8.3"
    "@babel/plugin-transform-shorthand-properties" "7.8.3"
    "@babel/plugin-transform-spread" "7.8.3"
    "@babel/plugin-transform-sticky-regex" "7.8.3"
    "@babel/plugin-transform-template-literals" "7.8.3"
    "@babel/plugin-transform-typeof-symbol" "7.8.4"
    "@babel/plugin-transform-unicode-regex" "7.8.3"
    "@babel/types" "7.8.7"
    "browserslist" "4.9.1"
    "core-js-compat" "3.6.4"
    "invariant" "2.2.4"
    "levenary" "1.1.1"
    "semver" "5.7.1"

"@babel/preset-react@7.0.0":
  "integrity" "sha512-oayxyPS4Zj+hF6Et11BwuBkmpgT/zMxyuZgFrMeZID6Hdh3dGlk4sHCAhdBCpuCKW2ppBfl2uCCetlrUIJRY3w=="
  "resolved" "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"
    "@babel/plugin-transform-react-display-name" "7.2.0"
    "@babel/plugin-transform-react-jsx" "7.8.3"
    "@babel/plugin-transform-react-jsx-self" "7.8.3"
    "@babel/plugin-transform-react-jsx-source" "7.8.3"

"@babel/preset-react@7.8.3":
  "integrity" "sha512-9hx0CwZg92jGb7iHYQVgi0tOEHP/kM60CtWJQnmbATSPIQQ2xYzfoCI3EdqAhFBeeJwYMdWQuDUHMsuDbH9hyQ=="
  "resolved" "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"
    "@babel/plugin-transform-react-display-name" "7.8.3"
    "@babel/plugin-transform-react-jsx" "7.8.3"
    "@babel/plugin-transform-react-jsx-self" "7.8.3"
    "@babel/plugin-transform-react-jsx-source" "7.8.3"

"@babel/preset-typescript@7.1.0":
  "integrity" "sha512-LYveByuF9AOM8WrsNne5+N79k1YxjNB6gmpCQsnuSBAcV8QUeB+ZUxQzL7Rz7HksPbahymKkq2qBR+o36ggFZA=="
  "resolved" "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.1.0.tgz"
  "version" "7.1.0"
  dependencies:
    "@babel/helper-plugin-utils" "7.8.3"
    "@babel/plugin-transform-typescript" "7.8.7"

"@babel/runtime@^7.12.1":
  "integrity" "sha512-/PCB2uJ7oM44tz8YhC4Z/6PeOKXp4K588f+5M3clr1M4zbqztlo0XEfJ2LEzj/FgwfgGcIdl8n7YYjTCI0BYwg=="
  "resolved" "https://registry.npmjs.org/@babel/runtime/-/runtime-7.14.6.tgz"
  "version" "7.14.6"
  dependencies:
    "regenerator-runtime" "^0.13.4"

"@babel/runtime@^7.9.2":
  "integrity" "sha512-/PCB2uJ7oM44tz8YhC4Z/6PeOKXp4K588f+5M3clr1M4zbqztlo0XEfJ2LEzj/FgwfgGcIdl8n7YYjTCI0BYwg=="
  "resolved" "https://registry.npmjs.org/@babel/runtime/-/runtime-7.14.6.tgz"
  "version" "7.14.6"
  dependencies:
    "regenerator-runtime" "^0.13.4"

"@babel/runtime@7.3.1":
  "integrity" "sha512-7jGW8ppV0ant637pIqAcFfQDDH1orEPGJb8aXfUozuCU3QqX7rX4DA8iwrbPrR1hcH0FTTHz47yQnk+bl5xHQA=="
  "resolved" "https://registry.npmjs.org/@babel/runtime/-/runtime-7.3.1.tgz"
  "version" "7.3.1"
  dependencies:
    "regenerator-runtime" "0.12.1"

"@babel/runtime@7.8.7":
  "integrity" "sha512-+AATMUFppJDw6aiR5NVPHqIQBlV/Pj8wY/EZH+lmvRdUo9xBaz/rF3alAwFJQavvKfeOlPE7oaaDHVbcySbCsg=="
  "resolved" "https://registry.npmjs.org/@babel/runtime/-/runtime-7.8.7.tgz"
  "version" "7.8.7"
  dependencies:
    "regenerator-runtime" "0.13.4"

"@babel/template@7.8.6":
  "integrity" "sha512-zbMsPMy/v0PWFZEhQJ66bqjhH+z0JgMoBWuikXybgG3Gkd/3t5oQ1Rw2WQhnSrsOmsKXnZOx15tkC4qON/+JPg=="
  "resolved" "https://registry.npmjs.org/@babel/template/-/template-7.8.6.tgz"
  "version" "7.8.6"
  dependencies:
    "@babel/code-frame" "7.8.3"
    "@babel/parser" "7.8.7"
    "@babel/types" "7.8.7"

"@babel/traverse@7.8.6":
  "integrity" "sha512-2B8l0db/DPi8iinITKuo7cbPznLCEk0kCxDoB9/N6gGNg/gxOXiR/IcymAFPiBwk5w6TtQ27w4wpElgp9btR9A=="
  "resolved" "https://registry.npmjs.org/@babel/traverse/-/traverse-7.8.6.tgz"
  "version" "7.8.6"
  dependencies:
    "@babel/code-frame" "7.8.3"
    "@babel/generator" "7.8.7"
    "@babel/helper-function-name" "7.8.3"
    "@babel/helper-split-export-declaration" "7.8.3"
    "@babel/parser" "7.8.7"
    "@babel/types" "7.8.7"
    "debug" "4.1.1"
    "globals" "11.12.0"
    "lodash" "4.17.15"

"@babel/types@7.8.7":
  "integrity" "sha512-k2TreEHxFA4CjGkL+GYjRyx35W0Mr7DP5+9q6WMkyKXB+904bYmG40syjMFV0oLlhhFCwWl0vA0DyzTDkwAiJw=="
  "resolved" "https://registry.npmjs.org/@babel/types/-/types-7.8.7.tgz"
  "version" "7.8.7"
  dependencies:
    "esutils" "2.0.3"
    "lodash" "4.17.15"
    "to-fast-properties" "2.0.0"

"@csstools/convert-colors@1.4.0":
  "integrity" "sha512-5a6wqoJV/xEdbRNKVo6I4hO3VjyDq//8q2f9I6PBAvMesJHFauXDorcNCsr9RzvsZnaWi5NYCcfyqP1QeFHFbw=="
  "resolved" "https://registry.npmjs.org/@csstools/convert-colors/-/convert-colors-1.4.0.tgz"
  "version" "1.4.0"

"@mrmlnc/readdir-enhanced@2.2.1":
  "integrity" "sha512-bPHp6Ji8b41szTOcaP63VlnbbO5Ny6dwAATtY6JTjh5N2OLrb5Qk/Th5cRkRQhkWCt+EJsYrNB0MiL+Gpn6e3g=="
  "resolved" "https://registry.npmjs.org/@mrmlnc/readdir-enhanced/-/readdir-enhanced-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "call-me-maybe" "1.0.1"
    "glob-to-regexp" "0.3.0"

"@nodelib/fs.stat@1.1.3":
  "integrity" "sha512-shAmDyaQC4H92APFoIaVDHCx5bStIocgvbwQyxPRrbUY20V1EYTbSDchWbuwlMG3V17cprZhA6+78JfB+3DTPw=="
  "resolved" "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-1.1.3.tgz"
  "version" "1.1.3"

"@svgr/babel-plugin-add-jsx-attribute@4.2.0":
  "integrity" "sha512-j7KnilGyZzYr/jhcrSYS3FGWMZVaqyCG0vzMCwzvei0coIkczuYMcniK07nI0aHJINciujjH11T72ICW5eL5Ig=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-add-jsx-attribute/-/babel-plugin-add-jsx-attribute-4.2.0.tgz"
  "version" "4.2.0"

"@svgr/babel-plugin-remove-jsx-attribute@4.2.0":
  "integrity" "sha512-3XHLtJ+HbRCH4n28S7y/yZoEQnRpl0tvTZQsHqvaeNXPra+6vE5tbRliH3ox1yZYPCxrlqaJT/Mg+75GpDKlvQ=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-remove-jsx-attribute/-/babel-plugin-remove-jsx-attribute-4.2.0.tgz"
  "version" "4.2.0"

"@svgr/babel-plugin-remove-jsx-empty-expression@4.2.0":
  "integrity" "sha512-yTr2iLdf6oEuUE9MsRdvt0NmdpMBAkgK8Bjhl6epb+eQWk6abBaX3d65UZ3E3FWaOwePyUgNyNCMVG61gGCQ7w=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-remove-jsx-empty-expression/-/babel-plugin-remove-jsx-empty-expression-4.2.0.tgz"
  "version" "4.2.0"

"@svgr/babel-plugin-replace-jsx-attribute-value@4.2.0":
  "integrity" "sha512-U9m870Kqm0ko8beHawRXLGLvSi/ZMrl89gJ5BNcT452fAjtF2p4uRzXkdzvGJJJYBgx7BmqlDjBN/eCp5AAX2w=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-replace-jsx-attribute-value/-/babel-plugin-replace-jsx-attribute-value-4.2.0.tgz"
  "version" "4.2.0"

"@svgr/babel-plugin-svg-dynamic-title@4.3.3":
  "integrity" "sha512-w3Be6xUNdwgParsvxkkeZb545VhXEwjGMwExMVBIdPQJeyMQHqm9Msnb2a1teHBqUYL66qtwfhNkbj1iarCG7w=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-svg-dynamic-title/-/babel-plugin-svg-dynamic-title-4.3.3.tgz"
  "version" "4.3.3"

"@svgr/babel-plugin-svg-em-dimensions@4.2.0":
  "integrity" "sha512-C0Uy+BHolCHGOZ8Dnr1zXy/KgpBOkEUYY9kI/HseHVPeMbluaX3CijJr7D4C5uR8zrc1T64nnq/k63ydQuGt4w=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-svg-em-dimensions/-/babel-plugin-svg-em-dimensions-4.2.0.tgz"
  "version" "4.2.0"

"@svgr/babel-plugin-transform-react-native-svg@4.2.0":
  "integrity" "sha512-7YvynOpZDpCOUoIVlaaOUU87J4Z6RdD6spYN4eUb5tfPoKGSF9OG2NuhgYnq4jSkAxcpMaXWPf1cePkzmqTPNw=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-transform-react-native-svg/-/babel-plugin-transform-react-native-svg-4.2.0.tgz"
  "version" "4.2.0"

"@svgr/babel-plugin-transform-svg-component@4.2.0":
  "integrity" "sha512-hYfYuZhQPCBVotABsXKSCfel2slf/yvJY8heTVX1PCTaq/IgASq1IyxPPKJ0chWREEKewIU/JMSsIGBtK1KKxw=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-transform-svg-component/-/babel-plugin-transform-svg-component-4.2.0.tgz"
  "version" "4.2.0"

"@svgr/babel-preset@4.3.3":
  "integrity" "sha512-6PG80tdz4eAlYUN3g5GZiUjg2FMcp+Wn6rtnz5WJG9ITGEF1pmFdzq02597Hn0OmnQuCVaBYQE1OVFAnwOl+0A=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-preset/-/babel-preset-4.3.3.tgz"
  "version" "4.3.3"
  dependencies:
    "@svgr/babel-plugin-add-jsx-attribute" "4.2.0"
    "@svgr/babel-plugin-remove-jsx-attribute" "4.2.0"
    "@svgr/babel-plugin-remove-jsx-empty-expression" "4.2.0"
    "@svgr/babel-plugin-replace-jsx-attribute-value" "4.2.0"
    "@svgr/babel-plugin-svg-dynamic-title" "4.3.3"
    "@svgr/babel-plugin-svg-em-dimensions" "4.2.0"
    "@svgr/babel-plugin-transform-react-native-svg" "4.2.0"
    "@svgr/babel-plugin-transform-svg-component" "4.2.0"

"@svgr/core@4.3.3":
  "integrity" "sha512-qNuGF1QON1626UCaZamWt5yedpgOytvLj5BQZe2j1k1B8DUG4OyugZyfEwBeXozCUwhLEpsrgPrE+eCu4fY17w=="
  "resolved" "https://registry.npmjs.org/@svgr/core/-/core-4.3.3.tgz"
  "version" "4.3.3"
  dependencies:
    "@svgr/plugin-jsx" "4.3.3"
    "camelcase" "5.3.1"
    "cosmiconfig" "5.2.1"

"@svgr/hast-util-to-babel-ast@4.3.2":
  "integrity" "sha512-JioXclZGhFIDL3ddn4Kiq8qEqYM2PyDKV0aYno8+IXTLuYt6TOgHUbUAAFvqtb0Xn37NwP0BTHglejFoYr8RZg=="
  "resolved" "https://registry.npmjs.org/@svgr/hast-util-to-babel-ast/-/hast-util-to-babel-ast-4.3.2.tgz"
  "version" "4.3.2"
  dependencies:
    "@babel/types" "7.8.7"

"@svgr/plugin-jsx@4.3.3":
  "integrity" "sha512-cLOCSpNWQnDB1/v+SUENHH7a0XY09bfuMKdq9+gYvtuwzC2rU4I0wKGFEp1i24holdQdwodCtDQdFtJiTCWc+w=="
  "resolved" "https://registry.npmjs.org/@svgr/plugin-jsx/-/plugin-jsx-4.3.3.tgz"
  "version" "4.3.3"
  dependencies:
    "@babel/core" "7.8.7"
    "@svgr/babel-preset" "4.3.3"
    "@svgr/hast-util-to-babel-ast" "4.3.2"
    "svg-parser" "2.0.4"

"@svgr/plugin-svgo@4.3.1":
  "integrity" "sha512-PrMtEDUWjX3Ea65JsVCwTIXuSqa3CG9px+DluF1/eo9mlDrgrtFE7NE/DjdhjJgSM9wenlVBzkzneSIUgfUI/w=="
  "resolved" "https://registry.npmjs.org/@svgr/plugin-svgo/-/plugin-svgo-4.3.1.tgz"
  "version" "4.3.1"
  dependencies:
    "cosmiconfig" "5.2.1"
    "merge-deep" "3.0.2"
    "svgo" "1.3.2"

"@svgr/webpack@4.1.0":
  "integrity" "sha512-d09ehQWqLMywP/PT/5JvXwPskPK9QCXUjiSkAHehreB381qExXf5JFCBWhfEyNonRbkIneCeYM99w+Ud48YIQQ=="
  "resolved" "https://registry.npmjs.org/@svgr/webpack/-/webpack-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "@babel/core" "7.2.2"
    "@babel/plugin-transform-react-constant-elements" "7.8.3"
    "@babel/preset-env" "7.8.7"
    "@babel/preset-react" "7.8.3"
    "@svgr/core" "4.3.3"
    "@svgr/plugin-jsx" "4.3.3"
    "@svgr/plugin-svgo" "4.3.1"
    "loader-utils" "1.4.0"

"@types/hoist-non-react-statics@^3.3.0":
  "integrity" "sha512-iMIqiko6ooLrTh1joXodJK5X9xeEALT1kM5G3ZLhD3hszxBdIEd5C75U834D9mLcINgD4OyZf5uQXjkuYydWvA=="
  "resolved" "https://registry.npmjs.org/@types/hoist-non-react-statics/-/hoist-non-react-statics-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "@types/react" "*"
    "hoist-non-react-statics" "^3.3.0"

"@types/prop-types@15.7.3":
  "integrity" "sha512-KfRL3PuHmqQLOG+2tGpRO26Ctg+Cq1E01D2DMriKEATHgWLfeNDmq9e29Q9WIky0dQ3NPkd1mzYH8Lm936Z9qw=="
  "resolved" "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.3.tgz"
  "version" "15.7.3"

"@types/q@1.5.2":
  "integrity" "sha512-ce5d3q03Ex0sy4R14722Rmt6MT07Ua+k4FwDfdcToYJcMKNtRVQvJ6JCAPdAmAnbRb6CsX6aYb9m96NGod9uTw=="
  "resolved" "https://registry.npmjs.org/@types/q/-/q-1.5.2.tgz"
  "version" "1.5.2"

"@types/react-redux@^7.1.16":
  "integrity" "sha512-f/FKzIrZwZk7YEO9E1yoxIuDNRiDducxkFlkw/GNMGEnK9n4K8wJzlJBghpSuOVDgEUHoDkDF7Gi9lHNQR4siw=="
  "resolved" "https://registry.npmjs.org/@types/react-redux/-/react-redux-7.1.16.tgz"
  "version" "7.1.16"
  dependencies:
    "@types/hoist-non-react-statics" "^3.3.0"
    "@types/react" "*"
    "hoist-non-react-statics" "^3.3.0"
    "redux" "^4.0.0"

"@types/react-slick@0.23.4":
  "integrity" "sha512-vXoIy4GUfB7/YgqubR4H7RALo+pRdMYCeLgWwV3MPwl5pggTlEkFBTF19R7u+LJc85uMqC7RfsbkqPLMQ4ab+A=="
  "resolved" "https://registry.npmjs.org/@types/react-slick/-/react-slick-0.23.4.tgz"
  "version" "0.23.4"
  dependencies:
    "@types/react" "16.9.23"

"@types/react@*", "@types/react@16.9.23":
  "integrity" "sha512-SsGVT4E7L2wLN3tPYLiF20hmZTPGuzaayVunfgXzUn1x4uHVsKH6QDJQ/TdpHqwsTLd4CwrmQ2vOgxN7gE24gw=="
  "resolved" "https://registry.npmjs.org/@types/react/-/react-16.9.23.tgz"
  "version" "16.9.23"
  dependencies:
    "@types/prop-types" "15.7.3"
    "csstype" "2.6.9"

"@types/tapable@1.0.2":
  "integrity" "sha512-42zEJkBpNfMEAvWR5WlwtTH22oDzcMjFsL9gDGExwF8X8WvAiw7Vwop7hPw03QT8TKfec83LwbHj6SvpqM4ELQ=="
  "resolved" "https://registry.npmjs.org/@types/tapable/-/tapable-1.0.2.tgz"
  "version" "1.0.2"

"@webassemblyjs/ast@1.7.11":
  "integrity" "sha512-ZEzy4vjvTzScC+SH8RBssQUawpaInUdMTYwYYLh54/s8TuT0gBLuyUnppKsVyZEi876VmmStKsUs28UxPgdvrA=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.7.11.tgz"
  "version" "1.7.11"
  dependencies:
    "@webassemblyjs/helper-module-context" "1.7.11"
    "@webassemblyjs/helper-wasm-bytecode" "1.7.11"
    "@webassemblyjs/wast-parser" "1.7.11"

"@webassemblyjs/floating-point-hex-parser@1.7.11":
  "integrity" "sha512-zY8dSNyYcgzNRNT666/zOoAyImshm3ycKdoLsyDw/Bwo6+/uktb7p4xyApuef1dwEBo/U/SYQzbGBvV+nru2Xg=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.7.11.tgz"
  "version" "1.7.11"

"@webassemblyjs/helper-api-error@1.7.11":
  "integrity" "sha512-7r1qXLmiglC+wPNkGuXCvkmalyEstKVwcueZRP2GNC2PAvxbLYwLLPr14rcdJaE4UtHxQKfFkuDFuv91ipqvXg=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.7.11.tgz"
  "version" "1.7.11"

"@webassemblyjs/helper-buffer@1.7.11":
  "integrity" "sha512-MynuervdylPPh3ix+mKZloTcL06P8tenNH3sx6s0qE8SLR6DdwnfgA7Hc9NSYeob2jrW5Vql6GVlsQzKQCa13w=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-buffer/-/helper-buffer-1.7.11.tgz"
  "version" "1.7.11"

"@webassemblyjs/helper-code-frame@1.7.11":
  "integrity" "sha512-T8ESC9KMXFTXA5urJcyor5cn6qWeZ4/zLPyWeEXZ03hj/x9weSokGNkVCdnhSabKGYWxElSdgJ+sFa9G/RdHNw=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-code-frame/-/helper-code-frame-1.7.11.tgz"
  "version" "1.7.11"
  dependencies:
    "@webassemblyjs/wast-printer" "1.7.11"

"@webassemblyjs/helper-fsm@1.7.11":
  "integrity" "sha512-nsAQWNP1+8Z6tkzdYlXT0kxfa2Z1tRTARd8wYnc/e3Zv3VydVVnaeePgqUzFrpkGUyhUUxOl5ML7f1NuT+gC0A=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-fsm/-/helper-fsm-1.7.11.tgz"
  "version" "1.7.11"

"@webassemblyjs/helper-module-context@1.7.11":
  "integrity" "sha512-JxfD5DX8Ygq4PvXDucq0M+sbUFA7BJAv/GGl9ITovqE+idGX+J3QSzJYz+LwQmL7fC3Rs+utvWoJxDb6pmC0qg=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-module-context/-/helper-module-context-1.7.11.tgz"
  "version" "1.7.11"

"@webassemblyjs/helper-wasm-bytecode@1.7.11":
  "integrity" "sha512-cMXeVS9rhoXsI9LLL4tJxBgVD/KMOKXuFqYb5oCJ/opScWpkCMEz9EJtkonaNcnLv2R3K5jIeS4TRj/drde1JQ=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.7.11.tgz"
  "version" "1.7.11"

"@webassemblyjs/helper-wasm-section@1.7.11":
  "integrity" "sha512-8ZRY5iZbZdtNFE5UFunB8mmBEAbSI3guwbrsCl4fWdfRiAcvqQpeqd5KHhSWLL5wuxo53zcaGZDBU64qgn4I4Q=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.7.11.tgz"
  "version" "1.7.11"
  dependencies:
    "@webassemblyjs/ast" "1.7.11"
    "@webassemblyjs/helper-buffer" "1.7.11"
    "@webassemblyjs/helper-wasm-bytecode" "1.7.11"
    "@webassemblyjs/wasm-gen" "1.7.11"

"@webassemblyjs/ieee754@1.7.11":
  "integrity" "sha512-Mmqx/cS68K1tSrvRLtaV/Lp3NZWzXtOHUW2IvDvl2sihAwJh4ACE0eL6A8FvMyDG9abes3saB6dMimLOs+HMoQ=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.7.11.tgz"
  "version" "1.7.11"
  dependencies:
    "@xtuc/ieee754" "1.2.0"

"@webassemblyjs/leb128@1.7.11":
  "integrity" "sha512-vuGmgZjjp3zjcerQg+JA+tGOncOnJLWVkt8Aze5eWQLwTQGNgVLcyOTqgSCxWTR4J42ijHbBxnuRaL1Rv7XMdw=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/leb128/-/leb128-1.7.11.tgz"
  "version" "1.7.11"
  dependencies:
    "@xtuc/long" "4.2.1"

"@webassemblyjs/utf8@1.7.11":
  "integrity" "sha512-C6GFkc7aErQIAH+BMrIdVSmW+6HSe20wg57HEC1uqJP8E/xpMjXqQUxkQw07MhNDSDcGpxI9G5JSNOQCqJk4sA=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.7.11.tgz"
  "version" "1.7.11"

"@webassemblyjs/wasm-edit@1.7.11":
  "integrity" "sha512-FUd97guNGsCZQgeTPKdgxJhBXkUbMTY6hFPf2Y4OedXd48H97J+sOY2Ltaq6WGVpIH8o/TGOVNiVz/SbpEMJGg=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.7.11.tgz"
  "version" "1.7.11"
  dependencies:
    "@webassemblyjs/ast" "1.7.11"
    "@webassemblyjs/helper-buffer" "1.7.11"
    "@webassemblyjs/helper-wasm-bytecode" "1.7.11"
    "@webassemblyjs/helper-wasm-section" "1.7.11"
    "@webassemblyjs/wasm-gen" "1.7.11"
    "@webassemblyjs/wasm-opt" "1.7.11"
    "@webassemblyjs/wasm-parser" "1.7.11"
    "@webassemblyjs/wast-printer" "1.7.11"

"@webassemblyjs/wasm-gen@1.7.11":
  "integrity" "sha512-U/KDYp7fgAZX5KPfq4NOupK/BmhDc5Kjy2GIqstMhvvdJRcER/kUsMThpWeRP8BMn4LXaKhSTggIJPOeYHwISA=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.7.11.tgz"
  "version" "1.7.11"
  dependencies:
    "@webassemblyjs/ast" "1.7.11"
    "@webassemblyjs/helper-wasm-bytecode" "1.7.11"
    "@webassemblyjs/ieee754" "1.7.11"
    "@webassemblyjs/leb128" "1.7.11"
    "@webassemblyjs/utf8" "1.7.11"

"@webassemblyjs/wasm-opt@1.7.11":
  "integrity" "sha512-XynkOwQyiRidh0GLua7SkeHvAPXQV/RxsUeERILmAInZegApOUAIJfRuPYe2F7RcjOC9tW3Cb9juPvAC/sCqvg=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.7.11.tgz"
  "version" "1.7.11"
  dependencies:
    "@webassemblyjs/ast" "1.7.11"
    "@webassemblyjs/helper-buffer" "1.7.11"
    "@webassemblyjs/wasm-gen" "1.7.11"
    "@webassemblyjs/wasm-parser" "1.7.11"

"@webassemblyjs/wasm-parser@1.7.11":
  "integrity" "sha512-6lmXRTrrZjYD8Ng8xRyvyXQJYUQKYSXhJqXOBLw24rdiXsHAOlvw5PhesjdcaMadU/pyPQOJ5dHreMjBxwnQKg=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.7.11.tgz"
  "version" "1.7.11"
  dependencies:
    "@webassemblyjs/ast" "1.7.11"
    "@webassemblyjs/helper-api-error" "1.7.11"
    "@webassemblyjs/helper-wasm-bytecode" "1.7.11"
    "@webassemblyjs/ieee754" "1.7.11"
    "@webassemblyjs/leb128" "1.7.11"
    "@webassemblyjs/utf8" "1.7.11"

"@webassemblyjs/wast-parser@1.7.11":
  "integrity" "sha512-lEyVCg2np15tS+dm7+JJTNhNWq9yTZvi3qEhAIIOaofcYlUp0UR5/tVqOwa/gXYr3gjwSZqw+/lS9dscyLelbQ=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/wast-parser/-/wast-parser-1.7.11.tgz"
  "version" "1.7.11"
  dependencies:
    "@webassemblyjs/ast" "1.7.11"
    "@webassemblyjs/floating-point-hex-parser" "1.7.11"
    "@webassemblyjs/helper-api-error" "1.7.11"
    "@webassemblyjs/helper-code-frame" "1.7.11"
    "@webassemblyjs/helper-fsm" "1.7.11"
    "@xtuc/long" "4.2.1"

"@webassemblyjs/wast-printer@1.7.11":
  "integrity" "sha512-m5vkAsuJ32QpkdkDOUPGSltrg8Cuk3KBx4YrmAGQwCZPRdUHXxG4phIOuuycLemHFr74sWL9Wthqss4fzdzSwg=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.7.11.tgz"
  "version" "1.7.11"
  dependencies:
    "@webassemblyjs/ast" "1.7.11"
    "@webassemblyjs/wast-parser" "1.7.11"
    "@xtuc/long" "4.2.1"

"@xtuc/ieee754@1.2.0":
  "integrity" "sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA=="
  "resolved" "https://registry.npmjs.org/@xtuc/ieee754/-/ieee754-1.2.0.tgz"
  "version" "1.2.0"

"@xtuc/long@4.2.1":
  "integrity" "sha512-FZdkNBDqBRHKQ2MEbSC17xnPFOhZxeJ2YGSfr2BKf3sujG49Qe3bB+rGCwQfIaA7WHnGeGkSijX4FuBCdrzW/g=="
  "resolved" "https://registry.npmjs.org/@xtuc/long/-/long-4.2.1.tgz"
  "version" "4.2.1"

"abab@2.0.3":
  "integrity" "sha512-tsFzPpcttalNjFBCFMqsKYQcWxxen1pgJR56by//QwvJc4/OUS3kPOOttx2tSIfjsylB0pYu7f5D3K1RCxUnUg=="
  "resolved" "https://registry.npmjs.org/abab/-/abab-2.0.3.tgz"
  "version" "2.0.3"

"accepts@1.3.7":
  "integrity" "sha512-Il80Qs2WjYlJIBNzNkK6KYqlVMTbZLXgHx2oT0pU/fjRHyEp+PEfEPY0R3WCwAGVOtauxh1hOxNgIf5bv7dQpA=="
  "resolved" "https://registry.npmjs.org/accepts/-/accepts-1.3.7.tgz"
  "version" "1.3.7"
  dependencies:
    "mime-types" "2.1.26"
    "negotiator" "0.6.2"

"acorn-dynamic-import@3.0.0":
  "integrity" "sha512-zVWV8Z8lislJoOKKqdNMOB+s6+XV5WERty8MnKBeFgwA+19XJjJHs2RP5dzM57FftIs+jQnRToLiWazKr6sSWg=="
  "resolved" "https://registry.npmjs.org/acorn-dynamic-import/-/acorn-dynamic-import-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "acorn" "5.7.3"

"acorn-globals@4.3.4":
  "integrity" "sha512-clfQEh21R+D0leSbUdWf3OcfqyaCSAQ8Ryq00bofSekfr9W8u1jyYZo6ir0xu9Gtcf7BjcHJpnbZH7JOCpP60A=="
  "resolved" "https://registry.npmjs.org/acorn-globals/-/acorn-globals-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "acorn" "6.4.0"
    "acorn-walk" "6.2.0"

"acorn-jsx@5.2.0":
  "integrity" "sha512-HiUX/+K2YpkpJ+SzBffkM/AQ2YE03S0U1kjTLVpoJdhZMOWy8qvXVN9JdLqv2QsaQ6MPYQIuNmwD8zOiYUofLQ=="
  "resolved" "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.2.0.tgz"
  "version" "5.2.0"

"acorn-walk@6.2.0":
  "integrity" "sha512-7evsyfH1cLOCdAzZAd43Cic04yKydNx0cF+7tiA19p1XnLLPU4dpCQOqpjqwokFe//vS0QqfqqjCS2JkiIs0cA=="
  "resolved" "https://registry.npmjs.org/acorn-walk/-/acorn-walk-6.2.0.tgz"
  "version" "6.2.0"

"acorn@5.7.3":
  "integrity" "sha512-T/zvzYRfbVojPWahDsE5evJdHb3oJoQfFbsrKM7w5Zcs++Tr257tia3BmMP8XYVjp1S9RZXQMh7gao96BlqZOw=="
  "resolved" "https://registry.npmjs.org/acorn/-/acorn-5.7.3.tgz"
  "version" "5.7.3"

"acorn@6.4.0":
  "integrity" "sha512-gac8OEcQ2Li1dxIEWGZzsp2BitJxwkwcOm0zHAJLcPJaVvm58FRnk6RkuLRpU1EujipU2ZFODv2P9DLMfnV8mw=="
  "resolved" "https://registry.npmjs.org/acorn/-/acorn-6.4.0.tgz"
  "version" "6.4.0"

"add-dom-event-listener@1.1.0":
  "integrity" "sha512-WCxx1ixHT0GQU9hb0KI/mhgRQhnU+U3GvwY6ZvVjYq8rsihIGoaIOUbY0yMPBxLH5MDtr0kz3fisWGNcbWW7Jw=="
  "resolved" "https://registry.npmjs.org/add-dom-event-listener/-/add-dom-event-listener-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "object-assign" "4.1.1"

"address@1.0.3":
  "integrity" "sha512-z55ocwKBRLryBs394Sm3ushTtBeg6VAeuku7utSoSnsJKvKcnXFIyC6vh27n3rXyxSgkJBBCAvyOn7gSUcTYjg=="
  "resolved" "https://registry.npmjs.org/address/-/address-1.0.3.tgz"
  "version" "1.0.3"

"ajv-errors@1.0.1":
  "integrity" "sha512-DCRfO/4nQ+89p/RK43i8Ezd41EqdGIU4ld7nGF8OQ14oc/we5rEntLCUa7+jrn3nn83BosfwZA0wb4pon2o8iQ=="
  "resolved" "https://registry.npmjs.org/ajv-errors/-/ajv-errors-1.0.1.tgz"
  "version" "1.0.1"

"ajv-keywords@3.4.1":
  "integrity" "sha512-RO1ibKvd27e6FEShVFfPALuHI3WjSVNeK5FIsmme/LYRNxjKuNj+Dt7bucLa6NdSv3JcVTyMlm9kGR84z1XpaQ=="
  "resolved" "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.4.1.tgz"
  "version" "3.4.1"

"ajv@6.12.0":
  "integrity" "sha512-D6gFiFA0RRLyUbvijN74DWAjXSFxWKaWP7mldxkVhyhAV3+SWA9HEJPHQ2c9soIeTFJqcSdFDGFgdqs1iUU2Hw=="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-6.12.0.tgz"
  "version" "6.12.0"
  dependencies:
    "fast-deep-equal" "3.1.1"
    "fast-json-stable-stringify" "2.1.0"
    "json-schema-traverse" "0.4.1"
    "uri-js" "4.2.2"

"alphanum-sort@1.0.2":
  "integrity" "sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM="
  "resolved" "https://registry.npmjs.org/alphanum-sort/-/alphanum-sort-1.0.2.tgz"
  "version" "1.0.2"

"ansi-colors@3.2.4":
  "integrity" "sha512-hHUXGagefjN2iRrID63xckIvotOXOojhQKWIPUZ4mNUZ9nLZW+7FMNoE1lOkEhNWYsx/7ysGIuJYCiMAA9FnrA=="
  "resolved" "https://registry.npmjs.org/ansi-colors/-/ansi-colors-3.2.4.tgz"
  "version" "3.2.4"

"ansi-escapes@3.2.0":
  "integrity" "sha512-cBhpre4ma+U0T1oM5fXg7Dy1Jw7zzwv7lt/GoCpr+hDQJoYnKVPLL4dCvSEFMmQurOQvSrwT7SL/DAlhBI97RQ=="
  "resolved" "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-3.2.0.tgz"
  "version" "3.2.0"

"ansi-html@0.0.7":
  "integrity" "sha1-gTWEAhliqenm/QOflA0S9WynhZ4="
  "resolved" "https://registry.npmjs.org/ansi-html/-/ansi-html-0.0.7.tgz"
  "version" "0.0.7"

"ansi-regex@2.1.1":
  "integrity" "sha1-w7M6te42DYbg5ijwRorn7yfWVN8="
  "resolved" "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.1.1.tgz"
  "version" "2.1.1"

"ansi-regex@3.0.0":
  "integrity" "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg="
  "resolved" "https://registry.npmjs.org/ansi-regex/-/ansi-regex-3.0.0.tgz"
  "version" "3.0.0"

"ansi-regex@4.1.0":
  "integrity" "sha512-1apePfXM1UOSqw0o9IiFAovVz9M5S1Dg+4TrDwfMewQ6p/rmMueb7tWZjQ1rx4Loy1ArBggoqGpfqqdI4rondg=="
  "resolved" "https://registry.npmjs.org/ansi-regex/-/ansi-regex-4.1.0.tgz"
  "version" "4.1.0"

"ansi-styles@2.2.1":
  "integrity" "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-2.2.1.tgz"
  "version" "2.2.1"

"ansi-styles@3.2.1":
  "integrity" "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "1.9.3"

"antd@^3.13.2":
  "integrity" "sha512-7DjaXAUig51kzVw9T9Mi4slmq0eFl6qGk7t3kjA5t3Sv/Yn2llwNWT0lJDbseooesRRWeFLNByfZV37cUCRJYQ=="
  "resolved" "https://registry.npmjs.org/antd/-/antd-3.26.13.tgz"
  "version" "3.26.13"
  dependencies:
    "@ant-design/create-react-context" "0.2.5"
    "@ant-design/icons" "2.1.1"
    "@ant-design/icons-react" "2.0.1"
    "@types/react-slick" "0.23.4"
    "array-tree-filter" "2.1.0"
    "babel-runtime" "6.26.0"
    "classnames" "2.2.6"
    "copy-to-clipboard" "3.3.1"
    "css-animation" "1.6.1"
    "dom-closest" "0.2.0"
    "enquire.js" "2.1.6"
    "is-mobile" "2.2.1"
    "lodash" "4.17.15"
    "moment" "2.24.0"
    "omit.js" "1.0.2"
    "prop-types" "15.7.2"
    "raf" "3.4.1"
    "rc-animate" "2.10.3"
    "rc-calendar" "9.15.9"
    "rc-cascader" "0.17.5"
    "rc-checkbox" "2.1.8"
    "rc-collapse" "1.11.8"
    "rc-dialog" "7.6.0"
    "rc-drawer" "3.1.3"
    "rc-dropdown" "2.4.1"
    "rc-editor-mention" "1.1.13"
    "rc-form" "2.4.11"
    "rc-input-number" "4.5.6"
    "rc-mentions" "0.4.2"
    "rc-menu" "7.5.5"
    "rc-notification" "3.3.1"
    "rc-pagination" "1.20.14"
    "rc-progress" "2.5.2"
    "rc-rate" "2.5.1"
    "rc-resize-observer" "0.1.3"
    "rc-select" "9.2.3"
    "rc-slider" "8.7.1"
    "rc-steps" "3.5.0"
    "rc-switch" "1.9.0"
    "rc-table" "6.10.13"
    "rc-tabs" "9.7.0"
    "rc-time-picker" "3.7.3"
    "rc-tooltip" "3.7.3"
    "rc-tree" "2.1.3"
    "rc-tree-select" "2.9.4"
    "rc-trigger" "2.6.5"
    "rc-upload" "2.9.4"
    "rc-util" "4.20.0"
    "react-lazy-load" "3.0.13"
    "react-lifecycles-compat" "3.0.4"
    "react-slick" "0.25.2"
    "resize-observer-polyfill" "1.5.1"
    "shallowequal" "1.1.0"
    "warning" "4.0.3"

"anymatch@2.0.0":
  "integrity" "sha512-5teOsQWABXHHBFP9y3skS5P3d/WfWXpv3FUpy+LorMrNYaT9pI4oLMQX7jzQ2KklNpGpWHzdCXTDT2Y3XGlZBw=="
  "resolved" "https://registry.npmjs.org/anymatch/-/anymatch-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "micromatch" "3.1.10"
    "normalize-path" "2.1.1"

"append-transform@0.4.0":
  "integrity" "sha1-126/jKlNJ24keja61EpLdKthGZE="
  "resolved" "https://registry.npmjs.org/append-transform/-/append-transform-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "default-require-extensions" "1.0.0"

"aproba@1.2.0":
  "integrity" "sha512-Y9J6ZjXtoYh8RnXVCMOU/ttDmk1aBjunq9vO0ta5x85WDQiQfUF9sIPBITdbiiIVcBo03Hi3jMxigBtsddlXRw=="
  "resolved" "https://registry.npmjs.org/aproba/-/aproba-1.2.0.tgz"
  "version" "1.2.0"

"argparse@1.0.10":
  "integrity" "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg=="
  "resolved" "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "sprintf-js" "1.0.3"

"aria-query@3.0.0":
  "integrity" "sha1-ZbP8wcoRVajJrmTW7uKX8V1RM8w="
  "resolved" "https://registry.npmjs.org/aria-query/-/aria-query-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "ast-types-flow" "0.0.7"
    "commander" "2.20.3"

"arr-diff@2.0.0":
  "integrity" "sha1-jzuCf5Vai9ZpaX5KQlasPOrjVs8="
  "resolved" "https://registry.npmjs.org/arr-diff/-/arr-diff-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "arr-flatten" "1.1.0"

"arr-diff@4.0.0":
  "integrity" "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA="
  "resolved" "https://registry.npmjs.org/arr-diff/-/arr-diff-4.0.0.tgz"
  "version" "4.0.0"

"arr-flatten@1.1.0":
  "integrity" "sha512-L3hKV5R/p5o81R7O02IGnwpDmkp6E982XhtbuwSe3O4qOtMMMtodicASA1Cny2U+aCXcNpml+m4dPsvsJ3jatg=="
  "resolved" "https://registry.npmjs.org/arr-flatten/-/arr-flatten-1.1.0.tgz"
  "version" "1.1.0"

"arr-union@3.1.0":
  "integrity" "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ="
  "resolved" "https://registry.npmjs.org/arr-union/-/arr-union-3.1.0.tgz"
  "version" "3.1.0"

"array-equal@1.0.0":
  "integrity" "sha1-jCpe8kcv2ep0KwTHenUJO6J1fJM="
  "resolved" "https://registry.npmjs.org/array-equal/-/array-equal-1.0.0.tgz"
  "version" "1.0.0"

"array-filter@0.0.1":
  "integrity" "sha1-fajPLiZijtcygDWB/SH2fKzS7uw="
  "resolved" "https://registry.npmjs.org/array-filter/-/array-filter-0.0.1.tgz"
  "version" "0.0.1"

"array-flatten@1.1.1":
  "integrity" "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI="
  "resolved" "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz"
  "version" "1.1.1"

"array-flatten@2.1.2":
  "integrity" "sha512-hNfzcOV8W4NdualtqBFPyVO+54DSJuZGY9qT4pRroB6S9e3iiido2ISIC5h9R2sPJ8H3FHCIiEnsv1lPXO3KtQ=="
  "resolved" "https://registry.npmjs.org/array-flatten/-/array-flatten-2.1.2.tgz"
  "version" "2.1.2"

"array-includes@3.1.1":
  "integrity" "sha512-c2VXaCHl7zPsvpkFsw4nxvFie4fh1ur9bpcgsVkIjqn0H/Xwdg+7fv3n2r/isyS8EBj5b06M9kHyZuIr4El6WQ=="
  "resolved" "https://registry.npmjs.org/array-includes/-/array-includes-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "define-properties" "1.1.3"
    "es-abstract" "1.17.4"
    "is-string" "1.0.5"

"array-map@0.0.0":
  "integrity" "sha1-iKK6tz0c97zVwbEYoAP2b2ZfpmI="
  "resolved" "https://registry.npmjs.org/array-map/-/array-map-0.0.0.tgz"
  "version" "0.0.0"

"array-reduce@0.0.0":
  "integrity" "sha1-FziZ0//Rx9k4PkR5Ul2+J4yrXys="
  "resolved" "https://registry.npmjs.org/array-reduce/-/array-reduce-0.0.0.tgz"
  "version" "0.0.0"

"array-tree-filter@2.1.0":
  "integrity" "sha512-4ROwICNlNw/Hqa9v+rk5h22KjmzB1JGTMVKP2AKJBOCgb0yL0ASf0+YvCcLNNwquOHNX48jkeZIJ3a+oOQqKcw=="
  "resolved" "https://registry.npmjs.org/array-tree-filter/-/array-tree-filter-2.1.0.tgz"
  "version" "2.1.0"

"array-union@1.0.2":
  "integrity" "sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk="
  "resolved" "https://registry.npmjs.org/array-union/-/array-union-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "array-uniq" "1.0.3"

"array-uniq@1.0.3":
  "integrity" "sha1-r2rId6Jcx/dOBYiUdThY39sk/bY="
  "resolved" "https://registry.npmjs.org/array-uniq/-/array-uniq-1.0.3.tgz"
  "version" "1.0.3"

"array-unique@0.2.1":
  "integrity" "sha1-odl8yvy8JiXMcPrc6zalDFiwGlM="
  "resolved" "https://registry.npmjs.org/array-unique/-/array-unique-0.2.1.tgz"
  "version" "0.2.1"

"array-unique@0.3.2":
  "integrity" "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg="
  "resolved" "https://registry.npmjs.org/array-unique/-/array-unique-0.3.2.tgz"
  "version" "0.3.2"

"arrify@1.0.1":
  "integrity" "sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0="
  "resolved" "https://registry.npmjs.org/arrify/-/arrify-1.0.1.tgz"
  "version" "1.0.1"

"asap@2.0.6":
  "integrity" "sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY="
  "resolved" "https://registry.npmjs.org/asap/-/asap-2.0.6.tgz"
  "version" "2.0.6"

"asn1.js@4.10.1":
  "integrity" "sha512-p32cOF5q0Zqs9uBiONKYLm6BClCoBCM5O9JfeUSlnQLBTxYdTK+pW+nXflm8UkKd2UYlEbYz5qEi0JuZR9ckSw=="
  "resolved" "https://registry.npmjs.org/asn1.js/-/asn1.js-4.10.1.tgz"
  "version" "4.10.1"
  dependencies:
    "bn.js" "4.11.8"
    "inherits" "2.0.4"
    "minimalistic-assert" "1.0.1"

"asn1@0.2.4":
  "integrity" "sha512-jxwzQpLQjSmWXgwaCZE9Nz+glAG01yF1QnWgbhGwHI5A6FRIEY6IVqtHhIepHqI7/kyEyQEagBC5mBEFlIYvdg=="
  "resolved" "https://registry.npmjs.org/asn1/-/asn1-0.2.4.tgz"
  "version" "0.2.4"
  dependencies:
    "safer-buffer" "2.1.2"

"assert-plus@1.0.0":
  "integrity" "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU="
  "resolved" "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz"
  "version" "1.0.0"

"assert@1.5.0":
  "integrity" "sha512-EDsgawzwoun2CZkCgtxJbv392v4nbk9XDD06zI+kQYoBM/3RBWLlEyJARDOmhAAosBjWACEkKL6S+lIZtcAubA=="
  "resolved" "https://registry.npmjs.org/assert/-/assert-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "object-assign" "4.1.1"
    "util" "0.10.3"

"assign-symbols@1.0.0":
  "integrity" "sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c="
  "resolved" "https://registry.npmjs.org/assign-symbols/-/assign-symbols-1.0.0.tgz"
  "version" "1.0.0"

"ast-types-flow@0.0.7":
  "integrity" "sha1-9wtzXGvKGlycItmCw+Oef+ujva0="
  "resolved" "https://registry.npmjs.org/ast-types-flow/-/ast-types-flow-0.0.7.tgz"
  "version" "0.0.7"

"astral-regex@1.0.0":
  "integrity" "sha512-+Ryf6g3BKoRc7jfp7ad8tM4TtMiaWvbF/1/sQcZPkkS7ag3D5nMBCe2UfOTONtAkaG0tO0ij3C5Lwmf1EiyjHg=="
  "resolved" "https://registry.npmjs.org/astral-regex/-/astral-regex-1.0.0.tgz"
  "version" "1.0.0"

"async-each@1.0.3":
  "integrity" "sha512-z/WhQ5FPySLdvREByI2vZiTWwCnF0moMJ1hK9YQwDTHKh6I7/uSckMetoRGb5UBZPC1z0jlw+n/XCgjeH7y1AQ=="
  "resolved" "https://registry.npmjs.org/async-each/-/async-each-1.0.3.tgz"
  "version" "1.0.3"

"async-limiter@1.0.1":
  "integrity" "sha512-csOlWGAcRFJaI6m+F2WKdnMKr4HhdhFVBk0H/QbJFMCr+uO2kwohwXQPxw/9OCxp05r5ghVBFSyioixx3gfkNQ=="
  "resolved" "https://registry.npmjs.org/async-limiter/-/async-limiter-1.0.1.tgz"
  "version" "1.0.1"

"async-validator@1.11.5":
  "integrity" "sha512-XNtCsMAeAH1pdLMEg1z8/Bb3a8cdCbui9QbJATRFHHHW5kT6+NPI3zSVQUXgikTFITzsg+kYY5NTWhM2Orwt9w=="
  "resolved" "https://registry.npmjs.org/async-validator/-/async-validator-1.11.5.tgz"
  "version" "1.11.5"

"async@2.6.3":
  "integrity" "sha512-zflvls11DCy+dQWzTW2dzuilv8Z5X/pjfmZOWba6TNIVDm+2UDaJmXSOXlasHKfNBs8oo3M0aT50fDEWfKZjXg=="
  "resolved" "https://registry.npmjs.org/async/-/async-2.6.3.tgz"
  "version" "2.6.3"
  dependencies:
    "lodash" "4.17.15"

"asynckit@0.4.0":
  "integrity" "sha1-x57Zf380y48robyXkLzDZkdLS3k="
  "resolved" "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  "version" "0.4.0"

"atob@2.1.2":
  "integrity" "sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg=="
  "resolved" "https://registry.npmjs.org/atob/-/atob-2.1.2.tgz"
  "version" "2.1.2"

"autoprefixer@9.7.4":
  "integrity" "sha512-g0Ya30YrMBAEZk60lp+qfX5YQllG+S5W3GYCFvyHTvhOki0AEQJLPEcIuGRsqVwLi8FvXPVtwTGhfr38hVpm0g=="
  "resolved" "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.7.4.tgz"
  "version" "9.7.4"
  dependencies:
    "browserslist" "4.9.1"
    "caniuse-lite" "1.0.30001032"
    "chalk" "2.4.2"
    "normalize-range" "0.1.2"
    "num2fraction" "1.2.2"
    "postcss" "7.0.27"
    "postcss-value-parser" "4.0.3"

"aws-sign2@0.7.0":
  "integrity" "sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg="
  "resolved" "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.7.0.tgz"
  "version" "0.7.0"

"aws4@1.9.1":
  "integrity" "sha512-wMHVg2EOHaMRxbzgFJ9gtjOOCrI80OHLG14rxi28XwOW8ux6IiEbRCGGGqCtdAIg4FQCbW20k9RsT4y3gJlFug=="
  "resolved" "https://registry.npmjs.org/aws4/-/aws4-1.9.1.tgz"
  "version" "1.9.1"

"axios@^0.18.0":
  "integrity" "sha512-0BfJq4NSfQXd+SkFdrvFbG7addhYSBA2mQwISr46pD6E5iqkWg02RAs8vyTT/j0RTnoYmeXauBuSv1qKwR179g=="
  "resolved" "https://registry.npmjs.org/axios/-/axios-0.18.1.tgz"
  "version" "0.18.1"
  dependencies:
    "follow-redirects" "1.5.10"
    "is-buffer" "2.0.4"

"axobject-query@2.1.2":
  "integrity" "sha512-ICt34ZmrVt8UQnvPl6TVyDTkmhXmAyAT4Jh5ugfGUX4MOrZ+U/ZY6/sdylRw3qGNr9Ub5AJsaHeDMzNLehRdOQ=="
  "resolved" "https://registry.npmjs.org/axobject-query/-/axobject-query-2.1.2.tgz"
  "version" "2.1.2"

"babel-code-frame@6.26.0":
  "integrity" "sha1-Y/1D99weO7fONZR9uP42mj9Yx0s="
  "resolved" "https://registry.npmjs.org/babel-code-frame/-/babel-code-frame-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "chalk" "1.1.3"
    "esutils" "2.0.3"
    "js-tokens" "3.0.2"

"babel-core@6.26.3":
  "integrity" "sha512-6jyFLuDmeidKmUEb3NM+/yawG0M2bDZ9Z1qbZP59cyHLz8kYGKYwpJP0UwUKKUiTRNvxfLesJnTedqczP7cTDA=="
  "resolved" "https://registry.npmjs.org/babel-core/-/babel-core-6.26.3.tgz"
  "version" "6.26.3"
  dependencies:
    "babel-code-frame" "6.26.0"
    "babel-generator" "6.26.1"
    "babel-helpers" "6.24.1"
    "babel-messages" "6.23.0"
    "babel-register" "6.26.0"
    "babel-runtime" "6.26.0"
    "babel-template" "6.26.0"
    "babel-traverse" "6.26.0"
    "babel-types" "6.26.0"
    "babylon" "6.18.0"
    "convert-source-map" "1.7.0"
    "debug" "2.6.9"
    "json5" "0.5.1"
    "lodash" "4.17.15"
    "minimatch" "3.0.4"
    "path-is-absolute" "1.0.1"
    "private" "0.1.8"
    "slash" "1.0.0"
    "source-map" "0.5.7"

"babel-core@7.0.0-bridge.0":
  "integrity" "sha512-poPX9mZH/5CSanm50Q+1toVci6pv5KSRv/5TWCwtzQS5XEwn40BcCrgIeMFWP9CKKIniKXNxoIOnOq4VVlGXhg=="
  "resolved" "https://registry.npmjs.org/babel-core/-/babel-core-7.0.0-bridge.0.tgz"
  "version" "7.0.0-bridge.0"

"babel-eslint@9.0.0":
  "integrity" "sha512-itv1MwE3TMbY0QtNfeL7wzak1mV47Uy+n6HtSOO4Xd7rvmO+tsGQSgyOEEgo6Y2vHZKZphaoelNeSVj4vkLA1g=="
  "resolved" "https://registry.npmjs.org/babel-eslint/-/babel-eslint-9.0.0.tgz"
  "version" "9.0.0"
  dependencies:
    "@babel/code-frame" "7.8.3"
    "@babel/parser" "7.8.7"
    "@babel/traverse" "7.8.6"
    "@babel/types" "7.8.7"
    "eslint-scope" "3.7.1"
    "eslint-visitor-keys" "1.1.0"

"babel-extract-comments@1.0.0":
  "integrity" "sha512-qWWzi4TlddohA91bFwgt6zO/J0X+io7Qp184Fw0m2JYRSTZnJbFR8+07KmzudHCZgOiKRCrjhylwv9Xd8gfhVQ=="
  "resolved" "https://registry.npmjs.org/babel-extract-comments/-/babel-extract-comments-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "babylon" "6.18.0"

"babel-generator@6.26.1":
  "integrity" "sha512-HyfwY6ApZj7BYTcJURpM5tznulaBvyio7/0d4zFOeMPUmfxkCjHocCuoLa2SAGzBI8AREcH3eP3758F672DppA=="
  "resolved" "https://registry.npmjs.org/babel-generator/-/babel-generator-6.26.1.tgz"
  "version" "6.26.1"
  dependencies:
    "babel-messages" "6.23.0"
    "babel-runtime" "6.26.0"
    "babel-types" "6.26.0"
    "detect-indent" "4.0.0"
    "jsesc" "1.3.0"
    "lodash" "4.17.15"
    "source-map" "0.5.7"
    "trim-right" "1.0.1"

"babel-helpers@6.24.1":
  "integrity" "sha1-NHHenK7DiOXIUOWX5Yom3fN2ArI="
  "resolved" "https://registry.npmjs.org/babel-helpers/-/babel-helpers-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "6.26.0"
    "babel-template" "6.26.0"

"babel-jest@23.6.0":
  "integrity" "sha512-lqKGG6LYXYu+DQh/slrQ8nxXQkEkhugdXsU6St7GmhVS7Ilc/22ArwqXNJrf0QaOBjZB0360qZMwXqDYQHXaew=="
  "resolved" "https://registry.npmjs.org/babel-jest/-/babel-jest-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "babel-plugin-istanbul" "4.1.6"
    "babel-preset-jest" "23.2.0"

"babel-loader@8.0.5":
  "integrity" "sha512-NTnHnVRd2JnRqPC0vW+iOQWU5pchDbYXsG2E6DMXEpMfUcQKclF9gmf3G3ZMhzG7IG9ji4coL0cm+FxeWxDpnw=="
  "resolved" "https://registry.npmjs.org/babel-loader/-/babel-loader-8.0.5.tgz"
  "version" "8.0.5"
  dependencies:
    "find-cache-dir" "2.1.0"
    "loader-utils" "1.4.0"
    "mkdirp" "0.5.1"
    "util.promisify" "1.0.1"

"babel-messages@6.23.0":
  "integrity" "sha1-8830cDhYA1sqKVHG7F7fbGLyYw4="
  "resolved" "https://registry.npmjs.org/babel-messages/-/babel-messages-6.23.0.tgz"
  "version" "6.23.0"
  dependencies:
    "babel-runtime" "6.26.0"

"babel-plugin-dynamic-import-node@2.2.0":
  "integrity" "sha512-fP899ELUnTaBcIzmrW7nniyqqdYWrWuJUyPWHxFa/c7r7hS6KC8FscNfLlBNIoPSc55kYMGEEKjPjJGCLbE1qA=="
  "resolved" "https://registry.npmjs.org/babel-plugin-dynamic-import-node/-/babel-plugin-dynamic-import-node-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "object.assign" "4.1.0"

"babel-plugin-dynamic-import-node@2.3.0":
  "integrity" "sha512-o6qFkpeQEBxcqt0XYlWzAVxNCSCZdUgcR8IRlhD/8DylxjjO4foPcvTW0GGKa/cVt3rvxZ7o5ippJ+/0nvLhlQ=="
  "resolved" "https://registry.npmjs.org/babel-plugin-dynamic-import-node/-/babel-plugin-dynamic-import-node-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "object.assign" "4.1.0"

"babel-plugin-import@^1.11.0":
  "integrity" "sha512-bHU8m0SrY89ub2hBBuYjbennOeH0YUYkVpH6jxKFk0uD8rhN+0jNHIPtXnac+Vn7N/hgkLGGDcIoYK7je3Hhew=="
  "resolved" "https://registry.npmjs.org/babel-plugin-import/-/babel-plugin-import-1.13.0.tgz"
  "version" "1.13.0"
  dependencies:
    "@babel/helper-module-imports" "7.8.3"
    "@babel/runtime" "7.8.7"

"babel-plugin-istanbul@4.1.6":
  "integrity" "sha512-PWP9FQ1AhZhS01T/4qLSKoHGY/xvkZdVBGlKM/HuxxS3+sC66HhTNR7+MpbO/so/cz/wY94MeSWJuP1hXIPfwQ=="
  "resolved" "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-4.1.6.tgz"
  "version" "4.1.6"
  dependencies:
    "babel-plugin-syntax-object-rest-spread" "6.13.0"
    "find-up" "2.1.0"
    "istanbul-lib-instrument" "1.10.2"
    "test-exclude" "4.2.3"

"babel-plugin-jest-hoist@23.2.0":
  "integrity" "sha1-5h+uBaHKiAGq3uV6bWa4zvr0QWc="
  "resolved" "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-23.2.0.tgz"
  "version" "23.2.0"

"babel-plugin-macros@2.5.0":
  "integrity" "sha512-BWw0lD0kVZAXRD3Od1kMrdmfudqzDzYv2qrN3l2ISR1HVp1EgLKfbOrYV9xmY5k3qx3RIu5uPAUZZZHpo0o5Iw=="
  "resolved" "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "cosmiconfig" "5.2.1"
    "resolve" "1.10.0"

"babel-plugin-named-asset-import@0.3.6":
  "integrity" "sha512-1aGDUfL1qOOIoqk9QKGIo2lANk+C7ko/fqH0uIyC71x3PEGz0uVP8ISgfEsFuG+FKmjHTvFK/nNM8dowpmUxLA=="
  "resolved" "https://registry.npmjs.org/babel-plugin-named-asset-import/-/babel-plugin-named-asset-import-0.3.6.tgz"
  "version" "0.3.6"

"babel-plugin-syntax-object-rest-spread@6.13.0":
  "integrity" "sha1-/WU28rzhODb/o6VFjEkDpZe7O/U="
  "resolved" "https://registry.npmjs.org/babel-plugin-syntax-object-rest-spread/-/babel-plugin-syntax-object-rest-spread-6.13.0.tgz"
  "version" "6.13.0"

"babel-plugin-transform-object-rest-spread@6.26.0":
  "integrity" "sha1-DzZpLVD+9rfi1LOsFHgTepY7ewY="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-object-rest-spread/-/babel-plugin-transform-object-rest-spread-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-plugin-syntax-object-rest-spread" "6.13.0"
    "babel-runtime" "6.26.0"

"babel-plugin-transform-react-remove-prop-types@0.4.24":
  "integrity" "sha512-eqj0hVcJUR57/Ug2zE1Yswsw4LhuqqHhD+8v120T1cl3kjg76QwtyBrdIk4WVwK+lAhBJVYCd/v+4nc4y+8JsA=="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-react-remove-prop-types/-/babel-plugin-transform-react-remove-prop-types-0.4.24.tgz"
  "version" "0.4.24"

"babel-polyfill@^6.26.0":
  "integrity" "sha1-N5k3q8Z9eJWXCtxiHyhM2WbPIVM="
  "resolved" "https://registry.npmjs.org/babel-polyfill/-/babel-polyfill-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "6.26.0"
    "core-js" "2.6.11"
    "regenerator-runtime" "0.10.5"

"babel-preset-jest@23.2.0":
  "integrity" "sha1-jsegOhOPABoaj7HoETZSvxpV2kY="
  "resolved" "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-23.2.0.tgz"
  "version" "23.2.0"
  dependencies:
    "babel-plugin-jest-hoist" "23.2.0"
    "babel-plugin-syntax-object-rest-spread" "6.13.0"

"babel-preset-react-app@7.0.2":
  "integrity" "sha512-mwCk/u2wuiO8qQqblN5PlDa44taY0acq7hw6W+a70W522P7a9mIcdggL1fe5/LgAT7tqCq46q9wwhqaMoYKslQ=="
  "resolved" "https://registry.npmjs.org/babel-preset-react-app/-/babel-preset-react-app-7.0.2.tgz"
  "version" "7.0.2"
  dependencies:
    "@babel/core" "7.2.2"
    "@babel/plugin-proposal-class-properties" "7.3.0"
    "@babel/plugin-proposal-decorators" "7.3.0"
    "@babel/plugin-proposal-object-rest-spread" "7.3.2"
    "@babel/plugin-syntax-dynamic-import" "7.2.0"
    "@babel/plugin-transform-classes" "7.2.2"
    "@babel/plugin-transform-destructuring" "7.3.2"
    "@babel/plugin-transform-flow-strip-types" "7.2.3"
    "@babel/plugin-transform-react-constant-elements" "7.2.0"
    "@babel/plugin-transform-react-display-name" "7.2.0"
    "@babel/plugin-transform-runtime" "7.2.0"
    "@babel/preset-env" "7.3.1"
    "@babel/preset-react" "7.0.0"
    "@babel/preset-typescript" "7.1.0"
    "@babel/runtime" "7.3.1"
    "babel-loader" "8.0.5"
    "babel-plugin-dynamic-import-node" "2.2.0"
    "babel-plugin-macros" "2.5.0"
    "babel-plugin-transform-react-remove-prop-types" "0.4.24"

"babel-register@6.26.0":
  "integrity" "sha1-btAhFz4vy0htestFxgCahW9kcHE="
  "resolved" "https://registry.npmjs.org/babel-register/-/babel-register-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-core" "6.26.3"
    "babel-runtime" "6.26.0"
    "core-js" "2.6.11"
    "home-or-tmp" "2.0.0"
    "lodash" "4.17.15"
    "mkdirp" "0.5.1"
    "source-map-support" "0.4.18"

"babel-runtime@6.26.0":
  "integrity" "sha1-llxwWGaOgrVde/4E/yM3vItWR/4="
  "resolved" "https://registry.npmjs.org/babel-runtime/-/babel-runtime-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "core-js" "2.6.11"
    "regenerator-runtime" "0.11.1"

"babel-template@6.26.0":
  "integrity" "sha1-3gPi0WOWsGn0bdn/+FIfsaDjXgI="
  "resolved" "https://registry.npmjs.org/babel-template/-/babel-template-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "6.26.0"
    "babel-traverse" "6.26.0"
    "babel-types" "6.26.0"
    "babylon" "6.18.0"
    "lodash" "4.17.15"

"babel-traverse@6.26.0":
  "integrity" "sha1-RqnL1+3MYsjlwGTi0tjQ9ANXZu4="
  "resolved" "https://registry.npmjs.org/babel-traverse/-/babel-traverse-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-code-frame" "6.26.0"
    "babel-messages" "6.23.0"
    "babel-runtime" "6.26.0"
    "babel-types" "6.26.0"
    "babylon" "6.18.0"
    "debug" "2.6.9"
    "globals" "9.18.0"
    "invariant" "2.2.4"
    "lodash" "4.17.15"

"babel-types@6.26.0":
  "integrity" "sha1-o7Bz+Uq0nrb6Vc1lInozQ4BjJJc="
  "resolved" "https://registry.npmjs.org/babel-types/-/babel-types-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "6.26.0"
    "esutils" "2.0.3"
    "lodash" "4.17.15"
    "to-fast-properties" "1.0.3"

"babylon@6.18.0":
  "integrity" "sha512-q/UEjfGJ2Cm3oKV71DJz9d25TPnq5rhBVL2Q4fA5wcC3jcrdn7+SssEybFIxwAvvP+YCsCYNKughoF33GxgycQ=="
  "resolved" "https://registry.npmjs.org/babylon/-/babylon-6.18.0.tgz"
  "version" "6.18.0"

"balanced-match@1.0.0":
  "integrity" "sha1-ibTRmasr7kneFk6gK4nORi1xt2c="
  "resolved" "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.0.tgz"
  "version" "1.0.0"

"base@0.11.2":
  "integrity" "sha512-5T6P4xPgpp0YDFvSWwEZ4NoE3aM4QBQXDzmVbraCkFj8zHM+mba8SyqB5DbZWyR7mYHo6Y7BdQo3MoA4m0TeQg=="
  "resolved" "https://registry.npmjs.org/base/-/base-0.11.2.tgz"
  "version" "0.11.2"
  dependencies:
    "cache-base" "1.0.1"
    "class-utils" "0.3.6"
    "component-emitter" "1.3.0"
    "define-property" "1.0.0"
    "isobject" "3.0.1"
    "mixin-deep" "1.3.2"
    "pascalcase" "0.1.1"

"base64-js@1.3.1":
  "integrity" "sha512-mLQ4i2QO1ytvGWFWmcngKO//JXAQueZvwEKtjgQFM4jIK0kU+ytMfplL8j+n5mspOfjHwoAg+9yhb7BwAHm36g=="
  "resolved" "https://registry.npmjs.org/base64-js/-/base64-js-1.3.1.tgz"
  "version" "1.3.1"

"batch@0.6.1":
  "integrity" "sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY="
  "resolved" "https://registry.npmjs.org/batch/-/batch-0.6.1.tgz"
  "version" "0.6.1"

"bcrypt-pbkdf@1.0.2":
  "integrity" "sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4="
  "resolved" "https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "tweetnacl" "0.14.5"

"bfj@6.1.1":
  "integrity" "sha512-+GUNvzHR4nRyGybQc2WpNJL4MJazMuvf92ueIyA0bIkPRwhhQu3IfZQ2PSoVPpCBJfmoSdOxu5rnotfFLlvYRQ=="
  "resolved" "https://registry.npmjs.org/bfj/-/bfj-6.1.1.tgz"
  "version" "6.1.1"
  dependencies:
    "bluebird" "3.7.2"
    "check-types" "7.4.0"
    "hoopy" "0.1.4"
    "tryer" "1.0.1"

"big.js@5.2.2":
  "integrity" "sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ=="
  "resolved" "https://registry.npmjs.org/big.js/-/big.js-5.2.2.tgz"
  "version" "5.2.2"

"binary-extensions@1.13.1":
  "integrity" "sha512-Un7MIEDdUC5gNpcGDV97op1Ywk748MpHcFTHoYs6qnj1Z3j7I53VG3nwZhKzoBZmbdRNnb6WRdFlwl7tSDuZGw=="
  "resolved" "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.13.1.tgz"
  "version" "1.13.1"

"bluebird@3.7.2":
  "integrity" "sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg=="
  "resolved" "https://registry.npmjs.org/bluebird/-/bluebird-3.7.2.tgz"
  "version" "3.7.2"

"bn.js@4.11.8":
  "integrity" "sha512-ItfYfPLkWHUjckQCk8xC+LwxgK8NYcXywGigJgSwOP8Y2iyWT4f2vsZnoOXTTbo+o5yXmIUJ4gn5538SO5S3gA=="
  "resolved" "https://registry.npmjs.org/bn.js/-/bn.js-4.11.8.tgz"
  "version" "4.11.8"

"body-parser@1.19.0":
  "integrity" "sha512-dhEPs72UPbDnAQJ9ZKMNTP6ptJaionhP5cBb541nXPlW60Jepo9RV/a4fX4XWW9CuFNK22krhrj1+rgzifNCsw=="
  "resolved" "https://registry.npmjs.org/body-parser/-/body-parser-1.19.0.tgz"
  "version" "1.19.0"
  dependencies:
    "bytes" "3.1.0"
    "content-type" "1.0.4"
    "debug" "2.6.9"
    "depd" "1.1.2"
    "http-errors" "1.7.2"
    "iconv-lite" "0.4.24"
    "on-finished" "2.3.0"
    "qs" "6.7.0"
    "raw-body" "2.4.0"
    "type-is" "1.6.18"

"bonjour@3.5.0":
  "integrity" "sha1-jokKGD2O6aI5OzhExpGkK897yfU="
  "resolved" "https://registry.npmjs.org/bonjour/-/bonjour-3.5.0.tgz"
  "version" "3.5.0"
  dependencies:
    "array-flatten" "2.1.2"
    "deep-equal" "1.1.1"
    "dns-equal" "1.0.0"
    "dns-txt" "2.0.2"
    "multicast-dns" "6.2.3"
    "multicast-dns-service-types" "1.1.0"

"boolbase@1.0.0":
  "integrity" "sha1-aN/1++YMUes3cl6p4+0xDcwed24="
  "resolved" "https://registry.npmjs.org/boolbase/-/boolbase-1.0.0.tgz"
  "version" "1.0.0"

"brace-expansion@1.1.11":
  "integrity" "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA=="
  "resolved" "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "balanced-match" "1.0.0"
    "concat-map" "0.0.1"

"braces@1.8.5":
  "integrity" "sha1-uneWLhLf+WnWt2cR6RS3N4V79qc="
  "resolved" "https://registry.npmjs.org/braces/-/braces-1.8.5.tgz"
  "version" "1.8.5"
  dependencies:
    "expand-range" "1.8.2"
    "preserve" "0.2.0"
    "repeat-element" "1.1.3"

"braces@2.3.2":
  "integrity" "sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w=="
  "resolved" "https://registry.npmjs.org/braces/-/braces-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "arr-flatten" "1.1.0"
    "array-unique" "0.3.2"
    "extend-shallow" "2.0.1"
    "fill-range" "4.0.0"
    "isobject" "3.0.1"
    "repeat-element" "1.1.3"
    "snapdragon" "0.8.2"
    "snapdragon-node" "2.1.1"
    "split-string" "3.1.0"
    "to-regex" "3.0.2"

"brorand@1.1.0":
  "integrity" "sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8="
  "resolved" "https://registry.npmjs.org/brorand/-/brorand-1.1.0.tgz"
  "version" "1.1.0"

"browser-process-hrtime@1.0.0":
  "integrity" "sha512-9o5UecI3GhkpM6DrXr69PblIuWxPKk9Y0jHBRhdocZ2y7YECBFCsHm79Pr3OyR2AvjhDkabFJaDJMYRazHgsow=="
  "resolved" "https://registry.npmjs.org/browser-process-hrtime/-/browser-process-hrtime-1.0.0.tgz"
  "version" "1.0.0"

"browser-resolve@1.11.3":
  "integrity" "sha512-exDi1BYWB/6raKHmDTCicQfTkqwN5fioMFV4j8BsfMU4R2DK/QfZfK7kOVkmWCNANf0snkBzqGqAJBao9gZMdQ=="
  "resolved" "https://registry.npmjs.org/browser-resolve/-/browser-resolve-1.11.3.tgz"
  "version" "1.11.3"
  dependencies:
    "resolve" "1.1.7"

"browserify-aes@1.2.0":
  "integrity" "sha512-+7CHXqGuspUn/Sl5aO7Ea0xWGAtETPXNSAjHo48JfLdPWcMng33Xe4znFvQweqc/uzk5zSOI3H52CYnjCfb5hA=="
  "resolved" "https://registry.npmjs.org/browserify-aes/-/browserify-aes-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "buffer-xor" "1.0.3"
    "cipher-base" "1.0.4"
    "create-hash" "1.2.0"
    "evp_bytestokey" "1.0.3"
    "inherits" "2.0.4"
    "safe-buffer" "5.2.0"

"browserify-cipher@1.0.1":
  "integrity" "sha512-sPhkz0ARKbf4rRQt2hTpAHqn47X3llLkUGn+xEJzLjwY8LRs2p0v7ljvI5EyoRO/mexrNunNECisZs+gw2zz1w=="
  "resolved" "https://registry.npmjs.org/browserify-cipher/-/browserify-cipher-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "browserify-aes" "1.2.0"
    "browserify-des" "1.0.2"
    "evp_bytestokey" "1.0.3"

"browserify-des@1.0.2":
  "integrity" "sha512-BioO1xf3hFwz4kc6iBhI3ieDFompMhrMlnDFC4/0/vd5MokpuAc3R+LYbwTA9A5Yc9pq9UYPqffKpW2ObuwX5A=="
  "resolved" "https://registry.npmjs.org/browserify-des/-/browserify-des-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "cipher-base" "1.0.4"
    "des.js" "1.0.1"
    "inherits" "2.0.4"
    "safe-buffer" "5.2.0"

"browserify-rsa@4.0.1":
  "integrity" "sha1-IeCr+vbyApzy+vsTNWenAdQTVSQ="
  "resolved" "https://registry.npmjs.org/browserify-rsa/-/browserify-rsa-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "bn.js" "4.11.8"
    "randombytes" "2.1.0"

"browserify-sign@4.0.4":
  "integrity" "sha1-qk62jl17ZYuqa/alfmMMvXqT0pg="
  "resolved" "https://registry.npmjs.org/browserify-sign/-/browserify-sign-4.0.4.tgz"
  "version" "4.0.4"
  dependencies:
    "bn.js" "4.11.8"
    "browserify-rsa" "4.0.1"
    "create-hash" "1.2.0"
    "create-hmac" "1.1.7"
    "elliptic" "6.5.2"
    "inherits" "2.0.4"
    "parse-asn1" "5.1.5"

"browserify-zlib@0.2.0":
  "integrity" "sha512-Z942RysHXmJrhqk88FmKBVq/v5tqmSkDz7p54G/MGyjMnCFFnC79XWNbg+Vta8W6Wb2qtSZTSxIGkJrRpCFEiA=="
  "resolved" "https://registry.npmjs.org/browserify-zlib/-/browserify-zlib-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "pako" "1.0.11"

"browserslist@4.4.1":
  "integrity" "sha512-pEBxEXg7JwaakBXjATYw/D1YZh4QUSCX/Mnd/wnqSRPPSi1U39iDhDoKGoBUcraKdxDlrYqJxSI5nNvD+dWP2A=="
  "resolved" "https://registry.npmjs.org/browserslist/-/browserslist-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "caniuse-lite" "1.0.30001032"
    "electron-to-chromium" "1.3.372"
    "node-releases" "1.1.51"

"browserslist@4.9.1":
  "integrity" "sha512-Q0DnKq20End3raFulq6Vfp1ecB9fh8yUNV55s8sekaDDeqBaCtWlRHCUdaWyUeSSBJM7IbM6HcsyaeYqgeDhnw=="
  "resolved" "https://registry.npmjs.org/browserslist/-/browserslist-4.9.1.tgz"
  "version" "4.9.1"
  dependencies:
    "caniuse-lite" "1.0.30001032"
    "electron-to-chromium" "1.3.372"
    "node-releases" "1.1.51"

"bser@2.1.1":
  "integrity" "sha512-gQxTNE/GAfIIrmHLUE3oJyp5FO6HRBfhjnw4/wMmA63ZGDJnWBmgY/lyQBpnDUkGmAhbSe39tx2d/iTOAfglwQ=="
  "resolved" "https://registry.npmjs.org/bser/-/bser-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "node-int64" "0.4.0"

"buffer-from@1.1.1":
  "integrity" "sha512-MQcXEUbCKtEo7bhqEs6560Hyd4XaovZlO/k9V3hjVUF/zwW7KBVdSK4gIt/bzwS9MbR5qob+F5jusZsb0YQK2A=="
  "resolved" "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.1.tgz"
  "version" "1.1.1"

"buffer-indexof@1.1.1":
  "integrity" "sha512-4/rOEg86jivtPTeOUUT61jJO1Ya1TrR/OkqCSZDyq84WJh3LuuiphBYJN+fm5xufIk4XAFcEwte/8WzC8If/1g=="
  "resolved" "https://registry.npmjs.org/buffer-indexof/-/buffer-indexof-1.1.1.tgz"
  "version" "1.1.1"

"buffer-xor@1.0.3":
  "integrity" "sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk="
  "resolved" "https://registry.npmjs.org/buffer-xor/-/buffer-xor-1.0.3.tgz"
  "version" "1.0.3"

"buffer@4.9.2":
  "integrity" "sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg=="
  "resolved" "https://registry.npmjs.org/buffer/-/buffer-4.9.2.tgz"
  "version" "4.9.2"
  dependencies:
    "base64-js" "1.3.1"
    "ieee754" "1.1.13"
    "isarray" "1.0.0"

"builtin-status-codes@3.0.0":
  "integrity" "sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug="
  "resolved" "https://registry.npmjs.org/builtin-status-codes/-/builtin-status-codes-3.0.0.tgz"
  "version" "3.0.0"

"bytes@3.0.0":
  "integrity" "sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg="
  "resolved" "https://registry.npmjs.org/bytes/-/bytes-3.0.0.tgz"
  "version" "3.0.0"

"bytes@3.1.0":
  "integrity" "sha512-zauLjrfCG+xvoyaqLoV8bLVXXNGC4JqlxFCutSDWA6fJrTo2ZuvLYTqZ7aHBLZSMOopbzwv8f+wZcVzfVTI2Dg=="
  "resolved" "https://registry.npmjs.org/bytes/-/bytes-3.1.0.tgz"
  "version" "3.1.0"

"cacache@11.3.3":
  "integrity" "sha512-p8WcneCytvzPxhDvYp31PD039vi77I12W+/KfR9S8AZbaiARFBCpsPJS+9uhWfeBfeAtW7o/4vt3MUqLkbY6nA=="
  "resolved" "https://registry.npmjs.org/cacache/-/cacache-11.3.3.tgz"
  "version" "11.3.3"
  dependencies:
    "bluebird" "3.7.2"
    "chownr" "1.1.4"
    "figgy-pudding" "3.5.1"
    "glob" "7.1.6"
    "graceful-fs" "4.2.3"
    "lru-cache" "5.1.1"
    "mississippi" "3.0.0"
    "mkdirp" "0.5.1"
    "move-concurrently" "1.0.1"
    "promise-inflight" "1.0.1"
    "rimraf" "2.6.3"
    "ssri" "6.0.1"
    "unique-filename" "1.1.1"
    "y18n" "4.0.0"

"cache-base@1.0.1":
  "integrity" "sha512-AKcdTnFSWATd5/GCPRxr2ChwIJ85CeyrEyjRHlKxQ56d4XJMGym0uAiKn0xbLOGOl3+yRpOTi484dVCEc5AUzQ=="
  "resolved" "https://registry.npmjs.org/cache-base/-/cache-base-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "collection-visit" "1.0.0"
    "component-emitter" "1.3.0"
    "get-value" "2.0.6"
    "has-value" "1.0.0"
    "isobject" "3.0.1"
    "set-value" "2.0.1"
    "to-object-path" "0.3.0"
    "union-value" "1.0.1"
    "unset-value" "1.0.0"

"call-me-maybe@1.0.1":
  "integrity" "sha1-JtII6onje1y95gJQoV8DHBak1ms="
  "resolved" "https://registry.npmjs.org/call-me-maybe/-/call-me-maybe-1.0.1.tgz"
  "version" "1.0.1"

"caller-callsite@2.0.0":
  "integrity" "sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ="
  "resolved" "https://registry.npmjs.org/caller-callsite/-/caller-callsite-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "callsites" "2.0.0"

"caller-path@2.0.0":
  "integrity" "sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ="
  "resolved" "https://registry.npmjs.org/caller-path/-/caller-path-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "caller-callsite" "2.0.0"

"callsites@2.0.0":
  "integrity" "sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA="
  "resolved" "https://registry.npmjs.org/callsites/-/callsites-2.0.0.tgz"
  "version" "2.0.0"

"callsites@3.1.0":
  "integrity" "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ=="
  "resolved" "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz"
  "version" "3.1.0"

"camel-case@3.0.0":
  "integrity" "sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M="
  "resolved" "https://registry.npmjs.org/camel-case/-/camel-case-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "no-case" "2.3.2"
    "upper-case" "1.1.3"

"camelcase@4.1.0":
  "integrity" "sha1-1UVjW+HjPFQmScaRc+Xeas+uNN0="
  "resolved" "https://registry.npmjs.org/camelcase/-/camelcase-4.1.0.tgz"
  "version" "4.1.0"

"camelcase@5.3.1":
  "integrity" "sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg=="
  "resolved" "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz"
  "version" "5.3.1"

"caniuse-api@3.0.0":
  "integrity" "sha512-bsTwuIg/BZZK/vreVTYYbSWoe2F+71P7K5QGEX+pT250DZbfU1MQ5prOKpPR+LL6uWKK3KMwMCAS74QB3Um1uw=="
  "resolved" "https://registry.npmjs.org/caniuse-api/-/caniuse-api-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "browserslist" "4.9.1"
    "caniuse-lite" "1.0.30001032"
    "lodash.memoize" "4.1.2"
    "lodash.uniq" "4.5.0"

"caniuse-lite@1.0.30001032":
  "integrity" "sha512-8joOm7BwcpEN4BfVHtfh0hBXSAPVYk+eUIcNntGtMkUWy/6AKRCDZINCLe3kB1vHhT2vBxBF85Hh9VlPXi/qjA=="
  "resolved" "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001032.tgz"
  "version" "1.0.30001032"

"capture-exit@1.2.0":
  "integrity" "sha1-HF/MSJ/QqwDU8ax64QcuMXP7q28="
  "resolved" "https://registry.npmjs.org/capture-exit/-/capture-exit-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "rsvp" "3.6.2"

"case-sensitive-paths-webpack-plugin@2.2.0":
  "integrity" "sha512-u5ElzokS8A1pm9vM3/iDgTcI3xqHxuCao94Oz8etI3cf0Tio0p8izkDYbTIn09uP3yUUr6+veaE6IkjnTYS46g=="
  "resolved" "https://registry.npmjs.org/case-sensitive-paths-webpack-plugin/-/case-sensitive-paths-webpack-plugin-2.2.0.tgz"
  "version" "2.2.0"

"caseless@0.12.0":
  "integrity" "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw="
  "resolved" "https://registry.npmjs.org/caseless/-/caseless-0.12.0.tgz"
  "version" "0.12.0"

"chalk@1.1.3":
  "integrity" "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "ansi-styles" "2.2.1"
    "escape-string-regexp" "1.0.5"
    "has-ansi" "2.0.0"
    "strip-ansi" "3.0.1"
    "supports-color" "2.0.0"

"chalk@2.4.2":
  "integrity" "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "3.2.1"
    "escape-string-regexp" "1.0.5"
    "supports-color" "5.5.0"

"chardet@0.7.0":
  "integrity" "sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA=="
  "resolved" "https://registry.npmjs.org/chardet/-/chardet-0.7.0.tgz"
  "version" "0.7.0"

"check-types@7.4.0":
  "integrity" "sha512-YbulWHdfP99UfZ73NcUDlNJhEIDgm9Doq9GhpyXbF+7Aegi3CVV7qqMCKTTqJxlvEvnQBp9IA+dxsGN6xK/nSg=="
  "resolved" "https://registry.npmjs.org/check-types/-/check-types-7.4.0.tgz"
  "version" "7.4.0"

"chokidar@2.1.8":
  "integrity" "sha512-ZmZUazfOzf0Nve7duiCKD23PFSCs4JPoYyccjUFF3aQkQadqBhfzhjkwBH2mNOG9cTBwhamM37EIsIkZw3nRgg=="
  "resolved" "https://registry.npmjs.org/chokidar/-/chokidar-2.1.8.tgz"
  "version" "2.1.8"
  dependencies:
    "anymatch" "2.0.0"
    "async-each" "1.0.3"
    "braces" "2.3.2"
    "glob-parent" "3.1.0"
    "inherits" "2.0.4"
    "is-binary-path" "1.0.1"
    "is-glob" "4.0.1"
    "normalize-path" "3.0.0"
    "path-is-absolute" "1.0.1"
    "readdirp" "2.2.1"
    "upath" "1.2.0"

"chownr@1.1.4":
  "integrity" "sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg=="
  "resolved" "https://registry.npmjs.org/chownr/-/chownr-1.1.4.tgz"
  "version" "1.1.4"

"chrome-trace-event@1.0.2":
  "integrity" "sha512-9e/zx1jw7B4CO+c/RXoCsfg/x1AfUBioy4owYH0bJprEYAx5hRFLRhWBqHAG57D0ZM4H7vxbP7bPe0VwhQRYDQ=="
  "resolved" "https://registry.npmjs.org/chrome-trace-event/-/chrome-trace-event-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "tslib" "1.11.1"

"ci-info@1.6.0":
  "integrity" "sha512-vsGdkwSCDpWmP80ncATX7iea5DWQemg1UgCW5J8tqjU3lYw4FBYuj89J0CTVomA7BEfvSZd84GmHko+MxFQU2A=="
  "resolved" "https://registry.npmjs.org/ci-info/-/ci-info-1.6.0.tgz"
  "version" "1.6.0"

"cipher-base@1.0.4":
  "integrity" "sha512-Kkht5ye6ZGmwv40uUDZztayT2ThLQGfnj/T71N/XzeZeo3nf8foyW7zGTsPYkEya3m5f3cAypH+qe7YOrM1U2Q=="
  "resolved" "https://registry.npmjs.org/cipher-base/-/cipher-base-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "inherits" "2.0.4"
    "safe-buffer" "5.2.0"

"circular-json@0.3.3":
  "integrity" "sha512-UZK3NBx2Mca+b5LsG7bY183pHWt5Y1xts4P3Pz7ENTwGVnJOUWbRb3ocjvX7hx9tq/yTAdclXm9sZ38gNuem4A=="
  "resolved" "https://registry.npmjs.org/circular-json/-/circular-json-0.3.3.tgz"
  "version" "0.3.3"

"class-utils@0.3.6":
  "integrity" "sha512-qOhPa/Fj7s6TY8H8esGu5QNpMMQxz79h+urzrNYN6mn+9BnxlDGf5QZ+XeCDsxSjPqsSR56XOZOJmpeurnLMeg=="
  "resolved" "https://registry.npmjs.org/class-utils/-/class-utils-0.3.6.tgz"
  "version" "0.3.6"
  dependencies:
    "arr-union" "3.1.0"
    "define-property" "0.2.5"
    "isobject" "3.0.1"
    "static-extend" "0.1.2"

"classnames@2.2.6":
  "integrity" "sha512-JR/iSQOSt+LQIWwrwEzJ9uk0xfN3mTVYMwt1Ir5mUcSN6pU+V4zQFFaJsclJbPuAUQH+yfWef6tm7l1quW3C8Q=="
  "resolved" "https://registry.npmjs.org/classnames/-/classnames-2.2.6.tgz"
  "version" "2.2.6"

"clean-css@4.2.3":
  "integrity" "sha512-VcMWDN54ZN/DS+g58HYL5/n4Zrqe8vHJpGA8KdgUXFU4fuP/aHNw8eld9SyEIyabIMJX/0RaY/fplOo5hYLSFA=="
  "resolved" "https://registry.npmjs.org/clean-css/-/clean-css-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "source-map" "0.6.1"

"cli-cursor@2.1.0":
  "integrity" "sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU="
  "resolved" "https://registry.npmjs.org/cli-cursor/-/cli-cursor-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "restore-cursor" "2.0.0"

"cli-width@2.2.0":
  "integrity" "sha1-/xnt6Kml5XkyQUewwR8PvLq+1jk="
  "resolved" "https://registry.npmjs.org/cli-width/-/cli-width-2.2.0.tgz"
  "version" "2.2.0"

"cliui@4.1.0":
  "integrity" "sha512-4FG+RSG9DL7uEwRUZXZn3SS34DiDPfzP0VOiEwtUWlE+AR2EIg+hSyvrIgUUfhdgR/UkAeW2QHgeP+hWrXs7jQ=="
  "resolved" "https://registry.npmjs.org/cliui/-/cliui-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "string-width" "2.1.1"
    "strip-ansi" "4.0.0"
    "wrap-ansi" "2.1.0"

"clone-deep@0.2.4":
  "integrity" "sha1-TnPdCen7lxzDhnDF3O2cGJZIHMY="
  "resolved" "https://registry.npmjs.org/clone-deep/-/clone-deep-0.2.4.tgz"
  "version" "0.2.4"
  dependencies:
    "for-own" "0.1.5"
    "is-plain-object" "2.0.4"
    "kind-of" "3.2.2"
    "lazy-cache" "1.0.4"
    "shallow-clone" "0.1.2"

"clone-deep@2.0.2":
  "integrity" "sha512-SZegPTKjCgpQH63E+eN6mVEEPdQBOUzjyJm5Pora4lrwWRFS8I0QAxV/KD6vV/i0WuijHZWQC1fMsPEdxfdVCQ=="
  "resolved" "https://registry.npmjs.org/clone-deep/-/clone-deep-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "for-own" "1.0.0"
    "is-plain-object" "2.0.4"
    "kind-of" "6.0.3"
    "shallow-clone" "1.0.0"

"clone@2.1.2":
  "integrity" "sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18="
  "resolved" "https://registry.npmjs.org/clone/-/clone-2.1.2.tgz"
  "version" "2.1.2"

"co@4.6.0":
  "integrity" "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ="
  "resolved" "https://registry.npmjs.org/co/-/co-4.6.0.tgz"
  "version" "4.6.0"

"coa@2.0.2":
  "integrity" "sha512-q5/jG+YQnSy4nRTV4F7lPepBJZ8qBNJJDBuJdoejDyLXgmL7IEo+Le2JDZudFTFt7mrCqIRaSjws4ygRCTCAXA=="
  "resolved" "https://registry.npmjs.org/coa/-/coa-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "@types/q" "1.5.2"
    "chalk" "2.4.2"
    "q" "1.5.1"

"code-point-at@1.1.0":
  "integrity" "sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c="
  "resolved" "https://registry.npmjs.org/code-point-at/-/code-point-at-1.1.0.tgz"
  "version" "1.1.0"

"collection-visit@1.0.0":
  "integrity" "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA="
  "resolved" "https://registry.npmjs.org/collection-visit/-/collection-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "map-visit" "1.0.0"
    "object-visit" "1.0.1"

"color-convert@1.9.3":
  "integrity" "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg=="
  "resolved" "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz"
  "version" "1.9.3"
  dependencies:
    "color-name" "1.1.3"

"color-name@1.1.3":
  "integrity" "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="
  "resolved" "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz"
  "version" "1.1.3"

"color-string@1.5.3":
  "integrity" "sha512-dC2C5qeWoYkxki5UAXapdjqO672AM4vZuPGRQfO8b5HKuKGBbKWpITyDYN7TOFKvRW7kOgAn3746clDBMDJyQw=="
  "resolved" "https://registry.npmjs.org/color-string/-/color-string-1.5.3.tgz"
  "version" "1.5.3"
  dependencies:
    "color-name" "1.1.3"
    "simple-swizzle" "0.2.2"

"color@3.1.2":
  "integrity" "sha512-vXTJhHebByxZn3lDvDJYw4lR5+uB3vuoHsuYA5AKuxRVn5wzzIfQKGLBmgdVRHKTJYeK5rvJcHnrd0Li49CFpg=="
  "resolved" "https://registry.npmjs.org/color/-/color-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "color-convert" "1.9.3"
    "color-string" "1.5.3"

"combined-stream@1.0.8":
  "integrity" "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg=="
  "resolved" "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "delayed-stream" "1.0.0"

"commander@2.17.1":
  "integrity" "sha512-wPMUt6FnH2yzG95SA6mzjQOEKUU3aLaDEmzs1ti+1E9h+CsrZghRlqEM/EJ4KscsQVG8uNN4uVreUeT8+drlgg=="
  "resolved" "https://registry.npmjs.org/commander/-/commander-2.17.1.tgz"
  "version" "2.17.1"

"commander@2.19.0":
  "integrity" "sha512-6tvAOO+D6OENvRAh524Dh9jcfKTYDQAqvqezbCW82xj5X0pSrcpxtvRKHLG0yBY6SD7PSDrJaj+0AiOcKVd1Xg=="
  "resolved" "https://registry.npmjs.org/commander/-/commander-2.19.0.tgz"
  "version" "2.19.0"

"commander@2.20.3":
  "integrity" "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ=="
  "resolved" "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz"
  "version" "2.20.3"

"common-tags@1.8.0":
  "integrity" "sha512-6P6g0uetGpW/sdyUy/iQQCbFF0kWVMSIVSyYz7Zgjcgh8mgw8PQzDNZeyZ5DQ2gM7LBoZPHmnjz8rUthkBG5tw=="
  "resolved" "https://registry.npmjs.org/common-tags/-/common-tags-1.8.0.tgz"
  "version" "1.8.0"

"commondir@1.0.1":
  "integrity" "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs="
  "resolved" "https://registry.npmjs.org/commondir/-/commondir-1.0.1.tgz"
  "version" "1.0.1"

"component-classes@1.2.6":
  "integrity" "sha1-xkI5TDYYpNiwuJGe/Mu9kw5c1pE="
  "resolved" "https://registry.npmjs.org/component-classes/-/component-classes-1.2.6.tgz"
  "version" "1.2.6"
  dependencies:
    "component-indexof" "0.0.3"

"component-emitter@1.3.0":
  "integrity" "sha512-Rd3se6QB+sO1TwqZjscQrurpEPIfO0/yYnSin6Q/rD3mOutHvUrCAhJub3r90uNb+SESBuE0QYoB90YdfatsRg=="
  "resolved" "https://registry.npmjs.org/component-emitter/-/component-emitter-1.3.0.tgz"
  "version" "1.3.0"

"component-indexof@0.0.3":
  "integrity" "sha1-EdCRMSI5648yyPJa6csAL/6NPCQ="
  "resolved" "https://registry.npmjs.org/component-indexof/-/component-indexof-0.0.3.tgz"
  "version" "0.0.3"

"compressible@2.0.18":
  "integrity" "sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg=="
  "resolved" "https://registry.npmjs.org/compressible/-/compressible-2.0.18.tgz"
  "version" "2.0.18"
  dependencies:
    "mime-db" "1.43.0"

"compression@1.7.4":
  "integrity" "sha512-jaSIDzP9pZVS4ZfQ+TzvtiWhdpFhE2RDHz8QJkpX9SIpLq88VueF5jJw6t+6CUQcAoA6t+x89MLrWAqpfDE8iQ=="
  "resolved" "https://registry.npmjs.org/compression/-/compression-1.7.4.tgz"
  "version" "1.7.4"
  dependencies:
    "accepts" "1.3.7"
    "bytes" "3.0.0"
    "compressible" "2.0.18"
    "debug" "2.6.9"
    "on-headers" "1.0.2"
    "safe-buffer" "5.1.2"
    "vary" "1.1.2"

"concat-map@0.0.1":
  "integrity" "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="
  "resolved" "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
  "version" "0.0.1"

"concat-stream@1.6.2":
  "integrity" "sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw=="
  "resolved" "https://registry.npmjs.org/concat-stream/-/concat-stream-1.6.2.tgz"
  "version" "1.6.2"
  dependencies:
    "buffer-from" "1.1.1"
    "inherits" "2.0.4"
    "readable-stream" "2.3.7"
    "typedarray" "0.0.6"

"confusing-browser-globals@1.0.9":
  "integrity" "sha512-KbS1Y0jMtyPgIxjO7ZzMAuUpAKMt1SzCL9fsrKsX6b0zJPTaT0SiSPmewwVZg9UAO83HVIlEhZF84LIjZ0lmAw=="
  "resolved" "https://registry.npmjs.org/confusing-browser-globals/-/confusing-browser-globals-1.0.9.tgz"
  "version" "1.0.9"

"connect-history-api-fallback@1.6.0":
  "integrity" "sha512-e54B99q/OUoH64zYYRf3HBP5z24G38h5D3qXu23JGRoigpX5Ss4r9ZnDk3g0Z8uQC2x2lPaJ+UlWBc1ZWBWdLg=="
  "resolved" "https://registry.npmjs.org/connect-history-api-fallback/-/connect-history-api-fallback-1.6.0.tgz"
  "version" "1.6.0"

"console-browserify@1.2.0":
  "integrity" "sha512-ZMkYO/LkF17QvCPqM0gxw8yUzigAOZOSWSHg91FH6orS7vcEj5dVZTidN2fQ14yBSdg97RqhSNwLUXInd52OTA=="
  "resolved" "https://registry.npmjs.org/console-browserify/-/console-browserify-1.2.0.tgz"
  "version" "1.2.0"

"constants-browserify@1.0.0":
  "integrity" "sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U="
  "resolved" "https://registry.npmjs.org/constants-browserify/-/constants-browserify-1.0.0.tgz"
  "version" "1.0.0"

"contains-path@0.1.0":
  "integrity" "sha1-/ozxhP9mcLa67wGp1IYaXL7EEgo="
  "resolved" "https://registry.npmjs.org/contains-path/-/contains-path-0.1.0.tgz"
  "version" "0.1.0"

"content-disposition@0.5.3":
  "integrity" "sha512-ExO0774ikEObIAEV9kDo50o+79VCUdEB6n6lzKgGwupcVeRlhrj3qGAfwq8G6uBJjkqLrhT0qEYFcWng8z1z0g=="
  "resolved" "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.3.tgz"
  "version" "0.5.3"
  dependencies:
    "safe-buffer" "5.1.2"

"content-type@1.0.4":
  "integrity" "sha512-hIP3EEPs8tB9AT1L+NUqtwOAps4mk2Zob89MWXMHjHWg9milF/j4osnnQLXBCBFBk/tvIG/tUc9mOUJiPBhPXA=="
  "resolved" "https://registry.npmjs.org/content-type/-/content-type-1.0.4.tgz"
  "version" "1.0.4"

"convert-source-map@1.7.0":
  "integrity" "sha512-4FJkXzKXEDB1snCFZlLP4gpC3JILicCpGbzG9f9G7tGqGCzETQ2hWPrcinA9oU4wtf2biUaEH5065UnMeR33oA=="
  "resolved" "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "safe-buffer" "5.1.2"

"cookie-signature@1.0.6":
  "integrity" "sha1-4wOogrNCzD7oylE6eZmXNNqzriw="
  "resolved" "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz"
  "version" "1.0.6"

"cookie@0.4.0":
  "integrity" "sha512-+Hp8fLp57wnUSt0tY0tHEXh4voZRDnoIrZPqlo3DPiI4y9lwg/jqx+1Om94/W6ZaPDOUbnjOt/99w66zk+l1Xg=="
  "resolved" "https://registry.npmjs.org/cookie/-/cookie-0.4.0.tgz"
  "version" "0.4.0"

"copy-concurrently@1.0.5":
  "integrity" "sha512-f2domd9fsVDFtaFcbaRZuYXwtdmnzqbADSwhSWYxYB/Q8zsdUUFMXVRwXGDMWmbEzAn1kdRrtI1T/KTFOL4X2A=="
  "resolved" "https://registry.npmjs.org/copy-concurrently/-/copy-concurrently-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "aproba" "1.2.0"
    "fs-write-stream-atomic" "1.0.10"
    "iferr" "0.1.5"
    "mkdirp" "0.5.1"
    "rimraf" "2.6.3"
    "run-queue" "1.0.3"

"copy-descriptor@0.1.1":
  "integrity" "sha1-Z29us8OZl8LuGsOpJP1hJHSPV40="
  "resolved" "https://registry.npmjs.org/copy-descriptor/-/copy-descriptor-0.1.1.tgz"
  "version" "0.1.1"

"copy-to-clipboard@3.3.1":
  "integrity" "sha512-i13qo6kIHTTpCm8/Wup+0b1mVWETvu2kIMzKoK8FpkLkFxlt0znUAHcMzox+T8sPlqtZXq3CulEjQHsYiGFJUw=="
  "resolved" "https://registry.npmjs.org/copy-to-clipboard/-/copy-to-clipboard-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "toggle-selection" "1.0.6"

"core-js-compat@3.6.4":
  "integrity" "sha512-zAa3IZPvsJ0slViBQ2z+vgyyTuhd3MFn1rBQjZSKVEgB0UMYhUkCj9jJUVPgGTGqWvsBVmfnruXgTcNyTlEiSA=="
  "resolved" "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.6.4.tgz"
  "version" "3.6.4"
  dependencies:
    "browserslist" "4.9.1"
    "semver" "7.0.0"

"core-js@1.2.7":
  "integrity" "sha1-ZSKUwUZR2yj6k70tX/KYOk8IxjY="
  "resolved" "https://registry.npmjs.org/core-js/-/core-js-1.2.7.tgz"
  "version" "1.2.7"

"core-js@2.6.11":
  "integrity" "sha512-5wjnpaT/3dV+XB4borEsnAYQchn00XSgTAWKDkEqv+K8KevjbzmofK6hfJ9TZIlpj2N0xQpazy7PiRQiWHqzWg=="
  "resolved" "https://registry.npmjs.org/core-js/-/core-js-2.6.11.tgz"
  "version" "2.6.11"

"core-js@2.6.4":
  "integrity" "sha512-05qQ5hXShcqGkPZpXEFLIpxayZscVD2kuMBZewxiIPPEagukO4mqgPA9CWhUvFBJfy3ODdK2p9xyHh7FTU9/7A=="
  "resolved" "https://registry.npmjs.org/core-js/-/core-js-2.6.4.tgz"
  "version" "2.6.4"

"core-util-is@1.0.2":
  "integrity" "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac="
  "resolved" "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz"
  "version" "1.0.2"

"cosmiconfig@5.2.1":
  "integrity" "sha512-H65gsXo1SKjf8zmrJ67eJk8aIRKV5ff2D4uKZIBZShbhGSpEmsQOPW/SKMKYhSTrqR7ufy6RP69rPogdaPh/kA=="
  "resolved" "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "import-fresh" "2.0.0"
    "is-directory" "0.3.1"
    "js-yaml" "3.13.1"
    "parse-json" "4.0.0"

"create-ecdh@4.0.3":
  "integrity" "sha512-GbEHQPMOswGpKXM9kCWVrremUcBmjteUaQ01T9rkKCPDXfUHX0IoP9LpHYo2NPFampa4e+/pFDc3jQdxrxQLaw=="
  "resolved" "https://registry.npmjs.org/create-ecdh/-/create-ecdh-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "bn.js" "4.11.8"
    "elliptic" "6.5.2"

"create-hash@1.2.0":
  "integrity" "sha512-z00bCGNHDG8mHAkP7CtT1qVu+bFQUPjYq/4Iv3C3kWjTFV10zIjfSoeqXo9Asws8gwSHDGj/hl2u4OGIjapeCg=="
  "resolved" "https://registry.npmjs.org/create-hash/-/create-hash-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "cipher-base" "1.0.4"
    "inherits" "2.0.4"
    "md5.js" "1.3.5"
    "ripemd160" "2.0.2"
    "sha.js" "2.4.11"

"create-hmac@1.1.7":
  "integrity" "sha512-MJG9liiZ+ogc4TzUwuvbER1JRdgvUFSB5+VR/g5h82fGaIRWMWddtKBHi7/sVhfjQZ6SehlyhvQYrcYkaUIpLg=="
  "resolved" "https://registry.npmjs.org/create-hmac/-/create-hmac-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "cipher-base" "1.0.4"
    "create-hash" "1.2.0"
    "inherits" "2.0.4"
    "ripemd160" "2.0.2"
    "safe-buffer" "5.2.0"
    "sha.js" "2.4.11"

"create-react-class@15.6.3":
  "integrity" "sha512-M+/3Q6E6DLO6Yx3OwrWjwHBnvfXXYA7W+dFjt/ZDBemHO1DDZhsalX/NUtnTYclN6GfnBDRh4qRHjcDHmlJBJg=="
  "resolved" "https://registry.npmjs.org/create-react-class/-/create-react-class-15.6.3.tgz"
  "version" "15.6.3"
  dependencies:
    "fbjs" "0.8.17"
    "loose-envify" "1.4.0"
    "object-assign" "4.1.1"

"cross-spawn@6.0.5":
  "integrity" "sha512-eTVLrBSt7fjbDygz805pMnstIs2VTBNkRm0qxZd+M7A5XDdxVRWO5MxGBXZhjY4cqLYLdtrGqRf8mBPmzwSpWQ=="
  "resolved" "https://registry.npmjs.org/cross-spawn/-/cross-spawn-6.0.5.tgz"
  "version" "6.0.5"
  dependencies:
    "nice-try" "1.0.5"
    "path-key" "2.0.1"
    "semver" "5.7.1"
    "shebang-command" "1.2.0"
    "which" "1.3.1"

"crypto-browserify@3.12.0":
  "integrity" "sha512-fz4spIh+znjO2VjL+IdhEpRJ3YN6sMzITSBijk6FK2UvTqruSQW+/cCZTSNsMiZNvUeq0CqurF+dAbyiGOY6Wg=="
  "resolved" "https://registry.npmjs.org/crypto-browserify/-/crypto-browserify-3.12.0.tgz"
  "version" "3.12.0"
  dependencies:
    "browserify-cipher" "1.0.1"
    "browserify-sign" "4.0.4"
    "create-ecdh" "4.0.3"
    "create-hash" "1.2.0"
    "create-hmac" "1.1.7"
    "diffie-hellman" "5.0.3"
    "inherits" "2.0.4"
    "pbkdf2" "3.0.17"
    "public-encrypt" "4.0.3"
    "randombytes" "2.1.0"
    "randomfill" "1.0.4"

"css-animation@1.6.1":
  "integrity" "sha512-/48+/BaEaHRY6kNQ2OIPzKf9A6g8WjZYjhiNDNuIVbsm5tXCGIAsHDjB4Xu1C4vXJtUWZo26O68OQkDpNBaPog=="
  "resolved" "https://registry.npmjs.org/css-animation/-/css-animation-1.6.1.tgz"
  "version" "1.6.1"
  dependencies:
    "babel-runtime" "6.26.0"
    "component-classes" "1.2.6"

"css-blank-pseudo@0.1.4":
  "integrity" "sha512-LHz35Hr83dnFeipc7oqFDmsjHdljj3TQtxGGiNWSOsTLIAubSm4TEz8qCaKFpk7idaQ1GfWscF4E6mgpBysA1w=="
  "resolved" "https://registry.npmjs.org/css-blank-pseudo/-/css-blank-pseudo-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "postcss" "7.0.27"

"css-color-names@0.0.4":
  "integrity" "sha1-gIrcLnnPhHOAabZGyyDsJ762KeA="
  "resolved" "https://registry.npmjs.org/css-color-names/-/css-color-names-0.0.4.tgz"
  "version" "0.0.4"

"css-declaration-sorter@4.0.1":
  "integrity" "sha512-BcxQSKTSEEQUftYpBVnsH4SF05NTuBokb19/sBt6asXGKZ/6VP7PLG1CBCkFDYOnhXhPh0jMhO6xZ71oYHXHBA=="
  "resolved" "https://registry.npmjs.org/css-declaration-sorter/-/css-declaration-sorter-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "7.0.27"
    "timsort" "0.3.0"

"css-has-pseudo@0.10.0":
  "integrity" "sha512-Z8hnfsZu4o/kt+AuFzeGpLVhFOGO9mluyHBaA2bA8aCGTwah5sT3WV/fTHH8UNZUytOIImuGPrl/prlb4oX4qQ=="
  "resolved" "https://registry.npmjs.org/css-has-pseudo/-/css-has-pseudo-0.10.0.tgz"
  "version" "0.10.0"
  dependencies:
    "postcss" "7.0.27"
    "postcss-selector-parser" "5.0.0"

"css-loader@1.0.0":
  "integrity" "sha512-tMXlTYf3mIMt3b0dDCOQFJiVvxbocJ5Ho577WiGPYPZcqVEO218L2iU22pDXzkTZCLDE+9AmGSUkWxeh/nZReA=="
  "resolved" "https://registry.npmjs.org/css-loader/-/css-loader-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "babel-code-frame" "6.26.0"
    "css-selector-tokenizer" "0.7.2"
    "icss-utils" "2.1.0"
    "loader-utils" "1.4.0"
    "lodash.camelcase" "4.3.0"
    "postcss" "6.0.23"
    "postcss-modules-extract-imports" "1.2.1"
    "postcss-modules-local-by-default" "1.2.0"
    "postcss-modules-scope" "1.1.0"
    "postcss-modules-values" "1.3.0"
    "postcss-value-parser" "3.3.1"
    "source-list-map" "2.0.1"

"css-prefers-color-scheme@3.1.1":
  "integrity" "sha512-MTu6+tMs9S3EUqzmqLXEcgNRbNkkD/TGFvowpeoWJn5Vfq7FMgsmRQs9X5NXAURiOBmOxm/lLjsDNXDE6k9bhg=="
  "resolved" "https://registry.npmjs.org/css-prefers-color-scheme/-/css-prefers-color-scheme-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "postcss" "7.0.27"

"css-select-base-adapter@0.1.1":
  "integrity" "sha512-jQVeeRG70QI08vSTwf1jHxp74JoZsr2XSgETae8/xC8ovSnL2WF87GTLO86Sbwdt2lK4Umg4HnnwMO4YF3Ce7w=="
  "resolved" "https://registry.npmjs.org/css-select-base-adapter/-/css-select-base-adapter-0.1.1.tgz"
  "version" "0.1.1"

"css-select@1.2.0":
  "integrity" "sha1-KzoRBTnFNV8c2NMUYj6HCxIeyFg="
  "resolved" "https://registry.npmjs.org/css-select/-/css-select-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "boolbase" "1.0.0"
    "css-what" "2.1.3"
    "domutils" "1.5.1"
    "nth-check" "1.0.2"

"css-select@2.1.0":
  "integrity" "sha512-Dqk7LQKpwLoH3VovzZnkzegqNSuAziQyNZUcrdDM401iY+R5NkGBXGmtO05/yaXQziALuPogeG0b7UAgjnTJTQ=="
  "resolved" "https://registry.npmjs.org/css-select/-/css-select-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "boolbase" "1.0.0"
    "css-what" "3.2.1"
    "domutils" "1.7.0"
    "nth-check" "1.0.2"

"css-selector-tokenizer@0.7.2":
  "integrity" "sha512-yj856NGuAymN6r8bn8/Jl46pR+OC3eEvAhfGYDUe7YPtTPAYrSSw4oAniZ9Y8T5B92hjhwTBLUen0/vKPxf6pw=="
  "resolved" "https://registry.npmjs.org/css-selector-tokenizer/-/css-selector-tokenizer-0.7.2.tgz"
  "version" "0.7.2"
  dependencies:
    "cssesc" "3.0.0"
    "fastparse" "1.1.2"
    "regexpu-core" "4.6.0"

"css-tree@1.0.0-alpha.37":
  "integrity" "sha512-DMxWJg0rnz7UgxKT0Q1HU/L9BeJI0M6ksor0OgqOnF+aRCDWg/N2641HmVyU9KVIu0OVVWOb2IpC9A+BJRnejg=="
  "resolved" "https://registry.npmjs.org/css-tree/-/css-tree-1.0.0-alpha.37.tgz"
  "version" "1.0.0-alpha.37"
  dependencies:
    "mdn-data" "2.0.4"
    "source-map" "0.6.1"

"css-what@2.1.3":
  "integrity" "sha512-a+EPoD+uZiNfh+5fxw2nO9QwFa6nJe2Or35fGY6Ipw1R3R4AGz1d1TEZrCegvw2YTmZ0jXirGYlzxxpYSHwpEg=="
  "resolved" "https://registry.npmjs.org/css-what/-/css-what-2.1.3.tgz"
  "version" "2.1.3"

"css-what@3.2.1":
  "integrity" "sha512-WwOrosiQTvyms+Ti5ZC5vGEK0Vod3FTt1ca+payZqvKuGJF+dq7bG63DstxtN0dpm6FxY27a/zS3Wten+gEtGw=="
  "resolved" "https://registry.npmjs.org/css-what/-/css-what-3.2.1.tgz"
  "version" "3.2.1"

"cssdb@4.4.0":
  "integrity" "sha512-LsTAR1JPEM9TpGhl/0p3nQecC2LJ0kD8X5YARu1hk/9I1gril5vDtMZyNxcEpxxDj34YNck/ucjuoUd66K03oQ=="
  "resolved" "https://registry.npmjs.org/cssdb/-/cssdb-4.4.0.tgz"
  "version" "4.4.0"

"cssesc@2.0.0":
  "integrity" "sha512-MsCAG1z9lPdoO/IUMLSBWBSVxVtJ1395VGIQ+Fc2gNdkQ1hNDnQdw3YhA71WJCBW1vdwA0cAnk/DnW6bqoEUYg=="
  "resolved" "https://registry.npmjs.org/cssesc/-/cssesc-2.0.0.tgz"
  "version" "2.0.0"

"cssesc@3.0.0":
  "integrity" "sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg=="
  "resolved" "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz"
  "version" "3.0.0"

"cssnano-preset-default@4.0.7":
  "integrity" "sha512-x0YHHx2h6p0fCl1zY9L9roD7rnlltugGu7zXSKQx6k2rYw0Hi3IqxcoAGF7u9Q5w1nt7vK0ulxV8Lo+EvllGsA=="
  "resolved" "https://registry.npmjs.org/cssnano-preset-default/-/cssnano-preset-default-4.0.7.tgz"
  "version" "4.0.7"
  dependencies:
    "css-declaration-sorter" "4.0.1"
    "cssnano-util-raw-cache" "4.0.1"
    "postcss" "7.0.27"
    "postcss-calc" "7.0.2"
    "postcss-colormin" "4.0.3"
    "postcss-convert-values" "4.0.1"
    "postcss-discard-comments" "4.0.2"
    "postcss-discard-duplicates" "4.0.2"
    "postcss-discard-empty" "4.0.1"
    "postcss-discard-overridden" "4.0.1"
    "postcss-merge-longhand" "4.0.11"
    "postcss-merge-rules" "4.0.3"
    "postcss-minify-font-values" "4.0.2"
    "postcss-minify-gradients" "4.0.2"
    "postcss-minify-params" "4.0.2"
    "postcss-minify-selectors" "4.0.2"
    "postcss-normalize-charset" "4.0.1"
    "postcss-normalize-display-values" "4.0.2"
    "postcss-normalize-positions" "4.0.2"
    "postcss-normalize-repeat-style" "4.0.2"
    "postcss-normalize-string" "4.0.2"
    "postcss-normalize-timing-functions" "4.0.2"
    "postcss-normalize-unicode" "4.0.1"
    "postcss-normalize-url" "4.0.1"
    "postcss-normalize-whitespace" "4.0.2"
    "postcss-ordered-values" "4.1.2"
    "postcss-reduce-initial" "4.0.3"
    "postcss-reduce-transforms" "4.0.2"
    "postcss-svgo" "4.0.2"
    "postcss-unique-selectors" "4.0.1"

"cssnano-util-get-arguments@4.0.0":
  "integrity" "sha1-7ToIKZ8h11dBsg87gfGU7UnMFQ8="
  "resolved" "https://registry.npmjs.org/cssnano-util-get-arguments/-/cssnano-util-get-arguments-4.0.0.tgz"
  "version" "4.0.0"

"cssnano-util-get-match@4.0.0":
  "integrity" "sha1-wOTKB/U4a7F+xeUiULT1lhNlFW0="
  "resolved" "https://registry.npmjs.org/cssnano-util-get-match/-/cssnano-util-get-match-4.0.0.tgz"
  "version" "4.0.0"

"cssnano-util-raw-cache@4.0.1":
  "integrity" "sha512-qLuYtWK2b2Dy55I8ZX3ky1Z16WYsx544Q0UWViebptpwn/xDBmog2TLg4f+DBMg1rJ6JDWtn96WHbOKDWt1WQA=="
  "resolved" "https://registry.npmjs.org/cssnano-util-raw-cache/-/cssnano-util-raw-cache-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "7.0.27"

"cssnano-util-same-parent@4.0.1":
  "integrity" "sha512-WcKx5OY+KoSIAxBW6UBBRay1U6vkYheCdjyVNDm85zt5K9mHoGOfsOsqIszfAqrQQFIIKgjh2+FDgIj/zsl21Q=="
  "resolved" "https://registry.npmjs.org/cssnano-util-same-parent/-/cssnano-util-same-parent-4.0.1.tgz"
  "version" "4.0.1"

"cssnano@4.1.10":
  "integrity" "sha512-5wny+F6H4/8RgNlaqab4ktc3e0/blKutmq8yNlBFXA//nSFFAqAngjNVRzUvCgYROULmZZUoosL/KSoZo5aUaQ=="
  "resolved" "https://registry.npmjs.org/cssnano/-/cssnano-4.1.10.tgz"
  "version" "4.1.10"
  dependencies:
    "cosmiconfig" "5.2.1"
    "cssnano-preset-default" "4.0.7"
    "is-resolvable" "1.1.0"
    "postcss" "7.0.27"

"csso@4.0.2":
  "integrity" "sha512-kS7/oeNVXkHWxby5tHVxlhjizRCSv8QdU7hB2FpdAibDU8FjTAolhNjKNTiLzXtUrKT6HwClE81yXwEk1309wg=="
  "resolved" "https://registry.npmjs.org/csso/-/csso-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "css-tree" "1.0.0-alpha.37"

"cssom@0.3.8":
  "integrity" "sha512-b0tGHbfegbhPJpxpiBPU2sCkigAqtM9O121le6bbOlgyV+NyGyCmVfJ6QW9eRjz8CpNfWEOYBIMIGRYkLwsIYg=="
  "resolved" "https://registry.npmjs.org/cssom/-/cssom-0.3.8.tgz"
  "version" "0.3.8"

"cssstyle@1.4.0":
  "integrity" "sha512-GBrLZYZ4X4x6/QEoBnIrqb8B/f5l4+8me2dkom/j1Gtbxy0kBv6OGzKuAsGM75bkGwGAFkt56Iwg28S3XTZgSA=="
  "resolved" "https://registry.npmjs.org/cssstyle/-/cssstyle-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "cssom" "0.3.8"

"csstype@2.6.9":
  "integrity" "sha512-xz39Sb4+OaTsULgUERcCk+TJj8ylkL4aSVDQiX/ksxbELSqwkgt4d4RD7fovIdgJGSuNYqwZEiVjYY5l0ask+Q=="
  "resolved" "https://registry.npmjs.org/csstype/-/csstype-2.6.9.tgz"
  "version" "2.6.9"

"customize-cra@^0.2.11":
  "integrity" "sha512-LtEMXNzkhnnqGPc1dP5fnPlF1ic1dj34hDbRVJIzfMQgOaGByHhx51fTR7fv7sTPEbCPrOBP777MkCo0GPV57g=="
  "resolved" "https://registry.npmjs.org/customize-cra/-/customize-cra-0.2.14.tgz"
  "version" "0.2.14"
  dependencies:
    "lodash.flow" "3.5.0"

"cyclist@1.0.1":
  "integrity" "sha1-WW6WmP0MgOEgOMK4LW6xs1tiJNk="
  "resolved" "https://registry.npmjs.org/cyclist/-/cyclist-1.0.1.tgz"
  "version" "1.0.1"

"damerau-levenshtein@1.0.6":
  "integrity" "sha512-JVrozIeElnj3QzfUIt8tB8YMluBJom4Vw9qTPpjGYQ9fYlB3D/rb6OordUxf3xeFB35LKWs0xqcO5U6ySvBtug=="
  "resolved" "https://registry.npmjs.org/damerau-levenshtein/-/damerau-levenshtein-1.0.6.tgz"
  "version" "1.0.6"

"dashdash@1.14.1":
  "integrity" "sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA="
  "resolved" "https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "assert-plus" "1.0.0"

"data-urls@1.1.0":
  "integrity" "sha512-YTWYI9se1P55u58gL5GkQHW4P6VJBJ5iBT+B5a7i2Tjadhv52paJG0qHX4A0OR6/t52odI64KP2YvFpkDOi3eQ=="
  "resolved" "https://registry.npmjs.org/data-urls/-/data-urls-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "abab" "2.0.3"
    "whatwg-mimetype" "2.3.0"
    "whatwg-url" "7.1.0"

"debug@2.6.9":
  "integrity" "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@3.1.0":
  "integrity" "sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "ms" "2.0.0"

"debug@3.2.6":
  "integrity" "sha512-mel+jf7nrtEl5Pn1Qx46zARXKDpBbvzezse7p7LqINmdoIk8PYP5SySaxEmYv6TZ0JyEKA1hsCId6DIhgITtWQ=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-3.2.6.tgz"
  "version" "3.2.6"
  dependencies:
    "ms" "2.1.2"

"debug@4.1.1":
  "integrity" "sha512-pYAIzeRo8J6KPEaJ0VWOh5Pzkbw/RetuzehGM7QRRX5he4fPHx2rdKMB256ehJCkX+XRQm16eZLqLNS8RSZXZw=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "ms" "2.1.2"

"decamelize@1.2.0":
  "integrity" "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA="
  "resolved" "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz"
  "version" "1.2.0"

"decamelize@2.0.0":
  "integrity" "sha512-Ikpp5scV3MSYxY39ymh45ZLEecsTdv/Xj2CaQfI8RLMuwi7XvjX9H/fhraiSuU+C5w5NTDu4ZU72xNiZnurBPg=="
  "resolved" "https://registry.npmjs.org/decamelize/-/decamelize-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "xregexp" "4.0.0"

"decode-uri-component@0.2.0":
  "integrity" "sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU="
  "resolved" "https://registry.npmjs.org/decode-uri-component/-/decode-uri-component-0.2.0.tgz"
  "version" "0.2.0"

"deep-equal@1.1.1":
  "integrity" "sha512-yd9c5AdiqVcR+JjcwUQb9DkhJc8ngNr0MahEBGvDiJw8puWab2yZlh+nkasOnZP+EGTAP6rRp2JzJhJZzvNF8g=="
  "resolved" "https://registry.npmjs.org/deep-equal/-/deep-equal-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "is-arguments" "1.0.4"
    "is-date-object" "1.0.2"
    "is-regex" "1.0.5"
    "object-is" "1.0.2"
    "object-keys" "1.1.1"
    "regexp.prototype.flags" "1.3.0"

"deep-is@0.1.3":
  "integrity" "sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ="
  "resolved" "https://registry.npmjs.org/deep-is/-/deep-is-0.1.3.tgz"
  "version" "0.1.3"

"default-gateway@2.7.2":
  "integrity" "sha512-lAc4i9QJR0YHSDFdzeBQKfZ1SRDG3hsJNEkrpcZa8QhBfidLAilT60BDEIVUUGqosFp425KOgB3uYqcnQrWafQ=="
  "resolved" "https://registry.npmjs.org/default-gateway/-/default-gateway-2.7.2.tgz"
  "version" "2.7.2"
  dependencies:
    "execa" "0.10.0"
    "ip-regex" "2.1.0"

"default-require-extensions@1.0.0":
  "integrity" "sha1-836hXT4T/9m0N9M+GnW1+5eHTLg="
  "resolved" "https://registry.npmjs.org/default-require-extensions/-/default-require-extensions-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "strip-bom" "2.0.0"

"define-properties@1.1.3":
  "integrity" "sha512-3MqfYKj2lLzdMSf8ZIZE/V+Zuy+BgD6f164e8K2w7dgnpKArBDerGYpM46IYYcjnkdPNMjPk9A6VFB8+3SKlXQ=="
  "resolved" "https://registry.npmjs.org/define-properties/-/define-properties-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "object-keys" "1.1.1"

"define-property@0.2.5":
  "integrity" "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY="
  "resolved" "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz"
  "version" "0.2.5"
  dependencies:
    "is-descriptor" "0.1.6"

"define-property@1.0.0":
  "integrity" "sha1-dp66rz9KY6rTr56NMEybvnm/sOY="
  "resolved" "https://registry.npmjs.org/define-property/-/define-property-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-descriptor" "1.0.2"

"define-property@2.0.2":
  "integrity" "sha512-jwK2UV4cnPpbcG7+VRARKTZPUWowwXA8bzH5NP6ud0oeAxyYPuGZUAC7hMugpCdz4BeSZl2Dl9k66CHJ/46ZYQ=="
  "resolved" "https://registry.npmjs.org/define-property/-/define-property-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "is-descriptor" "1.0.2"
    "isobject" "3.0.1"

"del@3.0.0":
  "integrity" "sha1-U+z2mf/LyzljdpGrE7rxYIGXZuU="
  "resolved" "https://registry.npmjs.org/del/-/del-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "globby" "6.1.0"
    "is-path-cwd" "1.0.0"
    "is-path-in-cwd" "1.0.1"
    "p-map" "1.2.0"
    "pify" "3.0.0"
    "rimraf" "2.6.3"

"delayed-stream@1.0.0":
  "integrity" "sha1-3zrhmayt+31ECqrgsp4icrJOxhk="
  "resolved" "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  "version" "1.0.0"

"depd@1.1.2":
  "integrity" "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak="
  "resolved" "https://registry.npmjs.org/depd/-/depd-1.1.2.tgz"
  "version" "1.1.2"

"des.js@1.0.1":
  "integrity" "sha512-Q0I4pfFrv2VPd34/vfLrFOoRmlYj3OV50i7fskps1jZWK1kApMWWT9G6RRUeYedLcBDIhnSDaUvJMb3AhUlaEA=="
  "resolved" "https://registry.npmjs.org/des.js/-/des.js-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "inherits" "2.0.4"
    "minimalistic-assert" "1.0.1"

"destroy@1.0.4":
  "integrity" "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA="
  "resolved" "https://registry.npmjs.org/destroy/-/destroy-1.0.4.tgz"
  "version" "1.0.4"

"detect-indent@4.0.0":
  "integrity" "sha1-920GQ1LN9Docts5hnE7jqUdd4gg="
  "resolved" "https://registry.npmjs.org/detect-indent/-/detect-indent-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "repeating" "2.0.1"

"detect-newline@2.1.0":
  "integrity" "sha1-9B8cEL5LAOh7XxPaaAdZ8sW/0+I="
  "resolved" "https://registry.npmjs.org/detect-newline/-/detect-newline-2.1.0.tgz"
  "version" "2.1.0"

"detect-node@2.0.4":
  "integrity" "sha512-ZIzRpLJrOj7jjP2miAtgqIfmzbxa4ZOr5jJc601zklsfEx9oTzmmj2nVpIPRpNlRTIh8lc1kyViIY7BWSGNmKw=="
  "resolved" "https://registry.npmjs.org/detect-node/-/detect-node-2.0.4.tgz"
  "version" "2.0.4"

"detect-port-alt@1.1.6":
  "integrity" "sha512-5tQykt+LqfJFBEYaDITx7S7cR7mJ/zQmLXZ2qt5w04ainYZw6tBf9dBunMjVeVOdYVRUzUOE4HkY5J7+uttb5Q=="
  "resolved" "https://registry.npmjs.org/detect-port-alt/-/detect-port-alt-1.1.6.tgz"
  "version" "1.1.6"
  dependencies:
    "address" "1.0.3"
    "debug" "2.6.9"

"diff@3.5.0":
  "integrity" "sha512-A46qtFgd+g7pDZinpnwiRJtxbC1hpgf0uzP3iG89scHk0AUC7A1TGxf5OiiOUv/JMZR8GOt8hL900hV0bOy5xA=="
  "resolved" "https://registry.npmjs.org/diff/-/diff-3.5.0.tgz"
  "version" "3.5.0"

"diffie-hellman@5.0.3":
  "integrity" "sha512-kqag/Nl+f3GwyK25fhUMYj81BUOrZ9IuJsjIcDE5icNM9FJHAVm3VcUDxdLPoQtTuUylWm6ZIknYJwwaPxsUzg=="
  "resolved" "https://registry.npmjs.org/diffie-hellman/-/diffie-hellman-5.0.3.tgz"
  "version" "5.0.3"
  dependencies:
    "bn.js" "4.11.8"
    "miller-rabin" "4.0.1"
    "randombytes" "2.1.0"

"dir-glob@2.0.0":
  "integrity" "sha512-37qirFDz8cA5fimp9feo43fSuRo2gHwaIn6dXL8Ber1dGwUosDrGZeCCXq57WnIqE4aQ+u3eQZzsk1yOzhdwag=="
  "resolved" "https://registry.npmjs.org/dir-glob/-/dir-glob-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "arrify" "1.0.1"
    "path-type" "3.0.0"

"dns-equal@1.0.0":
  "integrity" "sha1-s55/HabrCnW6nBcySzR1PEfgZU0="
  "resolved" "https://registry.npmjs.org/dns-equal/-/dns-equal-1.0.0.tgz"
  "version" "1.0.0"

"dns-packet@1.3.1":
  "integrity" "sha512-0UxfQkMhYAUaZI+xrNZOz/as5KgDU0M/fQ9b6SpkyLbk3GEswDi6PADJVaYJradtRVsRIlF1zLyOodbcTCDzUg=="
  "resolved" "https://registry.npmjs.org/dns-packet/-/dns-packet-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "ip" "1.1.5"
    "safe-buffer" "5.2.0"

"dns-txt@2.0.2":
  "integrity" "sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY="
  "resolved" "https://registry.npmjs.org/dns-txt/-/dns-txt-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "buffer-indexof" "1.1.1"

"doctrine@1.5.0":
  "integrity" "sha1-N53Ocw9hZvds76TmcHoVmwLFpvo="
  "resolved" "https://registry.npmjs.org/doctrine/-/doctrine-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "esutils" "2.0.3"
    "isarray" "1.0.0"

"doctrine@2.1.0":
  "integrity" "sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw=="
  "resolved" "https://registry.npmjs.org/doctrine/-/doctrine-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "esutils" "2.0.3"

"dom-align@1.10.4":
  "integrity" "sha512-wytDzaru67AmqFOY4B9GUb/hrwWagezoYYK97D/vpK+ezg+cnuZO0Q2gltUPa7KfNmIqfRIYVCF8UhRDEHAmgQ=="
  "resolved" "https://registry.npmjs.org/dom-align/-/dom-align-1.10.4.tgz"
  "version" "1.10.4"

"dom-closest@0.2.0":
  "integrity" "sha1-69n5HRvyLo1vR3h2u80+yQIWwM8="
  "resolved" "https://registry.npmjs.org/dom-closest/-/dom-closest-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "dom-matches" "2.0.0"

"dom-converter@0.2.0":
  "integrity" "sha512-gd3ypIPfOMr9h5jIKq8E3sHOTCjeirnl0WK5ZdS1AW0Odt0b1PaWaHdJ4Qk4klv+YB9aJBS7mESXjFoDQPu6DA=="
  "resolved" "https://registry.npmjs.org/dom-converter/-/dom-converter-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "utila" "0.4.0"

"dom-matches@2.0.0":
  "integrity" "sha1-0nKLQWqHUzmA6wibhI0lPPI6dYw="
  "resolved" "https://registry.npmjs.org/dom-matches/-/dom-matches-2.0.0.tgz"
  "version" "2.0.0"

"dom-scroll-into-view@1.2.1":
  "integrity" "sha1-6PNnMt0ImwIBqI14Fdw/iObWbH4="
  "resolved" "https://registry.npmjs.org/dom-scroll-into-view/-/dom-scroll-into-view-1.2.1.tgz"
  "version" "1.2.1"

"dom-serializer@0.2.2":
  "integrity" "sha512-2/xPb3ORsQ42nHYiSunXkDjPLBaEj/xTwUO4B7XCZQTRk7EBtTOPaygh10YAAh2OI1Qrp6NWfpAhzswj0ydt9g=="
  "resolved" "https://registry.npmjs.org/dom-serializer/-/dom-serializer-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "domelementtype" "2.0.1"
    "entities" "2.0.0"

"domain-browser@1.2.0":
  "integrity" "sha512-jnjyiM6eRyZl2H+W8Q/zLMA481hzi0eszAaBUzIVnmYVDBbnLxVNnfu1HgEBvCbL+71FrxMl3E6lpKH7Ge3OXA=="
  "resolved" "https://registry.npmjs.org/domain-browser/-/domain-browser-1.2.0.tgz"
  "version" "1.2.0"

"domelementtype@1.3.1":
  "integrity" "sha512-BSKB+TSpMpFI/HOxCNr1O8aMOTZ8hT3pM3GQ0w/mWRmkhEDSFJkkyzz4XQsBV44BChwGkrDfMyjVD0eA2aFV3w=="
  "resolved" "https://registry.npmjs.org/domelementtype/-/domelementtype-1.3.1.tgz"
  "version" "1.3.1"

"domelementtype@2.0.1":
  "integrity" "sha512-5HOHUDsYZWV8FGWN0Njbr/Rn7f/eWSQi1v7+HsUVwXgn8nWWlL64zKDkS0n8ZmQ3mlWOMuXOnR+7Nx/5tMO5AQ=="
  "resolved" "https://registry.npmjs.org/domelementtype/-/domelementtype-2.0.1.tgz"
  "version" "2.0.1"

"domexception@1.0.1":
  "integrity" "sha512-raigMkn7CJNNo6Ihro1fzG7wr3fHuYVytzquZKX5n0yizGsTcYgzdIUwj1X9pK0VvjeihV+XiclP+DjwbsSKug=="
  "resolved" "https://registry.npmjs.org/domexception/-/domexception-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "webidl-conversions" "4.0.2"

"domhandler@2.4.2":
  "integrity" "sha512-JiK04h0Ht5u/80fdLMCEmV4zkNh2BcoMFBmZ/91WtYZ8qVXSKjiw7fXMgFPnHcSZgOo3XdinHvmnDUeMf5R4wA=="
  "resolved" "https://registry.npmjs.org/domhandler/-/domhandler-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "domelementtype" "1.3.1"

"domutils@1.5.1":
  "integrity" "sha1-3NhIiib1Y9YQeeSMn3t+Mjc2gs8="
  "resolved" "https://registry.npmjs.org/domutils/-/domutils-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "dom-serializer" "0.2.2"
    "domelementtype" "1.3.1"

"domutils@1.7.0":
  "integrity" "sha512-Lgd2XcJ/NjEw+7tFvfKxOzCYKZsdct5lczQ2ZaQY8Djz7pfAD3Gbp8ySJWtreII/vDlMVmxwa6pHmdxIYgttDg=="
  "resolved" "https://registry.npmjs.org/domutils/-/domutils-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "dom-serializer" "0.2.2"
    "domelementtype" "1.3.1"

"dot-prop@5.2.0":
  "integrity" "sha512-uEUyaDKoSQ1M4Oq8l45hSE26SnTxL6snNnqvK/VWx5wJhmff5z0FUVJDKDanor/6w3kzE3i7XZOk+7wC0EXr1A=="
  "resolved" "https://registry.npmjs.org/dot-prop/-/dot-prop-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "is-obj" "2.0.0"

"dotenv-expand@4.2.0":
  "integrity" "sha1-3vHxyl1gWdJKdm5YeULCEQbOEnU="
  "resolved" "https://registry.npmjs.org/dotenv-expand/-/dotenv-expand-4.2.0.tgz"
  "version" "4.2.0"

"dotenv@6.0.0":
  "integrity" "sha512-FlWbnhgjtwD+uNLUGHbMykMOYQaTivdHEmYwAKFjn6GKe/CqY0fNae93ZHTd20snh9ZLr8mTzIL9m0APQ1pjQg=="
  "resolved" "https://registry.npmjs.org/dotenv/-/dotenv-6.0.0.tgz"
  "version" "6.0.0"

"draft-js@0.10.5":
  "integrity" "sha512-LE6jSCV9nkPhfVX2ggcRLA4FKs6zWq9ceuO/88BpXdNCS7mjRTgs0NsV6piUCJX9YxMsB9An33wnkMmU2sD2Zg=="
  "resolved" "https://registry.npmjs.org/draft-js/-/draft-js-0.10.5.tgz"
  "version" "0.10.5"
  dependencies:
    "fbjs" "0.8.17"
    "immutable" "3.7.6"
    "object-assign" "4.1.1"

"duplexer@0.1.1":
  "integrity" "sha1-rOb/gIwc5mtX0ev5eXessCM0z8E="
  "resolved" "https://registry.npmjs.org/duplexer/-/duplexer-0.1.1.tgz"
  "version" "0.1.1"

"duplexify@3.7.1":
  "integrity" "sha512-07z8uv2wMyS51kKhD1KsdXJg5WQ6t93RneqRxUHnskXVtlYYkLqM0gqStQZ3pj073g687jPCHrqNfCzawLYh5g=="
  "resolved" "https://registry.npmjs.org/duplexify/-/duplexify-3.7.1.tgz"
  "version" "3.7.1"
  dependencies:
    "end-of-stream" "1.4.4"
    "inherits" "2.0.4"
    "readable-stream" "2.3.7"
    "stream-shift" "1.0.1"

"ecc-jsbn@0.1.2":
  "integrity" "sha1-OoOpBOVDUyh4dMVkt1SThoSamMk="
  "resolved" "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "jsbn" "0.1.1"
    "safer-buffer" "2.1.2"

"echarts@^4.7.0":
  "integrity" "sha512-+ugizgtJ+KmsJyyDPxaw2Br5FqzuBnyOWwcxPKO6y0gc5caYcfnEUIlNStx02necw8jmKmTafmpHhGo4XDtEIA=="
  "resolved" "https://registry.npmjs.org/echarts/-/echarts-4.9.0.tgz"
  "version" "4.9.0"
  dependencies:
    "zrender" "4.3.2"

"ee-first@1.1.1":
  "integrity" "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="
  "resolved" "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz"
  "version" "1.1.1"

"electron-to-chromium@1.3.372":
  "integrity" "sha512-77a4jYC52OdisHM+Tne7dgWEvQT1FoNu/jYl279pP88ZtG4ZRIPyhQwAKxj6C2rzsyC1OwsOds9JlZtNncSz6g=="
  "resolved" "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.3.372.tgz"
  "version" "1.3.372"

"elliptic@6.5.2":
  "integrity" "sha512-f4x70okzZbIQl/NSRLkI/+tteV/9WqL98zx+SQ69KbXxmVrmjwsNUPn/gYJJ0sHvEak24cZgHIPegRePAtA/xw=="
  "resolved" "https://registry.npmjs.org/elliptic/-/elliptic-6.5.2.tgz"
  "version" "6.5.2"
  dependencies:
    "bn.js" "4.11.8"
    "brorand" "1.1.0"
    "hash.js" "1.1.7"
    "hmac-drbg" "1.0.1"
    "inherits" "2.0.4"
    "minimalistic-assert" "1.0.1"
    "minimalistic-crypto-utils" "1.0.1"

"emoji-regex@6.5.1":
  "integrity" "sha512-PAHp6TxrCy7MGMFidro8uikr+zlJJKJ/Q6mm2ExZ7HwkyR9lSVFfE3kt36qcwa24BQL7y0G9axycGjK1A/0uNQ=="
  "resolved" "https://registry.npmjs.org/emoji-regex/-/emoji-regex-6.5.1.tgz"
  "version" "6.5.1"

"emoji-regex@7.0.3":
  "integrity" "sha512-CwBLREIQ7LvYFB0WyRvwhq5N5qPhc6PMjD6bYggFlI5YyDgl+0vxq5VHbMOFqLg7hfWzmu8T5Z1QofhmTIhItA=="
  "resolved" "https://registry.npmjs.org/emoji-regex/-/emoji-regex-7.0.3.tgz"
  "version" "7.0.3"

"emojis-list@2.1.0":
  "integrity" "sha1-TapNnbAPmBmIDHn6RXrlsJof04k="
  "resolved" "https://registry.npmjs.org/emojis-list/-/emojis-list-2.1.0.tgz"
  "version" "2.1.0"

"emojis-list@3.0.0":
  "integrity" "sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q=="
  "resolved" "https://registry.npmjs.org/emojis-list/-/emojis-list-3.0.0.tgz"
  "version" "3.0.0"

"encodeurl@1.0.2":
  "integrity" "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k="
  "resolved" "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz"
  "version" "1.0.2"

"encoding@0.1.12":
  "integrity" "sha1-U4tm8+5izRq1HsMjgp0flIDHS+s="
  "resolved" "https://registry.npmjs.org/encoding/-/encoding-0.1.12.tgz"
  "version" "0.1.12"
  dependencies:
    "iconv-lite" "0.4.24"

"end-of-stream@1.4.4":
  "integrity" "sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q=="
  "resolved" "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz"
  "version" "1.4.4"
  dependencies:
    "once" "1.4.0"

"enhanced-resolve@4.1.1":
  "integrity" "sha512-98p2zE+rL7/g/DzMHMTF4zZlCgeVdJ7yr6xzEpJRYwFYrGi9ANdn5DnJURg6RpBkyk60XYDnWIv51VfIhfNGuA=="
  "resolved" "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "graceful-fs" "4.2.3"
    "memory-fs" "0.5.0"
    "tapable" "1.1.3"

"enquire.js@2.1.6":
  "integrity" "sha1-PoeAybi4NQhMP2DhZtvDwqPImBQ="
  "resolved" "https://registry.npmjs.org/enquire.js/-/enquire.js-2.1.6.tgz"
  "version" "2.1.6"

"entities@1.1.2":
  "integrity" "sha512-f2LZMYl1Fzu7YSBKg+RoROelpOaNrcGmE9AZubeDfrCEia483oW4MI4VyFd5VNHIgQ/7qm1I0wUHK1eJnn2y2w=="
  "resolved" "https://registry.npmjs.org/entities/-/entities-1.1.2.tgz"
  "version" "1.1.2"

"entities@2.0.0":
  "integrity" "sha512-D9f7V0JSRwIxlRI2mjMqufDrRDnx8p+eEOz7aUM9SuvF8gsBzra0/6tbjl1m8eQHrZlYj6PxqE00hZ1SAIKPLw=="
  "resolved" "https://registry.npmjs.org/entities/-/entities-2.0.0.tgz"
  "version" "2.0.0"

"errno@0.1.7":
  "integrity" "sha512-MfrRBDWzIWifgq6tJj60gkAwtLNb6sQPlcFrSOflcP1aFmmruKQ2wRnze/8V6kgyz7H3FF8Npzv78mZ7XLLflg=="
  "resolved" "https://registry.npmjs.org/errno/-/errno-0.1.7.tgz"
  "version" "0.1.7"
  dependencies:
    "prr" "1.0.1"

"error-ex@1.3.2":
  "integrity" "sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g=="
  "resolved" "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "is-arrayish" "0.2.1"

"es-abstract@1.17.4":
  "integrity" "sha512-Ae3um/gb8F0mui/jPL+QiqmglkUsaQf7FwBEHYIFkztkneosu9imhqHpBzQ3h1vit8t5iQ74t6PEVvphBZiuiQ=="
  "resolved" "https://registry.npmjs.org/es-abstract/-/es-abstract-1.17.4.tgz"
  "version" "1.17.4"
  dependencies:
    "es-to-primitive" "1.2.1"
    "function-bind" "1.1.1"
    "has" "1.0.3"
    "has-symbols" "1.0.1"
    "is-callable" "1.1.5"
    "is-regex" "1.0.5"
    "object-inspect" "1.7.0"
    "object-keys" "1.1.1"
    "object.assign" "4.1.0"
    "string.prototype.trimleft" "2.1.1"
    "string.prototype.trimright" "2.1.1"

"es-to-primitive@1.2.1":
  "integrity" "sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA=="
  "resolved" "https://registry.npmjs.org/es-to-primitive/-/es-to-primitive-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "is-callable" "1.1.5"
    "is-date-object" "1.0.2"
    "is-symbol" "1.0.3"

"es6-promise@^4.2.6":
  "integrity" "sha512-HJDGx5daxeIvxdBxvG2cb9g4tEvwIk3i8+nhX0yGrYmZUzbkdg8QbDevheDB8gd0//uPj4c1EQua8Q+MViT0/w=="
  "resolved" "https://registry.npmjs.org/es6-promise/-/es6-promise-4.2.8.tgz"
  "version" "4.2.8"

"escape-html@1.0.3":
  "integrity" "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="
  "resolved" "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz"
  "version" "1.0.3"

"escape-string-regexp@1.0.5":
  "integrity" "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="
  "resolved" "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  "version" "1.0.5"

"escodegen@1.14.1":
  "integrity" "sha512-Bmt7NcRySdIfNPfU2ZoXDrrXsG9ZjvDxcAlMfDUgRBjLOWTuIACXPBFJH7Z+cLb40JeQco5toikyc9t9P8E9SQ=="
  "resolved" "https://registry.npmjs.org/escodegen/-/escodegen-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "esprima" "4.0.1"
    "estraverse" "4.3.0"
    "esutils" "2.0.3"
    "optionator" "0.8.3"
    "source-map" "0.6.1"

"eslint-config-react-app@3.0.8":
  "integrity" "sha512-Ovi6Bva67OjXrom9Y/SLJRkrGqKhMAL0XCH8BizPhjEVEhYczl2ZKiNZI2CuqO5/CJwAfMwRXAVGY0KToWr1aA=="
  "resolved" "https://registry.npmjs.org/eslint-config-react-app/-/eslint-config-react-app-3.0.8.tgz"
  "version" "3.0.8"
  dependencies:
    "confusing-browser-globals" "1.0.9"

"eslint-import-resolver-node@0.3.3":
  "integrity" "sha512-b8crLDo0M5RSe5YG8Pu2DYBj71tSB6OvXkfzwbJU2w7y8P4/yo0MyF8jU26IEuEuHF2K5/gcAJE3LhQGqBBbVg=="
  "resolved" "https://registry.npmjs.org/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.3.tgz"
  "version" "0.3.3"
  dependencies:
    "debug" "2.6.9"
    "resolve" "1.15.1"

"eslint-loader@2.1.1":
  "integrity" "sha512-1GrJFfSevQdYpoDzx8mEE2TDWsb/zmFuY09l6hURg1AeFIKQOvZ+vH0UPjzmd1CZIbfTV5HUkMeBmFiDBkgIsQ=="
  "resolved" "https://registry.npmjs.org/eslint-loader/-/eslint-loader-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "loader-fs-cache" "1.0.2"
    "loader-utils" "1.4.0"
    "object-assign" "4.1.1"
    "object-hash" "1.3.1"
    "rimraf" "2.6.3"

"eslint-module-utils@2.5.2":
  "integrity" "sha512-LGScZ/JSlqGKiT8OC+cYRxseMjyqt6QO54nl281CK93unD89ijSeRV6An8Ci/2nvWVKe8K/Tqdm75RQoIOCr+Q=="
  "resolved" "https://registry.npmjs.org/eslint-module-utils/-/eslint-module-utils-2.5.2.tgz"
  "version" "2.5.2"
  dependencies:
    "debug" "2.6.9"
    "pkg-dir" "2.0.0"

"eslint-plugin-flowtype@2.50.1":
  "integrity" "sha512-9kRxF9hfM/O6WGZcZPszOVPd2W0TLHBtceulLTsGfwMPtiCCLnCW0ssRiOOiXyqrCA20pm1iXdXm7gQeN306zQ=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-flowtype/-/eslint-plugin-flowtype-2.50.1.tgz"
  "version" "2.50.1"
  dependencies:
    "lodash" "4.17.15"

"eslint-plugin-import@2.14.0":
  "integrity" "sha512-FpuRtniD/AY6sXByma2Wr0TXvXJ4nA/2/04VPlfpmUDPOpOY264x+ILiwnrk/k4RINgDAyFZByxqPUbSQ5YE7g=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-import/-/eslint-plugin-import-2.14.0.tgz"
  "version" "2.14.0"
  dependencies:
    "contains-path" "0.1.0"
    "debug" "2.6.9"
    "doctrine" "1.5.0"
    "eslint-import-resolver-node" "0.3.3"
    "eslint-module-utils" "2.5.2"
    "has" "1.0.3"
    "lodash" "4.17.15"
    "minimatch" "3.0.4"
    "read-pkg-up" "2.0.0"
    "resolve" "1.10.0"

"eslint-plugin-jsx-a11y@6.1.2":
  "integrity" "sha512-7gSSmwb3A+fQwtw0arguwMdOdzmKUgnUcbSNlo+GjKLAQFuC2EZxWqG9XHRI8VscBJD5a8raz3RuxQNFW+XJbw=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-jsx-a11y/-/eslint-plugin-jsx-a11y-6.1.2.tgz"
  "version" "6.1.2"
  dependencies:
    "aria-query" "3.0.0"
    "array-includes" "3.1.1"
    "ast-types-flow" "0.0.7"
    "axobject-query" "2.1.2"
    "damerau-levenshtein" "1.0.6"
    "emoji-regex" "6.5.1"
    "has" "1.0.3"
    "jsx-ast-utils" "2.2.3"

"eslint-plugin-react@7.12.4":
  "integrity" "sha512-1puHJkXJY+oS1t467MjbqjvX53uQ05HXwjqDgdbGBqf5j9eeydI54G3KwiJmWciQ0HTBacIKw2jgwSBSH3yfgQ=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-react/-/eslint-plugin-react-7.12.4.tgz"
  "version" "7.12.4"
  dependencies:
    "array-includes" "3.1.1"
    "doctrine" "2.1.0"
    "has" "1.0.3"
    "jsx-ast-utils" "2.2.3"
    "object.fromentries" "2.0.2"
    "prop-types" "15.7.2"
    "resolve" "1.10.0"

"eslint-scope@3.7.1":
  "integrity" "sha1-PWPD7f2gLgbgGkUq2IyqzHzctug="
  "resolved" "https://registry.npmjs.org/eslint-scope/-/eslint-scope-3.7.1.tgz"
  "version" "3.7.1"
  dependencies:
    "esrecurse" "4.2.1"
    "estraverse" "4.3.0"

"eslint-scope@4.0.3":
  "integrity" "sha512-p7VutNr1O/QrxysMo3E45FjYDTeXBy0iTltPFNSqKAIfjDSXC+4dj+qfyuD8bfAXrW/y6lW3O76VaYNPKfpKrg=="
  "resolved" "https://registry.npmjs.org/eslint-scope/-/eslint-scope-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "esrecurse" "4.2.1"
    "estraverse" "4.3.0"

"eslint-utils@1.4.3":
  "integrity" "sha512-fbBN5W2xdY45KulGXmLHZ3c3FHfVYmKg0IrAKGOkT/464PQsx2UeIzfz1RmEci+KLm1bBaAzZAh8+/E+XAeZ8Q=="
  "resolved" "https://registry.npmjs.org/eslint-utils/-/eslint-utils-1.4.3.tgz"
  "version" "1.4.3"
  dependencies:
    "eslint-visitor-keys" "1.1.0"

"eslint-visitor-keys@1.1.0":
  "integrity" "sha512-8y9YjtM1JBJU/A9Kc+SbaOV4y29sSWckBwMHa+FGtVj5gN/sbnKDf6xJUl+8g7FAij9LVaP8C24DUiH/f/2Z9A=="
  "resolved" "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-1.1.0.tgz"
  "version" "1.1.0"

"eslint@5.12.0":
  "integrity" "sha512-LntwyPxtOHrsJdcSwyQKVtHofPHdv+4+mFwEe91r2V13vqpM8yLr7b1sW+Oo/yheOPkWYsYlYJCkzlFAt8KV7g=="
  "resolved" "https://registry.npmjs.org/eslint/-/eslint-5.12.0.tgz"
  "version" "5.12.0"
  dependencies:
    "@babel/code-frame" "7.8.3"
    "ajv" "6.12.0"
    "chalk" "2.4.2"
    "cross-spawn" "6.0.5"
    "debug" "4.1.1"
    "doctrine" "2.1.0"
    "eslint-scope" "4.0.3"
    "eslint-utils" "1.4.3"
    "eslint-visitor-keys" "1.1.0"
    "espree" "5.0.1"
    "esquery" "1.1.0"
    "esutils" "2.0.3"
    "file-entry-cache" "2.0.0"
    "functional-red-black-tree" "1.0.1"
    "glob" "7.1.6"
    "globals" "11.12.0"
    "ignore" "4.0.6"
    "import-fresh" "3.2.1"
    "imurmurhash" "0.1.4"
    "inquirer" "6.5.2"
    "js-yaml" "3.13.1"
    "json-stable-stringify-without-jsonify" "1.0.1"
    "levn" "0.3.0"
    "lodash" "4.17.15"
    "minimatch" "3.0.4"
    "mkdirp" "0.5.1"
    "natural-compare" "1.4.0"
    "optionator" "0.8.3"
    "path-is-inside" "1.0.2"
    "pluralize" "7.0.0"
    "progress" "2.0.3"
    "regexpp" "2.0.1"
    "semver" "5.7.1"
    "strip-ansi" "4.0.0"
    "strip-json-comments" "2.0.1"
    "table" "5.4.6"
    "text-table" "0.2.0"

"espree@5.0.1":
  "integrity" "sha512-qWAZcWh4XE/RwzLJejfcofscgMc9CamR6Tn1+XRXNzrvUSSbiAjGOI/fggztjIi7y9VLPqnICMIPiGyr8JaZ0A=="
  "resolved" "https://registry.npmjs.org/espree/-/espree-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "acorn" "6.4.0"
    "acorn-jsx" "5.2.0"
    "eslint-visitor-keys" "1.1.0"

"esprima@4.0.1":
  "integrity" "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A=="
  "resolved" "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz"
  "version" "4.0.1"

"esquery@1.1.0":
  "integrity" "sha512-MxYW9xKmROWF672KqjO75sszsA8Mxhw06YFeS5VHlB98KDHbOSurm3ArsjO60Eaf3QmGMCP1yn+0JQkNLo/97Q=="
  "resolved" "https://registry.npmjs.org/esquery/-/esquery-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "estraverse" "4.3.0"

"esrecurse@4.2.1":
  "integrity" "sha512-64RBB++fIOAXPw3P9cy89qfMlvZEXZkqqJkjqqXIvzP5ezRZjW+lPWjw35UX/3EhUPFYbg5ER4JYgDw4007/DQ=="
  "resolved" "https://registry.npmjs.org/esrecurse/-/esrecurse-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "estraverse" "4.3.0"

"estraverse@4.3.0":
  "integrity" "sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw=="
  "resolved" "https://registry.npmjs.org/estraverse/-/estraverse-4.3.0.tgz"
  "version" "4.3.0"

"esutils@2.0.3":
  "integrity" "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g=="
  "resolved" "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz"
  "version" "2.0.3"

"etag@1.8.1":
  "integrity" "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc="
  "resolved" "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz"
  "version" "1.8.1"

"eventemitter3@4.0.0":
  "integrity" "sha512-qerSRB0p+UDEssxTtm6EDKcE7W4OaoisfIMl4CngyEhjpYglocpNg6UEqCvemdGhosAsg4sO2dXJOdyBifPGCg=="
  "resolved" "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.0.tgz"
  "version" "4.0.0"

"eventlistener@0.0.1":
  "integrity" "sha1-7Suqu4UiJ68rz4iRUscsY8pTLrg="
  "resolved" "https://registry.npmjs.org/eventlistener/-/eventlistener-0.0.1.tgz"
  "version" "0.0.1"

"events@3.1.0":
  "integrity" "sha512-Rv+u8MLHNOdMjTAFeT3nCjHn2aGlx435FP/sDHNaRhDEMwyI/aB22Kj2qIN8R0cw3z28psEQLYwxVKLsKrMgWg=="
  "resolved" "https://registry.npmjs.org/events/-/events-3.1.0.tgz"
  "version" "3.1.0"

"eventsource@1.0.7":
  "integrity" "sha512-4Ln17+vVT0k8aWq+t/bF5arcS3EpT9gYtW66EPacdj/mAFevznsnyoHLPy2BA8gbIQeIHoPsvwmfBftfcG//BQ=="
  "resolved" "https://registry.npmjs.org/eventsource/-/eventsource-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "original" "1.0.2"

"evp_bytestokey@1.0.3":
  "integrity" "sha512-/f2Go4TognH/KvCISP7OUsHn85hT9nUkxxA9BEWxFn+Oj9o8ZNLm/40hdlgSLyuOimsrTKLUMEorQexp/aPQeA=="
  "resolved" "https://registry.npmjs.org/evp_bytestokey/-/evp_bytestokey-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "md5.js" "1.3.5"
    "safe-buffer" "5.2.0"

"exec-sh@0.2.2":
  "integrity" "sha512-FIUCJz1RbuS0FKTdaAafAByGS0CPvU3R0MeHxgtl+djzCc//F8HakL8GzmVNZanasTbTAY/3DRFA0KpVqj/eAw=="
  "resolved" "https://registry.npmjs.org/exec-sh/-/exec-sh-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "merge" "1.2.1"

"execa@0.10.0":
  "integrity" "sha512-7XOMnz8Ynx1gGo/3hyV9loYNPWM94jG3+3T3Y8tsfSstFmETmENCMU/A/zj8Lyaj1lkgEepKepvd6240tBRvlw=="
  "resolved" "https://registry.npmjs.org/execa/-/execa-0.10.0.tgz"
  "version" "0.10.0"
  dependencies:
    "cross-spawn" "6.0.5"
    "get-stream" "3.0.0"
    "is-stream" "1.1.0"
    "npm-run-path" "2.0.2"
    "p-finally" "1.0.0"
    "signal-exit" "3.0.2"
    "strip-eof" "1.0.0"

"execa@1.0.0":
  "integrity" "sha512-adbxcyWV46qiHyvSp50TKt05tB4tK3HcmF7/nxfAdhnox83seTDbwnaqKO4sXRy7roHAIFqJP/Rw/AuEbX61LA=="
  "resolved" "https://registry.npmjs.org/execa/-/execa-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "cross-spawn" "6.0.5"
    "get-stream" "4.1.0"
    "is-stream" "1.1.0"
    "npm-run-path" "2.0.2"
    "p-finally" "1.0.0"
    "signal-exit" "3.0.2"
    "strip-eof" "1.0.0"

"exit@0.1.2":
  "integrity" "sha1-BjJjj42HfMghB9MKD/8aF8uhzQw="
  "resolved" "https://registry.npmjs.org/exit/-/exit-0.1.2.tgz"
  "version" "0.1.2"

"expand-brackets@0.1.5":
  "integrity" "sha1-3wcoTjQqgHzXM6xa9yQR5YHRF3s="
  "resolved" "https://registry.npmjs.org/expand-brackets/-/expand-brackets-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "is-posix-bracket" "0.1.1"

"expand-brackets@2.1.4":
  "integrity" "sha1-t3c14xXOMPa27/D4OwQVGiJEliI="
  "resolved" "https://registry.npmjs.org/expand-brackets/-/expand-brackets-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "debug" "2.6.9"
    "define-property" "0.2.5"
    "extend-shallow" "2.0.1"
    "posix-character-classes" "0.1.1"
    "regex-not" "1.0.2"
    "snapdragon" "0.8.2"
    "to-regex" "3.0.2"

"expand-range@1.8.2":
  "integrity" "sha1-opnv/TNf4nIeuujiV+x5ZE/IUzc="
  "resolved" "https://registry.npmjs.org/expand-range/-/expand-range-1.8.2.tgz"
  "version" "1.8.2"
  dependencies:
    "fill-range" "2.2.4"

"expect@23.6.0":
  "integrity" "sha512-dgSoOHgmtn/aDGRVFWclQyPDKl2CQRq0hmIEoUAuQs/2rn2NcvCWcSCovm6BLeuB/7EZuLGu2QfnR+qRt5OM4w=="
  "resolved" "https://registry.npmjs.org/expect/-/expect-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "ansi-styles" "3.2.1"
    "jest-diff" "23.6.0"
    "jest-get-type" "22.4.3"
    "jest-matcher-utils" "23.6.0"
    "jest-message-util" "23.4.0"
    "jest-regex-util" "23.3.0"

"express@4.17.1":
  "integrity" "sha512-mHJ9O79RqluphRrcw2X/GTh3k9tVv8YcoyY4Kkh4WDMUYKRZUq0h1o0w2rrrxBqM7VoeUVqgb27xlEMXTnYt4g=="
  "resolved" "https://registry.npmjs.org/express/-/express-4.17.1.tgz"
  "version" "4.17.1"
  dependencies:
    "accepts" "1.3.7"
    "array-flatten" "1.1.1"
    "body-parser" "1.19.0"
    "content-disposition" "0.5.3"
    "content-type" "1.0.4"
    "cookie" "0.4.0"
    "cookie-signature" "1.0.6"
    "debug" "2.6.9"
    "depd" "1.1.2"
    "encodeurl" "1.0.2"
    "escape-html" "1.0.3"
    "etag" "1.8.1"
    "finalhandler" "1.1.2"
    "fresh" "0.5.2"
    "merge-descriptors" "1.0.1"
    "methods" "1.1.2"
    "on-finished" "2.3.0"
    "parseurl" "1.3.3"
    "path-to-regexp" "0.1.7"
    "proxy-addr" "2.0.6"
    "qs" "6.7.0"
    "range-parser" "1.2.1"
    "safe-buffer" "5.1.2"
    "send" "0.17.1"
    "serve-static" "1.14.1"
    "setprototypeof" "1.1.1"
    "statuses" "1.5.0"
    "type-is" "1.6.18"
    "utils-merge" "1.0.1"
    "vary" "1.1.2"

"extend-shallow@2.0.1":
  "integrity" "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8="
  "resolved" "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-extendable" "0.1.1"

"extend-shallow@3.0.2":
  "integrity" "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg="
  "resolved" "https://registry.npmjs.org/extend-shallow/-/extend-shallow-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "assign-symbols" "1.0.0"
    "is-extendable" "1.0.1"

"extend@3.0.2":
  "integrity" "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g=="
  "resolved" "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz"
  "version" "3.0.2"

"external-editor@3.1.0":
  "integrity" "sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew=="
  "resolved" "https://registry.npmjs.org/external-editor/-/external-editor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "chardet" "0.7.0"
    "iconv-lite" "0.4.24"
    "tmp" "0.0.33"

"extglob@0.3.2":
  "integrity" "sha1-Lhj/PS9JqydlzskCPwEdqo2DSaE="
  "resolved" "https://registry.npmjs.org/extglob/-/extglob-0.3.2.tgz"
  "version" "0.3.2"
  dependencies:
    "is-extglob" "1.0.0"

"extglob@2.0.4":
  "integrity" "sha512-Nmb6QXkELsuBr24CJSkilo6UHHgbekK5UiZgfE6UHD3Eb27YC6oD+bhcT+tJ6cl8dmsgdQxnWlcry8ksBIBLpw=="
  "resolved" "https://registry.npmjs.org/extglob/-/extglob-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "array-unique" "0.3.2"
    "define-property" "1.0.0"
    "expand-brackets" "2.1.4"
    "extend-shallow" "2.0.1"
    "fragment-cache" "0.2.1"
    "regex-not" "1.0.2"
    "snapdragon" "0.8.2"
    "to-regex" "3.0.2"

"extsprintf@1.3.0":
  "integrity" "sha1-lpGEQOMEGnpBT4xS48V06zw+HgU="
  "resolved" "https://registry.npmjs.org/extsprintf/-/extsprintf-1.3.0.tgz"
  "version" "1.3.0"

"fast-deep-equal@3.1.1":
  "integrity" "sha512-8UEa58QDLauDNfpbrX55Q9jrGHThw2ZMdOky5Gl1CDtVeJDPVrG4Jxx1N8jw2gkWaff5UUuX1KJd+9zGe2B+ZA=="
  "resolved" "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.1.tgz"
  "version" "3.1.1"

"fast-glob@2.2.7":
  "integrity" "sha512-g1KuQwHOZAmOZMuBtHdxDtju+T2RT8jgCC9aANsbpdiDDTSnjgfuVsIBNKbUeJI3oKMRExcfNDtJl4OhbffMsw=="
  "resolved" "https://registry.npmjs.org/fast-glob/-/fast-glob-2.2.7.tgz"
  "version" "2.2.7"
  dependencies:
    "@mrmlnc/readdir-enhanced" "2.2.1"
    "@nodelib/fs.stat" "1.1.3"
    "glob-parent" "3.1.0"
    "is-glob" "4.0.1"
    "merge2" "1.3.0"
    "micromatch" "3.1.10"

"fast-json-stable-stringify@2.1.0":
  "integrity" "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw=="
  "resolved" "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  "version" "2.1.0"

"fast-levenshtein@2.0.6":
  "integrity" "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc="
  "resolved" "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  "version" "2.0.6"

"fastparse@1.1.2":
  "integrity" "sha512-483XLLxTVIwWK3QTrMGRqUfUpoOs/0hbQrl2oz4J0pAcm3A3bu84wxTFqGqkJzewCLdME38xJLJAxBABfQT8sQ=="
  "resolved" "https://registry.npmjs.org/fastparse/-/fastparse-1.1.2.tgz"
  "version" "1.1.2"

"faye-websocket@0.10.0":
  "integrity" "sha1-TkkvjQTftviQA1B/btvy1QHnxvQ="
  "resolved" "https://registry.npmjs.org/faye-websocket/-/faye-websocket-0.10.0.tgz"
  "version" "0.10.0"
  dependencies:
    "websocket-driver" "0.7.3"

"faye-websocket@0.11.3":
  "integrity" "sha512-D2y4bovYpzziGgbHYtGCMjlJM36vAl/y+xUyn1C+FVx8szd1E+86KwVw6XvYSzOP8iMpm1X0I4xJD+QtUb36OA=="
  "resolved" "https://registry.npmjs.org/faye-websocket/-/faye-websocket-0.11.3.tgz"
  "version" "0.11.3"
  dependencies:
    "websocket-driver" "0.7.3"

"fb-watchman@2.0.1":
  "integrity" "sha512-DkPJKQeY6kKwmuMretBhr7G6Vodr7bFwDYTXIkfG1gjvNpaxBTQV3PbXg6bR1c1UP4jPOX0jHUbbHANL9vRjVg=="
  "resolved" "https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "bser" "2.1.1"

"fbjs@0.8.17":
  "integrity" "sha1-xNWY6taUkRJlPWWIsBpc3Nn5D90="
  "resolved" "https://registry.npmjs.org/fbjs/-/fbjs-0.8.17.tgz"
  "version" "0.8.17"
  dependencies:
    "core-js" "1.2.7"
    "isomorphic-fetch" "2.2.1"
    "loose-envify" "1.4.0"
    "object-assign" "4.1.1"
    "promise" "7.3.1"
    "setimmediate" "1.0.5"
    "ua-parser-js" "0.7.21"

"figgy-pudding@3.5.1":
  "integrity" "sha512-vNKxJHTEKNThjfrdJwHc7brvM6eVevuO5nTj6ez8ZQ1qbXTvGthucRF7S4vf2cr71QVnT70V34v0S1DyQsti0w=="
  "resolved" "https://registry.npmjs.org/figgy-pudding/-/figgy-pudding-3.5.1.tgz"
  "version" "3.5.1"

"figures@2.0.0":
  "integrity" "sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI="
  "resolved" "https://registry.npmjs.org/figures/-/figures-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "escape-string-regexp" "1.0.5"

"file-entry-cache@2.0.0":
  "integrity" "sha1-w5KZDD5oR4PYOLjISkXYoEhFg2E="
  "resolved" "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "flat-cache" "1.3.4"
    "object-assign" "4.1.1"

"file-loader@^3.0.1":
  "integrity" "sha512-4sNIOXgtH/9WZq4NvlfU3Opn5ynUsqBwSLyM+I7UOwdGigTBYfVVQEwe/msZNX/j4pCJTIM14Fsw66Svo1oVrw=="
  "resolved" "https://registry.npmjs.org/file-loader/-/file-loader-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "loader-utils" "1.4.0"
    "schema-utils" "1.0.0"

"file-loader@2.0.0":
  "integrity" "sha512-YCsBfd1ZGCyonOKLxPiKPdu+8ld9HAaMEvJewzz+b2eTF7uL5Zm/HdBF6FjCrpCMRq25Mi0U1gl4pwn2TlH7hQ=="
  "resolved" "https://registry.npmjs.org/file-loader/-/file-loader-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "loader-utils" "1.4.0"
    "schema-utils" "1.0.0"

"filename-regex@2.0.1":
  "integrity" "sha1-wcS5vuPglyXdsQa3XB4wH+LxiyY="
  "resolved" "https://registry.npmjs.org/filename-regex/-/filename-regex-2.0.1.tgz"
  "version" "2.0.1"

"fileset@2.0.3":
  "integrity" "sha1-jnVIqW08wjJ+5eZ0FocjozO7oqA="
  "resolved" "https://registry.npmjs.org/fileset/-/fileset-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "glob" "7.1.6"
    "minimatch" "3.0.4"

"filesize@3.6.1":
  "integrity" "sha512-7KjR1vv6qnicaPMi1iiTcI85CyYwRO/PSFCu6SvqL8jN2Wjt/NIYQTFtFs7fSDCYOstUkEWIQGFUg5YZQfjlcg=="
  "resolved" "https://registry.npmjs.org/filesize/-/filesize-3.6.1.tgz"
  "version" "3.6.1"

"fill-range@2.2.4":
  "integrity" "sha512-cnrcCbj01+j2gTG921VZPnHbjmdAf8oQV/iGeV2kZxGSyfYjjTyY79ErsK1WJWMpw6DaApEX72binqJE+/d+5Q=="
  "resolved" "https://registry.npmjs.org/fill-range/-/fill-range-2.2.4.tgz"
  "version" "2.2.4"
  dependencies:
    "is-number" "2.1.0"
    "isobject" "2.1.0"
    "randomatic" "3.1.1"
    "repeat-element" "1.1.3"
    "repeat-string" "1.6.1"

"fill-range@4.0.0":
  "integrity" "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc="
  "resolved" "https://registry.npmjs.org/fill-range/-/fill-range-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "extend-shallow" "2.0.1"
    "is-number" "3.0.0"
    "repeat-string" "1.6.1"
    "to-regex-range" "2.1.1"

"finalhandler@1.1.2":
  "integrity" "sha512-aAWcW57uxVNrQZqFXjITpW3sIUQmHGG3qSb9mUah9MgMC4NeWhNOlNjXEYq3HjRAvL6arUviZGGJsBg6z0zsWA=="
  "resolved" "https://registry.npmjs.org/finalhandler/-/finalhandler-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "debug" "2.6.9"
    "encodeurl" "1.0.2"
    "escape-html" "1.0.3"
    "on-finished" "2.3.0"
    "parseurl" "1.3.3"
    "statuses" "1.5.0"
    "unpipe" "1.0.0"

"find-cache-dir@0.1.1":
  "integrity" "sha1-yN765XyKUqinhPnjHFfHQumToLk="
  "resolved" "https://registry.npmjs.org/find-cache-dir/-/find-cache-dir-0.1.1.tgz"
  "version" "0.1.1"
  dependencies:
    "commondir" "1.0.1"
    "mkdirp" "0.5.1"
    "pkg-dir" "1.0.0"

"find-cache-dir@2.1.0":
  "integrity" "sha512-Tq6PixE0w/VMFfCgbONnkiQIVol/JJL7nRMi20fqzA4NRs9AfeqMGeRdPi3wIhYkxjeBaWh2rxwapn5Tu3IqOQ=="
  "resolved" "https://registry.npmjs.org/find-cache-dir/-/find-cache-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "commondir" "1.0.1"
    "make-dir" "2.1.0"
    "pkg-dir" "3.0.0"

"find-up@1.1.2":
  "integrity" "sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8="
  "resolved" "https://registry.npmjs.org/find-up/-/find-up-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "path-exists" "2.1.0"
    "pinkie-promise" "2.0.1"

"find-up@2.1.0":
  "integrity" "sha1-RdG35QbHF93UgndaK3eSCjwMV6c="
  "resolved" "https://registry.npmjs.org/find-up/-/find-up-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "locate-path" "2.0.0"

"find-up@3.0.0":
  "integrity" "sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg=="
  "resolved" "https://registry.npmjs.org/find-up/-/find-up-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "locate-path" "3.0.0"

"flat-cache@1.3.4":
  "integrity" "sha512-VwyB3Lkgacfik2vhqR4uv2rvebqmDvFu4jlN/C1RzWoJEo8I7z4Q404oiqYCkq41mni8EzQnm95emU9seckwtg=="
  "resolved" "https://registry.npmjs.org/flat-cache/-/flat-cache-1.3.4.tgz"
  "version" "1.3.4"
  dependencies:
    "circular-json" "0.3.3"
    "graceful-fs" "4.2.3"
    "rimraf" "2.6.3"
    "write" "0.2.1"

"flatten@1.0.3":
  "integrity" "sha512-dVsPA/UwQ8+2uoFe5GHtiBMu48dWLTdsuEd7CKGlZlD78r1TTWBvDuFaFGKCo/ZfEr95Uk56vZoX86OsHkUeIg=="
  "resolved" "https://registry.npmjs.org/flatten/-/flatten-1.0.3.tgz"
  "version" "1.0.3"

"flush-write-stream@1.1.1":
  "integrity" "sha512-3Z4XhFZ3992uIq0XOqb9AreonueSYphE6oYbpt5+3u06JWklbsPkNv3ZKkP9Bz/r+1MWCaMoSQ28P85+1Yc77w=="
  "resolved" "https://registry.npmjs.org/flush-write-stream/-/flush-write-stream-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "inherits" "2.0.4"
    "readable-stream" "2.3.7"

"follow-redirects@1.5.10":
  "integrity" "sha512-0V5l4Cizzvqt5D44aTXbFZz+FtyXV1vrDN6qrelxtfYQKW0KO0W2T/hkE8xvGa/540LkZlkaUjO4ailYTFtHVQ=="
  "resolved" "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.5.10.tgz"
  "version" "1.5.10"
  dependencies:
    "debug" "3.1.0"

"for-in@0.1.8":
  "integrity" "sha1-2Hc5COMSVhCZUrH9ubP6hn0ndeE="
  "resolved" "https://registry.npmjs.org/for-in/-/for-in-0.1.8.tgz"
  "version" "0.1.8"

"for-in@1.0.2":
  "integrity" "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA="
  "resolved" "https://registry.npmjs.org/for-in/-/for-in-1.0.2.tgz"
  "version" "1.0.2"

"for-own@0.1.5":
  "integrity" "sha1-UmXGgaTylNq78XyVCbZ2OqhFEM4="
  "resolved" "https://registry.npmjs.org/for-own/-/for-own-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "for-in" "1.0.2"

"for-own@1.0.0":
  "integrity" "sha1-xjMy9BXO3EsE2/5wz4NklMU8tEs="
  "resolved" "https://registry.npmjs.org/for-own/-/for-own-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "for-in" "1.0.2"

"forever-agent@0.6.1":
  "integrity" "sha1-+8cfDEGt6zf5bFd60e1C2P2sypE="
  "resolved" "https://registry.npmjs.org/forever-agent/-/forever-agent-0.6.1.tgz"
  "version" "0.6.1"

"fork-ts-checker-webpack-plugin-alt@0.4.14":
  "integrity" "sha512-s0wjOBuPdylMRBzZ4yO8LSJuzem3g0MYZFxsjRXrFDQyL5KJBVSq30+GoHM/t/r2CRU4tI6zi04sq6OXK0UYnw=="
  "resolved" "https://registry.npmjs.org/fork-ts-checker-webpack-plugin-alt/-/fork-ts-checker-webpack-plugin-alt-0.4.14.tgz"
  "version" "0.4.14"
  dependencies:
    "babel-code-frame" "6.26.0"
    "chalk" "2.4.2"
    "chokidar" "2.1.8"
    "lodash" "4.17.15"
    "micromatch" "3.1.10"
    "minimatch" "3.0.4"
    "resolve" "1.10.0"
    "tapable" "1.1.3"

"form-data@2.3.3":
  "integrity" "sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ=="
  "resolved" "https://registry.npmjs.org/form-data/-/form-data-2.3.3.tgz"
  "version" "2.3.3"
  dependencies:
    "asynckit" "0.4.0"
    "combined-stream" "1.0.8"
    "mime-types" "2.1.26"

"forwarded@0.1.2":
  "integrity" "sha1-mMI9qxF1ZXuMBXPozszZGw/xjIQ="
  "resolved" "https://registry.npmjs.org/forwarded/-/forwarded-0.1.2.tgz"
  "version" "0.1.2"

"fragment-cache@0.2.1":
  "integrity" "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk="
  "resolved" "https://registry.npmjs.org/fragment-cache/-/fragment-cache-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "map-cache" "0.2.2"

"fresh@0.5.2":
  "integrity" "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac="
  "resolved" "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz"
  "version" "0.5.2"

"from2@2.3.0":
  "integrity" "sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8="
  "resolved" "https://registry.npmjs.org/from2/-/from2-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "inherits" "2.0.4"
    "readable-stream" "2.3.7"

"fs-extra@4.0.3":
  "integrity" "sha512-q6rbdDd1o2mAnQreO7YADIxf/Whx4AHBiRf6d+/cVT8h44ss+lHgxf1FemcqDnQt9X3ct4McHr+JMGlYSsK7Cg=="
  "resolved" "https://registry.npmjs.org/fs-extra/-/fs-extra-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "graceful-fs" "4.2.3"
    "jsonfile" "4.0.0"
    "universalify" "0.1.2"

"fs-extra@7.0.1":
  "integrity" "sha512-YJDaCJZEnBmcbw13fvdAM9AwNOJwOzrE4pqMqBq5nFiEqXUqHwlK4B+3pUw6JNvfSPtX05xFHtYy/1ni01eGCw=="
  "resolved" "https://registry.npmjs.org/fs-extra/-/fs-extra-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "graceful-fs" "4.2.3"
    "jsonfile" "4.0.0"
    "universalify" "0.1.2"

"fs-write-stream-atomic@1.0.10":
  "integrity" "sha1-tH31NJPvkR33VzHnCp3tAYnbQMk="
  "resolved" "https://registry.npmjs.org/fs-write-stream-atomic/-/fs-write-stream-atomic-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "graceful-fs" "4.2.3"
    "iferr" "0.1.5"
    "imurmurhash" "0.1.4"
    "readable-stream" "2.3.7"

"fs.realpath@1.0.0":
  "integrity" "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="
  "resolved" "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz"
  "version" "1.0.0"

"function-bind@1.1.1":
  "integrity" "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A=="
  "resolved" "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz"
  "version" "1.1.1"

"functional-red-black-tree@1.0.1":
  "integrity" "sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc="
  "resolved" "https://registry.npmjs.org/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz"
  "version" "1.0.1"

"gensync@1.0.0-beta.1":
  "integrity" "sha512-r8EC6NO1sngH/zdD9fiRDLdcgnbayXah+mLgManTaIZJqEC1MZstmnox8KpnI2/fxQwrp5OpCOYWLp4rBl4Jcg=="
  "resolved" "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.1.tgz"
  "version" "1.0.0-beta.1"

"get-caller-file@1.0.3":
  "integrity" "sha512-3t6rVToeoZfYSGd8YoLFR2DJkiQrIiUrGcjvFX2mDw3bn6k2OtwHN0TNCLbBO+w8qTvimhDkv+LSscbJY1vE6w=="
  "resolved" "https://registry.npmjs.org/get-caller-file/-/get-caller-file-1.0.3.tgz"
  "version" "1.0.3"

"get-own-enumerable-property-symbols@3.0.2":
  "integrity" "sha512-I0UBV/XOz1XkIJHEUDMZAbzCThU/H8DxmSfmdGcKPnVhu2VfFqr34jr9777IyaTYvxjedWhqVIilEDsCdP5G6g=="
  "resolved" "https://registry.npmjs.org/get-own-enumerable-property-symbols/-/get-own-enumerable-property-symbols-3.0.2.tgz"
  "version" "3.0.2"

"get-stream@3.0.0":
  "integrity" "sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ="
  "resolved" "https://registry.npmjs.org/get-stream/-/get-stream-3.0.0.tgz"
  "version" "3.0.0"

"get-stream@4.1.0":
  "integrity" "sha512-GMat4EJ5161kIy2HevLlr4luNjBgvmj413KaQA7jt4V8B4RDsfpHk7WQ9GVqfYyyx8OS/L66Kox+rJRNklLK7w=="
  "resolved" "https://registry.npmjs.org/get-stream/-/get-stream-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "pump" "3.0.0"

"get-value@2.0.6":
  "integrity" "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg="
  "resolved" "https://registry.npmjs.org/get-value/-/get-value-2.0.6.tgz"
  "version" "2.0.6"

"getpass@0.1.7":
  "integrity" "sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo="
  "resolved" "https://registry.npmjs.org/getpass/-/getpass-0.1.7.tgz"
  "version" "0.1.7"
  dependencies:
    "assert-plus" "1.0.0"

"glob-base@0.3.0":
  "integrity" "sha1-27Fk9iIbHAscz4Kuoyi0l98Oo8Q="
  "resolved" "https://registry.npmjs.org/glob-base/-/glob-base-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "glob-parent" "2.0.0"
    "is-glob" "2.0.1"

"glob-parent@2.0.0":
  "integrity" "sha1-gTg9ctsFT8zPUzbaqQLxgvbtuyg="
  "resolved" "https://registry.npmjs.org/glob-parent/-/glob-parent-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "is-glob" "2.0.1"

"glob-parent@3.1.0":
  "integrity" "sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4="
  "resolved" "https://registry.npmjs.org/glob-parent/-/glob-parent-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "is-glob" "3.1.0"
    "path-dirname" "1.0.2"

"glob-to-regexp@0.3.0":
  "integrity" "sha1-jFoUlNIGbFcMw7/kSWF1rMTVAqs="
  "resolved" "https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.3.0.tgz"
  "version" "0.3.0"

"glob@7.1.6":
  "integrity" "sha512-LwaxwyZ72Lk7vZINtNNrywX0ZuLyStrdDtabefZKAY5ZGJhVtgdznluResxNmPitE0SAO+O26sWTHeKSI2wMBA=="
  "resolved" "https://registry.npmjs.org/glob/-/glob-7.1.6.tgz"
  "version" "7.1.6"
  dependencies:
    "fs.realpath" "1.0.0"
    "inflight" "1.0.6"
    "inherits" "2.0.4"
    "minimatch" "3.0.4"
    "once" "1.4.0"
    "path-is-absolute" "1.0.1"

"global-modules@2.0.0":
  "integrity" "sha512-NGbfmJBp9x8IxyJSd1P+otYK8vonoJactOogrVfFRIAEY1ukil8RSKDz2Yo7wh1oihl51l/r6W4epkeKJHqL8A=="
  "resolved" "https://registry.npmjs.org/global-modules/-/global-modules-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "global-prefix" "3.0.0"

"global-prefix@3.0.0":
  "integrity" "sha512-awConJSVCHVGND6x3tmMaKcQvwXLhjdkmomy2W+Goaui8YPgYgXJZewhg3fWC+DlfqqQuWg8AwqjGTD2nAPVWg=="
  "resolved" "https://registry.npmjs.org/global-prefix/-/global-prefix-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "ini" "1.3.5"
    "kind-of" "6.0.3"
    "which" "1.3.1"

"globals@11.12.0":
  "integrity" "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA=="
  "resolved" "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz"
  "version" "11.12.0"

"globals@9.18.0":
  "integrity" "sha512-S0nG3CLEQiY/ILxqtztTWH/3iRRdyBLw6KMDxnKMchrtbj2OFmehVh0WUCfW3DUrIgx/qFrJPICrq4Z4sTR9UQ=="
  "resolved" "https://registry.npmjs.org/globals/-/globals-9.18.0.tgz"
  "version" "9.18.0"

"globby@6.1.0":
  "integrity" "sha1-9abXDoOV4hyFj7BInWTfAkJNUGw="
  "resolved" "https://registry.npmjs.org/globby/-/globby-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "array-union" "1.0.2"
    "glob" "7.1.6"
    "object-assign" "4.1.1"
    "pify" "2.3.0"
    "pinkie-promise" "2.0.1"

"globby@8.0.2":
  "integrity" "sha512-yTzMmKygLp8RUpG1Ymu2VXPSJQZjNAZPD4ywgYEaG7e4tBJeUQBO8OpXrf1RCNcEs5alsoJYPAMiIHP0cmeC7w=="
  "resolved" "https://registry.npmjs.org/globby/-/globby-8.0.2.tgz"
  "version" "8.0.2"
  dependencies:
    "array-union" "1.0.2"
    "dir-glob" "2.0.0"
    "fast-glob" "2.2.7"
    "glob" "7.1.6"
    "ignore" "3.3.10"
    "pify" "3.0.0"
    "slash" "1.0.0"

"graceful-fs@4.2.3":
  "integrity" "sha512-a30VEBm4PEdx1dRB7MFK7BejejvCvBronbLjht+sHuGYj8PHs7M/5Z+rt5lw551vZ7yfTCj4Vuyy3mSJytDWRQ=="
  "resolved" "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.3.tgz"
  "version" "4.2.3"

"growly@1.3.0":
  "integrity" "sha1-8QdIy+dq+WS3yWyTxrzCivEgwIE="
  "resolved" "https://registry.npmjs.org/growly/-/growly-1.3.0.tgz"
  "version" "1.3.0"

"gud@1.0.0":
  "integrity" "sha512-zGEOVKFM5sVPPrYs7J5/hYEw2Pof8KCyOwyhG8sAF26mCAeUFAcYPu1mwB7hhpIP29zOIBaDqwuHdLp0jvZXjw=="
  "resolved" "https://registry.npmjs.org/gud/-/gud-1.0.0.tgz"
  "version" "1.0.0"

"gzip-size@5.0.0":
  "integrity" "sha512-5iI7omclyqrnWw4XbXAmGhPsABkSIDQonv2K0h61lybgofWa6iZyvrI3r2zsJH4P8Nb64fFVzlvfhs0g7BBxAA=="
  "resolved" "https://registry.npmjs.org/gzip-size/-/gzip-size-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "duplexer" "0.1.1"
    "pify" "3.0.0"

"hammerjs@2.0.8":
  "integrity" "sha1-BO93hiz/K7edMPdpIJWTAiK/YPE="
  "resolved" "https://registry.npmjs.org/hammerjs/-/hammerjs-2.0.8.tgz"
  "version" "2.0.8"

"handle-thing@2.0.0":
  "integrity" "sha512-d4sze1JNC454Wdo2fkuyzCr6aHcbL6PGGuFAz0Li/NcOm1tCHGnWDRmJP85dh9IhQErTc2svWFEX5xHIOo//kQ=="
  "resolved" "https://registry.npmjs.org/handle-thing/-/handle-thing-2.0.0.tgz"
  "version" "2.0.0"

"handlebars@4.7.3":
  "integrity" "sha512-SRGwSYuNfx8DwHD/6InAPzD6RgeruWLT+B8e8a7gGs8FWgHzlExpTFMEq2IA6QpAfOClpKHy6+8IqTjeBCu6Kg=="
  "resolved" "https://registry.npmjs.org/handlebars/-/handlebars-4.7.3.tgz"
  "version" "4.7.3"
  dependencies:
    "neo-async" "2.6.1"
    "optimist" "0.6.1"
    "source-map" "0.6.1"
    "uglify-js" "3.4.10"

"har-schema@2.0.0":
  "integrity" "sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI="
  "resolved" "https://registry.npmjs.org/har-schema/-/har-schema-2.0.0.tgz"
  "version" "2.0.0"

"har-validator@5.1.3":
  "integrity" "sha512-sNvOCzEQNr/qrvJgc3UG/kD4QtlHycrzwS+6mfTrrSq97BvaYcPZZI1ZSqGSPR73Cxn4LKTD4PttRwfU7jWq5g=="
  "resolved" "https://registry.npmjs.org/har-validator/-/har-validator-5.1.3.tgz"
  "version" "5.1.3"
  dependencies:
    "ajv" "6.12.0"
    "har-schema" "2.0.0"

"harmony-reflect@1.6.1":
  "integrity" "sha512-WJTeyp0JzGtHcuMsi7rw2VwtkvLa+JyfEKJCFyfcS0+CDkjQ5lHPu7zEhFZP+PDSRrEgXa5Ah0l1MbgbE41XjA=="
  "resolved" "https://registry.npmjs.org/harmony-reflect/-/harmony-reflect-1.6.1.tgz"
  "version" "1.6.1"

"has-ansi@2.0.0":
  "integrity" "sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE="
  "resolved" "https://registry.npmjs.org/has-ansi/-/has-ansi-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "ansi-regex" "2.1.1"

"has-flag@1.0.0":
  "integrity" "sha1-nZ55MWXOAXoA8AQYxD+UKnsdEfo="
  "resolved" "https://registry.npmjs.org/has-flag/-/has-flag-1.0.0.tgz"
  "version" "1.0.0"

"has-flag@3.0.0":
  "integrity" "sha1-tdRU3CGZriJWmfNGfloH87lVuv0="
  "resolved" "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz"
  "version" "3.0.0"

"has-symbols@1.0.1":
  "integrity" "sha512-PLcsoqu++dmEIZB+6totNFKq/7Do+Z0u4oT0zKOJNl3lYK6vGwwu2hjHs+68OEZbTjiUE9bgOABXbP/GvrS0Kg=="
  "resolved" "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.1.tgz"
  "version" "1.0.1"

"has-value@0.3.1":
  "integrity" "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8="
  "resolved" "https://registry.npmjs.org/has-value/-/has-value-0.3.1.tgz"
  "version" "0.3.1"
  dependencies:
    "get-value" "2.0.6"
    "has-values" "0.1.4"
    "isobject" "2.1.0"

"has-value@1.0.0":
  "integrity" "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc="
  "resolved" "https://registry.npmjs.org/has-value/-/has-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "get-value" "2.0.6"
    "has-values" "1.0.0"
    "isobject" "3.0.1"

"has-values@0.1.4":
  "integrity" "sha1-bWHeldkd/Km5oCCJrThL/49it3E="
  "resolved" "https://registry.npmjs.org/has-values/-/has-values-0.1.4.tgz"
  "version" "0.1.4"

"has-values@1.0.0":
  "integrity" "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8="
  "resolved" "https://registry.npmjs.org/has-values/-/has-values-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-number" "3.0.0"
    "kind-of" "4.0.0"

"has@1.0.3":
  "integrity" "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw=="
  "resolved" "https://registry.npmjs.org/has/-/has-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "function-bind" "1.1.1"

"hash-base@3.0.4":
  "integrity" "sha1-X8hoaEfs1zSZQDMZprCj8/auSRg="
  "resolved" "https://registry.npmjs.org/hash-base/-/hash-base-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "inherits" "2.0.4"
    "safe-buffer" "5.2.0"

"hash.js@1.1.7":
  "integrity" "sha512-taOaskGt4z4SOANNseOviYDvjEJinIkRgmp7LbKP2YTTmVxWBl87s/uzK9r+44BclBSp2X7K1hqeNfz9JbBeXA=="
  "resolved" "https://registry.npmjs.org/hash.js/-/hash.js-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "inherits" "2.0.4"
    "minimalistic-assert" "1.0.1"

"he@1.2.0":
  "integrity" "sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw=="
  "resolved" "https://registry.npmjs.org/he/-/he-1.2.0.tgz"
  "version" "1.2.0"

"hex-color-regex@1.1.0":
  "integrity" "sha512-l9sfDFsuqtOqKDsQdqrMRk0U85RZc0RtOR9yPI7mRVOa4FsR/BVnZ0shmQRM96Ji99kYZP/7hn1cedc1+ApsTQ=="
  "resolved" "https://registry.npmjs.org/hex-color-regex/-/hex-color-regex-1.1.0.tgz"
  "version" "1.1.0"

"highcharts-react-official@^3.0.0":
  "integrity" "sha512-VefJgDY2hkT9gfppsQGrRF2g5u8d9dtfHGcx2/xqiP+PkZXCqalw9xOeKVCRvJKTOh0coiDFwvVjOvB7KaGl4A=="
  "resolved" "https://registry.npmjs.org/highcharts-react-official/-/highcharts-react-official-3.0.0.tgz"
  "version" "3.0.0"

"highcharts@^8.1.0", "highcharts@>=6.0.0":
  "integrity" "sha512-F63TXO7RxsvTcpO/KOubQZWualYpCMyCTuKtoWbt7KCsfQ3Kl7Fr6HEyyJdjkYl+XlnmnKlSRi9d3HjLK9Q0wg=="
  "resolved" "https://registry.npmjs.org/highcharts/-/highcharts-8.2.2.tgz"
  "version" "8.2.2"

"history@4.10.1":
  "integrity" "sha512-36nwAD620w12kuzPAsyINPWJqlNbij+hpK1k9XRloDtym8mxzGYl2c17LnV6IAGB2Dmg4tEa7G7DlawS0+qjew=="
  "resolved" "https://registry.npmjs.org/history/-/history-4.10.1.tgz"
  "version" "4.10.1"
  dependencies:
    "@babel/runtime" "7.8.7"
    "loose-envify" "1.4.0"
    "resolve-pathname" "3.0.0"
    "tiny-invariant" "1.1.0"
    "tiny-warning" "1.0.3"
    "value-equal" "1.0.1"

"hmac-drbg@1.0.1":
  "integrity" "sha1-0nRXAQJabHdabFRXk+1QL8DGSaE="
  "resolved" "https://registry.npmjs.org/hmac-drbg/-/hmac-drbg-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "hash.js" "1.1.7"
    "minimalistic-assert" "1.0.1"
    "minimalistic-crypto-utils" "1.0.1"

"hoek@4.2.1":
  "integrity" "sha512-QLg82fGkfnJ/4iy1xZ81/9SIJiq1NGFUMGs6ParyjBZr6jW2Ufj/snDqTHixNlHdPNwN2RLVD0Pi3igeK9+JfA=="
  "resolved" "https://registry.npmjs.org/hoek/-/hoek-4.2.1.tgz"
  "version" "4.2.1"

"hoist-non-react-statics@^3.3.0", "hoist-non-react-statics@^3.3.2", "hoist-non-react-statics@3.3.2":
  "integrity" "sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw=="
  "resolved" "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz"
  "version" "3.3.2"
  dependencies:
    "react-is" "16.13.0"

"hoist-non-react-statics@2.5.5":
  "integrity" "sha512-rqcy4pJo55FTTLWt+bU8ukscqHeE/e9KWvsOW2b/a3afxQZhwkQdT1rPPCJ0rYXdj4vNcasY8zHTH+jF/qStxw=="
  "resolved" "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-2.5.5.tgz"
  "version" "2.5.5"

"home-or-tmp@2.0.0":
  "integrity" "sha1-42w/LSyufXRqhX440Y1fMqeILbg="
  "resolved" "https://registry.npmjs.org/home-or-tmp/-/home-or-tmp-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "os-homedir" "1.0.2"
    "os-tmpdir" "1.0.2"

"hoopy@0.1.4":
  "integrity" "sha512-HRcs+2mr52W0K+x8RzcLzuPPmVIKMSv97RGHy0Ea9y/mpcaK+xTrjICA04KAHi4GRzxliNqNJEFYWHghy3rSfQ=="
  "resolved" "https://registry.npmjs.org/hoopy/-/hoopy-0.1.4.tgz"
  "version" "0.1.4"

"hosted-git-info@2.8.8":
  "integrity" "sha512-f/wzC2QaWBs7t9IYqB4T3sR1xviIViXJRJTWBlx2Gf3g0Xi5vI7Yy4koXQ1c9OYDGHN9sBy1DQ2AB8fqZBWhUg=="
  "resolved" "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-2.8.8.tgz"
  "version" "2.8.8"

"hpack.js@2.1.6":
  "integrity" "sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI="
  "resolved" "https://registry.npmjs.org/hpack.js/-/hpack.js-2.1.6.tgz"
  "version" "2.1.6"
  dependencies:
    "inherits" "2.0.4"
    "obuf" "1.1.2"
    "readable-stream" "2.3.7"
    "wbuf" "1.7.3"

"hsl-regex@1.0.0":
  "integrity" "sha1-1JMwx4ntgZ4nakwNJy3/owsY/m4="
  "resolved" "https://registry.npmjs.org/hsl-regex/-/hsl-regex-1.0.0.tgz"
  "version" "1.0.0"

"hsla-regex@1.0.0":
  "integrity" "sha1-wc56MWjIxmFAM6S194d/OyJfnDg="
  "resolved" "https://registry.npmjs.org/hsla-regex/-/hsla-regex-1.0.0.tgz"
  "version" "1.0.0"

"html-comment-regex@1.1.2":
  "integrity" "sha512-P+M65QY2JQ5Y0G9KKdlDpo0zK+/OHptU5AaBwUfAIDJZk1MYf32Frm84EcOytfJE0t5JvkAnKlmjsXDnWzCJmQ=="
  "resolved" "https://registry.npmjs.org/html-comment-regex/-/html-comment-regex-1.1.2.tgz"
  "version" "1.1.2"

"html-encoding-sniffer@1.0.2":
  "integrity" "sha512-71lZziiDnsuabfdYiUeWdCVyKuqwWi23L8YeIgV9jSSZHCtb6wB1BKWooH7L3tn4/FuZJMVWyNaIDr4RGmaSYw=="
  "resolved" "https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "whatwg-encoding" "1.0.5"

"html-entities@1.2.1":
  "integrity" "sha1-DfKTUfByEWNRXfueVUPl9u7VFi8="
  "resolved" "https://registry.npmjs.org/html-entities/-/html-entities-1.2.1.tgz"
  "version" "1.2.1"

"html-minifier@3.5.21":
  "integrity" "sha512-LKUKwuJDhxNa3uf/LPR/KVjm/l3rBqtYeCOAekvG8F1vItxMUpueGd94i/asDDr8/1u7InxzFA5EeGjhhG5mMA=="
  "resolved" "https://registry.npmjs.org/html-minifier/-/html-minifier-3.5.21.tgz"
  "version" "3.5.21"
  dependencies:
    "camel-case" "3.0.0"
    "clean-css" "4.2.3"
    "commander" "2.17.1"
    "he" "1.2.0"
    "param-case" "2.1.1"
    "relateurl" "0.2.7"
    "uglify-js" "3.4.10"

"html-webpack-plugin@4.0.0-alpha.2":
  "integrity" "sha512-tyvhjVpuGqD7QYHi1l1drMQTg5i+qRxpQEGbdnYFREgOKy7aFDf/ocQ/V1fuEDlQx7jV2zMap3Hj2nE9i5eGXw=="
  "resolved" "https://registry.npmjs.org/html-webpack-plugin/-/html-webpack-plugin-4.0.0-alpha.2.tgz"
  "version" "4.0.0-alpha.2"
  dependencies:
    "@types/tapable" "1.0.2"
    "html-minifier" "3.5.21"
    "loader-utils" "1.4.0"
    "lodash" "4.17.15"
    "pretty-error" "2.1.1"
    "tapable" "1.1.3"
    "util.promisify" "1.0.0"

"htmlparser2@3.10.1":
  "integrity" "sha512-IgieNijUMbkDovyoKObU1DUhm1iwNYE/fuifEoEHfd1oZKZDaONBSkal7Y01shxsM49R4XaMdGez3WnF9UfiCQ=="
  "resolved" "https://registry.npmjs.org/htmlparser2/-/htmlparser2-3.10.1.tgz"
  "version" "3.10.1"
  dependencies:
    "domelementtype" "1.3.1"
    "domhandler" "2.4.2"
    "domutils" "1.7.0"
    "entities" "1.1.2"
    "inherits" "2.0.4"
    "readable-stream" "3.6.0"

"http-deceiver@1.2.7":
  "integrity" "sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc="
  "resolved" "https://registry.npmjs.org/http-deceiver/-/http-deceiver-1.2.7.tgz"
  "version" "1.2.7"

"http-errors@1.6.3":
  "integrity" "sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0="
  "resolved" "https://registry.npmjs.org/http-errors/-/http-errors-1.6.3.tgz"
  "version" "1.6.3"
  dependencies:
    "depd" "1.1.2"
    "inherits" "2.0.3"
    "setprototypeof" "1.1.0"
    "statuses" "1.5.0"

"http-errors@1.7.2":
  "integrity" "sha512-uUQBt3H/cSIVfch6i1EuPNy/YsRSOUBXTVfZ+yR7Zjez3qjBz6i9+i4zjNaoqcoFVI4lQJ5plg63TvGfRSDCRg=="
  "resolved" "https://registry.npmjs.org/http-errors/-/http-errors-1.7.2.tgz"
  "version" "1.7.2"
  dependencies:
    "depd" "1.1.2"
    "inherits" "2.0.3"
    "setprototypeof" "1.1.1"
    "statuses" "1.5.0"
    "toidentifier" "1.0.0"

"http-parser-js@0.4.10":
  "integrity" "sha1-ksnBN0w1CF912zWexWzCV8u5P6Q="
  "resolved" "https://registry.npmjs.org/http-parser-js/-/http-parser-js-0.4.10.tgz"
  "version" "0.4.10"

"http-proxy-middleware@0.18.0":
  "integrity" "sha512-Fs25KVMPAIIcgjMZkVHJoKg9VcXcC1C8yb9JUgeDvVXY0S/zgVIhMb+qVswDIgtJe2DfckMSY2d6TuTEutlk6Q=="
  "resolved" "https://registry.npmjs.org/http-proxy-middleware/-/http-proxy-middleware-0.18.0.tgz"
  "version" "0.18.0"
  dependencies:
    "http-proxy" "1.18.0"
    "is-glob" "4.0.1"
    "lodash" "4.17.15"
    "micromatch" "3.1.10"

"http-proxy@1.18.0":
  "integrity" "sha512-84I2iJM/n1d4Hdgc6y2+qY5mDaz2PUVjlg9znE9byl+q0uC3DeByqBGReQu5tpLK0TAqTIXScRUV+dg7+bUPpQ=="
  "resolved" "https://registry.npmjs.org/http-proxy/-/http-proxy-1.18.0.tgz"
  "version" "1.18.0"
  dependencies:
    "eventemitter3" "4.0.0"
    "follow-redirects" "1.5.10"
    "requires-port" "1.0.0"

"http-signature@1.2.0":
  "integrity" "sha1-muzZJRFHcvPZW2WmCruPfBj7rOE="
  "resolved" "https://registry.npmjs.org/http-signature/-/http-signature-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "assert-plus" "1.0.0"
    "jsprim" "1.4.1"
    "sshpk" "1.16.1"

"https-browserify@1.0.0":
  "integrity" "sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM="
  "resolved" "https://registry.npmjs.org/https-browserify/-/https-browserify-1.0.0.tgz"
  "version" "1.0.0"

"iconv-lite@0.4.24":
  "integrity" "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA=="
  "resolved" "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz"
  "version" "0.4.24"
  dependencies:
    "safer-buffer" "2.1.2"

"icss-replace-symbols@1.1.0":
  "integrity" "sha1-Bupvg2ead0njhs/h/oEq5dsiPe0="
  "resolved" "https://registry.npmjs.org/icss-replace-symbols/-/icss-replace-symbols-1.1.0.tgz"
  "version" "1.1.0"

"icss-utils@2.1.0":
  "integrity" "sha1-g/Cg7DeL8yRheLbCrZE28TWxyWI="
  "resolved" "https://registry.npmjs.org/icss-utils/-/icss-utils-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "postcss" "6.0.23"

"identity-obj-proxy@3.0.0":
  "integrity" "sha1-lNK9qWCERT7zb7xarsN+D3nx/BQ="
  "resolved" "https://registry.npmjs.org/identity-obj-proxy/-/identity-obj-proxy-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "harmony-reflect" "1.6.1"

"ieee754@1.1.13":
  "integrity" "sha512-4vf7I2LYV/HaWerSo3XmlMkp5eZ83i+/CDluXi/IGTs/O1sejBNhTtnxzmRZfvOUqj7lZjqHkeTvpgSFDlWZTg=="
  "resolved" "https://registry.npmjs.org/ieee754/-/ieee754-1.1.13.tgz"
  "version" "1.1.13"

"iferr@0.1.5":
  "integrity" "sha1-xg7taebY/bazEEofy8ocGS3FtQE="
  "resolved" "https://registry.npmjs.org/iferr/-/iferr-0.1.5.tgz"
  "version" "0.1.5"

"ignore@3.3.10":
  "integrity" "sha512-Pgs951kaMm5GXP7MOvxERINe3gsaVjUWFm+UZPSq9xYriQAksyhg0csnS0KXSNRD5NmNdapXEpjxG49+AKh/ug=="
  "resolved" "https://registry.npmjs.org/ignore/-/ignore-3.3.10.tgz"
  "version" "3.3.10"

"ignore@4.0.6":
  "integrity" "sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg=="
  "resolved" "https://registry.npmjs.org/ignore/-/ignore-4.0.6.tgz"
  "version" "4.0.6"

"image-size@0.5.5":
  "integrity" "sha1-Cd/Uq50g4p6xw+gLiZA3jfnjy5w="
  "resolved" "https://registry.npmjs.org/image-size/-/image-size-0.5.5.tgz"
  "version" "0.5.5"

"immer@1.10.0":
  "integrity" "sha512-O3sR1/opvCDGLEVcvrGTMtLac8GJ5IwZC4puPrLuRj3l7ICKvkmA0vGuU9OW8mV9WIBRnaxp5GJh9IEAaNOoYg=="
  "resolved" "https://registry.npmjs.org/immer/-/immer-1.10.0.tgz"
  "version" "1.10.0"

"immutable@3.7.6":
  "integrity" "sha1-E7TTyxK++hVIKib+Gy665kAHHks="
  "resolved" "https://registry.npmjs.org/immutable/-/immutable-3.7.6.tgz"
  "version" "3.7.6"

"import-cwd@2.1.0":
  "integrity" "sha1-qmzzbnInYShcs3HsZRn1PiQ1sKk="
  "resolved" "https://registry.npmjs.org/import-cwd/-/import-cwd-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "import-from" "2.1.0"

"import-fresh@2.0.0":
  "integrity" "sha1-2BNVwVYS04bGH53dOSLUMEgipUY="
  "resolved" "https://registry.npmjs.org/import-fresh/-/import-fresh-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "caller-path" "2.0.0"
    "resolve-from" "3.0.0"

"import-fresh@3.2.1":
  "integrity" "sha512-6e1q1cnWP2RXD9/keSkxHScg508CdXqXWgWBaETNhyuBFz+kUZlKboh+ISK+bU++DmbHimVBrOz/zzPe0sZ3sQ=="
  "resolved" "https://registry.npmjs.org/import-fresh/-/import-fresh-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "parent-module" "1.0.1"
    "resolve-from" "4.0.0"

"import-from@2.1.0":
  "integrity" "sha1-M1238qev/VOqpHHUuAId7ja387E="
  "resolved" "https://registry.npmjs.org/import-from/-/import-from-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "resolve-from" "3.0.0"

"import-local@1.0.0":
  "integrity" "sha512-vAaZHieK9qjGo58agRBg+bhHX3hoTZU/Oa3GESWLz7t1U62fk63aHuDJJEteXoDeTCcPmUT+z38gkHPZkkmpmQ=="
  "resolved" "https://registry.npmjs.org/import-local/-/import-local-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "pkg-dir" "2.0.0"
    "resolve-cwd" "2.0.0"

"import-local@2.0.0":
  "integrity" "sha512-b6s04m3O+s3CGSbqDIyP4R6aAwAeYlVq9+WUWep6iHa8ETRf9yei1U48C5MmfJmV9AiLYYBKPMq/W+/WRpQmCQ=="
  "resolved" "https://registry.npmjs.org/import-local/-/import-local-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "pkg-dir" "3.0.0"
    "resolve-cwd" "2.0.0"

"imurmurhash@0.1.4":
  "integrity" "sha1-khi5srkoojixPcT7a21XbyMUU+o="
  "resolved" "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz"
  "version" "0.1.4"

"indexes-of@1.0.1":
  "integrity" "sha1-8w9xbI4r00bHtn0985FVZqfAVgc="
  "resolved" "https://registry.npmjs.org/indexes-of/-/indexes-of-1.0.1.tgz"
  "version" "1.0.1"

"inflight@1.0.6":
  "integrity" "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk="
  "resolved" "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "once" "1.4.0"
    "wrappy" "1.0.2"

"inherits@2.0.1":
  "integrity" "sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE="
  "resolved" "https://registry.npmjs.org/inherits/-/inherits-2.0.1.tgz"
  "version" "2.0.1"

"inherits@2.0.3":
  "integrity" "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="
  "resolved" "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz"
  "version" "2.0.3"

"inherits@2.0.4":
  "integrity" "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="
  "resolved" "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
  "version" "2.0.4"

"ini@1.3.5":
  "integrity" "sha512-RZY5huIKCMRWDUqZlEi72f/lmXKMvuszcMBduliQ3nnWbx9X/ZBQO7DijMEYS9EhHBb2qacRUMtC7svLwe0lcw=="
  "resolved" "https://registry.npmjs.org/ini/-/ini-1.3.5.tgz"
  "version" "1.3.5"

"inquirer@6.2.1":
  "integrity" "sha512-088kl3DRT2dLU5riVMKKr1DlImd6X7smDhpXUCkJDCKvTEJeRiXh0G132HG9u5a+6Ylw9plFRY7RuTnwohYSpg=="
  "resolved" "https://registry.npmjs.org/inquirer/-/inquirer-6.2.1.tgz"
  "version" "6.2.1"
  dependencies:
    "ansi-escapes" "3.2.0"
    "chalk" "2.4.2"
    "cli-cursor" "2.1.0"
    "cli-width" "2.2.0"
    "external-editor" "3.1.0"
    "figures" "2.0.0"
    "lodash" "4.17.15"
    "mute-stream" "0.0.7"
    "run-async" "2.4.0"
    "rxjs" "6.5.4"
    "string-width" "2.1.1"
    "strip-ansi" "5.0.0"
    "through" "2.3.8"

"inquirer@6.5.2":
  "integrity" "sha512-cntlB5ghuB0iuO65Ovoi8ogLHiWGs/5yNrtUcKjFhSSiVeAIVpD7koaSU9RM8mpXw5YDi9RdYXGQMaOURB7ycQ=="
  "resolved" "https://registry.npmjs.org/inquirer/-/inquirer-6.5.2.tgz"
  "version" "6.5.2"
  dependencies:
    "ansi-escapes" "3.2.0"
    "chalk" "2.4.2"
    "cli-cursor" "2.1.0"
    "cli-width" "2.2.0"
    "external-editor" "3.1.0"
    "figures" "2.0.0"
    "lodash" "4.17.15"
    "mute-stream" "0.0.7"
    "run-async" "2.4.0"
    "rxjs" "6.5.4"
    "string-width" "2.1.1"
    "strip-ansi" "5.2.0"
    "through" "2.3.8"

"insert-css@2.0.0":
  "integrity" "sha1-610Ql7dUL0x56jBg067gfQU4gPQ="
  "resolved" "https://registry.npmjs.org/insert-css/-/insert-css-2.0.0.tgz"
  "version" "2.0.0"

"internal-ip@3.0.1":
  "integrity" "sha512-NXXgESC2nNVtU+pqmC9e6R8B1GpKxzsAQhffvh5AL79qKnodd+L7tnEQmTiUAVngqLalPbSqRA7XGIEL5nCd0Q=="
  "resolved" "https://registry.npmjs.org/internal-ip/-/internal-ip-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "default-gateway" "2.7.2"
    "ipaddr.js" "1.9.1"

"invariant@2.2.4":
  "integrity" "sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA=="
  "resolved" "https://registry.npmjs.org/invariant/-/invariant-2.2.4.tgz"
  "version" "2.2.4"
  dependencies:
    "loose-envify" "1.4.0"

"invert-kv@2.0.0":
  "integrity" "sha512-wPVv/y/QQ/Uiirj/vh3oP+1Ww+AWehmi1g5fFWGPF6IpCBCDVrhgHRMvrLfdYcwDh3QJbGXDW4JAuzxElLSqKA=="
  "resolved" "https://registry.npmjs.org/invert-kv/-/invert-kv-2.0.0.tgz"
  "version" "2.0.0"

"ip-regex@2.1.0":
  "integrity" "sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk="
  "resolved" "https://registry.npmjs.org/ip-regex/-/ip-regex-2.1.0.tgz"
  "version" "2.1.0"

"ip@1.1.5":
  "integrity" "sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo="
  "resolved" "https://registry.npmjs.org/ip/-/ip-1.1.5.tgz"
  "version" "1.1.5"

"ipaddr.js@1.9.1":
  "integrity" "sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g=="
  "resolved" "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz"
  "version" "1.9.1"

"is-absolute-url@2.1.0":
  "integrity" "sha1-UFMN+4T8yap9vnhS6Do3uTufKqY="
  "resolved" "https://registry.npmjs.org/is-absolute-url/-/is-absolute-url-2.1.0.tgz"
  "version" "2.1.0"

"is-accessor-descriptor@0.1.6":
  "integrity" "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY="
  "resolved" "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "kind-of" "3.2.2"

"is-accessor-descriptor@1.0.0":
  "integrity" "sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ=="
  "resolved" "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "6.0.3"

"is-arguments@1.0.4":
  "integrity" "sha512-xPh0Rmt8NE65sNzvyUmWgI1tz3mKq74lGA0mL8LYZcoIzKOzDh6HmrYm3d18k60nHerC8A9Km8kYu87zfSFnLA=="
  "resolved" "https://registry.npmjs.org/is-arguments/-/is-arguments-1.0.4.tgz"
  "version" "1.0.4"

"is-arrayish@0.2.1":
  "integrity" "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0="
  "resolved" "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz"
  "version" "0.2.1"

"is-arrayish@0.3.2":
  "integrity" "sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ=="
  "resolved" "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.3.2.tgz"
  "version" "0.3.2"

"is-binary-path@1.0.1":
  "integrity" "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg="
  "resolved" "https://registry.npmjs.org/is-binary-path/-/is-binary-path-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "binary-extensions" "1.13.1"

"is-buffer@1.1.6":
  "integrity" "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w=="
  "resolved" "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz"
  "version" "1.1.6"

"is-buffer@2.0.4":
  "integrity" "sha512-Kq1rokWXOPXWuaMAqZiJW4XxsmD9zGx9q4aePabbn3qCRGedtH7Cm+zV8WETitMfu1wdh+Rvd6w5egwSngUX2A=="
  "resolved" "https://registry.npmjs.org/is-buffer/-/is-buffer-2.0.4.tgz"
  "version" "2.0.4"

"is-callable@1.1.5":
  "integrity" "sha512-ESKv5sMCJB2jnHTWZ3O5itG+O128Hsus4K4Qh1h2/cgn2vbgnLSVqfV46AeJA9D5EeeLa9w81KUXMtn34zhX+Q=="
  "resolved" "https://registry.npmjs.org/is-callable/-/is-callable-1.1.5.tgz"
  "version" "1.1.5"

"is-ci@1.2.1":
  "integrity" "sha512-s6tfsaQaQi3JNciBH6shVqEDvhGut0SUXr31ag8Pd8BBbVVlcGfWhpPmEOoM6RJ5TFhbypvf5yyRw/VXW1IiWg=="
  "resolved" "https://registry.npmjs.org/is-ci/-/is-ci-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "ci-info" "1.6.0"

"is-color-stop@1.1.0":
  "integrity" "sha1-z/9HGu5N1cnhWFmPvhKWe1za00U="
  "resolved" "https://registry.npmjs.org/is-color-stop/-/is-color-stop-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "css-color-names" "0.0.4"
    "hex-color-regex" "1.1.0"
    "hsl-regex" "1.0.0"
    "hsla-regex" "1.0.0"
    "rgb-regex" "1.0.1"
    "rgba-regex" "1.0.0"

"is-data-descriptor@0.1.4":
  "integrity" "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y="
  "resolved" "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "kind-of" "3.2.2"

"is-data-descriptor@1.0.0":
  "integrity" "sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ=="
  "resolved" "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "6.0.3"

"is-date-object@1.0.2":
  "integrity" "sha512-USlDT524woQ08aoZFzh3/Z6ch9Y/EWXEHQ/AaRN0SkKq4t2Jw2R2339tSXmwuVoY7LLlBCbOIlx2myP/L5zk0g=="
  "resolved" "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.2.tgz"
  "version" "1.0.2"

"is-descriptor@0.1.6":
  "integrity" "sha512-avDYr0SB3DwO9zsMov0gKCESFYqCnE4hq/4z3TdUlukEy5t9C0YRq7HLrsN52NAcqXKaepeCD0n+B0arnVG3Hg=="
  "resolved" "https://registry.npmjs.org/is-descriptor/-/is-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "is-accessor-descriptor" "0.1.6"
    "is-data-descriptor" "0.1.4"
    "kind-of" "5.1.0"

"is-descriptor@1.0.2":
  "integrity" "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg=="
  "resolved" "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-accessor-descriptor" "1.0.0"
    "is-data-descriptor" "1.0.0"
    "kind-of" "6.0.3"

"is-directory@0.3.1":
  "integrity" "sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE="
  "resolved" "https://registry.npmjs.org/is-directory/-/is-directory-0.3.1.tgz"
  "version" "0.3.1"

"is-dotfile@1.0.3":
  "integrity" "sha1-pqLzL/0t+wT1yiXs0Pa4PPeYoeE="
  "resolved" "https://registry.npmjs.org/is-dotfile/-/is-dotfile-1.0.3.tgz"
  "version" "1.0.3"

"is-equal-shallow@0.1.3":
  "integrity" "sha1-IjgJj8Ih3gvPpdnqxMRdY4qhxTQ="
  "resolved" "https://registry.npmjs.org/is-equal-shallow/-/is-equal-shallow-0.1.3.tgz"
  "version" "0.1.3"
  dependencies:
    "is-primitive" "2.0.0"

"is-extendable@0.1.1":
  "integrity" "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik="
  "resolved" "https://registry.npmjs.org/is-extendable/-/is-extendable-0.1.1.tgz"
  "version" "0.1.1"

"is-extendable@1.0.1":
  "integrity" "sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA=="
  "resolved" "https://registry.npmjs.org/is-extendable/-/is-extendable-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "is-plain-object" "2.0.4"

"is-extglob@1.0.0":
  "integrity" "sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA="
  "resolved" "https://registry.npmjs.org/is-extglob/-/is-extglob-1.0.0.tgz"
  "version" "1.0.0"

"is-extglob@2.1.1":
  "integrity" "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI="
  "resolved" "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-finite@1.1.0":
  "integrity" "sha512-cdyMtqX/BOqqNBBiKlIVkytNHm49MtMlYyn1zxzvJKWmFMlGzm+ry5BBfYyeY9YmNKbRSo/o7OX9w9ale0wg3w=="
  "resolved" "https://registry.npmjs.org/is-finite/-/is-finite-1.1.0.tgz"
  "version" "1.1.0"

"is-fullwidth-code-point@1.0.0":
  "integrity" "sha1-754xOG8DGn8NZDr4L95QxFfvAMs="
  "resolved" "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "number-is-nan" "1.0.1"

"is-fullwidth-code-point@2.0.0":
  "integrity" "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8="
  "resolved" "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz"
  "version" "2.0.0"

"is-generator-fn@1.0.0":
  "integrity" "sha1-lp1J4bszKfa7fwkIm+JleLLd1Go="
  "resolved" "https://registry.npmjs.org/is-generator-fn/-/is-generator-fn-1.0.0.tgz"
  "version" "1.0.0"

"is-glob@2.0.1":
  "integrity" "sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM="
  "resolved" "https://registry.npmjs.org/is-glob/-/is-glob-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-extglob" "1.0.0"

"is-glob@3.1.0":
  "integrity" "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo="
  "resolved" "https://registry.npmjs.org/is-glob/-/is-glob-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "is-extglob" "2.1.1"

"is-glob@4.0.1":
  "integrity" "sha512-5G0tKtBTFImOqDnLB2hG6Bp2qcKEFduo4tZu9MT/H6NQv/ghhy30o55ufafxJ/LdH79LLs2Kfrn85TLKyA7BUg=="
  "resolved" "https://registry.npmjs.org/is-glob/-/is-glob-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "is-extglob" "2.1.1"

"is-mobile@2.2.1":
  "integrity" "sha512-6zELsfVFr326eq2CI53yvqq6YBanOxKBybwDT+MbMS2laBnK6Ez8m5XHSuTQQbnKRfpDzCod1CMWW5q3wZYMvA=="
  "resolved" "https://registry.npmjs.org/is-mobile/-/is-mobile-2.2.1.tgz"
  "version" "2.2.1"

"is-number@2.1.0":
  "integrity" "sha1-Afy7s5NGOlSPL0ZszhbezknbkI8="
  "resolved" "https://registry.npmjs.org/is-number/-/is-number-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "kind-of" "3.2.2"

"is-number@3.0.0":
  "integrity" "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU="
  "resolved" "https://registry.npmjs.org/is-number/-/is-number-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "kind-of" "3.2.2"

"is-number@4.0.0":
  "integrity" "sha512-rSklcAIlf1OmFdyAqbnWTLVelsQ58uvZ66S/ZyawjWqIviTWCjg2PzVGw8WUA+nNuPTqb4wgA+NszrJ+08LlgQ=="
  "resolved" "https://registry.npmjs.org/is-number/-/is-number-4.0.0.tgz"
  "version" "4.0.0"

"is-obj@1.0.1":
  "integrity" "sha1-PkcprB9f3gJc19g6iW2rn09n2w8="
  "resolved" "https://registry.npmjs.org/is-obj/-/is-obj-1.0.1.tgz"
  "version" "1.0.1"

"is-obj@2.0.0":
  "integrity" "sha512-drqDG3cbczxxEJRoOXcOjtdp1J/lyp1mNn0xaznRs8+muBhgQcrnbspox5X5fOw0HnMnbfDzvnEMEtqDEJEo8w=="
  "resolved" "https://registry.npmjs.org/is-obj/-/is-obj-2.0.0.tgz"
  "version" "2.0.0"

"is-path-cwd@1.0.0":
  "integrity" "sha1-0iXsIxMuie3Tj9p2dHLmLmXxEG0="
  "resolved" "https://registry.npmjs.org/is-path-cwd/-/is-path-cwd-1.0.0.tgz"
  "version" "1.0.0"

"is-path-in-cwd@1.0.1":
  "integrity" "sha512-FjV1RTW48E7CWM7eE/J2NJvAEEVektecDBVBE5Hh3nM1Jd0kvhHtX68Pr3xsDf857xt3Y4AkwVULK1Vku62aaQ=="
  "resolved" "https://registry.npmjs.org/is-path-in-cwd/-/is-path-in-cwd-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "is-path-inside" "1.0.1"

"is-path-inside@1.0.1":
  "integrity" "sha1-jvW33lBDej/cprToZe96pVy0gDY="
  "resolved" "https://registry.npmjs.org/is-path-inside/-/is-path-inside-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "path-is-inside" "1.0.2"

"is-plain-object@2.0.4":
  "integrity" "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og=="
  "resolved" "https://registry.npmjs.org/is-plain-object/-/is-plain-object-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "isobject" "3.0.1"

"is-posix-bracket@0.1.1":
  "integrity" "sha1-MzTceXdDaOkvAW5vvAqI9c1ua8Q="
  "resolved" "https://registry.npmjs.org/is-posix-bracket/-/is-posix-bracket-0.1.1.tgz"
  "version" "0.1.1"

"is-primitive@2.0.0":
  "integrity" "sha1-IHurkWOEmcB7Kt8kCkGochADRXU="
  "resolved" "https://registry.npmjs.org/is-primitive/-/is-primitive-2.0.0.tgz"
  "version" "2.0.0"

"is-promise@2.1.0":
  "integrity" "sha1-eaKp7OfwlugPNtKy87wWwf9L8/o="
  "resolved" "https://registry.npmjs.org/is-promise/-/is-promise-2.1.0.tgz"
  "version" "2.1.0"

"is-regex@1.0.5":
  "integrity" "sha512-vlKW17SNq44owv5AQR3Cq0bQPEb8+kF3UKZ2fiZNOWtztYE5i0CzCZxFDwO58qAOWtxdBRVO/V5Qin1wjCqFYQ=="
  "resolved" "https://registry.npmjs.org/is-regex/-/is-regex-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "has" "1.0.3"

"is-regexp@1.0.0":
  "integrity" "sha1-/S2INUXEa6xaYz57mgnof6LLUGk="
  "resolved" "https://registry.npmjs.org/is-regexp/-/is-regexp-1.0.0.tgz"
  "version" "1.0.0"

"is-resolvable@1.1.0":
  "integrity" "sha512-qgDYXFSR5WvEfuS5dMj6oTMEbrrSaM0CrFk2Yiq/gXnBvD9pMa2jGXxyhGLfvhZpuMZe18CJpFxAt3CRs42NMg=="
  "resolved" "https://registry.npmjs.org/is-resolvable/-/is-resolvable-1.1.0.tgz"
  "version" "1.1.0"

"is-root@2.0.0":
  "integrity" "sha512-F/pJIk8QD6OX5DNhRB7hWamLsUilmkDGho48KbgZ6xg/lmAZXHxzXQ91jzB3yRSw5kdQGGGc4yz8HYhTYIMWPg=="
  "resolved" "https://registry.npmjs.org/is-root/-/is-root-2.0.0.tgz"
  "version" "2.0.0"

"is-stream@1.1.0":
  "integrity" "sha1-EtSj3U5o4Lec6428hBc66A2RykQ="
  "resolved" "https://registry.npmjs.org/is-stream/-/is-stream-1.1.0.tgz"
  "version" "1.1.0"

"is-string@1.0.5":
  "integrity" "sha512-buY6VNRjhQMiF1qWDouloZlQbRhDPCebwxSjxMjxgemYT46YMd2NR0/H+fBhEfWX4A/w9TBJ+ol+okqJKFE6vQ=="
  "resolved" "https://registry.npmjs.org/is-string/-/is-string-1.0.5.tgz"
  "version" "1.0.5"

"is-svg@3.0.0":
  "integrity" "sha512-gi4iHK53LR2ujhLVVj+37Ykh9GLqYHX6JOVXbLAucaG/Cqw9xwdFOjDM2qeifLs1sF1npXXFvDu0r5HNgCMrzQ=="
  "resolved" "https://registry.npmjs.org/is-svg/-/is-svg-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "html-comment-regex" "1.1.2"

"is-symbol@1.0.3":
  "integrity" "sha512-OwijhaRSgqvhm/0ZdAcXNZt9lYdKFpcRDT5ULUuYXPoT794UNOdU+gpT6Rzo7b4V2HUl/op6GqY894AZwv9faQ=="
  "resolved" "https://registry.npmjs.org/is-symbol/-/is-symbol-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "has-symbols" "1.0.1"

"is-typedarray@1.0.0":
  "integrity" "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo="
  "resolved" "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz"
  "version" "1.0.0"

"is-utf8@0.2.1":
  "integrity" "sha1-Sw2hRCEE0bM2NA6AeX6GXPOffXI="
  "resolved" "https://registry.npmjs.org/is-utf8/-/is-utf8-0.2.1.tgz"
  "version" "0.2.1"

"is-windows@1.0.2":
  "integrity" "sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA=="
  "resolved" "https://registry.npmjs.org/is-windows/-/is-windows-1.0.2.tgz"
  "version" "1.0.2"

"is-wsl@1.1.0":
  "integrity" "sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0="
  "resolved" "https://registry.npmjs.org/is-wsl/-/is-wsl-1.1.0.tgz"
  "version" "1.1.0"

"isarray@0.0.1":
  "integrity" "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="
  "resolved" "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz"
  "version" "0.0.1"

"isarray@1.0.0":
  "integrity" "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="
  "resolved" "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz"
  "version" "1.0.0"

"isemail@3.2.0":
  "integrity" "sha512-zKqkK+O+dGqevc93KNsbZ/TqTUFd46MwWjYOoMrjIMZ51eU7DtQG3Wmd9SQQT7i7RVnuTPEiYEWHU3MSbxC1Tg=="
  "resolved" "https://registry.npmjs.org/isemail/-/isemail-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "punycode" "2.1.1"

"isexe@2.0.0":
  "integrity" "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="
  "resolved" "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  "version" "2.0.0"

"isobject@2.1.0":
  "integrity" "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk="
  "resolved" "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "isarray" "1.0.0"

"isobject@3.0.1":
  "integrity" "sha1-TkMekrEalzFjaqH5yNHMvP2reN8="
  "resolved" "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz"
  "version" "3.0.1"

"isomorphic-fetch@2.2.1":
  "integrity" "sha1-YRrhrPFPXoH3KVB0coGf6XM1WKk="
  "resolved" "https://registry.npmjs.org/isomorphic-fetch/-/isomorphic-fetch-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "node-fetch" "1.7.3"
    "whatwg-fetch" "3.0.0"

"isstream@0.1.2":
  "integrity" "sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo="
  "resolved" "https://registry.npmjs.org/isstream/-/isstream-0.1.2.tgz"
  "version" "0.1.2"

"istanbul-api@1.3.7":
  "integrity" "sha512-4/ApBnMVeEPG3EkSzcw25wDe4N66wxwn+KKn6b47vyek8Xb3NBAcg4xfuQbS7BqcZuTX4wxfD5lVagdggR3gyA=="
  "resolved" "https://registry.npmjs.org/istanbul-api/-/istanbul-api-1.3.7.tgz"
  "version" "1.3.7"
  dependencies:
    "async" "2.6.3"
    "fileset" "2.0.3"
    "istanbul-lib-coverage" "1.2.1"
    "istanbul-lib-hook" "1.2.2"
    "istanbul-lib-instrument" "1.10.2"
    "istanbul-lib-report" "1.1.5"
    "istanbul-lib-source-maps" "1.2.6"
    "istanbul-reports" "1.5.1"
    "js-yaml" "3.13.1"
    "mkdirp" "0.5.1"
    "once" "1.4.0"

"istanbul-lib-coverage@1.2.1":
  "integrity" "sha512-PzITeunAgyGbtY1ibVIUiV679EFChHjoMNRibEIobvmrCRaIgwLxNucOSimtNWUhEib/oO7QY2imD75JVgCJWQ=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-1.2.1.tgz"
  "version" "1.2.1"

"istanbul-lib-hook@1.2.2":
  "integrity" "sha512-/Jmq7Y1VeHnZEQ3TL10VHyb564mn6VrQXHchON9Jf/AEcmQ3ZIiyD1BVzNOKTZf/G3gE+kiGK6SmpF9y3qGPLw=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-hook/-/istanbul-lib-hook-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "append-transform" "0.4.0"

"istanbul-lib-instrument@1.10.2":
  "integrity" "sha512-aWHxfxDqvh/ZlxR8BBaEPVSWDPUkGD63VjGQn3jcw8jCp7sHEMKcrj4xfJn/ABzdMEHiQNyvDQhqm5o8+SQg7A=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.10.2.tgz"
  "version" "1.10.2"
  dependencies:
    "babel-generator" "6.26.1"
    "babel-template" "6.26.0"
    "babel-traverse" "6.26.0"
    "babel-types" "6.26.0"
    "babylon" "6.18.0"
    "istanbul-lib-coverage" "1.2.1"
    "semver" "5.7.1"

"istanbul-lib-report@1.1.5":
  "integrity" "sha512-UsYfRMoi6QO/doUshYNqcKJqVmFe9w51GZz8BS3WB0lYxAllQYklka2wP9+dGZeHYaWIdcXUx8JGdbqaoXRXzw=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "istanbul-lib-coverage" "1.2.1"
    "mkdirp" "0.5.1"
    "path-parse" "1.0.6"
    "supports-color" "3.2.3"

"istanbul-lib-source-maps@1.2.6":
  "integrity" "sha512-TtbsY5GIHgbMsMiRw35YBHGpZ1DVFEO19vxxeiDMYaeOFOCzfnYVxvl6pOUIZR4dtPhAGpSMup8OyF8ubsaqEg=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-1.2.6.tgz"
  "version" "1.2.6"
  dependencies:
    "debug" "3.1.0"
    "istanbul-lib-coverage" "1.2.1"
    "mkdirp" "0.5.1"
    "rimraf" "2.6.3"
    "source-map" "0.5.7"

"istanbul-reports@1.5.1":
  "integrity" "sha512-+cfoZ0UXzWjhAdzosCPP3AN8vvef8XDkWtTfgaN+7L3YTpNYITnCaEkceo5SEYy644VkHka/P1FvkWvrG/rrJw=="
  "resolved" "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "handlebars" "4.7.3"

"jest-changed-files@23.4.2":
  "integrity" "sha512-EyNhTAUWEfwnK0Is/09LxoqNDOn7mU7S3EHskG52djOFS/z+IT0jT3h3Ql61+dklcG7bJJitIWEMB4Sp1piHmA=="
  "resolved" "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-23.4.2.tgz"
  "version" "23.4.2"
  dependencies:
    "throat" "4.1.0"

"jest-cli@23.6.0":
  "integrity" "sha512-hgeD1zRUp1E1zsiyOXjEn4LzRLWdJBV//ukAHGlx6s5mfCNJTbhbHjgxnDUXA8fsKWN/HqFFF6X5XcCwC/IvYQ=="
  "resolved" "https://registry.npmjs.org/jest-cli/-/jest-cli-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "ansi-escapes" "3.2.0"
    "chalk" "2.4.2"
    "exit" "0.1.2"
    "glob" "7.1.6"
    "graceful-fs" "4.2.3"
    "import-local" "1.0.0"
    "is-ci" "1.2.1"
    "istanbul-api" "1.3.7"
    "istanbul-lib-coverage" "1.2.1"
    "istanbul-lib-instrument" "1.10.2"
    "istanbul-lib-source-maps" "1.2.6"
    "jest-changed-files" "23.4.2"
    "jest-config" "23.6.0"
    "jest-environment-jsdom" "23.4.0"
    "jest-get-type" "22.4.3"
    "jest-haste-map" "23.6.0"
    "jest-message-util" "23.4.0"
    "jest-regex-util" "23.3.0"
    "jest-resolve-dependencies" "23.6.0"
    "jest-runner" "23.6.0"
    "jest-runtime" "23.6.0"
    "jest-snapshot" "23.6.0"
    "jest-util" "23.4.0"
    "jest-validate" "23.6.0"
    "jest-watcher" "23.4.0"
    "jest-worker" "23.2.0"
    "micromatch" "2.3.11"
    "node-notifier" "5.4.3"
    "prompts" "0.1.14"
    "realpath-native" "1.1.0"
    "rimraf" "2.6.3"
    "slash" "1.0.0"
    "string-length" "2.0.0"
    "strip-ansi" "4.0.0"
    "which" "1.3.1"
    "yargs" "11.1.1"

"jest-config@23.6.0":
  "integrity" "sha512-i8V7z9BeDXab1+VNo78WM0AtWpBRXJLnkT+lyT+Slx/cbP5sZJ0+NDuLcmBE5hXAoK0aUp7vI+MOxR+R4d8SRQ=="
  "resolved" "https://registry.npmjs.org/jest-config/-/jest-config-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "babel-core" "6.26.3"
    "babel-jest" "23.6.0"
    "chalk" "2.4.2"
    "glob" "7.1.6"
    "jest-environment-jsdom" "23.4.0"
    "jest-environment-node" "23.4.0"
    "jest-get-type" "22.4.3"
    "jest-jasmine2" "23.6.0"
    "jest-regex-util" "23.3.0"
    "jest-resolve" "23.6.0"
    "jest-util" "23.4.0"
    "jest-validate" "23.6.0"
    "micromatch" "2.3.11"
    "pretty-format" "23.6.0"

"jest-diff@23.6.0":
  "integrity" "sha512-Gz9l5Ov+X3aL5L37IT+8hoCUsof1CVYBb2QEkOupK64XyRR3h+uRpYIm97K7sY8diFxowR8pIGEdyfMKTixo3g=="
  "resolved" "https://registry.npmjs.org/jest-diff/-/jest-diff-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "chalk" "2.4.2"
    "diff" "3.5.0"
    "jest-get-type" "22.4.3"
    "pretty-format" "23.6.0"

"jest-docblock@23.2.0":
  "integrity" "sha1-8IXh8YVI2Z/dabICB+b9VdkTg6c="
  "resolved" "https://registry.npmjs.org/jest-docblock/-/jest-docblock-23.2.0.tgz"
  "version" "23.2.0"
  dependencies:
    "detect-newline" "2.1.0"

"jest-each@23.6.0":
  "integrity" "sha512-x7V6M/WGJo6/kLoissORuvLIeAoyo2YqLOoCDkohgJ4XOXSqOtyvr8FbInlAWS77ojBsZrafbozWoKVRdtxFCg=="
  "resolved" "https://registry.npmjs.org/jest-each/-/jest-each-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "chalk" "2.4.2"
    "pretty-format" "23.6.0"

"jest-environment-jsdom@23.4.0":
  "integrity" "sha1-BWp5UrP+pROsYqFAosNox52eYCM="
  "resolved" "https://registry.npmjs.org/jest-environment-jsdom/-/jest-environment-jsdom-23.4.0.tgz"
  "version" "23.4.0"
  dependencies:
    "jest-mock" "23.2.0"
    "jest-util" "23.4.0"
    "jsdom" "11.12.0"

"jest-environment-node@23.4.0":
  "integrity" "sha1-V+gO0IQd6jAxZ8zozXlSHeuv3hA="
  "resolved" "https://registry.npmjs.org/jest-environment-node/-/jest-environment-node-23.4.0.tgz"
  "version" "23.4.0"
  dependencies:
    "jest-mock" "23.2.0"
    "jest-util" "23.4.0"

"jest-get-type@22.4.3":
  "integrity" "sha512-/jsz0Y+V29w1chdXVygEKSz2nBoHoYqNShPe+QgxSNjAuP1i8+k4LbQNrfoliKej0P45sivkSCh7yiD6ubHS3w=="
  "resolved" "https://registry.npmjs.org/jest-get-type/-/jest-get-type-22.4.3.tgz"
  "version" "22.4.3"

"jest-haste-map@23.6.0":
  "integrity" "sha512-uyNhMyl6dr6HaXGHp8VF7cK6KpC6G9z9LiMNsst+rJIZ8l7wY0tk8qwjPmEghczojZ2/ZhtEdIabZ0OQRJSGGg=="
  "resolved" "https://registry.npmjs.org/jest-haste-map/-/jest-haste-map-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "fb-watchman" "2.0.1"
    "graceful-fs" "4.2.3"
    "invariant" "2.2.4"
    "jest-docblock" "23.2.0"
    "jest-serializer" "23.0.1"
    "jest-worker" "23.2.0"
    "micromatch" "2.3.11"
    "sane" "2.5.2"

"jest-jasmine2@23.6.0":
  "integrity" "sha512-pe2Ytgs1nyCs8IvsEJRiRTPC0eVYd8L/dXJGU08GFuBwZ4sYH/lmFDdOL3ZmvJR8QKqV9MFuwlsAi/EWkFUbsQ=="
  "resolved" "https://registry.npmjs.org/jest-jasmine2/-/jest-jasmine2-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "babel-traverse" "6.26.0"
    "chalk" "2.4.2"
    "co" "4.6.0"
    "expect" "23.6.0"
    "is-generator-fn" "1.0.0"
    "jest-diff" "23.6.0"
    "jest-each" "23.6.0"
    "jest-matcher-utils" "23.6.0"
    "jest-message-util" "23.4.0"
    "jest-snapshot" "23.6.0"
    "jest-util" "23.4.0"
    "pretty-format" "23.6.0"

"jest-leak-detector@23.6.0":
  "integrity" "sha512-f/8zA04rsl1Nzj10HIyEsXvYlMpMPcy0QkQilVZDFOaPbv2ur71X5u2+C4ZQJGyV/xvVXtCCZ3wQ99IgQxftCg=="
  "resolved" "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "pretty-format" "23.6.0"

"jest-matcher-utils@23.6.0":
  "integrity" "sha512-rosyCHQfBcol4NsckTn01cdelzWLU9Cq7aaigDf8VwwpIRvWE/9zLgX2bON+FkEW69/0UuYslUe22SOdEf2nog=="
  "resolved" "https://registry.npmjs.org/jest-matcher-utils/-/jest-matcher-utils-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "chalk" "2.4.2"
    "jest-get-type" "22.4.3"
    "pretty-format" "23.6.0"

"jest-message-util@23.4.0":
  "integrity" "sha1-F2EMUJQjSVCNAaPR4L2iwHkIap8="
  "resolved" "https://registry.npmjs.org/jest-message-util/-/jest-message-util-23.4.0.tgz"
  "version" "23.4.0"
  dependencies:
    "@babel/code-frame" "7.8.3"
    "chalk" "2.4.2"
    "micromatch" "2.3.11"
    "slash" "1.0.0"
    "stack-utils" "1.0.2"

"jest-mock@23.2.0":
  "integrity" "sha1-rRxg8p6HGdR8JuETgJi20YsmETQ="
  "resolved" "https://registry.npmjs.org/jest-mock/-/jest-mock-23.2.0.tgz"
  "version" "23.2.0"

"jest-pnp-resolver@1.0.2":
  "integrity" "sha512-H2DvUlwdMedNGv4FOliPDnxani6ATWy70xe2eckGJgkLoMaWzRPqpSlc5ShqX0Ltk5OhRQvPQY2LLZPOpgcc7g=="
  "resolved" "https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.0.2.tgz"
  "version" "1.0.2"

"jest-regex-util@23.3.0":
  "integrity" "sha1-X4ZylUfCeFxAAs6qj4Sf6MpHG8U="
  "resolved" "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-23.3.0.tgz"
  "version" "23.3.0"

"jest-resolve-dependencies@23.6.0":
  "integrity" "sha512-EkQWkFWjGKwRtRyIwRwI6rtPAEyPWlUC2MpzHissYnzJeHcyCn1Hc8j7Nn1xUVrS5C6W5+ZL37XTem4D4pLZdA=="
  "resolved" "https://registry.npmjs.org/jest-resolve-dependencies/-/jest-resolve-dependencies-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "jest-regex-util" "23.3.0"
    "jest-snapshot" "23.6.0"

"jest-resolve@23.6.0":
  "integrity" "sha512-XyoRxNtO7YGpQDmtQCmZjum1MljDqUCob7XlZ6jy9gsMugHdN2hY4+Acz9Qvjz2mSsOnPSH7skBmDYCHXVZqkA=="
  "resolved" "https://registry.npmjs.org/jest-resolve/-/jest-resolve-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "browser-resolve" "1.11.3"
    "chalk" "2.4.2"
    "realpath-native" "1.1.0"

"jest-runner@23.6.0":
  "integrity" "sha512-kw0+uj710dzSJKU6ygri851CObtCD9cN8aNkg8jWJf4ewFyEa6kwmiH/r/M1Ec5IL/6VFa0wnAk6w+gzUtjJzA=="
  "resolved" "https://registry.npmjs.org/jest-runner/-/jest-runner-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "exit" "0.1.2"
    "graceful-fs" "4.2.3"
    "jest-config" "23.6.0"
    "jest-docblock" "23.2.0"
    "jest-haste-map" "23.6.0"
    "jest-jasmine2" "23.6.0"
    "jest-leak-detector" "23.6.0"
    "jest-message-util" "23.4.0"
    "jest-runtime" "23.6.0"
    "jest-util" "23.4.0"
    "jest-worker" "23.2.0"
    "source-map-support" "0.5.16"
    "throat" "4.1.0"

"jest-runtime@23.6.0":
  "integrity" "sha512-ycnLTNPT2Gv+TRhnAYAQ0B3SryEXhhRj1kA6hBPSeZaNQkJ7GbZsxOLUkwg6YmvWGdX3BB3PYKFLDQCAE1zNOw=="
  "resolved" "https://registry.npmjs.org/jest-runtime/-/jest-runtime-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "babel-core" "6.26.3"
    "babel-plugin-istanbul" "4.1.6"
    "chalk" "2.4.2"
    "convert-source-map" "1.7.0"
    "exit" "0.1.2"
    "fast-json-stable-stringify" "2.1.0"
    "graceful-fs" "4.2.3"
    "jest-config" "23.6.0"
    "jest-haste-map" "23.6.0"
    "jest-message-util" "23.4.0"
    "jest-regex-util" "23.3.0"
    "jest-resolve" "23.6.0"
    "jest-snapshot" "23.6.0"
    "jest-util" "23.4.0"
    "jest-validate" "23.6.0"
    "micromatch" "2.3.11"
    "realpath-native" "1.1.0"
    "slash" "1.0.0"
    "strip-bom" "3.0.0"
    "write-file-atomic" "2.4.3"
    "yargs" "11.1.1"

"jest-serializer@23.0.1":
  "integrity" "sha1-o3dq6zEekP6D+rnlM+hRAr0WQWU="
  "resolved" "https://registry.npmjs.org/jest-serializer/-/jest-serializer-23.0.1.tgz"
  "version" "23.0.1"

"jest-snapshot@23.6.0":
  "integrity" "sha512-tM7/Bprftun6Cvj2Awh/ikS7zV3pVwjRYU2qNYS51VZHgaAMBs5l4o/69AiDHhQrj5+LA2Lq4VIvK7zYk/bswg=="
  "resolved" "https://registry.npmjs.org/jest-snapshot/-/jest-snapshot-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "babel-types" "6.26.0"
    "chalk" "2.4.2"
    "jest-diff" "23.6.0"
    "jest-matcher-utils" "23.6.0"
    "jest-message-util" "23.4.0"
    "jest-resolve" "23.6.0"
    "mkdirp" "0.5.1"
    "natural-compare" "1.4.0"
    "pretty-format" "23.6.0"
    "semver" "5.7.1"

"jest-util@23.4.0":
  "integrity" "sha1-TQY8uSe68KI4Mf9hvsLLv0l5NWE="
  "resolved" "https://registry.npmjs.org/jest-util/-/jest-util-23.4.0.tgz"
  "version" "23.4.0"
  dependencies:
    "callsites" "2.0.0"
    "chalk" "2.4.2"
    "graceful-fs" "4.2.3"
    "is-ci" "1.2.1"
    "jest-message-util" "23.4.0"
    "mkdirp" "0.5.1"
    "slash" "1.0.0"
    "source-map" "0.6.1"

"jest-validate@23.6.0":
  "integrity" "sha512-OFKapYxe72yz7agrDAWi8v2WL8GIfVqcbKRCLbRG9PAxtzF9b1SEDdTpytNDN12z2fJynoBwpMpvj2R39plI2A=="
  "resolved" "https://registry.npmjs.org/jest-validate/-/jest-validate-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "chalk" "2.4.2"
    "jest-get-type" "22.4.3"
    "leven" "2.1.0"
    "pretty-format" "23.6.0"

"jest-watch-typeahead@0.2.1":
  "integrity" "sha512-xdhEtKSj0gmnkDQbPTIHvcMmXNUDzYpHLEJ5TFqlaI+schi2NI96xhWiZk9QoesAS7oBmKwWWsHazTrYl2ORgg=="
  "resolved" "https://registry.npmjs.org/jest-watch-typeahead/-/jest-watch-typeahead-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "ansi-escapes" "3.2.0"
    "chalk" "2.4.2"
    "jest-watcher" "23.4.0"
    "slash" "2.0.0"
    "string-length" "2.0.0"
    "strip-ansi" "5.2.0"

"jest-watcher@23.4.0":
  "integrity" "sha1-0uKM50+NrWxq/JIrksq+9u0FyRw="
  "resolved" "https://registry.npmjs.org/jest-watcher/-/jest-watcher-23.4.0.tgz"
  "version" "23.4.0"
  dependencies:
    "ansi-escapes" "3.2.0"
    "chalk" "2.4.2"
    "string-length" "2.0.0"

"jest-worker@23.2.0":
  "integrity" "sha1-+vcGqNo2+uYOsmlXJX+ntdjqArk="
  "resolved" "https://registry.npmjs.org/jest-worker/-/jest-worker-23.2.0.tgz"
  "version" "23.2.0"
  dependencies:
    "merge-stream" "1.0.1"

"jest@23.6.0":
  "integrity" "sha512-lWzcd+HSiqeuxyhG+EnZds6iO3Y3ZEnMrfZq/OTGvF/C+Z4fPMCdhWTGSAiO2Oym9rbEXfwddHhh6jqrTF3+Lw=="
  "resolved" "https://registry.npmjs.org/jest/-/jest-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "import-local" "1.0.0"
    "jest-cli" "23.6.0"

"joi@11.4.0":
  "integrity" "sha512-O7Uw+w/zEWgbL6OcHbyACKSj0PkQeUgmehdoXVSxt92QFCq4+1390Rwh5moI2K/OgC7D8RHRZqHZxT2husMJHA=="
  "resolved" "https://registry.npmjs.org/joi/-/joi-11.4.0.tgz"
  "version" "11.4.0"
  dependencies:
    "hoek" "4.2.1"
    "isemail" "3.2.0"
    "topo" "2.0.2"

"js-levenshtein@1.1.6":
  "integrity" "sha512-X2BB11YZtrRqY4EnQcLX5Rh373zbK4alC1FW7D7MBhL2gtcC17cTnr6DmfHZeS0s2rTHjUTMMHfG7gO8SSdw+g=="
  "resolved" "https://registry.npmjs.org/js-levenshtein/-/js-levenshtein-1.1.6.tgz"
  "version" "1.1.6"

"js-tokens@3.0.2":
  "integrity" "sha1-mGbfOVECEw449/mWvOtlRDIJwls="
  "resolved" "https://registry.npmjs.org/js-tokens/-/js-tokens-3.0.2.tgz"
  "version" "3.0.2"

"js-tokens@4.0.0":
  "integrity" "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="
  "resolved" "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
  "version" "4.0.0"

"js-yaml@3.13.1":
  "integrity" "sha512-YfbcO7jXDdyj0DGxYVSlSeQNHbD7XPWvrVWeVUujrQEoZzWJIRrCPoyk6kL6IAjAG2IolMK4T0hNUe0HOUs5Jw=="
  "resolved" "https://registry.npmjs.org/js-yaml/-/js-yaml-3.13.1.tgz"
  "version" "3.13.1"
  dependencies:
    "argparse" "1.0.10"
    "esprima" "4.0.1"

"jsbn@0.1.1":
  "integrity" "sha1-peZUwuWi3rXyAdls77yoDA7y9RM="
  "resolved" "https://registry.npmjs.org/jsbn/-/jsbn-0.1.1.tgz"
  "version" "0.1.1"

"jsdom@11.12.0":
  "integrity" "sha512-y8Px43oyiBM13Zc1z780FrfNLJCXTL40EWlty/LXUtcjykRBNgLlCjWXpfSPBl2iv+N7koQN+dvqszHZgT/Fjw=="
  "resolved" "https://registry.npmjs.org/jsdom/-/jsdom-11.12.0.tgz"
  "version" "11.12.0"
  dependencies:
    "abab" "2.0.3"
    "acorn" "5.7.3"
    "acorn-globals" "4.3.4"
    "array-equal" "1.0.0"
    "cssom" "0.3.8"
    "cssstyle" "1.4.0"
    "data-urls" "1.1.0"
    "domexception" "1.0.1"
    "escodegen" "1.14.1"
    "html-encoding-sniffer" "1.0.2"
    "left-pad" "1.3.0"
    "nwsapi" "2.2.0"
    "parse5" "4.0.0"
    "pn" "1.1.0"
    "request" "2.88.2"
    "request-promise-native" "1.0.8"
    "sax" "1.2.4"
    "symbol-tree" "3.2.4"
    "tough-cookie" "2.5.0"
    "w3c-hr-time" "1.0.2"
    "webidl-conversions" "4.0.2"
    "whatwg-encoding" "1.0.5"
    "whatwg-mimetype" "2.3.0"
    "whatwg-url" "6.5.0"
    "ws" "5.2.2"
    "xml-name-validator" "3.0.0"

"jsesc@0.5.0":
  "integrity" "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0="
  "resolved" "https://registry.npmjs.org/jsesc/-/jsesc-0.5.0.tgz"
  "version" "0.5.0"

"jsesc@1.3.0":
  "integrity" "sha1-RsP+yMGJKxKwgz25vHYiF226s0s="
  "resolved" "https://registry.npmjs.org/jsesc/-/jsesc-1.3.0.tgz"
  "version" "1.3.0"

"jsesc@2.5.2":
  "integrity" "sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA=="
  "resolved" "https://registry.npmjs.org/jsesc/-/jsesc-2.5.2.tgz"
  "version" "2.5.2"

"json-parse-better-errors@1.0.2":
  "integrity" "sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw=="
  "resolved" "https://registry.npmjs.org/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz"
  "version" "1.0.2"

"json-schema-traverse@0.4.1":
  "integrity" "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg=="
  "resolved" "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  "version" "0.4.1"

"json-schema@0.2.3":
  "integrity" "sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM="
  "resolved" "https://registry.npmjs.org/json-schema/-/json-schema-0.2.3.tgz"
  "version" "0.2.3"

"json-stable-stringify-without-jsonify@1.0.1":
  "integrity" "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE="
  "resolved" "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  "version" "1.0.1"

"json-stable-stringify@1.0.1":
  "integrity" "sha1-mnWdOcXy/1A/1TAGRu1EX4jE+a8="
  "resolved" "https://registry.npmjs.org/json-stable-stringify/-/json-stable-stringify-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "jsonify" "0.0.0"

"json-stringify-safe@5.0.1":
  "integrity" "sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus="
  "resolved" "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz"
  "version" "5.0.1"

"json2mq@0.2.0":
  "integrity" "sha1-tje9O6nqvhIsg+lyBIOusQ0skEo="
  "resolved" "https://registry.npmjs.org/json2mq/-/json2mq-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "string-convert" "0.2.1"

"json3@3.3.3":
  "integrity" "sha512-c7/8mbUsKigAbLkD5B010BK4D9LZm7A1pNItkEwiUZRpIN66exu/e7YQWysGun+TRKaJp8MhemM+VkfWv42aCA=="
  "resolved" "https://registry.npmjs.org/json3/-/json3-3.3.3.tgz"
  "version" "3.3.3"

"json5@0.5.1":
  "integrity" "sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE="
  "resolved" "https://registry.npmjs.org/json5/-/json5-0.5.1.tgz"
  "version" "0.5.1"

"json5@1.0.1":
  "integrity" "sha512-aKS4WQjPenRxiQsC93MNfjx+nbF4PAdYzmd/1JIj8HYzqfbu86beTuNgXDzPknWk0n0uARlyewZo4s++ES36Ow=="
  "resolved" "https://registry.npmjs.org/json5/-/json5-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "minimist" "1.2.0"

"json5@2.1.1":
  "integrity" "sha512-l+3HXD0GEI3huGq1njuqtzYK8OYJyXMkOLtQ53pjWh89tvWS2h6l+1zMkYWqlb57+SiQodKZyvMEFb2X+KrFhQ=="
  "resolved" "https://registry.npmjs.org/json5/-/json5-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "minimist" "1.2.0"

"jsonfile@4.0.0":
  "integrity" "sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss="
  "resolved" "https://registry.npmjs.org/jsonfile/-/jsonfile-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "graceful-fs" "4.2.3"

"jsonify@0.0.0":
  "integrity" "sha1-LHS27kHZPKUbe1qu6PUDYx0lKnM="
  "resolved" "https://registry.npmjs.org/jsonify/-/jsonify-0.0.0.tgz"
  "version" "0.0.0"

"jsonp@^0.2.1":
  "integrity" "sha1-pltPoPEL2nGaBUQep7lMVfPhW64="
  "resolved" "https://registry.npmjs.org/jsonp/-/jsonp-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "debug" "2.6.9"

"jsprim@1.4.1":
  "integrity" "sha1-MT5mvB5cwG5Di8G3SZwuXFastqI="
  "resolved" "https://registry.npmjs.org/jsprim/-/jsprim-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "assert-plus" "1.0.0"
    "extsprintf" "1.3.0"
    "json-schema" "0.2.3"
    "verror" "1.10.0"

"jsx-ast-utils@2.2.3":
  "integrity" "sha512-EdIHFMm+1BPynpKOpdPqiOsvnIrInRGJD7bzPZdPkjitQEqpdpUuFpq4T0npZFKTiB3RhWFdGN+oqOJIdhDhQA=="
  "resolved" "https://registry.npmjs.org/jsx-ast-utils/-/jsx-ast-utils-2.2.3.tgz"
  "version" "2.2.3"
  dependencies:
    "array-includes" "3.1.1"
    "object.assign" "4.1.0"

"killable@1.0.1":
  "integrity" "sha512-LzqtLKlUwirEUyl/nicirVmNiPvYs7l5n8wOPP7fyJVpUPkvCnW/vuiXGpylGUlnPDnB7311rARzAt3Mhswpjg=="
  "resolved" "https://registry.npmjs.org/killable/-/killable-1.0.1.tgz"
  "version" "1.0.1"

"kind-of@2.0.1":
  "integrity" "sha1-AY7HpM5+OobLkUG+UZ0kyPqpgbU="
  "resolved" "https://registry.npmjs.org/kind-of/-/kind-of-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-buffer" "1.1.6"

"kind-of@3.2.2":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
  "resolved" "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "1.1.6"

"kind-of@4.0.0":
  "integrity" "sha1-IIE989cSkosgc3hpGkUGb65y3Vc="
  "resolved" "https://registry.npmjs.org/kind-of/-/kind-of-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "is-buffer" "1.1.6"

"kind-of@5.1.0":
  "integrity" "sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw=="
  "resolved" "https://registry.npmjs.org/kind-of/-/kind-of-5.1.0.tgz"
  "version" "5.1.0"

"kind-of@6.0.3":
  "integrity" "sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw=="
  "resolved" "https://registry.npmjs.org/kind-of/-/kind-of-6.0.3.tgz"
  "version" "6.0.3"

"kleur@2.0.2":
  "integrity" "sha512-77XF9iTllATmG9lSlIv0qdQ2BQ/h9t0bJllHlbvsQ0zUWfU7Yi0S8L5JXzPZgkefIiajLmBJJ4BsMJmqcf7oxQ=="
  "resolved" "https://registry.npmjs.org/kleur/-/kleur-2.0.2.tgz"
  "version" "2.0.2"

"last-call-webpack-plugin@3.0.0":
  "integrity" "sha512-7KI2l2GIZa9p2spzPIVZBYyNKkN+e/SQPpnjlTiPhdbDW3F86tdKKELxKpzJ5sgU19wQWsACULZmpTPYHeWO5w=="
  "resolved" "https://registry.npmjs.org/last-call-webpack-plugin/-/last-call-webpack-plugin-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "lodash" "4.17.15"
    "webpack-sources" "1.4.3"

"lazy-cache@0.2.7":
  "integrity" "sha1-f+3fLctu23fRHvHRF6tf/fCrG2U="
  "resolved" "https://registry.npmjs.org/lazy-cache/-/lazy-cache-0.2.7.tgz"
  "version" "0.2.7"

"lazy-cache@1.0.4":
  "integrity" "sha1-odePw6UEdMuAhF07O24dpJpEbo4="
  "resolved" "https://registry.npmjs.org/lazy-cache/-/lazy-cache-1.0.4.tgz"
  "version" "1.0.4"

"lcid@2.0.0":
  "integrity" "sha512-avPEb8P8EGnwXKClwsNUgryVjllcRqtMYa49NTsbQagYuT1DcXnl1915oxWjoyGrXR6zH/Y0Zc96xWsPcoDKeA=="
  "resolved" "https://registry.npmjs.org/lcid/-/lcid-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "invert-kv" "2.0.0"

"left-pad@1.3.0":
  "integrity" "sha512-XI5MPzVNApjAyhQzphX8BkmKsKUxD4LdyK24iZeQGinBN9yTQT3bFlCBy/aVx2HrNcqQGsdot8ghrjyrvMCoEA=="
  "resolved" "https://registry.npmjs.org/left-pad/-/left-pad-1.3.0.tgz"
  "version" "1.3.0"

"less-loader@^4.1.0":
  "integrity" "sha512-KNTsgCE9tMOM70+ddxp9yyt9iHqgmSs0yTZc5XH5Wo+g80RWRIYNqE58QJKm/yMud5wZEvz50ugRDuzVIkyahg=="
  "resolved" "https://registry.npmjs.org/less-loader/-/less-loader-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "clone" "2.1.2"
    "loader-utils" "1.4.0"
    "pify" "3.0.0"

"less@^3.9.0":
  "integrity" "sha512-tlWX341RECuTOvoDIvtFqXsKj072hm3+9ymRBe76/mD6O5ZZecnlAOVDlWAleF2+aohFrxNidXhv2773f6kY7g=="
  "resolved" "https://registry.npmjs.org/less/-/less-3.11.1.tgz"
  "version" "3.11.1"
  dependencies:
    "clone" "2.1.2"
    "errno" "0.1.7"
    "graceful-fs" "4.2.3"
    "mime" "1.6.0"
    "mkdirp" "0.5.1"
    "promise" "7.3.1"
    "request" "2.88.2"
    "source-map" "0.6.1"
    "tslib" "1.11.1"
  optionalDependencies:
    "image-size" "0.5.5"

"leven@2.1.0":
  "integrity" "sha1-wuep93IJTe6dNCAq6KzORoeHVYA="
  "resolved" "https://registry.npmjs.org/leven/-/leven-2.1.0.tgz"
  "version" "2.1.0"

"leven@3.1.0":
  "integrity" "sha512-qsda+H8jTaUaN/x5vzW2rzc+8Rw4TAQ/4KjB46IwK5VH+IlVeeeje/EoZRpiXvIqjFgK84QffqPztGI3VBLG1A=="
  "resolved" "https://registry.npmjs.org/leven/-/leven-3.1.0.tgz"
  "version" "3.1.0"

"levenary@1.1.1":
  "integrity" "sha512-mkAdOIt79FD6irqjYSs4rdbnlT5vRonMEvBVPVb3XmevfS8kgRXwfes0dhPdEtzTWD/1eNE/Bm/G1iRt6DcnQQ=="
  "resolved" "https://registry.npmjs.org/levenary/-/levenary-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "leven" "3.1.0"

"levn@0.3.0":
  "integrity" "sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4="
  "resolved" "https://registry.npmjs.org/levn/-/levn-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "prelude-ls" "1.1.2"
    "type-check" "0.3.2"

"load-json-file@1.1.0":
  "integrity" "sha1-lWkFcI1YtLq0wiYbBPWfMcmTdMA="
  "resolved" "https://registry.npmjs.org/load-json-file/-/load-json-file-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "graceful-fs" "4.2.3"
    "parse-json" "2.2.0"
    "pify" "2.3.0"
    "pinkie-promise" "2.0.1"
    "strip-bom" "2.0.0"

"load-json-file@2.0.0":
  "integrity" "sha1-eUfkIUmvgNaWy/eXvKq8/h/inKg="
  "resolved" "https://registry.npmjs.org/load-json-file/-/load-json-file-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "graceful-fs" "4.2.3"
    "parse-json" "2.2.0"
    "pify" "2.3.0"
    "strip-bom" "3.0.0"

"loader-fs-cache@1.0.2":
  "integrity" "sha512-70IzT/0/L+M20jUlEqZhZyArTU6VKLRTYRDAYN26g4jfzpJqjipLL3/hgYpySqI9PwsVRHHFja0LfEmsx9X2Cw=="
  "resolved" "https://registry.npmjs.org/loader-fs-cache/-/loader-fs-cache-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "find-cache-dir" "0.1.1"
    "mkdirp" "0.5.1"

"loader-runner@2.4.0":
  "integrity" "sha512-Jsmr89RcXGIwivFY21FcRrisYZfvLMTWx5kOLc+JTxtpBOG6xML0vzbc6SEQG2FO9/4Fc3wW4LVcB5DmGflaRw=="
  "resolved" "https://registry.npmjs.org/loader-runner/-/loader-runner-2.4.0.tgz"
  "version" "2.4.0"

"loader-utils@1.2.3":
  "integrity" "sha512-fkpz8ejdnEMG3s37wGL07iSBDg99O9D5yflE9RGNH3hRdx9SOwYfnGYdZOUIZitN8E+E2vkq3MUMYMvPYl5ZZA=="
  "resolved" "https://registry.npmjs.org/loader-utils/-/loader-utils-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "big.js" "5.2.2"
    "emojis-list" "2.1.0"
    "json5" "1.0.1"

"loader-utils@1.4.0":
  "integrity" "sha512-qH0WSMBtn/oHuwjy/NucEgbx5dbxxnxup9s4PVXJUDHZBQY+s0NWA9rJf53RBnQZxfch7euUui7hpoAPvALZdA=="
  "resolved" "https://registry.npmjs.org/loader-utils/-/loader-utils-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "big.js" "5.2.2"
    "emojis-list" "3.0.0"
    "json5" "1.0.1"

"locate-path@2.0.0":
  "integrity" "sha1-K1aLJl7slExtnA3pw9u7ygNUzY4="
  "resolved" "https://registry.npmjs.org/locate-path/-/locate-path-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "p-locate" "2.0.0"
    "path-exists" "3.0.0"

"locate-path@3.0.0":
  "integrity" "sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A=="
  "resolved" "https://registry.npmjs.org/locate-path/-/locate-path-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "p-locate" "3.0.0"
    "path-exists" "3.0.0"

"lodash._reinterpolate@3.0.0":
  "integrity" "sha1-DM8tiRZq8Ds2Y8eWU4t1rG4RTZ0="
  "resolved" "https://registry.npmjs.org/lodash._reinterpolate/-/lodash._reinterpolate-3.0.0.tgz"
  "version" "3.0.0"

"lodash.camelcase@4.3.0":
  "integrity" "sha1-soqmKIorn8ZRA1x3EfZathkDMaY="
  "resolved" "https://registry.npmjs.org/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz"
  "version" "4.3.0"

"lodash.debounce@4.0.8":
  "integrity" "sha1-gteb/zCmfEAF/9XiUVMArZyk168="
  "resolved" "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.8.tgz"
  "version" "4.0.8"

"lodash.flow@3.5.0":
  "integrity" "sha1-h79AKSuM+D5OjOGjrkIJ4gBxZ1o="
  "resolved" "https://registry.npmjs.org/lodash.flow/-/lodash.flow-3.5.0.tgz"
  "version" "3.5.0"

"lodash.memoize@4.1.2":
  "integrity" "sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4="
  "resolved" "https://registry.npmjs.org/lodash.memoize/-/lodash.memoize-4.1.2.tgz"
  "version" "4.1.2"

"lodash.sortby@4.7.0":
  "integrity" "sha1-7dFMgk4sycHgsKG0K7UhBRakJDg="
  "resolved" "https://registry.npmjs.org/lodash.sortby/-/lodash.sortby-4.7.0.tgz"
  "version" "4.7.0"

"lodash.tail@4.1.1":
  "integrity" "sha1-0jM6NtnncXyK0vfKyv7HwytERmQ="
  "resolved" "https://registry.npmjs.org/lodash.tail/-/lodash.tail-4.1.1.tgz"
  "version" "4.1.1"

"lodash.template@4.5.0":
  "integrity" "sha512-84vYFxIkmidUiFxidA/KjjH9pAycqW+h980j7Fuz5qxRtO9pgB7MDFTdys1N7A5mcucRiDyEq4fusljItR1T/A=="
  "resolved" "https://registry.npmjs.org/lodash.template/-/lodash.template-4.5.0.tgz"
  "version" "4.5.0"
  dependencies:
    "lodash._reinterpolate" "3.0.0"
    "lodash.templatesettings" "4.2.0"

"lodash.templatesettings@4.2.0":
  "integrity" "sha512-stgLz+i3Aa9mZgnjr/O+v9ruKZsPsndy7qPZOchbqk2cnTU1ZaldKK+v7m54WoKIyxiuMZTKT2H81F8BeAc3ZQ=="
  "resolved" "https://registry.npmjs.org/lodash.templatesettings/-/lodash.templatesettings-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "lodash._reinterpolate" "3.0.0"

"lodash.throttle@4.1.1":
  "integrity" "sha1-wj6RtxAkKscMN/HhzaknTMOb8vQ="
  "resolved" "https://registry.npmjs.org/lodash.throttle/-/lodash.throttle-4.1.1.tgz"
  "version" "4.1.1"

"lodash.uniq@4.5.0":
  "integrity" "sha1-0CJTc662Uq3BvILklFM5qEJ1R3M="
  "resolved" "https://registry.npmjs.org/lodash.uniq/-/lodash.uniq-4.5.0.tgz"
  "version" "4.5.0"

"lodash@4.17.15":
  "integrity" "sha512-8xOcRHvCjnocdS5cpwXQXVzmmh5e5+saE2QGoeQmbKmRS6J3VQppPOIt0MnmE+4xlZoumy0GPG0D0MVIQbNA1A=="
  "resolved" "https://registry.npmjs.org/lodash/-/lodash-4.17.15.tgz"
  "version" "4.17.15"

"loglevel@1.6.7":
  "integrity" "sha512-cY2eLFrQSAfVPhCgH1s7JI73tMbg9YC3v3+ZHVW67sBS7UxWzNEk/ZBbSfLykBWHp33dqqtOv82gjhKEi81T/A=="
  "resolved" "https://registry.npmjs.org/loglevel/-/loglevel-1.6.7.tgz"
  "version" "1.6.7"

"loose-envify@^1.4.0", "loose-envify@1.4.0":
  "integrity" "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q=="
  "resolved" "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "js-tokens" "4.0.0"

"lower-case@1.1.4":
  "integrity" "sha1-miyr0bno4K6ZOkv31YdcOcQujqw="
  "resolved" "https://registry.npmjs.org/lower-case/-/lower-case-1.1.4.tgz"
  "version" "1.1.4"

"lru-cache@5.1.1":
  "integrity" "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w=="
  "resolved" "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "yallist" "3.1.1"

"make-dir@2.1.0":
  "integrity" "sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA=="
  "resolved" "https://registry.npmjs.org/make-dir/-/make-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pify" "4.0.1"
    "semver" "5.7.1"

"makeerror@1.0.11":
  "integrity" "sha1-4BpckQnyr3lmDk6LlYd5AYT1qWw="
  "resolved" "https://registry.npmjs.org/makeerror/-/makeerror-1.0.11.tgz"
  "version" "1.0.11"
  dependencies:
    "tmpl" "1.0.4"

"map-age-cleaner@0.1.3":
  "integrity" "sha512-bJzx6nMoP6PDLPBFmg7+xRKeFZvFboMrGlxmNj9ClvX53KrmvM5bXFXEWjbz4cz1AFn+jWJ9z/DJSz7hrs0w3w=="
  "resolved" "https://registry.npmjs.org/map-age-cleaner/-/map-age-cleaner-0.1.3.tgz"
  "version" "0.1.3"
  dependencies:
    "p-defer" "1.0.0"

"map-cache@0.2.2":
  "integrity" "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8="
  "resolved" "https://registry.npmjs.org/map-cache/-/map-cache-0.2.2.tgz"
  "version" "0.2.2"

"map-visit@1.0.0":
  "integrity" "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48="
  "resolved" "https://registry.npmjs.org/map-visit/-/map-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "object-visit" "1.0.1"

"math-random@1.0.4":
  "integrity" "sha512-rUxjysqif/BZQH2yhd5Aaq7vXMSx9NdEsQcyA07uEzIvxgI7zIr33gGsh+RU0/XjmQpCW7RsVof1vlkvQVCK5A=="
  "resolved" "https://registry.npmjs.org/math-random/-/math-random-1.0.4.tgz"
  "version" "1.0.4"

"md5.js@1.3.5":
  "integrity" "sha512-xitP+WxNPcTTOgnTJcrhM0xvdPepipPSf3I8EIpGKeFLjt3PlJLIDG3u8EX53ZIubkb+5U2+3rELYpEhHhzdkg=="
  "resolved" "https://registry.npmjs.org/md5.js/-/md5.js-1.3.5.tgz"
  "version" "1.3.5"
  dependencies:
    "hash-base" "3.0.4"
    "inherits" "2.0.4"
    "safe-buffer" "5.2.0"

"mdn-data@2.0.4":
  "integrity" "sha512-iV3XNKw06j5Q7mi6h+9vbx23Tv7JkjEVgKHW4pimwyDGWm0OIQntJJ+u1C6mg6mK1EaTv42XQ7w76yuzH7M2cA=="
  "resolved" "https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.4.tgz"
  "version" "2.0.4"

"media-typer@0.3.0":
  "integrity" "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g="
  "resolved" "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz"
  "version" "0.3.0"

"mem@4.3.0":
  "integrity" "sha512-qX2bG48pTqYRVmDB37rn/6PT7LcR8T7oAX3bf99u1Tt1nzxYfxkgqDwUwolPlXweM0XzBOBFzSx4kfp7KP1s/w=="
  "resolved" "https://registry.npmjs.org/mem/-/mem-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "map-age-cleaner" "0.1.3"
    "mimic-fn" "2.1.0"
    "p-is-promise" "2.1.0"

"memory-fs@0.4.1":
  "integrity" "sha1-OpoguEYlI+RHz7x+i7gO1me/xVI="
  "resolved" "https://registry.npmjs.org/memory-fs/-/memory-fs-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "errno" "0.1.7"
    "readable-stream" "2.3.7"

"memory-fs@0.5.0":
  "integrity" "sha512-jA0rdU5KoQMC0e6ppoNRtpp6vjFq6+NY7r8hywnC7V+1Xj/MtHwGIbB1QaK/dunyjWteJzmkpd7ooeWg10T7GA=="
  "resolved" "https://registry.npmjs.org/memory-fs/-/memory-fs-0.5.0.tgz"
  "version" "0.5.0"
  dependencies:
    "errno" "0.1.7"
    "readable-stream" "2.3.7"

"merge-deep@3.0.2":
  "integrity" "sha512-T7qC8kg4Zoti1cFd8Cr0M+qaZfOwjlPDEdZIIPPB2JZctjaPM4fX+i7HOId69tAti2fvO6X5ldfYUONDODsrkA=="
  "resolved" "https://registry.npmjs.org/merge-deep/-/merge-deep-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "arr-union" "3.1.0"
    "clone-deep" "0.2.4"
    "kind-of" "3.2.2"

"merge-descriptors@1.0.1":
  "integrity" "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E="
  "resolved" "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.1.tgz"
  "version" "1.0.1"

"merge-stream@1.0.1":
  "integrity" "sha1-QEEgLVCKNCugAXQAjfDCUbjBNeE="
  "resolved" "https://registry.npmjs.org/merge-stream/-/merge-stream-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "readable-stream" "2.3.7"

"merge@1.2.1":
  "integrity" "sha512-VjFo4P5Whtj4vsLzsYBu5ayHhoHJ0UqNm7ibvShmbmoz7tGi0vXaoJbGdB+GmDMLUdg8DpQXEIeVDAe8MaABvQ=="
  "resolved" "https://registry.npmjs.org/merge/-/merge-1.2.1.tgz"
  "version" "1.2.1"

"merge2@1.3.0":
  "integrity" "sha512-2j4DAdlBOkiSZIsaXk4mTE3sRS02yBHAtfy127xRV3bQUFqXkjHCHLW6Scv7DwNRbIWNHH8zpnz9zMaKXIdvYw=="
  "resolved" "https://registry.npmjs.org/merge2/-/merge2-1.3.0.tgz"
  "version" "1.3.0"

"methods@1.1.2":
  "integrity" "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4="
  "resolved" "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz"
  "version" "1.1.2"

"micromatch@2.3.11":
  "integrity" "sha1-hmd8l9FyCzY0MdBNDRUpO9OMFWU="
  "resolved" "https://registry.npmjs.org/micromatch/-/micromatch-2.3.11.tgz"
  "version" "2.3.11"
  dependencies:
    "arr-diff" "2.0.0"
    "array-unique" "0.2.1"
    "braces" "1.8.5"
    "expand-brackets" "0.1.5"
    "extglob" "0.3.2"
    "filename-regex" "2.0.1"
    "is-extglob" "1.0.0"
    "is-glob" "2.0.1"
    "kind-of" "3.2.2"
    "normalize-path" "2.1.1"
    "object.omit" "2.0.1"
    "parse-glob" "3.0.4"
    "regex-cache" "0.4.4"

"micromatch@3.1.10":
  "integrity" "sha512-MWikgl9n9M3w+bpsY3He8L+w9eF9338xRl8IAO5viDizwSzziFEyUzo2xrrloB64ADbTf8uA8vRqqttDTOmccg=="
  "resolved" "https://registry.npmjs.org/micromatch/-/micromatch-3.1.10.tgz"
  "version" "3.1.10"
  dependencies:
    "arr-diff" "4.0.0"
    "array-unique" "0.3.2"
    "braces" "2.3.2"
    "define-property" "2.0.2"
    "extend-shallow" "3.0.2"
    "extglob" "2.0.4"
    "fragment-cache" "0.2.1"
    "kind-of" "6.0.3"
    "nanomatch" "1.2.13"
    "object.pick" "1.3.0"
    "regex-not" "1.0.2"
    "snapdragon" "0.8.2"
    "to-regex" "3.0.2"

"miller-rabin@4.0.1":
  "integrity" "sha512-115fLhvZVqWwHPbClyntxEVfVDfl9DLLTuJvq3g2O/Oxi8AiNouAHvDSzHS0viUJc+V5vm3eq91Xwqn9dp4jRA=="
  "resolved" "https://registry.npmjs.org/miller-rabin/-/miller-rabin-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "bn.js" "4.11.8"
    "brorand" "1.1.0"

"mime-db@1.43.0":
  "integrity" "sha512-+5dsGEEovYbT8UY9yD7eE4XTc4UwJ1jBYlgaQQF38ENsKR3wj/8q8RFZrF9WIZpB2V1ArTVFUva8sAul1NzRzQ=="
  "resolved" "https://registry.npmjs.org/mime-db/-/mime-db-1.43.0.tgz"
  "version" "1.43.0"

"mime-types@2.1.26":
  "integrity" "sha512-01paPWYgLrkqAyrlDorC1uDwl2p3qZT7yl806vW7DvDoxwXi46jsjFbg+WdwotBIk6/MbEhO/dh5aZ5sNj/dWQ=="
  "resolved" "https://registry.npmjs.org/mime-types/-/mime-types-2.1.26.tgz"
  "version" "2.1.26"
  dependencies:
    "mime-db" "1.43.0"

"mime@1.6.0":
  "integrity" "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg=="
  "resolved" "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz"
  "version" "1.6.0"

"mime@2.4.4":
  "integrity" "sha512-LRxmNwziLPT828z+4YkNzloCFC2YM4wrB99k+AV5ZbEyfGNWfG8SO1FUXLmLDBSo89NrJZ4DIWeLjy1CHGhMGA=="
  "resolved" "https://registry.npmjs.org/mime/-/mime-2.4.4.tgz"
  "version" "2.4.4"

"mimic-fn@1.2.0":
  "integrity" "sha512-jf84uxzwiuiIVKiOLpfYk7N46TSy8ubTonmneY9vrpHNAnp0QBt2BxWV9dO3/j+BoVAb+a5G6YDPW3M5HOdMWQ=="
  "resolved" "https://registry.npmjs.org/mimic-fn/-/mimic-fn-1.2.0.tgz"
  "version" "1.2.0"

"mimic-fn@2.1.0":
  "integrity" "sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg=="
  "resolved" "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz"
  "version" "2.1.0"

"mini-css-extract-plugin@0.5.0":
  "integrity" "sha512-IuaLjruM0vMKhUUT51fQdQzBYTX49dLj8w68ALEAe2A4iYNpIC4eMac67mt3NzycvjOlf07/kYxJDc0RTl1Wqw=="
  "resolved" "https://registry.npmjs.org/mini-css-extract-plugin/-/mini-css-extract-plugin-0.5.0.tgz"
  "version" "0.5.0"
  dependencies:
    "loader-utils" "1.4.0"
    "schema-utils" "1.0.0"
    "webpack-sources" "1.4.3"

"mini-store@2.0.0":
  "integrity" "sha512-EG0CuwpQmX+XL4QVS0kxNwHW5ftSbhygu1qxQH0pipugjnPkbvkalCdQbEihMwtQY6d3MTN+MS0q+aurs+RfLQ=="
  "resolved" "https://registry.npmjs.org/mini-store/-/mini-store-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "hoist-non-react-statics" "2.5.5"
    "prop-types" "15.7.2"
    "react-lifecycles-compat" "3.0.4"
    "shallowequal" "1.1.0"

"minimalistic-assert@1.0.1":
  "integrity" "sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A=="
  "resolved" "https://registry.npmjs.org/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz"
  "version" "1.0.1"

"minimalistic-crypto-utils@1.0.1":
  "integrity" "sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo="
  "resolved" "https://registry.npmjs.org/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz"
  "version" "1.0.1"

"minimatch@3.0.4":
  "integrity" "sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA=="
  "resolved" "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "brace-expansion" "1.1.11"

"minimist@0.0.10":
  "integrity" "sha1-3j+YVD2/lggr5IrRoMfNqDYwHc8="
  "resolved" "https://registry.npmjs.org/minimist/-/minimist-0.0.10.tgz"
  "version" "0.0.10"

"minimist@0.0.8":
  "integrity" "sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0="
  "resolved" "https://registry.npmjs.org/minimist/-/minimist-0.0.8.tgz"
  "version" "0.0.8"

"minimist@1.2.0":
  "integrity" "sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ="
  "resolved" "https://registry.npmjs.org/minimist/-/minimist-1.2.0.tgz"
  "version" "1.2.0"

"mississippi@3.0.0":
  "integrity" "sha512-x471SsVjUtBRtcvd4BzKE9kFC+/2TeWgKCgw0bZcw1b9l2X3QX5vCWgF+KaZaYm87Ss//rHnWryupDrgLvmSkA=="
  "resolved" "https://registry.npmjs.org/mississippi/-/mississippi-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "concat-stream" "1.6.2"
    "duplexify" "3.7.1"
    "end-of-stream" "1.4.4"
    "flush-write-stream" "1.1.1"
    "from2" "2.3.0"
    "parallel-transform" "1.2.0"
    "pump" "3.0.0"
    "pumpify" "1.5.1"
    "stream-each" "1.2.3"
    "through2" "2.0.5"

"mixin-deep@1.3.2":
  "integrity" "sha512-WRoDn//mXBiJ1H40rqa3vH0toePwSsGb45iInWlTySa+Uu4k3tYUSxa2v1KqAiLtvlrSzaExqS1gtk96A9zvEA=="
  "resolved" "https://registry.npmjs.org/mixin-deep/-/mixin-deep-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "for-in" "1.0.2"
    "is-extendable" "1.0.1"

"mixin-object@2.0.1":
  "integrity" "sha1-T7lJRB2rGCVA8f4DW6YOGUel5X4="
  "resolved" "https://registry.npmjs.org/mixin-object/-/mixin-object-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "for-in" "0.1.8"
    "is-extendable" "0.1.1"

"mkdirp@0.5.1":
  "integrity" "sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM="
  "resolved" "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.1.tgz"
  "version" "0.5.1"
  dependencies:
    "minimist" "0.0.8"

"moment@2.24.0", "moment@latest":
  "integrity" "sha512-bV7f+6l2QigeBBZSM/6yTNq4P2fNpSWj/0e7jQcy87A8e7o2nAfP/34/2ky5Vw4B9S446EtIhodAzkFCcR4dQg=="
  "resolved" "https://registry.npmjs.org/moment/-/moment-2.24.0.tgz"
  "version" "2.24.0"

"move-concurrently@1.0.1":
  "integrity" "sha1-viwAX9oy4LKa8fBdfEszIUxwH5I="
  "resolved" "https://registry.npmjs.org/move-concurrently/-/move-concurrently-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "aproba" "1.2.0"
    "copy-concurrently" "1.0.5"
    "fs-write-stream-atomic" "1.0.10"
    "mkdirp" "0.5.1"
    "rimraf" "2.6.3"
    "run-queue" "1.0.3"

"ms@2.0.0":
  "integrity" "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="
  "resolved" "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz"
  "version" "2.0.0"

"ms@2.1.1":
  "integrity" "sha512-tgp+dl5cGk28utYktBsrFqA7HKgrhgPsg6Z/EfhWI4gl1Hwq8B/GmY/0oXZ6nF8hDVesS/FpnYaD/kOWhYQvyg=="
  "resolved" "https://registry.npmjs.org/ms/-/ms-2.1.1.tgz"
  "version" "2.1.1"

"ms@2.1.2":
  "integrity" "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="
  "resolved" "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz"
  "version" "2.1.2"

"multicast-dns-service-types@1.1.0":
  "integrity" "sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE="
  "resolved" "https://registry.npmjs.org/multicast-dns-service-types/-/multicast-dns-service-types-1.1.0.tgz"
  "version" "1.1.0"

"multicast-dns@6.2.3":
  "integrity" "sha512-ji6J5enbMyGRHIAkAOu3WdV8nggqviKCEKtXcOqfphZZtQrmHKycfynJ2V7eVPUA4NhJ6V7Wf4TmGbTwKE9B6g=="
  "resolved" "https://registry.npmjs.org/multicast-dns/-/multicast-dns-6.2.3.tgz"
  "version" "6.2.3"
  dependencies:
    "dns-packet" "1.3.1"
    "thunky" "1.1.0"

"mutationobserver-shim@0.3.3":
  "integrity" "sha512-gciOLNN8Vsf7YzcqRjKzlAJ6y7e+B86u7i3KXes0xfxx/nfLmozlW1Vn+Sc9x3tPIePFgc1AeIFhtRgkqTjzDQ=="
  "resolved" "https://registry.npmjs.org/mutationobserver-shim/-/mutationobserver-shim-0.3.3.tgz"
  "version" "0.3.3"

"mute-stream@0.0.7":
  "integrity" "sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s="
  "resolved" "https://registry.npmjs.org/mute-stream/-/mute-stream-0.0.7.tgz"
  "version" "0.0.7"

"nanomatch@1.2.13":
  "integrity" "sha512-fpoe2T0RbHwBTBUOftAfBPaDEi06ufaUai0mE6Yn1kacc3SnTErfb/h+X94VXzI64rKFHYImXSvdwGGCmwOqCA=="
  "resolved" "https://registry.npmjs.org/nanomatch/-/nanomatch-1.2.13.tgz"
  "version" "1.2.13"
  dependencies:
    "arr-diff" "4.0.0"
    "array-unique" "0.3.2"
    "define-property" "2.0.2"
    "extend-shallow" "3.0.2"
    "fragment-cache" "0.2.1"
    "is-windows" "1.0.2"
    "kind-of" "6.0.3"
    "object.pick" "1.3.0"
    "regex-not" "1.0.2"
    "snapdragon" "0.8.2"
    "to-regex" "3.0.2"

"natural-compare@1.4.0":
  "integrity" "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc="
  "resolved" "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz"
  "version" "1.4.0"

"negotiator@0.6.2":
  "integrity" "sha512-hZXc7K2e+PgeI1eDBe/10Ard4ekbfrrqG8Ep+8Jmf4JID2bNg7NvCPOZN+kfF574pFQI7mum2AUqDidoKqcTOw=="
  "resolved" "https://registry.npmjs.org/negotiator/-/negotiator-0.6.2.tgz"
  "version" "0.6.2"

"neo-async@2.6.1":
  "integrity" "sha512-iyam8fBuCUpWeKPGpaNMetEocMt364qkCsfL9JuhjXX6dRnguRVOfk2GZaDpPjcOKiiXCPINZC1GczQ7iTq3Zw=="
  "resolved" "https://registry.npmjs.org/neo-async/-/neo-async-2.6.1.tgz"
  "version" "2.6.1"

"nice-try@1.0.5":
  "integrity" "sha512-1nh45deeb5olNY7eX82BkPO7SSxR5SSYJiPTrTdFUVYwAl8CKMA5N9PjTYkHiRjisVcxcQ1HXdLhx2qxxJzLNQ=="
  "resolved" "https://registry.npmjs.org/nice-try/-/nice-try-1.0.5.tgz"
  "version" "1.0.5"

"no-case@2.3.2":
  "integrity" "sha512-rmTZ9kz+f3rCvK2TD1Ue/oZlns7OGoIWP4fc3llxxRXlOkHKoWPPWJOfFYpITabSow43QJbRIoHQXtt10VldyQ=="
  "resolved" "https://registry.npmjs.org/no-case/-/no-case-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "lower-case" "1.1.4"

"node-fetch@1.7.3":
  "integrity" "sha512-NhZ4CsKx7cYm2vSrBAr2PvFOe6sWDf0UYLRqA6svUYg7+/TSfVAu49jYC4BvQ4Sms9SZgdqGBgroqfDhJdTyKQ=="
  "resolved" "https://registry.npmjs.org/node-fetch/-/node-fetch-1.7.3.tgz"
  "version" "1.7.3"
  dependencies:
    "encoding" "0.1.12"
    "is-stream" "1.1.0"

"node-forge@0.9.0":
  "integrity" "sha512-7ASaDa3pD+lJ3WvXFsxekJQelBKRpne+GOVbLbtHYdd7pFspyeuJHnWfLplGf3SwKGbfs/aYl5V/JCIaHVUKKQ=="
  "resolved" "https://registry.npmjs.org/node-forge/-/node-forge-0.9.0.tgz"
  "version" "0.9.0"

"node-int64@0.4.0":
  "integrity" "sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs="
  "resolved" "https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz"
  "version" "0.4.0"

"node-libs-browser@2.2.1":
  "integrity" "sha512-h/zcD8H9kaDZ9ALUWwlBUDo6TKF8a7qBSCSEGfjTVIYeqsioSKaAX+BN7NgiMGp6iSIXZ3PxgCu8KS3b71YK5Q=="
  "resolved" "https://registry.npmjs.org/node-libs-browser/-/node-libs-browser-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "assert" "1.5.0"
    "browserify-zlib" "0.2.0"
    "buffer" "4.9.2"
    "console-browserify" "1.2.0"
    "constants-browserify" "1.0.0"
    "crypto-browserify" "3.12.0"
    "domain-browser" "1.2.0"
    "events" "3.1.0"
    "https-browserify" "1.0.0"
    "os-browserify" "0.3.0"
    "path-browserify" "0.0.1"
    "process" "0.11.10"
    "punycode" "1.4.1"
    "querystring-es3" "0.2.1"
    "readable-stream" "2.3.7"
    "stream-browserify" "2.0.2"
    "stream-http" "2.8.3"
    "string_decoder" "1.1.1"
    "timers-browserify" "2.0.11"
    "tty-browserify" "0.0.0"
    "url" "0.11.0"
    "util" "0.11.1"
    "vm-browserify" "1.1.2"

"node-notifier@5.4.3":
  "integrity" "sha512-M4UBGcs4jeOK9CjTsYwkvH6/MzuUmGCyTW+kCY7uO+1ZVr0+FHGdPdIf5CCLqAaxnRrWidyoQlNkMIIVwbKB8Q=="
  "resolved" "https://registry.npmjs.org/node-notifier/-/node-notifier-5.4.3.tgz"
  "version" "5.4.3"
  dependencies:
    "growly" "1.3.0"
    "is-wsl" "1.1.0"
    "semver" "5.7.1"
    "shellwords" "0.1.1"
    "which" "1.3.1"

"node-releases@1.1.51":
  "integrity" "sha512-1eQEs6HFYY1kMXQPOLzCf7HdjReErmvn85tZESMczdCNVWP3Y7URYLBAyYynuI7yef1zj4HN5q+oB2x67QU0lw=="
  "resolved" "https://registry.npmjs.org/node-releases/-/node-releases-1.1.51.tgz"
  "version" "1.1.51"
  dependencies:
    "semver" "6.3.0"

"normalize-package-data@2.5.0":
  "integrity" "sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA=="
  "resolved" "https://registry.npmjs.org/normalize-package-data/-/normalize-package-data-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "hosted-git-info" "2.8.8"
    "resolve" "1.10.0"
    "semver" "5.7.1"
    "validate-npm-package-license" "3.0.4"

"normalize-path@2.1.1":
  "integrity" "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk="
  "resolved" "https://registry.npmjs.org/normalize-path/-/normalize-path-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "remove-trailing-separator" "1.1.0"

"normalize-path@3.0.0":
  "integrity" "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA=="
  "resolved" "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  "version" "3.0.0"

"normalize-range@0.1.2":
  "integrity" "sha1-LRDAa9/TEuqXd2laTShDlFa3WUI="
  "resolved" "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz"
  "version" "0.1.2"

"normalize-url@3.3.0":
  "integrity" "sha512-U+JJi7duF1o+u2pynbp2zXDW2/PADgC30f0GsHZtRh+HOcXHnw137TrNlyxxRvWW5fjKd3bcLHPxofWuCjaeZg=="
  "resolved" "https://registry.npmjs.org/normalize-url/-/normalize-url-3.3.0.tgz"
  "version" "3.3.0"

"npm-run-path@2.0.2":
  "integrity" "sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8="
  "resolved" "https://registry.npmjs.org/npm-run-path/-/npm-run-path-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "path-key" "2.0.1"

"nth-check@1.0.2":
  "integrity" "sha512-WeBOdju8SnzPN5vTUJYxYUxLeXpCaVP5i5e0LF8fg7WORF2Wd7wFX/pk0tYZk7s8T+J7VLy0Da6J1+wCT0AtHg=="
  "resolved" "https://registry.npmjs.org/nth-check/-/nth-check-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "boolbase" "1.0.0"

"num2fraction@1.2.2":
  "integrity" "sha1-b2gragJ6Tp3fpFZM0lidHU5mnt4="
  "resolved" "https://registry.npmjs.org/num2fraction/-/num2fraction-1.2.2.tgz"
  "version" "1.2.2"

"number-is-nan@1.0.1":
  "integrity" "sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0="
  "resolved" "https://registry.npmjs.org/number-is-nan/-/number-is-nan-1.0.1.tgz"
  "version" "1.0.1"

"nwsapi@2.2.0":
  "integrity" "sha512-h2AatdwYH+JHiZpv7pt/gSX1XoRGb7L/qSIeuqA6GwYoF9w1vP1cw42TO0aI2pNyshRK5893hNSl+1//vHK7hQ=="
  "resolved" "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.0.tgz"
  "version" "2.2.0"

"oauth-sign@0.9.0":
  "integrity" "sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ=="
  "resolved" "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.9.0.tgz"
  "version" "0.9.0"

"object-assign@4.1.1":
  "integrity" "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="
  "resolved" "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
  "version" "4.1.1"

"object-copy@0.1.0":
  "integrity" "sha1-fn2Fi3gb18mRpBupde04EnVOmYw="
  "resolved" "https://registry.npmjs.org/object-copy/-/object-copy-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "copy-descriptor" "0.1.1"
    "define-property" "0.2.5"
    "kind-of" "3.2.2"

"object-hash@1.3.1":
  "integrity" "sha512-OSuu/pU4ENM9kmREg0BdNrUDIl1heYa4mBZacJc+vVWz4GtAwu7jO8s4AIt2aGRUTqxykpWzI3Oqnsm13tTMDA=="
  "resolved" "https://registry.npmjs.org/object-hash/-/object-hash-1.3.1.tgz"
  "version" "1.3.1"

"object-inspect@1.7.0":
  "integrity" "sha512-a7pEHdh1xKIAgTySUGgLMx/xwDZskN1Ud6egYYN3EdRW4ZMPNEDUTF+hwy2LUC+Bl+SyLXANnwz/jyh/qutKUw=="
  "resolved" "https://registry.npmjs.org/object-inspect/-/object-inspect-1.7.0.tgz"
  "version" "1.7.0"

"object-is@1.0.2":
  "integrity" "sha512-Epah+btZd5wrrfjkJZq1AOB9O6OxUQto45hzFd7lXGrpHPGE0W1k+426yrZV+k6NJOzLNNW/nVsmZdIWsAqoOQ=="
  "resolved" "https://registry.npmjs.org/object-is/-/object-is-1.0.2.tgz"
  "version" "1.0.2"

"object-keys@1.1.1":
  "integrity" "sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA=="
  "resolved" "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz"
  "version" "1.1.1"

"object-visit@1.0.1":
  "integrity" "sha1-95xEk68MU3e1n+OdOV5BBC3QRbs="
  "resolved" "https://registry.npmjs.org/object-visit/-/object-visit-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "isobject" "3.0.1"

"object.assign@4.1.0":
  "integrity" "sha512-exHJeq6kBKj58mqGyTQ9DFvrZC/eR6OwxzoM9YRoGBqrXYonaFyGiFMuc9VZrXf7DarreEwMpurG3dd+CNyW5w=="
  "resolved" "https://registry.npmjs.org/object.assign/-/object.assign-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "define-properties" "1.1.3"
    "function-bind" "1.1.1"
    "has-symbols" "1.0.1"
    "object-keys" "1.1.1"

"object.fromentries@2.0.2":
  "integrity" "sha512-r3ZiBH7MQppDJVLx6fhD618GKNG40CZYH9wgwdhKxBDDbQgjeWGGd4AtkZad84d291YxvWe7bJGuE65Anh0dxQ=="
  "resolved" "https://registry.npmjs.org/object.fromentries/-/object.fromentries-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "define-properties" "1.1.3"
    "es-abstract" "1.17.4"
    "function-bind" "1.1.1"
    "has" "1.0.3"

"object.getownpropertydescriptors@2.1.0":
  "integrity" "sha512-Z53Oah9A3TdLoblT7VKJaTDdXdT+lQO+cNpKVnya5JDe9uLvzu1YyY1yFDFrcxrlRgWrEFH0jJtD/IbuwjcEVg=="
  "resolved" "https://registry.npmjs.org/object.getownpropertydescriptors/-/object.getownpropertydescriptors-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "define-properties" "1.1.3"
    "es-abstract" "1.17.4"

"object.omit@2.0.1":
  "integrity" "sha1-Gpx0SCnznbuFjHbKNXmuKlTr0fo="
  "resolved" "https://registry.npmjs.org/object.omit/-/object.omit-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "for-own" "0.1.5"
    "is-extendable" "0.1.1"

"object.pick@1.3.0":
  "integrity" "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c="
  "resolved" "https://registry.npmjs.org/object.pick/-/object.pick-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "isobject" "3.0.1"

"object.values@1.1.1":
  "integrity" "sha512-WTa54g2K8iu0kmS/us18jEmdv1a4Wi//BZ/DTVYEcH0XhLM5NYdpDHja3gt57VrZLcNAO2WGA+KpWsDBaHt6eA=="
  "resolved" "https://registry.npmjs.org/object.values/-/object.values-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "define-properties" "1.1.3"
    "es-abstract" "1.17.4"
    "function-bind" "1.1.1"
    "has" "1.0.3"

"obuf@1.1.2":
  "integrity" "sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg=="
  "resolved" "https://registry.npmjs.org/obuf/-/obuf-1.1.2.tgz"
  "version" "1.1.2"

"omit.js@1.0.2":
  "integrity" "sha512-/QPc6G2NS+8d4L/cQhbk6Yit1WTB6Us2g84A7A/1+w9d/eRGHyEqC5kkQtHVoHZ5NFWGG7tUGgrhVZwgZanKrQ=="
  "resolved" "https://registry.npmjs.org/omit.js/-/omit.js-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "babel-runtime" "6.26.0"

"on-finished@2.3.0":
  "integrity" "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc="
  "resolved" "https://registry.npmjs.org/on-finished/-/on-finished-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "ee-first" "1.1.1"

"on-headers@1.0.2":
  "integrity" "sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA=="
  "resolved" "https://registry.npmjs.org/on-headers/-/on-headers-1.0.2.tgz"
  "version" "1.0.2"

"once@1.4.0":
  "integrity" "sha1-WDsap3WWHUsROsF9nFC6753Xa9E="
  "resolved" "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "wrappy" "1.0.2"

"onetime@2.0.1":
  "integrity" "sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ="
  "resolved" "https://registry.npmjs.org/onetime/-/onetime-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "mimic-fn" "1.2.0"

"opn@5.4.0":
  "integrity" "sha512-YF9MNdVy/0qvJvDtunAOzFw9iasOQHpVthTCvGzxt61Il64AYSGdK+rYwld7NAfk9qJ7dt+hymBNSc9LNYS+Sw=="
  "resolved" "https://registry.npmjs.org/opn/-/opn-5.4.0.tgz"
  "version" "5.4.0"
  dependencies:
    "is-wsl" "1.1.0"

"optimist@0.6.1":
  "integrity" "sha1-2j6nRob6IaGaERwybpDrFaAZZoY="
  "resolved" "https://registry.npmjs.org/optimist/-/optimist-0.6.1.tgz"
  "version" "0.6.1"
  dependencies:
    "minimist" "0.0.10"
    "wordwrap" "0.0.3"

"optimize-css-assets-webpack-plugin@5.0.1":
  "integrity" "sha512-Rqm6sSjWtx9FchdP0uzTQDc7GXDKnwVEGoSxjezPkzMewx7gEWE9IMUYKmigTRC4U3RaNSwYVnUDLuIdtTpm0A=="
  "resolved" "https://registry.npmjs.org/optimize-css-assets-webpack-plugin/-/optimize-css-assets-webpack-plugin-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "cssnano" "4.1.10"
    "last-call-webpack-plugin" "3.0.0"

"optionator@0.8.3":
  "integrity" "sha512-+IW9pACdk3XWmmTXG8m3upGUJst5XRGzxMRjXzAuJ1XnIFNvfhjjIuYkDvysnPQ7qzqVzLt78BCruntqRhWQbA=="
  "resolved" "https://registry.npmjs.org/optionator/-/optionator-0.8.3.tgz"
  "version" "0.8.3"
  dependencies:
    "deep-is" "0.1.3"
    "fast-levenshtein" "2.0.6"
    "levn" "0.3.0"
    "prelude-ls" "1.1.2"
    "type-check" "0.3.2"
    "word-wrap" "1.2.3"

"original@1.0.2":
  "integrity" "sha512-hyBVl6iqqUOJ8FqRe+l/gS8H+kKYjrEndd5Pm1MfBtsEKA038HkkdbAl/72EAXGyonD/PFsvmVG+EvcIpliMBg=="
  "resolved" "https://registry.npmjs.org/original/-/original-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "url-parse" "1.4.7"

"os-browserify@0.3.0":
  "integrity" "sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc="
  "resolved" "https://registry.npmjs.org/os-browserify/-/os-browserify-0.3.0.tgz"
  "version" "0.3.0"

"os-homedir@1.0.2":
  "integrity" "sha1-/7xJiDNuDoM94MFox+8VISGqf7M="
  "resolved" "https://registry.npmjs.org/os-homedir/-/os-homedir-1.0.2.tgz"
  "version" "1.0.2"

"os-locale@3.1.0":
  "integrity" "sha512-Z8l3R4wYWM40/52Z+S265okfFj8Kt2cC2MKY+xNi3kFs+XGI7WXu/I309QQQYbRW4ijiZ+yxs9pqEhJh0DqW3Q=="
  "resolved" "https://registry.npmjs.org/os-locale/-/os-locale-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "execa" "1.0.0"
    "lcid" "2.0.0"
    "mem" "4.3.0"

"os-tmpdir@1.0.2":
  "integrity" "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ="
  "resolved" "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz"
  "version" "1.0.2"

"p-defer@1.0.0":
  "integrity" "sha1-n26xgvbJqozXQwBKfU+WsZaw+ww="
  "resolved" "https://registry.npmjs.org/p-defer/-/p-defer-1.0.0.tgz"
  "version" "1.0.0"

"p-finally@1.0.0":
  "integrity" "sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4="
  "resolved" "https://registry.npmjs.org/p-finally/-/p-finally-1.0.0.tgz"
  "version" "1.0.0"

"p-is-promise@2.1.0":
  "integrity" "sha512-Y3W0wlRPK8ZMRbNq97l4M5otioeA5lm1z7bkNkxCka8HSPjR0xRWmpCmc9utiaLP9Jb1eD8BgeIxTW4AIF45Pg=="
  "resolved" "https://registry.npmjs.org/p-is-promise/-/p-is-promise-2.1.0.tgz"
  "version" "2.1.0"

"p-limit@1.3.0":
  "integrity" "sha512-vvcXsLAJ9Dr5rQOPk7toZQZJApBl2K4J6dANSsEuh6QI41JYcsS/qhTGa9ErIUUgK3WNQoJYvylxvjqmiqEA9Q=="
  "resolved" "https://registry.npmjs.org/p-limit/-/p-limit-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "p-try" "1.0.0"

"p-limit@2.2.2":
  "integrity" "sha512-WGR+xHecKTr7EbUEhyLSh5Dube9JtdiG78ufaeLxTgpudf/20KqyMioIUZJAezlTIi6evxuoUs9YXc11cU+yzQ=="
  "resolved" "https://registry.npmjs.org/p-limit/-/p-limit-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "p-try" "2.2.0"

"p-locate@2.0.0":
  "integrity" "sha1-IKAQOyIqcMj9OcwuWAaA893l7EM="
  "resolved" "https://registry.npmjs.org/p-locate/-/p-locate-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "p-limit" "1.3.0"

"p-locate@3.0.0":
  "integrity" "sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ=="
  "resolved" "https://registry.npmjs.org/p-locate/-/p-locate-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "p-limit" "2.2.2"

"p-map@1.2.0":
  "integrity" "sha512-r6zKACMNhjPJMTl8KcFH4li//gkrXWfbD6feV8l6doRHlzljFWGJ2AP6iKaCJXyZmAUMOPtvbW7EXkbWO/pLEA=="
  "resolved" "https://registry.npmjs.org/p-map/-/p-map-1.2.0.tgz"
  "version" "1.2.0"

"p-try@1.0.0":
  "integrity" "sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M="
  "resolved" "https://registry.npmjs.org/p-try/-/p-try-1.0.0.tgz"
  "version" "1.0.0"

"p-try@2.2.0":
  "integrity" "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ=="
  "resolved" "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz"
  "version" "2.2.0"

"pako@1.0.11":
  "integrity" "sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw=="
  "resolved" "https://registry.npmjs.org/pako/-/pako-1.0.11.tgz"
  "version" "1.0.11"

"parallel-transform@1.2.0":
  "integrity" "sha512-P2vSmIu38uIlvdcU7fDkyrxj33gTUy/ABO5ZUbGowxNCopBq/OoD42bP4UmMrJoPyk4Uqf0mu3mtWBhHCZD8yg=="
  "resolved" "https://registry.npmjs.org/parallel-transform/-/parallel-transform-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "cyclist" "1.0.1"
    "inherits" "2.0.4"
    "readable-stream" "2.3.7"

"param-case@2.1.1":
  "integrity" "sha1-35T9jPZTHs915r75oIWPvHK+Ikc="
  "resolved" "https://registry.npmjs.org/param-case/-/param-case-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "no-case" "2.3.2"

"parent-module@1.0.1":
  "integrity" "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g=="
  "resolved" "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "callsites" "3.1.0"

"parse-asn1@5.1.5":
  "integrity" "sha512-jkMYn1dcJqF6d5CpU689bq7w/b5ALS9ROVSpQDPrZsqqesUJii9qutvoT5ltGedNXMO2e16YUWIghG9KxaViTQ=="
  "resolved" "https://registry.npmjs.org/parse-asn1/-/parse-asn1-5.1.5.tgz"
  "version" "5.1.5"
  dependencies:
    "asn1.js" "4.10.1"
    "browserify-aes" "1.2.0"
    "create-hash" "1.2.0"
    "evp_bytestokey" "1.0.3"
    "pbkdf2" "3.0.17"
    "safe-buffer" "5.2.0"

"parse-glob@3.0.4":
  "integrity" "sha1-ssN2z7EfNVE7rdFz7wu246OIORw="
  "resolved" "https://registry.npmjs.org/parse-glob/-/parse-glob-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "glob-base" "0.3.0"
    "is-dotfile" "1.0.3"
    "is-extglob" "1.0.0"
    "is-glob" "2.0.1"

"parse-json@2.2.0":
  "integrity" "sha1-9ID0BDTvgHQfhGkJn43qGPVaTck="
  "resolved" "https://registry.npmjs.org/parse-json/-/parse-json-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "error-ex" "1.3.2"

"parse-json@4.0.0":
  "integrity" "sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA="
  "resolved" "https://registry.npmjs.org/parse-json/-/parse-json-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "error-ex" "1.3.2"
    "json-parse-better-errors" "1.0.2"

"parse5@4.0.0":
  "integrity" "sha512-VrZ7eOd3T1Fk4XWNXMgiGBK/z0MG48BWG2uQNU4I72fkQuKUTZpl+u9k+CxEG0twMVzSmXEEz12z5Fnw1jIQFA=="
  "resolved" "https://registry.npmjs.org/parse5/-/parse5-4.0.0.tgz"
  "version" "4.0.0"

"parseurl@1.3.3":
  "integrity" "sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ=="
  "resolved" "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz"
  "version" "1.3.3"

"pascalcase@0.1.1":
  "integrity" "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ="
  "resolved" "https://registry.npmjs.org/pascalcase/-/pascalcase-0.1.1.tgz"
  "version" "0.1.1"

"path-browserify@0.0.1":
  "integrity" "sha512-BapA40NHICOS+USX9SN4tyhq+A2RrN/Ws5F0Z5aMHDp98Fl86lX8Oti8B7uN93L4Ifv4fHOEA+pQw87gmMO/lQ=="
  "resolved" "https://registry.npmjs.org/path-browserify/-/path-browserify-0.0.1.tgz"
  "version" "0.0.1"

"path-dirname@1.0.2":
  "integrity" "sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA="
  "resolved" "https://registry.npmjs.org/path-dirname/-/path-dirname-1.0.2.tgz"
  "version" "1.0.2"

"path-exists@2.1.0":
  "integrity" "sha1-D+tsZPD8UY2adU3V77YscCJ2H0s="
  "resolved" "https://registry.npmjs.org/path-exists/-/path-exists-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pinkie-promise" "2.0.1"

"path-exists@3.0.0":
  "integrity" "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU="
  "resolved" "https://registry.npmjs.org/path-exists/-/path-exists-3.0.0.tgz"
  "version" "3.0.0"

"path-is-absolute@1.0.1":
  "integrity" "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="
  "resolved" "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  "version" "1.0.1"

"path-is-inside@1.0.2":
  "integrity" "sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM="
  "resolved" "https://registry.npmjs.org/path-is-inside/-/path-is-inside-1.0.2.tgz"
  "version" "1.0.2"

"path-key@2.0.1":
  "integrity" "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A="
  "resolved" "https://registry.npmjs.org/path-key/-/path-key-2.0.1.tgz"
  "version" "2.0.1"

"path-parse@1.0.6":
  "integrity" "sha512-GSmOT2EbHrINBf9SR7CDELwlJ8AENk3Qn7OikK4nFYAu3Ote2+JYNVvkpAEQm3/TLNEJFD/xZJjzyxg3KBWOzw=="
  "resolved" "https://registry.npmjs.org/path-parse/-/path-parse-1.0.6.tgz"
  "version" "1.0.6"

"path-to-regexp@0.1.7":
  "integrity" "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w="
  "resolved" "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.7.tgz"
  "version" "0.1.7"

"path-to-regexp@1.8.0":
  "integrity" "sha512-n43JRhlUKUAlibEJhPeir1ncUID16QnEjNpwzNdO3Lm4ywrBpBZ5oLD0I6br9evr1Y9JTqwRtAh7JLoOzAQdVA=="
  "resolved" "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-1.8.0.tgz"
  "version" "1.8.0"
  dependencies:
    "isarray" "0.0.1"

"path-type@1.1.0":
  "integrity" "sha1-WcRPfuSR2nBNpBXaWkBwuk+P5EE="
  "resolved" "https://registry.npmjs.org/path-type/-/path-type-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "graceful-fs" "4.2.3"
    "pify" "2.3.0"
    "pinkie-promise" "2.0.1"

"path-type@2.0.0":
  "integrity" "sha1-8BLMuEFbcJb8LaoQVMPXI4lZTHM="
  "resolved" "https://registry.npmjs.org/path-type/-/path-type-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "pify" "2.3.0"

"path-type@3.0.0":
  "integrity" "sha512-T2ZUsdZFHgA3u4e5PfPbjd7HDDpxPnQb5jN0SrDsjNSuVXHJqtwTnWqG0B1jZrgmJ/7lj1EmVIByWt1gxGkWvg=="
  "resolved" "https://registry.npmjs.org/path-type/-/path-type-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "pify" "3.0.0"

"pbkdf2@3.0.17":
  "integrity" "sha512-U/il5MsrZp7mGg3mSQfn742na2T+1/vHDCG5/iTI3X9MKUuYUZVLQhyRsg06mCgDBTd57TxzgZt7P+fYfjRLtA=="
  "resolved" "https://registry.npmjs.org/pbkdf2/-/pbkdf2-3.0.17.tgz"
  "version" "3.0.17"
  dependencies:
    "create-hash" "1.2.0"
    "create-hmac" "1.1.7"
    "ripemd160" "2.0.2"
    "safe-buffer" "5.2.0"
    "sha.js" "2.4.11"

"performance-now@2.1.0":
  "integrity" "sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns="
  "resolved" "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz"
  "version" "2.1.0"

"pify@2.3.0":
  "integrity" "sha1-7RQaasBDqEnqWISY59yosVMw6Qw="
  "resolved" "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz"
  "version" "2.3.0"

"pify@3.0.0":
  "integrity" "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY="
  "resolved" "https://registry.npmjs.org/pify/-/pify-3.0.0.tgz"
  "version" "3.0.0"

"pify@4.0.1":
  "integrity" "sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g=="
  "resolved" "https://registry.npmjs.org/pify/-/pify-4.0.1.tgz"
  "version" "4.0.1"

"pinkie-promise@2.0.1":
  "integrity" "sha1-ITXW36ejWMBprJsXh3YogihFD/o="
  "resolved" "https://registry.npmjs.org/pinkie-promise/-/pinkie-promise-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "pinkie" "2.0.4"

"pinkie@2.0.4":
  "integrity" "sha1-clVrgM+g1IqXToDnckjoDtT3+HA="
  "resolved" "https://registry.npmjs.org/pinkie/-/pinkie-2.0.4.tgz"
  "version" "2.0.4"

"pkg-dir@1.0.0":
  "integrity" "sha1-ektQio1bstYp1EcFb/TpyTFM89Q="
  "resolved" "https://registry.npmjs.org/pkg-dir/-/pkg-dir-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "find-up" "1.1.2"

"pkg-dir@2.0.0":
  "integrity" "sha1-9tXREJ4Z1j7fQo4L1X4Sd3YVM0s="
  "resolved" "https://registry.npmjs.org/pkg-dir/-/pkg-dir-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "find-up" "2.1.0"

"pkg-dir@3.0.0":
  "integrity" "sha512-/E57AYkoeQ25qkxMj5PBOVgF8Kiu/h7cYS30Z5+R7WaiCCBfLq58ZI/dSeaEKb9WVJV5n/03QwrN3IeWIFllvw=="
  "resolved" "https://registry.npmjs.org/pkg-dir/-/pkg-dir-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "find-up" "3.0.0"

"pkg-up@2.0.0":
  "integrity" "sha1-yBmscoBZpGHKscOImivjxJoATX8="
  "resolved" "https://registry.npmjs.org/pkg-up/-/pkg-up-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "find-up" "2.1.0"

"pluralize@7.0.0":
  "integrity" "sha512-ARhBOdzS3e41FbkW/XWrTEtukqqLoK5+Z/4UeDaLuSW+39JPeFgs4gCGqsrJHVZX0fUrx//4OF0K1CUGwlIFow=="
  "resolved" "https://registry.npmjs.org/pluralize/-/pluralize-7.0.0.tgz"
  "version" "7.0.0"

"pn@1.1.0":
  "integrity" "sha512-2qHaIQr2VLRFoxe2nASzsV6ef4yOOH+Fi9FBOVH6cqeSgUnoyySPZkxzLuzd+RYOQTRpROA0ztTMqxROKSb/nA=="
  "resolved" "https://registry.npmjs.org/pn/-/pn-1.1.0.tgz"
  "version" "1.1.0"

"pnp-webpack-plugin@1.2.1":
  "integrity" "sha512-W6GctK7K2qQiVR+gYSv/Gyt6jwwIH4vwdviFqx+Y2jAtVf5eZyYIDf5Ac2NCDMBiX5yWscBLZElPTsyA1UtVVA=="
  "resolved" "https://registry.npmjs.org/pnp-webpack-plugin/-/pnp-webpack-plugin-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "ts-pnp" "1.1.6"

"portfinder@1.0.25":
  "integrity" "sha512-6ElJnHBbxVA1XSLgBp7G1FiCkQdlqGzuF7DswL5tcea+E8UpuvPU7beVAjjRwCioTS9ZluNbu+ZyRvgTsmqEBg=="
  "resolved" "https://registry.npmjs.org/portfinder/-/portfinder-1.0.25.tgz"
  "version" "1.0.25"
  dependencies:
    "async" "2.6.3"
    "debug" "3.2.6"
    "mkdirp" "0.5.1"

"posix-character-classes@0.1.1":
  "integrity" "sha1-AerA/jta9xoqbAL+q7jB/vfgDqs="
  "resolved" "https://registry.npmjs.org/posix-character-classes/-/posix-character-classes-0.1.1.tgz"
  "version" "0.1.1"

"postcss-attribute-case-insensitive@4.0.2":
  "integrity" "sha512-clkFxk/9pcdb4Vkn0hAHq3YnxBQ2p0CGD1dy24jN+reBck+EWxMbxSUqN4Yj7t0w8csl87K6p0gxBe1utkJsYA=="
  "resolved" "https://registry.npmjs.org/postcss-attribute-case-insensitive/-/postcss-attribute-case-insensitive-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "7.0.27"
    "postcss-selector-parser" "6.0.2"

"postcss-calc@7.0.2":
  "integrity" "sha512-rofZFHUg6ZIrvRwPeFktv06GdbDYLcGqh9EwiMutZg+a0oePCCw1zHOEiji6LCpyRcjTREtPASuUqeAvYlEVvQ=="
  "resolved" "https://registry.npmjs.org/postcss-calc/-/postcss-calc-7.0.2.tgz"
  "version" "7.0.2"
  dependencies:
    "postcss" "7.0.27"
    "postcss-selector-parser" "6.0.2"
    "postcss-value-parser" "4.0.3"

"postcss-color-functional-notation@2.0.1":
  "integrity" "sha512-ZBARCypjEDofW4P6IdPVTLhDNXPRn8T2s1zHbZidW6rPaaZvcnCS2soYFIQJrMZSxiePJ2XIYTlcb2ztr/eT2g=="
  "resolved" "https://registry.npmjs.org/postcss-color-functional-notation/-/postcss-color-functional-notation-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "postcss" "7.0.27"
    "postcss-values-parser" "2.0.1"

"postcss-color-gray@5.0.0":
  "integrity" "sha512-q6BuRnAGKM/ZRpfDascZlIZPjvwsRye7UDNalqVz3s7GDxMtqPY6+Q871liNxsonUw8oC61OG+PSaysYpl1bnw=="
  "resolved" "https://registry.npmjs.org/postcss-color-gray/-/postcss-color-gray-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "@csstools/convert-colors" "1.4.0"
    "postcss" "7.0.27"
    "postcss-values-parser" "2.0.1"

"postcss-color-hex-alpha@5.0.3":
  "integrity" "sha512-PF4GDel8q3kkreVXKLAGNpHKilXsZ6xuu+mOQMHWHLPNyjiUBOr75sp5ZKJfmv1MCus5/DWUGcK9hm6qHEnXYw=="
  "resolved" "https://registry.npmjs.org/postcss-color-hex-alpha/-/postcss-color-hex-alpha-5.0.3.tgz"
  "version" "5.0.3"
  dependencies:
    "postcss" "7.0.27"
    "postcss-values-parser" "2.0.1"

"postcss-color-mod-function@3.0.3":
  "integrity" "sha512-YP4VG+xufxaVtzV6ZmhEtc+/aTXH3d0JLpnYfxqTvwZPbJhWqp8bSY3nfNzNRFLgB4XSaBA82OE4VjOOKpCdVQ=="
  "resolved" "https://registry.npmjs.org/postcss-color-mod-function/-/postcss-color-mod-function-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "@csstools/convert-colors" "1.4.0"
    "postcss" "7.0.27"
    "postcss-values-parser" "2.0.1"

"postcss-color-rebeccapurple@4.0.1":
  "integrity" "sha512-aAe3OhkS6qJXBbqzvZth2Au4V3KieR5sRQ4ptb2b2O8wgvB3SJBsdG+jsn2BZbbwekDG8nTfcCNKcSfe/lEy8g=="
  "resolved" "https://registry.npmjs.org/postcss-color-rebeccapurple/-/postcss-color-rebeccapurple-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "7.0.27"
    "postcss-values-parser" "2.0.1"

"postcss-colormin@4.0.3":
  "integrity" "sha512-WyQFAdDZpExQh32j0U0feWisZ0dmOtPl44qYmJKkq9xFWY3p+4qnRzCHeNrkeRhwPHz9bQ3mo0/yVkaply0MNw=="
  "resolved" "https://registry.npmjs.org/postcss-colormin/-/postcss-colormin-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "4.9.1"
    "color" "3.1.2"
    "has" "1.0.3"
    "postcss" "7.0.27"
    "postcss-value-parser" "3.3.1"

"postcss-convert-values@4.0.1":
  "integrity" "sha512-Kisdo1y77KUC0Jmn0OXU/COOJbzM8cImvw1ZFsBgBgMgb1iL23Zs/LXRe3r+EZqM3vGYKdQ2YJVQ5VkJI+zEJQ=="
  "resolved" "https://registry.npmjs.org/postcss-convert-values/-/postcss-convert-values-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "7.0.27"
    "postcss-value-parser" "3.3.1"

"postcss-custom-media@7.0.8":
  "integrity" "sha512-c9s5iX0Ge15o00HKbuRuTqNndsJUbaXdiNsksnVH8H4gdc+zbLzr/UasOwNG6CTDpLFekVY4672eWdiiWu2GUg=="
  "resolved" "https://registry.npmjs.org/postcss-custom-media/-/postcss-custom-media-7.0.8.tgz"
  "version" "7.0.8"
  dependencies:
    "postcss" "7.0.27"

"postcss-custom-properties@8.0.11":
  "integrity" "sha512-nm+o0eLdYqdnJ5abAJeXp4CEU1c1k+eB2yMCvhgzsds/e0umabFrN6HoTy/8Q4K5ilxERdl/JD1LO5ANoYBeMA=="
  "resolved" "https://registry.npmjs.org/postcss-custom-properties/-/postcss-custom-properties-8.0.11.tgz"
  "version" "8.0.11"
  dependencies:
    "postcss" "7.0.27"
    "postcss-values-parser" "2.0.1"

"postcss-custom-selectors@5.1.2":
  "integrity" "sha512-DSGDhqinCqXqlS4R7KGxL1OSycd1lydugJ1ky4iRXPHdBRiozyMHrdu0H3o7qNOCiZwySZTUI5MV0T8QhCLu+w=="
  "resolved" "https://registry.npmjs.org/postcss-custom-selectors/-/postcss-custom-selectors-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "postcss" "7.0.27"
    "postcss-selector-parser" "5.0.0"

"postcss-dir-pseudo-class@5.0.0":
  "integrity" "sha512-3pm4oq8HYWMZePJY+5ANriPs3P07q+LW6FAdTlkFH2XqDdP4HeeJYMOzn0HYLhRSjBO3fhiqSwwU9xEULSrPgw=="
  "resolved" "https://registry.npmjs.org/postcss-dir-pseudo-class/-/postcss-dir-pseudo-class-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "postcss" "7.0.27"
    "postcss-selector-parser" "5.0.0"

"postcss-discard-comments@4.0.2":
  "integrity" "sha512-RJutN259iuRf3IW7GZyLM5Sw4GLTOH8FmsXBnv8Ab/Tc2k4SR4qbV4DNbyyY4+Sjo362SyDmW2DQ7lBSChrpkg=="
  "resolved" "https://registry.npmjs.org/postcss-discard-comments/-/postcss-discard-comments-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "7.0.27"

"postcss-discard-duplicates@4.0.2":
  "integrity" "sha512-ZNQfR1gPNAiXZhgENFfEglF93pciw0WxMkJeVmw8eF+JZBbMD7jp6C67GqJAXVZP2BWbOztKfbsdmMp/k8c6oQ=="
  "resolved" "https://registry.npmjs.org/postcss-discard-duplicates/-/postcss-discard-duplicates-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "7.0.27"

"postcss-discard-empty@4.0.1":
  "integrity" "sha512-B9miTzbznhDjTfjvipfHoqbWKwd0Mj+/fL5s1QOz06wufguil+Xheo4XpOnc4NqKYBCNqqEzgPv2aPBIJLox0w=="
  "resolved" "https://registry.npmjs.org/postcss-discard-empty/-/postcss-discard-empty-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "7.0.27"

"postcss-discard-overridden@4.0.1":
  "integrity" "sha512-IYY2bEDD7g1XM1IDEsUT4//iEYCxAmP5oDSFMVU/JVvT7gh+l4fmjciLqGgwjdWpQIdb0Che2VX00QObS5+cTg=="
  "resolved" "https://registry.npmjs.org/postcss-discard-overridden/-/postcss-discard-overridden-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "7.0.27"

"postcss-double-position-gradients@1.0.0":
  "integrity" "sha512-G+nV8EnQq25fOI8CH/B6krEohGWnF5+3A6H/+JEpOncu5dCnkS1QQ6+ct3Jkaepw1NGVqqOZH6lqrm244mCftA=="
  "resolved" "https://registry.npmjs.org/postcss-double-position-gradients/-/postcss-double-position-gradients-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "postcss" "7.0.27"
    "postcss-values-parser" "2.0.1"

"postcss-env-function@2.0.2":
  "integrity" "sha512-rwac4BuZlITeUbiBq60h/xbLzXY43qOsIErngWa4l7Mt+RaSkT7QBjXVGTcBHupykkblHMDrBFh30zchYPaOUw=="
  "resolved" "https://registry.npmjs.org/postcss-env-function/-/postcss-env-function-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "postcss" "7.0.27"
    "postcss-values-parser" "2.0.1"

"postcss-flexbugs-fixes@4.1.0":
  "integrity" "sha512-jr1LHxQvStNNAHlgco6PzY308zvLklh7SJVYuWUwyUQncofaAlD2l+P/gxKHOdqWKe7xJSkVLFF/2Tp+JqMSZA=="
  "resolved" "https://registry.npmjs.org/postcss-flexbugs-fixes/-/postcss-flexbugs-fixes-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "postcss" "7.0.27"

"postcss-focus-visible@4.0.0":
  "integrity" "sha512-Z5CkWBw0+idJHSV6+Bgf2peDOFf/x4o+vX/pwcNYrWpXFrSfTkQ3JQ1ojrq9yS+upnAlNRHeg8uEwFTgorjI8g=="
  "resolved" "https://registry.npmjs.org/postcss-focus-visible/-/postcss-focus-visible-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "postcss" "7.0.27"

"postcss-focus-within@3.0.0":
  "integrity" "sha512-W0APui8jQeBKbCGZudW37EeMCjDeVxKgiYfIIEo8Bdh5SpB9sxds/Iq8SEuzS0Q4YFOlG7EPFulbbxujpkrV2w=="
  "resolved" "https://registry.npmjs.org/postcss-focus-within/-/postcss-focus-within-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "postcss" "7.0.27"

"postcss-font-variant@4.0.0":
  "integrity" "sha512-M8BFYKOvCrI2aITzDad7kWuXXTm0YhGdP9Q8HanmN4EF1Hmcgs1KK5rSHylt/lUJe8yLxiSwWAHdScoEiIxztg=="
  "resolved" "https://registry.npmjs.org/postcss-font-variant/-/postcss-font-variant-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "postcss" "7.0.27"

"postcss-gap-properties@2.0.0":
  "integrity" "sha512-QZSqDaMgXCHuHTEzMsS2KfVDOq7ZFiknSpkrPJY6jmxbugUPTuSzs/vuE5I3zv0WAS+3vhrlqhijiprnuQfzmg=="
  "resolved" "https://registry.npmjs.org/postcss-gap-properties/-/postcss-gap-properties-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "postcss" "7.0.27"

"postcss-image-set-function@3.0.1":
  "integrity" "sha512-oPTcFFip5LZy8Y/whto91L9xdRHCWEMs3e1MdJxhgt4jy2WYXfhkng59fH5qLXSCPN8k4n94p1Czrfe5IOkKUw=="
  "resolved" "https://registry.npmjs.org/postcss-image-set-function/-/postcss-image-set-function-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "postcss" "7.0.27"
    "postcss-values-parser" "2.0.1"

"postcss-initial@3.0.2":
  "integrity" "sha512-ugA2wKonC0xeNHgirR4D3VWHs2JcU08WAi1KFLVcnb7IN89phID6Qtg2RIctWbnvp1TM2BOmDtX8GGLCKdR8YA=="
  "resolved" "https://registry.npmjs.org/postcss-initial/-/postcss-initial-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "lodash.template" "4.5.0"
    "postcss" "7.0.27"

"postcss-lab-function@2.0.1":
  "integrity" "sha512-whLy1IeZKY+3fYdqQFuDBf8Auw+qFuVnChWjmxm/UhHWqNHZx+B99EwxTvGYmUBqe3Fjxs4L1BoZTJmPu6usVg=="
  "resolved" "https://registry.npmjs.org/postcss-lab-function/-/postcss-lab-function-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "@csstools/convert-colors" "1.4.0"
    "postcss" "7.0.27"
    "postcss-values-parser" "2.0.1"

"postcss-load-config@2.1.0":
  "integrity" "sha512-4pV3JJVPLd5+RueiVVB+gFOAa7GWc25XQcMp86Zexzke69mKf6Nx9LRcQywdz7yZI9n1udOxmLuAwTBypypF8Q=="
  "resolved" "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "cosmiconfig" "5.2.1"
    "import-cwd" "2.1.0"

"postcss-loader@3.0.0":
  "integrity" "sha512-cLWoDEY5OwHcAjDnkyRQzAXfs2jrKjXpO/HQFcc5b5u/r7aa471wdmChmwfnv7x2u840iat/wi0lQ5nbRgSkUA=="
  "resolved" "https://registry.npmjs.org/postcss-loader/-/postcss-loader-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "loader-utils" "1.4.0"
    "postcss" "7.0.27"
    "postcss-load-config" "2.1.0"
    "schema-utils" "1.0.0"

"postcss-logical@3.0.0":
  "integrity" "sha512-1SUKdJc2vuMOmeItqGuNaC+N8MzBWFWEkAnRnLpFYj1tGGa7NqyVBujfRtgNa2gXR+6RkGUiB2O5Vmh7E2RmiA=="
  "resolved" "https://registry.npmjs.org/postcss-logical/-/postcss-logical-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "postcss" "7.0.27"

"postcss-media-minmax@4.0.0":
  "integrity" "sha512-fo9moya6qyxsjbFAYl97qKO9gyre3qvbMnkOZeZwlsW6XYFsvs2DMGDlchVLfAd8LHPZDxivu/+qW2SMQeTHBw=="
  "resolved" "https://registry.npmjs.org/postcss-media-minmax/-/postcss-media-minmax-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "postcss" "7.0.27"

"postcss-merge-longhand@4.0.11":
  "integrity" "sha512-alx/zmoeXvJjp7L4mxEMjh8lxVlDFX1gqWHzaaQewwMZiVhLo42TEClKaeHbRf6J7j82ZOdTJ808RtN0ZOZwvw=="
  "resolved" "https://registry.npmjs.org/postcss-merge-longhand/-/postcss-merge-longhand-4.0.11.tgz"
  "version" "4.0.11"
  dependencies:
    "css-color-names" "0.0.4"
    "postcss" "7.0.27"
    "postcss-value-parser" "3.3.1"
    "stylehacks" "4.0.3"

"postcss-merge-rules@4.0.3":
  "integrity" "sha512-U7e3r1SbvYzO0Jr3UT/zKBVgYYyhAz0aitvGIYOYK5CPmkNih+WDSsS5tvPrJ8YMQYlEMvsZIiqmn7HdFUaeEQ=="
  "resolved" "https://registry.npmjs.org/postcss-merge-rules/-/postcss-merge-rules-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "4.9.1"
    "caniuse-api" "3.0.0"
    "cssnano-util-same-parent" "4.0.1"
    "postcss" "7.0.27"
    "postcss-selector-parser" "3.1.2"
    "vendors" "1.0.4"

"postcss-minify-font-values@4.0.2":
  "integrity" "sha512-j85oO6OnRU9zPf04+PZv1LYIYOprWm6IA6zkXkrJXyRveDEuQggG6tvoy8ir8ZwjLxLuGfNkCZEQG7zan+Hbtg=="
  "resolved" "https://registry.npmjs.org/postcss-minify-font-values/-/postcss-minify-font-values-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "7.0.27"
    "postcss-value-parser" "3.3.1"

"postcss-minify-gradients@4.0.2":
  "integrity" "sha512-qKPfwlONdcf/AndP1U8SJ/uzIJtowHlMaSioKzebAXSG4iJthlWC9iSWznQcX4f66gIWX44RSA841HTHj3wK+Q=="
  "resolved" "https://registry.npmjs.org/postcss-minify-gradients/-/postcss-minify-gradients-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-arguments" "4.0.0"
    "is-color-stop" "1.1.0"
    "postcss" "7.0.27"
    "postcss-value-parser" "3.3.1"

"postcss-minify-params@4.0.2":
  "integrity" "sha512-G7eWyzEx0xL4/wiBBJxJOz48zAKV2WG3iZOqVhPet/9geefm/Px5uo1fzlHu+DOjT+m0Mmiz3jkQzVHe6wxAWg=="
  "resolved" "https://registry.npmjs.org/postcss-minify-params/-/postcss-minify-params-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "alphanum-sort" "1.0.2"
    "browserslist" "4.9.1"
    "cssnano-util-get-arguments" "4.0.0"
    "postcss" "7.0.27"
    "postcss-value-parser" "3.3.1"
    "uniqs" "2.0.0"

"postcss-minify-selectors@4.0.2":
  "integrity" "sha512-D5S1iViljXBj9kflQo4YutWnJmwm8VvIsU1GeXJGiG9j8CIg9zs4voPMdQDUmIxetUOh60VilsNzCiAFTOqu3g=="
  "resolved" "https://registry.npmjs.org/postcss-minify-selectors/-/postcss-minify-selectors-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "alphanum-sort" "1.0.2"
    "has" "1.0.3"
    "postcss" "7.0.27"
    "postcss-selector-parser" "3.1.2"

"postcss-modules-extract-imports@1.2.1":
  "integrity" "sha512-6jt9XZwUhwmRUhb/CkyJY020PYaPJsCyt3UjbaWo6XEbH/94Hmv6MP7fG2C5NDU/BcHzyGYxNtHvM+LTf9HrYw=="
  "resolved" "https://registry.npmjs.org/postcss-modules-extract-imports/-/postcss-modules-extract-imports-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "postcss" "6.0.23"

"postcss-modules-local-by-default@1.2.0":
  "integrity" "sha1-99gMOYxaOT+nlkRmvRlQCn1hwGk="
  "resolved" "https://registry.npmjs.org/postcss-modules-local-by-default/-/postcss-modules-local-by-default-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "css-selector-tokenizer" "0.7.2"
    "postcss" "6.0.23"

"postcss-modules-scope@1.1.0":
  "integrity" "sha1-1upkmUx5+XtipytCb75gVqGUu5A="
  "resolved" "https://registry.npmjs.org/postcss-modules-scope/-/postcss-modules-scope-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "css-selector-tokenizer" "0.7.2"
    "postcss" "6.0.23"

"postcss-modules-values@1.3.0":
  "integrity" "sha1-7P+p1+GSUYOJ9CrQ6D9yrsRW6iA="
  "resolved" "https://registry.npmjs.org/postcss-modules-values/-/postcss-modules-values-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "icss-replace-symbols" "1.1.0"
    "postcss" "6.0.23"

"postcss-nesting@7.0.1":
  "integrity" "sha512-FrorPb0H3nuVq0Sff7W2rnc3SmIcruVC6YwpcS+k687VxyxO33iE1amna7wHuRVzM8vfiYofXSBHNAZ3QhLvYg=="
  "resolved" "https://registry.npmjs.org/postcss-nesting/-/postcss-nesting-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "postcss" "7.0.27"

"postcss-normalize-charset@4.0.1":
  "integrity" "sha512-gMXCrrlWh6G27U0hF3vNvR3w8I1s2wOBILvA87iNXaPvSNo5uZAMYsZG7XjCUf1eVxuPfyL4TJ7++SGZLc9A3g=="
  "resolved" "https://registry.npmjs.org/postcss-normalize-charset/-/postcss-normalize-charset-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "7.0.27"

"postcss-normalize-display-values@4.0.2":
  "integrity" "sha512-3F2jcsaMW7+VtRMAqf/3m4cPFhPD3EFRgNs18u+k3lTJJlVe7d0YPO+bnwqo2xg8YiRpDXJI2u8A0wqJxMsQuQ=="
  "resolved" "https://registry.npmjs.org/postcss-normalize-display-values/-/postcss-normalize-display-values-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-match" "4.0.0"
    "postcss" "7.0.27"
    "postcss-value-parser" "3.3.1"

"postcss-normalize-positions@4.0.2":
  "integrity" "sha512-Dlf3/9AxpxE+NF1fJxYDeggi5WwV35MXGFnnoccP/9qDtFrTArZ0D0R+iKcg5WsUd8nUYMIl8yXDCtcrT8JrdA=="
  "resolved" "https://registry.npmjs.org/postcss-normalize-positions/-/postcss-normalize-positions-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-arguments" "4.0.0"
    "has" "1.0.3"
    "postcss" "7.0.27"
    "postcss-value-parser" "3.3.1"

"postcss-normalize-repeat-style@4.0.2":
  "integrity" "sha512-qvigdYYMpSuoFs3Is/f5nHdRLJN/ITA7huIoCyqqENJe9PvPmLhNLMu7QTjPdtnVf6OcYYO5SHonx4+fbJE1+Q=="
  "resolved" "https://registry.npmjs.org/postcss-normalize-repeat-style/-/postcss-normalize-repeat-style-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-arguments" "4.0.0"
    "cssnano-util-get-match" "4.0.0"
    "postcss" "7.0.27"
    "postcss-value-parser" "3.3.1"

"postcss-normalize-string@4.0.2":
  "integrity" "sha512-RrERod97Dnwqq49WNz8qo66ps0swYZDSb6rM57kN2J+aoyEAJfZ6bMx0sx/F9TIEX0xthPGCmeyiam/jXif0eA=="
  "resolved" "https://registry.npmjs.org/postcss-normalize-string/-/postcss-normalize-string-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "has" "1.0.3"
    "postcss" "7.0.27"
    "postcss-value-parser" "3.3.1"

"postcss-normalize-timing-functions@4.0.2":
  "integrity" "sha512-acwJY95edP762e++00Ehq9L4sZCEcOPyaHwoaFOhIwWCDfik6YvqsYNxckee65JHLKzuNSSmAdxwD2Cud1Z54A=="
  "resolved" "https://registry.npmjs.org/postcss-normalize-timing-functions/-/postcss-normalize-timing-functions-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-match" "4.0.0"
    "postcss" "7.0.27"
    "postcss-value-parser" "3.3.1"

"postcss-normalize-unicode@4.0.1":
  "integrity" "sha512-od18Uq2wCYn+vZ/qCOeutvHjB5jm57ToxRaMeNuf0nWVHaP9Hua56QyMF6fs/4FSUnVIw0CBPsU0K4LnBPwYwg=="
  "resolved" "https://registry.npmjs.org/postcss-normalize-unicode/-/postcss-normalize-unicode-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "browserslist" "4.9.1"
    "postcss" "7.0.27"
    "postcss-value-parser" "3.3.1"

"postcss-normalize-url@4.0.1":
  "integrity" "sha512-p5oVaF4+IHwu7VpMan/SSpmpYxcJMtkGppYf0VbdH5B6hN8YNmVyJLuY9FmLQTzY3fag5ESUUHDqM+heid0UVA=="
  "resolved" "https://registry.npmjs.org/postcss-normalize-url/-/postcss-normalize-url-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "is-absolute-url" "2.1.0"
    "normalize-url" "3.3.0"
    "postcss" "7.0.27"
    "postcss-value-parser" "3.3.1"

"postcss-normalize-whitespace@4.0.2":
  "integrity" "sha512-tO8QIgrsI3p95r8fyqKV+ufKlSHh9hMJqACqbv2XknufqEDhDvbguXGBBqxw9nsQoXWf0qOqppziKJKHMD4GtA=="
  "resolved" "https://registry.npmjs.org/postcss-normalize-whitespace/-/postcss-normalize-whitespace-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "7.0.27"
    "postcss-value-parser" "3.3.1"

"postcss-ordered-values@4.1.2":
  "integrity" "sha512-2fCObh5UanxvSxeXrtLtlwVThBvHn6MQcu4ksNT2tsaV2Fg76R2CV98W7wNSlX+5/pFwEyaDwKLLoEV7uRybAw=="
  "resolved" "https://registry.npmjs.org/postcss-ordered-values/-/postcss-ordered-values-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "cssnano-util-get-arguments" "4.0.0"
    "postcss" "7.0.27"
    "postcss-value-parser" "3.3.1"

"postcss-overflow-shorthand@2.0.0":
  "integrity" "sha512-aK0fHc9CBNx8jbzMYhshZcEv8LtYnBIRYQD5i7w/K/wS9c2+0NSR6B3OVMu5y0hBHYLcMGjfU+dmWYNKH0I85g=="
  "resolved" "https://registry.npmjs.org/postcss-overflow-shorthand/-/postcss-overflow-shorthand-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "postcss" "7.0.27"

"postcss-page-break@2.0.0":
  "integrity" "sha512-tkpTSrLpfLfD9HvgOlJuigLuk39wVTbbd8RKcy8/ugV2bNBUW3xU+AIqyxhDrQr1VUj1RmyJrBn1YWrqUm9zAQ=="
  "resolved" "https://registry.npmjs.org/postcss-page-break/-/postcss-page-break-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "postcss" "7.0.27"

"postcss-place@4.0.1":
  "integrity" "sha512-Zb6byCSLkgRKLODj/5mQugyuj9bvAAw9LqJJjgwz5cYryGeXfFZfSXoP1UfveccFmeq0b/2xxwcTEVScnqGxBg=="
  "resolved" "https://registry.npmjs.org/postcss-place/-/postcss-place-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "7.0.27"
    "postcss-values-parser" "2.0.1"

"postcss-preset-env@6.5.0":
  "integrity" "sha512-RdsIrYJd9p9AouQoJ8dFP5ksBJEIegA4q4WzJDih8nevz3cZyIP/q1Eaw3pTVpUAu3n7Y32YmvAW3X07mSRGkw=="
  "resolved" "https://registry.npmjs.org/postcss-preset-env/-/postcss-preset-env-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "autoprefixer" "9.7.4"
    "browserslist" "4.9.1"
    "caniuse-lite" "1.0.30001032"
    "css-blank-pseudo" "0.1.4"
    "css-has-pseudo" "0.10.0"
    "css-prefers-color-scheme" "3.1.1"
    "cssdb" "4.4.0"
    "postcss" "7.0.27"
    "postcss-attribute-case-insensitive" "4.0.2"
    "postcss-color-functional-notation" "2.0.1"
    "postcss-color-gray" "5.0.0"
    "postcss-color-hex-alpha" "5.0.3"
    "postcss-color-mod-function" "3.0.3"
    "postcss-color-rebeccapurple" "4.0.1"
    "postcss-custom-media" "7.0.8"
    "postcss-custom-properties" "8.0.11"
    "postcss-custom-selectors" "5.1.2"
    "postcss-dir-pseudo-class" "5.0.0"
    "postcss-double-position-gradients" "1.0.0"
    "postcss-env-function" "2.0.2"
    "postcss-focus-visible" "4.0.0"
    "postcss-focus-within" "3.0.0"
    "postcss-font-variant" "4.0.0"
    "postcss-gap-properties" "2.0.0"
    "postcss-image-set-function" "3.0.1"
    "postcss-initial" "3.0.2"
    "postcss-lab-function" "2.0.1"
    "postcss-logical" "3.0.0"
    "postcss-media-minmax" "4.0.0"
    "postcss-nesting" "7.0.1"
    "postcss-overflow-shorthand" "2.0.0"
    "postcss-page-break" "2.0.0"
    "postcss-place" "4.0.1"
    "postcss-pseudo-class-any-link" "6.0.0"
    "postcss-replace-overflow-wrap" "3.0.0"
    "postcss-selector-matches" "4.0.0"
    "postcss-selector-not" "4.0.0"

"postcss-pseudo-class-any-link@6.0.0":
  "integrity" "sha512-lgXW9sYJdLqtmw23otOzrtbDXofUdfYzNm4PIpNE322/swES3VU9XlXHeJS46zT2onFO7V1QFdD4Q9LiZj8mew=="
  "resolved" "https://registry.npmjs.org/postcss-pseudo-class-any-link/-/postcss-pseudo-class-any-link-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "postcss" "7.0.27"
    "postcss-selector-parser" "5.0.0"

"postcss-reduce-initial@4.0.3":
  "integrity" "sha512-gKWmR5aUulSjbzOfD9AlJiHCGH6AEVLaM0AV+aSioxUDd16qXP1PCh8d1/BGVvpdWn8k/HiK7n6TjeoXN1F7DA=="
  "resolved" "https://registry.npmjs.org/postcss-reduce-initial/-/postcss-reduce-initial-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "4.9.1"
    "caniuse-api" "3.0.0"
    "has" "1.0.3"
    "postcss" "7.0.27"

"postcss-reduce-transforms@4.0.2":
  "integrity" "sha512-EEVig1Q2QJ4ELpJXMZR8Vt5DQx8/mo+dGWSR7vWXqcob2gQLyQGsionYcGKATXvQzMPn6DSN1vTN7yFximdIAg=="
  "resolved" "https://registry.npmjs.org/postcss-reduce-transforms/-/postcss-reduce-transforms-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-match" "4.0.0"
    "has" "1.0.3"
    "postcss" "7.0.27"
    "postcss-value-parser" "3.3.1"

"postcss-replace-overflow-wrap@3.0.0":
  "integrity" "sha512-2T5hcEHArDT6X9+9dVSPQdo7QHzG4XKclFT8rU5TzJPDN7RIRTbO9c4drUISOVemLj03aezStHCR2AIcr8XLpw=="
  "resolved" "https://registry.npmjs.org/postcss-replace-overflow-wrap/-/postcss-replace-overflow-wrap-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "postcss" "7.0.27"

"postcss-safe-parser@4.0.1":
  "integrity" "sha512-xZsFA3uX8MO3yAda03QrG3/Eg1LN3EPfjjf07vke/46HERLZyHrTsQ9E1r1w1W//fWEhtYNndo2hQplN2cVpCQ=="
  "resolved" "https://registry.npmjs.org/postcss-safe-parser/-/postcss-safe-parser-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "7.0.27"

"postcss-selector-matches@4.0.0":
  "integrity" "sha512-LgsHwQR/EsRYSqlwdGzeaPKVT0Ml7LAT6E75T8W8xLJY62CE4S/l03BWIt3jT8Taq22kXP08s2SfTSzaraoPww=="
  "resolved" "https://registry.npmjs.org/postcss-selector-matches/-/postcss-selector-matches-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "balanced-match" "1.0.0"
    "postcss" "7.0.27"

"postcss-selector-not@4.0.0":
  "integrity" "sha512-W+bkBZRhqJaYN8XAnbbZPLWMvZD1wKTu0UxtFKdhtGjWYmxhkUneoeOhRJKdAE5V7ZTlnbHfCR+6bNwK9e1dTQ=="
  "resolved" "https://registry.npmjs.org/postcss-selector-not/-/postcss-selector-not-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "balanced-match" "1.0.0"
    "postcss" "7.0.27"

"postcss-selector-parser@3.1.2":
  "integrity" "sha512-h7fJ/5uWuRVyOtkO45pnt1Ih40CEleeyCHzipqAZO2e5H20g25Y48uYnFUiShvY4rZWNJ/Bib/KVPmanaCtOhA=="
  "resolved" "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "dot-prop" "5.2.0"
    "indexes-of" "1.0.1"
    "uniq" "1.0.1"

"postcss-selector-parser@5.0.0":
  "integrity" "sha512-w+zLE5Jhg6Liz8+rQOWEAwtwkyqpfnmsinXjXg6cY7YIONZZtgvE0v2O0uhQBs0peNomOJwWRKt6JBfTdTd3OQ=="
  "resolved" "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "cssesc" "2.0.0"
    "indexes-of" "1.0.1"
    "uniq" "1.0.1"

"postcss-selector-parser@6.0.2":
  "integrity" "sha512-36P2QR59jDTOAiIkqEprfJDsoNrvwFei3eCqKd1Y0tUsBimsq39BLp7RD+JWny3WgB1zGhJX8XVePwm9k4wdBg=="
  "resolved" "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "cssesc" "3.0.0"
    "indexes-of" "1.0.1"
    "uniq" "1.0.1"

"postcss-svgo@4.0.2":
  "integrity" "sha512-C6wyjo3VwFm0QgBy+Fu7gCYOkCmgmClghO+pjcxvrcBKtiKt0uCF+hvbMO1fyv5BMImRK90SMb+dwUnfbGd+jw=="
  "resolved" "https://registry.npmjs.org/postcss-svgo/-/postcss-svgo-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "is-svg" "3.0.0"
    "postcss" "7.0.27"
    "postcss-value-parser" "3.3.1"
    "svgo" "1.3.2"

"postcss-unique-selectors@4.0.1":
  "integrity" "sha512-+JanVaryLo9QwZjKrmJgkI4Fn8SBgRO6WXQBJi7KiAVPlmxikB5Jzc4EvXMT2H0/m0RjrVVm9rGNhZddm/8Spg=="
  "resolved" "https://registry.npmjs.org/postcss-unique-selectors/-/postcss-unique-selectors-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "alphanum-sort" "1.0.2"
    "postcss" "7.0.27"
    "uniqs" "2.0.0"

"postcss-value-parser@3.3.1":
  "integrity" "sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ=="
  "resolved" "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz"
  "version" "3.3.1"

"postcss-value-parser@4.0.3":
  "integrity" "sha512-N7h4pG+Nnu5BEIzyeaaIYWs0LI5XC40OrRh5L60z0QjFsqGWcHcbkBvpe1WYpcIS9yQ8sOi/vIPt1ejQCrMVrg=="
  "resolved" "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.0.3.tgz"
  "version" "4.0.3"

"postcss-values-parser@2.0.1":
  "integrity" "sha512-2tLuBsA6P4rYTNKCXYG/71C7j1pU6pK503suYOmn4xYrQIzW+opD+7FAFNuGSdZC/3Qfy334QbeMu7MEb8gOxg=="
  "resolved" "https://registry.npmjs.org/postcss-values-parser/-/postcss-values-parser-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "flatten" "1.0.3"
    "indexes-of" "1.0.1"
    "uniq" "1.0.1"

"postcss@6.0.23":
  "integrity" "sha512-soOk1h6J3VMTZtVeVpv15/Hpdl2cBLX3CAw4TAbkpTJiNPk9YP/zWcD1ND+xEtvyuuvKzbxliTOIyvkSeSJ6ag=="
  "resolved" "https://registry.npmjs.org/postcss/-/postcss-6.0.23.tgz"
  "version" "6.0.23"
  dependencies:
    "chalk" "2.4.2"
    "source-map" "0.6.1"
    "supports-color" "5.5.0"

"postcss@7.0.27":
  "integrity" "sha512-WuQETPMcW9Uf1/22HWUWP9lgsIC+KEHg2kozMflKjbeUtw9ujvFX6QmIfozaErDkmLWS9WEnEdEe6Uo9/BNTdQ=="
  "resolved" "https://registry.npmjs.org/postcss/-/postcss-7.0.27.tgz"
  "version" "7.0.27"
  dependencies:
    "chalk" "2.4.2"
    "source-map" "0.6.1"
    "supports-color" "6.1.0"

"prelude-ls@1.1.2":
  "integrity" "sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ="
  "resolved" "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.1.2.tgz"
  "version" "1.1.2"

"preserve@0.2.0":
  "integrity" "sha1-gV7R9uvGWSb4ZbMQwHE7yzMVzks="
  "resolved" "https://registry.npmjs.org/preserve/-/preserve-0.2.0.tgz"
  "version" "0.2.0"

"pretty-bytes@4.0.2":
  "integrity" "sha1-sr+C5zUNZcbDOqlaqlpPYyf2HNk="
  "resolved" "https://registry.npmjs.org/pretty-bytes/-/pretty-bytes-4.0.2.tgz"
  "version" "4.0.2"

"pretty-error@2.1.1":
  "integrity" "sha1-X0+HyPkeWuPzuoerTPXgOxoX8aM="
  "resolved" "https://registry.npmjs.org/pretty-error/-/pretty-error-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "renderkid" "2.0.3"
    "utila" "0.4.0"

"pretty-format@23.6.0":
  "integrity" "sha512-zf9NV1NSlDLDjycnwm6hpFATCGl/K1lt0R/GdkAK2O5LN/rwJoB+Mh93gGJjut4YbmecbfgLWVGSTCr0Ewvvbw=="
  "resolved" "https://registry.npmjs.org/pretty-format/-/pretty-format-23.6.0.tgz"
  "version" "23.6.0"
  dependencies:
    "ansi-regex" "3.0.0"
    "ansi-styles" "3.2.1"

"private@0.1.8":
  "integrity" "sha512-VvivMrbvd2nKkiG38qjULzlc+4Vx4wm/whI9pQD35YrARNnhxeiRktSOhSukRLFNlzg6Br/cJPet5J/u19r/mg=="
  "resolved" "https://registry.npmjs.org/private/-/private-0.1.8.tgz"
  "version" "0.1.8"

"process-nextick-args@2.0.1":
  "integrity" "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag=="
  "resolved" "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  "version" "2.0.1"

"process@0.11.10":
  "integrity" "sha1-czIwDoQBYb2j5podHZGn1LwW8YI="
  "resolved" "https://registry.npmjs.org/process/-/process-0.11.10.tgz"
  "version" "0.11.10"

"progress@2.0.3":
  "integrity" "sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA=="
  "resolved" "https://registry.npmjs.org/progress/-/progress-2.0.3.tgz"
  "version" "2.0.3"

"promise-inflight@1.0.1":
  "integrity" "sha1-mEcocL8igTL8vdhoEputEsPAKeM="
  "resolved" "https://registry.npmjs.org/promise-inflight/-/promise-inflight-1.0.1.tgz"
  "version" "1.0.1"

"promise@7.3.1":
  "integrity" "sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg=="
  "resolved" "https://registry.npmjs.org/promise/-/promise-7.3.1.tgz"
  "version" "7.3.1"
  dependencies:
    "asap" "2.0.6"

"promise@8.0.2":
  "integrity" "sha512-EIyzM39FpVOMbqgzEHhxdrEhtOSDOtjMZQ0M6iVfCE+kWNgCkAyOdnuCWqfmflylftfadU6FkiMgHZA2kUzwRw=="
  "resolved" "https://registry.npmjs.org/promise/-/promise-8.0.2.tgz"
  "version" "8.0.2"
  dependencies:
    "asap" "2.0.6"

"prompts@0.1.14":
  "integrity" "sha512-rxkyiE9YH6zAz/rZpywySLKkpaj0NMVyNw1qhsubdbjjSgcayjTShDreZGlFMcGSu5sab3bAKPfFk78PB90+8w=="
  "resolved" "https://registry.npmjs.org/prompts/-/prompts-0.1.14.tgz"
  "version" "0.1.14"
  dependencies:
    "kleur" "2.0.2"
    "sisteransi" "0.1.1"

"prop-types@^15.7.2", "prop-types@15.7.2":
  "integrity" "sha512-8QQikdH7//R2vurIJSutZ1smHYTcLpRWEOlHnzcWHmBYrOGUysKwSsrC89BCiFj3CbrfJ/nXFdJepOVrY1GCHQ=="
  "resolved" "https://registry.npmjs.org/prop-types/-/prop-types-15.7.2.tgz"
  "version" "15.7.2"
  dependencies:
    "loose-envify" "1.4.0"
    "object-assign" "4.1.1"
    "react-is" "16.13.0"

"proxy-addr@2.0.6":
  "integrity" "sha512-dh/frvCBVmSsDYzw6n926jv974gddhkFPfiN8hPOi30Wax25QZyZEGveluCgliBnqmuM+UJmBErbAUFIoDbjOw=="
  "resolved" "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.6.tgz"
  "version" "2.0.6"
  dependencies:
    "forwarded" "0.1.2"
    "ipaddr.js" "1.9.1"

"prr@1.0.1":
  "integrity" "sha1-0/wRS6BplaRexok/SEzrHXj19HY="
  "resolved" "https://registry.npmjs.org/prr/-/prr-1.0.1.tgz"
  "version" "1.0.1"

"psl@1.7.0":
  "integrity" "sha512-5NsSEDv8zY70ScRnOTn7bK7eanl2MvFrOrS/R6x+dBt5g1ghnj9Zv90kO8GwT8gxcu2ANyFprnFYB85IogIJOQ=="
  "resolved" "https://registry.npmjs.org/psl/-/psl-1.7.0.tgz"
  "version" "1.7.0"

"public-encrypt@4.0.3":
  "integrity" "sha512-zVpa8oKZSz5bTMTFClc1fQOnyyEzpl5ozpi1B5YcvBrdohMjH2rfsBtyXcuNuwjsDIXmBYlF2N5FlJYhR29t8Q=="
  "resolved" "https://registry.npmjs.org/public-encrypt/-/public-encrypt-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "bn.js" "4.11.8"
    "browserify-rsa" "4.0.1"
    "create-hash" "1.2.0"
    "parse-asn1" "5.1.5"
    "randombytes" "2.1.0"
    "safe-buffer" "5.2.0"

"pump@2.0.1":
  "integrity" "sha512-ruPMNRkN3MHP1cWJc9OWr+T/xDP0jhXYCLfJcBuX54hhfIBnaQmAUMfDcG4DM5UMWByBbJY69QSphm3jtDKIkA=="
  "resolved" "https://registry.npmjs.org/pump/-/pump-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "end-of-stream" "1.4.4"
    "once" "1.4.0"

"pump@3.0.0":
  "integrity" "sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww=="
  "resolved" "https://registry.npmjs.org/pump/-/pump-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "end-of-stream" "1.4.4"
    "once" "1.4.0"

"pumpify@1.5.1":
  "integrity" "sha512-oClZI37HvuUJJxSKKrC17bZ9Cu0ZYhEAGPsPUy9KlMUmv9dKX2o77RUmq7f3XjIxbwyGwYzbzQ1L2Ks8sIradQ=="
  "resolved" "https://registry.npmjs.org/pumpify/-/pumpify-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "duplexify" "3.7.1"
    "inherits" "2.0.4"
    "pump" "2.0.1"

"punycode@1.3.2":
  "integrity" "sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0="
  "resolved" "https://registry.npmjs.org/punycode/-/punycode-1.3.2.tgz"
  "version" "1.3.2"

"punycode@1.4.1":
  "integrity" "sha1-wNWmOycYgArY4esPpSachN1BhF4="
  "resolved" "https://registry.npmjs.org/punycode/-/punycode-1.4.1.tgz"
  "version" "1.4.1"

"punycode@2.1.1":
  "integrity" "sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A=="
  "resolved" "https://registry.npmjs.org/punycode/-/punycode-2.1.1.tgz"
  "version" "2.1.1"

"q@1.5.1":
  "integrity" "sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc="
  "resolved" "https://registry.npmjs.org/q/-/q-1.5.1.tgz"
  "version" "1.5.1"

"qs@6.5.2":
  "integrity" "sha512-N5ZAX4/LxJmF+7wN74pUD6qAh9/wnvdQcjq9TZjevvXzSUo7bfmw91saqMjzGS2xq91/odN2dW/WOl7qQHNDGA=="
  "resolved" "https://registry.npmjs.org/qs/-/qs-6.5.2.tgz"
  "version" "6.5.2"

"qs@6.7.0":
  "integrity" "sha512-VCdBRNFTX1fyE7Nb6FYoURo/SPe62QCaAyzJvUjwRaIsc+NePBEniHlvxFmmX56+HZphIGtV0XeCirBtpDrTyQ=="
  "resolved" "https://registry.npmjs.org/qs/-/qs-6.7.0.tgz"
  "version" "6.7.0"

"querystring-es3@0.2.1":
  "integrity" "sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM="
  "resolved" "https://registry.npmjs.org/querystring-es3/-/querystring-es3-0.2.1.tgz"
  "version" "0.2.1"

"querystring@0.2.0":
  "integrity" "sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA="
  "resolved" "https://registry.npmjs.org/querystring/-/querystring-0.2.0.tgz"
  "version" "0.2.0"

"querystringify@2.1.1":
  "integrity" "sha512-w7fLxIRCRT7U8Qu53jQnJyPkYZIaR4n5151KMfcJlO/A9397Wxb1amJvROTK6TOnp7PfoAmg/qXiNHI+08jRfA=="
  "resolved" "https://registry.npmjs.org/querystringify/-/querystringify-2.1.1.tgz"
  "version" "2.1.1"

"raf@3.4.1":
  "integrity" "sha512-Sq4CW4QhwOHE8ucn6J34MqtZCeWFP2aQSmrlroYgqAV1PjStIhJXxYuTgUIfkEk7zTLjmIjLmU5q+fbD1NnOJA=="
  "resolved" "https://registry.npmjs.org/raf/-/raf-3.4.1.tgz"
  "version" "3.4.1"
  dependencies:
    "performance-now" "2.1.0"

"randomatic@3.1.1":
  "integrity" "sha512-TuDE5KxZ0J461RVjrJZCJc+J+zCkTb1MbH9AQUq68sMhOMcy9jLcb3BrZKgp9q9Ncltdg4QVqWrH02W2EFFVYw=="
  "resolved" "https://registry.npmjs.org/randomatic/-/randomatic-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "is-number" "4.0.0"
    "kind-of" "6.0.3"
    "math-random" "1.0.4"

"randombytes@2.1.0":
  "integrity" "sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ=="
  "resolved" "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "safe-buffer" "5.2.0"

"randomfill@1.0.4":
  "integrity" "sha512-87lcbR8+MhcWcUiQ+9e+Rwx8MyR2P7qnt15ynUlbm3TU/fjbgz4GsvfSUDTemtCCtVCqb4ZcEFlyPNTh9bBTLw=="
  "resolved" "https://registry.npmjs.org/randomfill/-/randomfill-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "randombytes" "2.1.0"
    "safe-buffer" "5.2.0"

"range-parser@1.2.1":
  "integrity" "sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg=="
  "resolved" "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz"
  "version" "1.2.1"

"raw-body@2.4.0":
  "integrity" "sha512-4Oz8DUIwdvoa5qMJelxipzi/iJIi40O5cGV1wNYp5hvZP8ZN0T+jiNkL0QepXs+EsQ9XJ8ipEDoiH70ySUJP3Q=="
  "resolved" "https://registry.npmjs.org/raw-body/-/raw-body-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "bytes" "3.1.0"
    "http-errors" "1.7.2"
    "iconv-lite" "0.4.24"
    "unpipe" "1.0.0"

"rc-align@2.4.5":
  "integrity" "sha512-nv9wYUYdfyfK+qskThf4BQUSIadeI/dCsfaMZfNEoxm9HwOIioQ+LyqmMK6jWHAZQgOzMLaqawhuBXlF63vgjw=="
  "resolved" "https://registry.npmjs.org/rc-align/-/rc-align-2.4.5.tgz"
  "version" "2.4.5"
  dependencies:
    "babel-runtime" "6.26.0"
    "dom-align" "1.10.4"
    "prop-types" "15.7.2"
    "rc-util" "4.20.0"

"rc-animate@2.10.3":
  "integrity" "sha512-A9qQ5Y8BLlM7EhuCO3fWb/dChndlbWtY/P5QvPqBU7h4r5Q2QsvsbpTGgdYZATRDZbTRnJXXfVk9UtlyS7MBLg=="
  "resolved" "https://registry.npmjs.org/rc-animate/-/rc-animate-2.10.3.tgz"
  "version" "2.10.3"
  dependencies:
    "babel-runtime" "6.26.0"
    "classnames" "2.2.6"
    "css-animation" "1.6.1"
    "prop-types" "15.7.2"
    "raf" "3.4.1"
    "rc-util" "4.20.0"
    "react-lifecycles-compat" "3.0.4"

"rc-animate@3.0.0-rc.6":
  "integrity" "sha512-oBLPpiT6Q4t6YvD/pkLcmofBP1p01TX0Otse8Q4+Mxt8J+VSDflLZGIgf62EwkvRwsQUkLPjZVFBsldnPKLzjg=="
  "resolved" "https://registry.npmjs.org/rc-animate/-/rc-animate-3.0.0-rc.6.tgz"
  "version" "3.0.0-rc.6"
  dependencies:
    "babel-runtime" "6.26.0"
    "classnames" "2.2.6"
    "component-classes" "1.2.6"
    "fbjs" "0.8.17"
    "prop-types" "15.7.2"
    "raf" "3.4.1"
    "rc-util" "4.20.0"
    "react-lifecycles-compat" "3.0.4"

"rc-calendar@9.15.9":
  "integrity" "sha512-XOPzJlXYmLFIcwalXmzxKZrrAMD6dEPLRVoHG3wbBpErqjE8ugnXVjm9yXgtQh3Ho3Imhmt+KO0WGLv5T4WuAA=="
  "resolved" "https://registry.npmjs.org/rc-calendar/-/rc-calendar-9.15.9.tgz"
  "version" "9.15.9"
  dependencies:
    "babel-runtime" "6.26.0"
    "classnames" "2.2.6"
    "moment" "2.24.0"
    "prop-types" "15.7.2"
    "rc-trigger" "2.6.5"
    "rc-util" "4.20.0"
    "react-lifecycles-compat" "3.0.4"

"rc-cascader@0.17.5":
  "integrity" "sha512-WYMVcxU0+Lj+xLr4YYH0+yXODumvNXDcVEs5i7L1mtpWwYkubPV/zbQpn+jGKFCIW/hOhjkU4J1db8/P/UKE7A=="
  "resolved" "https://registry.npmjs.org/rc-cascader/-/rc-cascader-0.17.5.tgz"
  "version" "0.17.5"
  dependencies:
    "array-tree-filter" "2.1.0"
    "prop-types" "15.7.2"
    "rc-trigger" "2.6.5"
    "rc-util" "4.20.0"
    "react-lifecycles-compat" "3.0.4"
    "shallow-equal" "1.2.1"
    "warning" "4.0.3"

"rc-checkbox@2.1.8":
  "integrity" "sha512-6qOgh0/by0nVNASx6LZnhRTy17Etcgav+IrI7kL9V9kcDZ/g7K14JFlqrtJ3NjDq/Kyn+BPI1st1XvbkhfaJeg=="
  "resolved" "https://registry.npmjs.org/rc-checkbox/-/rc-checkbox-2.1.8.tgz"
  "version" "2.1.8"
  dependencies:
    "babel-runtime" "6.26.0"
    "classnames" "2.2.6"
    "prop-types" "15.7.2"
    "react-lifecycles-compat" "3.0.4"

"rc-collapse@1.11.8":
  "integrity" "sha512-8EhfPyScTYljkbRuIoHniSwZagD5UPpZ3CToYgoNYWC85L2qCbPYF7+OaC713FOrIkp6NbfNqXsITNxmDAmxog=="
  "resolved" "https://registry.npmjs.org/rc-collapse/-/rc-collapse-1.11.8.tgz"
  "version" "1.11.8"
  dependencies:
    "classnames" "2.2.6"
    "css-animation" "1.6.1"
    "prop-types" "15.7.2"
    "rc-animate" "2.10.3"
    "react-is" "16.13.0"
    "react-lifecycles-compat" "3.0.4"
    "shallowequal" "1.1.0"

"rc-dialog@7.6.0":
  "integrity" "sha512-N48vBPW8I53WycFHI4KXhuTUkB4mx+hixq1a9tcFMLoE7EhkAjbHvs0vGg+Bh/uFg5V00jmZBgQOIEbhcNal/A=="
  "resolved" "https://registry.npmjs.org/rc-dialog/-/rc-dialog-7.6.0.tgz"
  "version" "7.6.0"
  dependencies:
    "babel-runtime" "6.26.0"
    "rc-animate" "2.10.3"
    "rc-util" "4.20.0"

"rc-drawer@3.1.3":
  "integrity" "sha512-2z+RdxmzXyZde/1OhVMfDR1e/GBswFeWSZ7FS3Fdd0qhgVdpV1wSzILzzxRaT481ItB5hOV+e8pZT07vdJE8kg=="
  "resolved" "https://registry.npmjs.org/rc-drawer/-/rc-drawer-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "classnames" "2.2.6"
    "rc-util" "4.20.0"
    "react-lifecycles-compat" "3.0.4"

"rc-dropdown@2.4.1":
  "integrity" "sha512-p0XYn0wrOpAZ2fUGE6YJ6U8JBNc5ASijznZ6dkojdaEfQJAeZtV9KMEewhxkVlxGSbbdXe10ptjBlTEW9vEwEg=="
  "resolved" "https://registry.npmjs.org/rc-dropdown/-/rc-dropdown-2.4.1.tgz"
  "version" "2.4.1"
  dependencies:
    "babel-runtime" "6.26.0"
    "classnames" "2.2.6"
    "prop-types" "15.7.2"
    "rc-trigger" "2.6.5"
    "react-lifecycles-compat" "3.0.4"

"rc-editor-core@0.8.10":
  "integrity" "sha512-T3aHpeMCIYA1sdAI7ynHHjXy5fqp83uPlD68ovZ0oClTSc3tbHmyCxXlA+Ti4YgmcpCYv7avF6a+TIbAka53kw=="
  "resolved" "https://registry.npmjs.org/rc-editor-core/-/rc-editor-core-0.8.10.tgz"
  "version" "0.8.10"
  dependencies:
    "babel-runtime" "6.26.0"
    "classnames" "2.2.6"
    "draft-js" "0.10.5"
    "immutable" "3.7.6"
    "lodash" "4.17.15"
    "prop-types" "15.7.2"
    "setimmediate" "1.0.5"

"rc-editor-mention@1.1.13":
  "integrity" "sha512-3AOmGir91Fi2ogfRRaXLtqlNuIwQpvla7oUnGHS1+3eo7b+fUp5IlKcagqtwUBB5oDNofoySXkLBxzWvSYNp/Q=="
  "resolved" "https://registry.npmjs.org/rc-editor-mention/-/rc-editor-mention-1.1.13.tgz"
  "version" "1.1.13"
  dependencies:
    "babel-runtime" "6.26.0"
    "classnames" "2.2.6"
    "dom-scroll-into-view" "1.2.1"
    "draft-js" "0.10.5"
    "immutable" "3.7.6"
    "prop-types" "15.7.2"
    "rc-animate" "2.10.3"
    "rc-editor-core" "0.8.10"

"rc-form@2.4.11":
  "integrity" "sha512-8BL+FNlFLTOY/A5X6tU35GQJLSIpsmqpwn/tFAYQTczXc4dMJ33ggtH248Cum8+LS0jLTsJKG2L4Qp+1CkY+sA=="
  "resolved" "https://registry.npmjs.org/rc-form/-/rc-form-2.4.11.tgz"
  "version" "2.4.11"
  dependencies:
    "async-validator" "1.11.5"
    "babel-runtime" "6.26.0"
    "create-react-class" "15.6.3"
    "dom-scroll-into-view" "1.2.1"
    "hoist-non-react-statics" "3.3.2"
    "lodash" "4.17.15"
    "rc-util" "4.20.0"
    "warning" "4.0.3"

"rc-hammerjs@0.6.9":
  "integrity" "sha512-4llgWO3RgLyVbEqUdGsDfzUDqklRlQW5VEhE3x35IvhV+w//VPRG34SBavK3D2mD/UaLKaohgU41V4agiftC8g=="
  "resolved" "https://registry.npmjs.org/rc-hammerjs/-/rc-hammerjs-0.6.9.tgz"
  "version" "0.6.9"
  dependencies:
    "babel-runtime" "6.26.0"
    "hammerjs" "2.0.8"
    "prop-types" "15.7.2"

"rc-input-number@4.5.6":
  "integrity" "sha512-AXbL4gtQ1mSQnu6v/JtMv3UbGRCzLvQznmf0a7U/SAtZ8+dCEAqD4JpJhkjv73Wog53eRYhw4l7ApdXflc9ymg=="
  "resolved" "https://registry.npmjs.org/rc-input-number/-/rc-input-number-4.5.6.tgz"
  "version" "4.5.6"
  dependencies:
    "babel-runtime" "6.26.0"
    "classnames" "2.2.6"
    "prop-types" "15.7.2"
    "rc-util" "4.20.0"
    "rmc-feedback" "2.0.0"

"rc-mentions@0.4.2":
  "integrity" "sha512-DTZurQzacLXOfVuiHydGzqkq7cFMHXF18l2jZ9PhWUn2cqvOSY3W4osN0Pq29AOMOBpcxdZCzgc7Lb0r/bgkDw=="
  "resolved" "https://registry.npmjs.org/rc-mentions/-/rc-mentions-0.4.2.tgz"
  "version" "0.4.2"
  dependencies:
    "@ant-design/create-react-context" "0.2.5"
    "classnames" "2.2.6"
    "rc-menu" "7.5.5"
    "rc-trigger" "2.6.5"
    "rc-util" "4.20.0"
    "react-lifecycles-compat" "3.0.4"

"rc-menu@7.5.5":
  "integrity" "sha512-4YJXJgrpUGEA1rMftXN7bDhrV5rPB8oBJoHqT+GVXtIWCanfQxEnM3fmhHQhatL59JoAFMZhJaNzhJIk4FUWCQ=="
  "resolved" "https://registry.npmjs.org/rc-menu/-/rc-menu-7.5.5.tgz"
  "version" "7.5.5"
  dependencies:
    "classnames" "2.2.6"
    "dom-scroll-into-view" "1.2.1"
    "mini-store" "2.0.0"
    "mutationobserver-shim" "0.3.3"
    "rc-animate" "2.10.3"
    "rc-trigger" "2.6.5"
    "rc-util" "4.20.0"
    "resize-observer-polyfill" "1.5.1"
    "shallowequal" "1.1.0"

"rc-notification@3.3.1":
  "integrity" "sha512-U5+f4BmBVfMSf3OHSLyRagsJ74yKwlrQAtbbL5ijoA0F2C60BufwnOcHG18tVprd7iaIjzZt1TKMmQSYSvgrig=="
  "resolved" "https://registry.npmjs.org/rc-notification/-/rc-notification-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "babel-runtime" "6.26.0"
    "classnames" "2.2.6"
    "prop-types" "15.7.2"
    "rc-animate" "2.10.3"
    "rc-util" "4.20.0"

"rc-pagination@1.20.14":
  "integrity" "sha512-sNKwbFrxiqATqcIIShfrFs8BT03n4UUwTAMYae+JhHTmILQmXdvimEnZbVuWcno6G02DAJcLrFpmkn1h2tmEJw=="
  "resolved" "https://registry.npmjs.org/rc-pagination/-/rc-pagination-1.20.14.tgz"
  "version" "1.20.14"
  dependencies:
    "babel-runtime" "6.26.0"
    "classnames" "2.2.6"
    "prop-types" "15.7.2"
    "react-lifecycles-compat" "3.0.4"

"rc-progress@2.5.2":
  "integrity" "sha512-ajI+MJkbBz9zYDuE9GQsY5gsyqPF7HFioZEDZ9Fmc+ebNZoiSeSJsTJImPFCg0dW/5WiRGUy2F69SX1aPtSJgA=="
  "resolved" "https://registry.npmjs.org/rc-progress/-/rc-progress-2.5.2.tgz"
  "version" "2.5.2"
  dependencies:
    "babel-runtime" "6.26.0"
    "prop-types" "15.7.2"

"rc-rate@2.5.1":
  "integrity" "sha512-3iJkNJT8xlHklPCdeZtUZmJmRVUbr6AHRlfSsztfYTXVlHrv2TcPn3XkHsH+12j812WVB7gvilS2j3+ffjUHXg=="
  "resolved" "https://registry.npmjs.org/rc-rate/-/rc-rate-2.5.1.tgz"
  "version" "2.5.1"
  dependencies:
    "classnames" "2.2.6"
    "prop-types" "15.7.2"
    "rc-util" "4.20.0"
    "react-lifecycles-compat" "3.0.4"

"rc-resize-observer@0.1.3":
  "integrity" "sha512-uzOQEwx83xdQSFOkOAM7x7GHIQKYnrDV4dWxtCxyG1BS1pkfJ4EvDeMfsvAJHSYkQXVBu+sgRHGbRtLG3qiuUg=="
  "resolved" "https://registry.npmjs.org/rc-resize-observer/-/rc-resize-observer-0.1.3.tgz"
  "version" "0.1.3"
  dependencies:
    "classnames" "2.2.6"
    "rc-util" "4.20.0"
    "resize-observer-polyfill" "1.5.1"

"rc-select@9.2.3":
  "integrity" "sha512-WhswxOMWiNnkXRbxyrj0kiIvyCfo/BaRPaYbsDetSIAU2yEDwKHF798blCP5u86KLOBKBvtxWLFCkSsQw1so5w=="
  "resolved" "https://registry.npmjs.org/rc-select/-/rc-select-9.2.3.tgz"
  "version" "9.2.3"
  dependencies:
    "babel-runtime" "6.26.0"
    "classnames" "2.2.6"
    "component-classes" "1.2.6"
    "dom-scroll-into-view" "1.2.1"
    "prop-types" "15.7.2"
    "raf" "3.4.1"
    "rc-animate" "2.10.3"
    "rc-menu" "7.5.5"
    "rc-trigger" "2.6.5"
    "rc-util" "4.20.0"
    "react-lifecycles-compat" "3.0.4"
    "warning" "4.0.3"

"rc-slider@8.7.1":
  "integrity" "sha512-WMT5mRFUEcrLWwTxsyS8jYmlaMsTVCZIGENLikHsNv+tE8ThU2lCoPfi/xFNUfJFNFSBFP3MwPez9ZsJmNp13g=="
  "resolved" "https://registry.npmjs.org/rc-slider/-/rc-slider-8.7.1.tgz"
  "version" "8.7.1"
  dependencies:
    "babel-runtime" "6.26.0"
    "classnames" "2.2.6"
    "prop-types" "15.7.2"
    "rc-tooltip" "3.7.3"
    "rc-util" "4.20.0"
    "react-lifecycles-compat" "3.0.4"
    "shallowequal" "1.1.0"
    "warning" "4.0.3"

"rc-steps@3.5.0":
  "integrity" "sha512-2Vkkrpa7PZbg7qPsqTNzVDov4u78cmxofjjnIHiGB9+9rqKS8oTLPzbW2uiWDr3Lk+yGwh8rbpGO1E6VAgBCOg=="
  "resolved" "https://registry.npmjs.org/rc-steps/-/rc-steps-3.5.0.tgz"
  "version" "3.5.0"
  dependencies:
    "babel-runtime" "6.26.0"
    "classnames" "2.2.6"
    "lodash" "4.17.15"
    "prop-types" "15.7.2"

"rc-switch@1.9.0":
  "integrity" "sha512-Isas+egaK6qSk64jaEw4GgPStY4umYDbT7ZY93bZF1Af+b/JEsKsJdNOU2qG3WI0Z6tXo2DDq0kJCv8Yhu0zww=="
  "resolved" "https://registry.npmjs.org/rc-switch/-/rc-switch-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "classnames" "2.2.6"
    "prop-types" "15.7.2"
    "react-lifecycles-compat" "3.0.4"

"rc-table@6.10.13":
  "integrity" "sha512-Pqb83gVWWdmfeE9tR+7vdi9dWO963Vq2n+nz5oE8LOpCkF/1UVLKcw7Som5FHpilOjd44c+wLPIL4H8Gx2i6qg=="
  "resolved" "https://registry.npmjs.org/rc-table/-/rc-table-6.10.13.tgz"
  "version" "6.10.13"
  dependencies:
    "classnames" "2.2.6"
    "component-classes" "1.2.6"
    "lodash" "4.17.15"
    "mini-store" "2.0.0"
    "prop-types" "15.7.2"
    "rc-util" "4.20.0"
    "react-lifecycles-compat" "3.0.4"
    "shallowequal" "1.1.0"

"rc-tabs@9.7.0":
  "integrity" "sha512-kvmgp8/MfLzFZ06hWHignqomFQ5nF7BqKr5O1FfhE4VKsGrep52YSF/1MvS5oe0NPcI9XGNS2p751C5v6cYDpQ=="
  "resolved" "https://registry.npmjs.org/rc-tabs/-/rc-tabs-9.7.0.tgz"
  "version" "9.7.0"
  dependencies:
    "@ant-design/create-react-context" "0.2.5"
    "babel-runtime" "6.26.0"
    "classnames" "2.2.6"
    "lodash" "4.17.15"
    "prop-types" "15.7.2"
    "raf" "3.4.1"
    "rc-hammerjs" "0.6.9"
    "rc-util" "4.20.0"
    "react-lifecycles-compat" "3.0.4"
    "resize-observer-polyfill" "1.5.1"
    "warning" "4.0.3"

"rc-time-picker@3.7.3":
  "integrity" "sha512-Lv1Mvzp9fRXhXEnRLO4nW6GLNxUkfAZ3RsiIBsWjGjXXvMNjdr4BX/ayElHAFK0DoJqOhm7c5tjmIYpEOwcUXg=="
  "resolved" "https://registry.npmjs.org/rc-time-picker/-/rc-time-picker-3.7.3.tgz"
  "version" "3.7.3"
  dependencies:
    "classnames" "2.2.6"
    "moment" "2.24.0"
    "prop-types" "15.7.2"
    "raf" "3.4.1"
    "rc-trigger" "2.6.5"
    "react-lifecycles-compat" "3.0.4"

"rc-tooltip@3.7.3":
  "integrity" "sha512-dE2ibukxxkrde7wH9W8ozHKUO4aQnPZ6qBHtrTH9LoO836PjDdiaWO73fgPB05VfJs9FbZdmGPVEbXCeOP99Ww=="
  "resolved" "https://registry.npmjs.org/rc-tooltip/-/rc-tooltip-3.7.3.tgz"
  "version" "3.7.3"
  dependencies:
    "babel-runtime" "6.26.0"
    "prop-types" "15.7.2"
    "rc-trigger" "2.6.5"

"rc-tree-select@2.9.4":
  "integrity" "sha512-0HQkXAN4XbfBW20CZYh3G+V+VMrjX42XRtDCpyv6PDUm5vikC0Ob682ZBCVS97Ww2a5Hf6Ajmu0ahWEdIEpwhg=="
  "resolved" "https://registry.npmjs.org/rc-tree-select/-/rc-tree-select-2.9.4.tgz"
  "version" "2.9.4"
  dependencies:
    "classnames" "2.2.6"
    "dom-scroll-into-view" "1.2.1"
    "prop-types" "15.7.2"
    "raf" "3.4.1"
    "rc-animate" "2.10.3"
    "rc-tree" "2.1.3"
    "rc-trigger" "3.0.0"
    "rc-util" "4.20.0"
    "react-lifecycles-compat" "3.0.4"
    "shallowequal" "1.1.0"
    "warning" "4.0.3"

"rc-tree@2.1.3":
  "integrity" "sha512-COvV65spQ6omrHBUhHRKqKNL5+ddXjlS+qWZchaL9FFuQNvjM5pjp9RnmMWK4fJJ5kBhhpLneh6wh9Vh3kSMXQ=="
  "resolved" "https://registry.npmjs.org/rc-tree/-/rc-tree-2.1.3.tgz"
  "version" "2.1.3"
  dependencies:
    "@ant-design/create-react-context" "0.2.5"
    "classnames" "2.2.6"
    "prop-types" "15.7.2"
    "rc-animate" "2.10.3"
    "rc-util" "4.20.0"
    "react-lifecycles-compat" "3.0.4"
    "warning" "4.0.3"

"rc-trigger@2.6.5":
  "integrity" "sha512-m6Cts9hLeZWsTvWnuMm7oElhf+03GOjOLfTuU0QmdB9ZrW7jR2IpI5rpNM7i9MvAAlMAmTx5Zr7g3uu/aMvZAw=="
  "resolved" "https://registry.npmjs.org/rc-trigger/-/rc-trigger-2.6.5.tgz"
  "version" "2.6.5"
  dependencies:
    "babel-runtime" "6.26.0"
    "classnames" "2.2.6"
    "prop-types" "15.7.2"
    "rc-align" "2.4.5"
    "rc-animate" "2.10.3"
    "rc-util" "4.20.0"
    "react-lifecycles-compat" "3.0.4"

"rc-trigger@3.0.0":
  "integrity" "sha512-hQxbbJpo23E2QnYczfq3Ec5J5tVl2mUDhkqxrEsQAqk16HfADQg+iKNWzEYXyERSncdxfnzYuaBgy764mNRzTA=="
  "resolved" "https://registry.npmjs.org/rc-trigger/-/rc-trigger-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "babel-runtime" "6.26.0"
    "classnames" "2.2.6"
    "prop-types" "15.7.2"
    "raf" "3.4.1"
    "rc-align" "2.4.5"
    "rc-animate" "3.0.0-rc.6"
    "rc-util" "4.20.0"

"rc-upload@2.9.4":
  "integrity" "sha512-WXt0HGxXyzLrPV6iec/96Rbl/6dyrAW8pKuY6wwD7yFYwfU5bjgKjv7vC8KNMJ6wzitFrZjnoiogNL3dF9dj3Q=="
  "resolved" "https://registry.npmjs.org/rc-upload/-/rc-upload-2.9.4.tgz"
  "version" "2.9.4"
  dependencies:
    "babel-runtime" "6.26.0"
    "classnames" "2.2.6"
    "prop-types" "15.7.2"
    "warning" "4.0.3"

"rc-util@4.20.0":
  "integrity" "sha512-rUqk4RqtDe4OfTsSk2GpbvIQNVtfmmebw4Rn7ZAA1TO1zLMLfyOF78ZyrEKqs8RDwoE3S1aXp0AX0ogLfSxXrQ=="
  "resolved" "https://registry.npmjs.org/rc-util/-/rc-util-4.20.0.tgz"
  "version" "4.20.0"
  dependencies:
    "add-dom-event-listener" "1.1.0"
    "babel-runtime" "6.26.0"
    "prop-types" "15.7.2"
    "react-is" "16.13.0"
    "react-lifecycles-compat" "3.0.4"
    "shallowequal" "1.1.0"

"react-app-polyfill@0.2.2":
  "integrity" "sha512-mAYn96B/nB6kWG87Ry70F4D4rsycU43VYTj3ZCbKP+SLJXwC0x6YCbwcICh3uW8/C9s1VgP197yx+w7SCWeDdQ=="
  "resolved" "https://registry.npmjs.org/react-app-polyfill/-/react-app-polyfill-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "core-js" "2.6.4"
    "object-assign" "4.1.1"
    "promise" "8.0.2"
    "raf" "3.4.1"
    "whatwg-fetch" "3.0.0"

"react-app-rewired@^2.1.0":
  "integrity" "sha512-Gr8KfCeL9/PTQs8Vvxc7v8wQ9vCFMnYPhcAkrMlzkLiMFXS+BgSwm11MoERjZm7dpA2WjTi+Pvbu/w7rujAV+A=="
  "resolved" "https://registry.npmjs.org/react-app-rewired/-/react-app-rewired-2.1.5.tgz"
  "version" "2.1.5"
  dependencies:
    "semver" "5.7.1"

"react-dev-utils@7.0.5":
  "integrity" "sha512-zJnqqb0x6gd63E3xoz5pXAxBPNaW75Hyz7GgQp0qPhMroBCRQtRvG67AoTZZY1z4yCYVJQZAfQJFdnea0Ujbug=="
  "resolved" "https://registry.npmjs.org/react-dev-utils/-/react-dev-utils-7.0.5.tgz"
  "version" "7.0.5"
  dependencies:
    "@babel/code-frame" "7.0.0"
    "address" "1.0.3"
    "browserslist" "4.4.1"
    "chalk" "2.4.2"
    "cross-spawn" "6.0.5"
    "detect-port-alt" "1.1.6"
    "escape-string-regexp" "1.0.5"
    "filesize" "3.6.1"
    "find-up" "3.0.0"
    "global-modules" "2.0.0"
    "globby" "8.0.2"
    "gzip-size" "5.0.0"
    "immer" "1.10.0"
    "inquirer" "6.2.1"
    "is-root" "2.0.0"
    "loader-utils" "1.2.3"
    "opn" "5.4.0"
    "pkg-up" "2.0.0"
    "react-error-overlay" "5.1.6"
    "recursive-readdir" "2.2.2"
    "shell-quote" "1.6.1"
    "sockjs-client" "1.3.0"
    "strip-ansi" "5.0.0"
    "text-table" "0.2.0"

"react-dom@^16.8.2":
  "integrity" "sha512-y09d2c4cG220DzdlFkPTnVvGTszVvNpC73v+AaLGLHbkpy3SSgvYq8x0rNwPJ/Rk/CicTNgk0hbHNw1gMEZAXg=="
  "resolved" "https://registry.npmjs.org/react-dom/-/react-dom-16.13.0.tgz"
  "version" "16.13.0"
  dependencies:
    "loose-envify" "1.4.0"
    "object-assign" "4.1.1"
    "prop-types" "15.7.2"
    "scheduler" "0.19.0"

"react-error-overlay@5.1.6":
  "integrity" "sha512-X1Y+0jR47ImDVr54Ab6V9eGk0Hnu7fVWGeHQSOXHf/C2pF9c6uy3gef8QUeuUiWlNb0i08InPSE5a/KJzNzw1Q=="
  "resolved" "https://registry.npmjs.org/react-error-overlay/-/react-error-overlay-5.1.6.tgz"
  "version" "5.1.6"

"react-is@^16.13.1":
  "integrity" "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ=="
  "resolved" "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz"
  "version" "16.13.1"

"react-is@16.13.0":
  "integrity" "sha512-GFMtL0vHkiBv9HluwNZTggSn/sCyEt9n02aM0dSAjGGyqyNlAyftYm4phPxdvCigG15JreC5biwxCgTAJZ7yAA=="
  "resolved" "https://registry.npmjs.org/react-is/-/react-is-16.13.0.tgz"
  "version" "16.13.0"

"react-lazy-load@3.0.13":
  "integrity" "sha1-OwqS0zbUPT8Nc8vm81sXBQsIuCQ="
  "resolved" "https://registry.npmjs.org/react-lazy-load/-/react-lazy-load-3.0.13.tgz"
  "version" "3.0.13"
  dependencies:
    "eventlistener" "0.0.1"
    "lodash.debounce" "4.0.8"
    "lodash.throttle" "4.1.1"
    "prop-types" "15.7.2"

"react-lifecycles-compat@3.0.4":
  "integrity" "sha512-fBASbA6LnOU9dOU2eW7aQ8xmYBSXUIWr+UmF9b1efZBazGNO+rcXT/icdKnYm2pTwcRylVUYwW7H1PHfLekVzA=="
  "resolved" "https://registry.npmjs.org/react-lifecycles-compat/-/react-lifecycles-compat-3.0.4.tgz"
  "version" "3.0.4"

"react-redux@^7.2.0":
  "integrity" "sha512-hOQ5eOSkEJEXdpIKbnRyl04LhaWabkDPV+Ix97wqQX3T3d2NQ8DUblNXXtNMavc7DpswyQM6xfaN4HQDKNY2JA=="
  "resolved" "https://registry.npmjs.org/react-redux/-/react-redux-7.2.4.tgz"
  "version" "7.2.4"
  dependencies:
    "@babel/runtime" "^7.12.1"
    "@types/react-redux" "^7.1.16"
    "hoist-non-react-statics" "^3.3.2"
    "loose-envify" "^1.4.0"
    "prop-types" "^15.7.2"
    "react-is" "^16.13.1"

"react-router-dom@^4.3.1":
  "integrity" "sha512-c/MlywfxDdCp7EnB7YfPMOfMD3tOtIjrQlj/CKfNMBxdmpJP8xcz5P/UAFn3JbnQCNUxsHyVVqllF9LhgVyFCA=="
  "resolved" "https://registry.npmjs.org/react-router-dom/-/react-router-dom-4.3.1.tgz"
  "version" "4.3.1"
  dependencies:
    "history" "4.10.1"
    "invariant" "2.2.4"
    "loose-envify" "1.4.0"
    "prop-types" "15.7.2"
    "react-router" "4.3.1"
    "warning" "4.0.3"

"react-router@^4.3.1", "react-router@4.3.1":
  "integrity" "sha512-yrvL8AogDh2X42Dt9iknk4wF4V8bWREPirFfS9gLU1huk6qK41sg7Z/1S81jjTrGHxa3B8R3J6xIkDAA6CVarg=="
  "resolved" "https://registry.npmjs.org/react-router/-/react-router-4.3.1.tgz"
  "version" "4.3.1"
  dependencies:
    "history" "4.10.1"
    "hoist-non-react-statics" "2.5.5"
    "invariant" "2.2.4"
    "loose-envify" "1.4.0"
    "path-to-regexp" "1.8.0"
    "prop-types" "15.7.2"
    "warning" "4.0.3"

"react-scripts@2.1.5":
  "integrity" "sha512-NaDKSxBLlU/jmO3TjTChz0PnnGToaQn0R/y4l4/Uk1vZdrIUsKCGdOWV1z0SJoRg7bFgNKxh55yS0qMr8rFwCg=="
  "resolved" "https://registry.npmjs.org/react-scripts/-/react-scripts-2.1.5.tgz"
  "version" "2.1.5"
  dependencies:
    "@babel/core" "7.2.2"
    "@svgr/webpack" "4.1.0"
    "babel-core" "7.0.0-bridge.0"
    "babel-eslint" "9.0.0"
    "babel-jest" "23.6.0"
    "babel-loader" "8.0.5"
    "babel-plugin-named-asset-import" "0.3.6"
    "babel-preset-react-app" "7.0.2"
    "bfj" "6.1.1"
    "case-sensitive-paths-webpack-plugin" "2.2.0"
    "css-loader" "1.0.0"
    "dotenv" "6.0.0"
    "dotenv-expand" "4.2.0"
    "eslint" "5.12.0"
    "eslint-config-react-app" "3.0.8"
    "eslint-loader" "2.1.1"
    "eslint-plugin-flowtype" "2.50.1"
    "eslint-plugin-import" "2.14.0"
    "eslint-plugin-jsx-a11y" "6.1.2"
    "eslint-plugin-react" "7.12.4"
    "file-loader" "2.0.0"
    "fork-ts-checker-webpack-plugin-alt" "0.4.14"
    "fs-extra" "7.0.1"
    "html-webpack-plugin" "4.0.0-alpha.2"
    "identity-obj-proxy" "3.0.0"
    "jest" "23.6.0"
    "jest-pnp-resolver" "1.0.2"
    "jest-resolve" "23.6.0"
    "jest-watch-typeahead" "0.2.1"
    "mini-css-extract-plugin" "0.5.0"
    "optimize-css-assets-webpack-plugin" "5.0.1"
    "pnp-webpack-plugin" "1.2.1"
    "postcss-flexbugs-fixes" "4.1.0"
    "postcss-loader" "3.0.0"
    "postcss-preset-env" "6.5.0"
    "postcss-safe-parser" "4.0.1"
    "react-app-polyfill" "0.2.2"
    "react-dev-utils" "7.0.5"
    "resolve" "1.10.0"
    "sass-loader" "7.1.0"
    "style-loader" "0.23.1"
    "terser-webpack-plugin" "1.2.2"
    "url-loader" "1.1.2"
    "webpack" "4.28.3"
    "webpack-dev-server" "3.1.14"
    "webpack-manifest-plugin" "2.0.4"
    "workbox-webpack-plugin" "3.6.3"

"react-slick@0.25.2":
  "integrity" "sha512-8MNH/NFX/R7zF6W/w+FS5VXNyDusF+XDW1OU0SzODEU7wqYB+ZTGAiNJ++zVNAVqCAHdyCybScaUB+FCZOmBBw=="
  "resolved" "https://registry.npmjs.org/react-slick/-/react-slick-0.25.2.tgz"
  "version" "0.25.2"
  dependencies:
    "classnames" "2.2.6"
    "enquire.js" "2.1.6"
    "json2mq" "0.2.0"
    "lodash.debounce" "4.0.8"
    "resize-observer-polyfill" "1.5.1"

"react@^16.8.2", "react@^16.8.3 || ^17", "react@>=16.8.0":
  "integrity" "sha512-TSavZz2iSLkq5/oiE7gnFzmURKZMltmi193rm5HEoUDAXpzT9Kzw6oNZnGoai/4+fUnm7FqS5dwgUL34TujcWQ=="
  "resolved" "https://registry.npmjs.org/react/-/react-16.13.0.tgz"
  "version" "16.13.0"
  dependencies:
    "loose-envify" "1.4.0"
    "object-assign" "4.1.1"
    "prop-types" "15.7.2"

"read-pkg-up@1.0.1":
  "integrity" "sha1-nWPBMnbAZZGNV/ACpX9AobZD+wI="
  "resolved" "https://registry.npmjs.org/read-pkg-up/-/read-pkg-up-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "find-up" "1.1.2"
    "read-pkg" "1.1.0"

"read-pkg-up@2.0.0":
  "integrity" "sha1-a3KoBImE4MQeeVEP1en6mbO1Sb4="
  "resolved" "https://registry.npmjs.org/read-pkg-up/-/read-pkg-up-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "find-up" "2.1.0"
    "read-pkg" "2.0.0"

"read-pkg@1.1.0":
  "integrity" "sha1-9f+qXs0pyzHAR0vKfXVra7KePyg="
  "resolved" "https://registry.npmjs.org/read-pkg/-/read-pkg-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "load-json-file" "1.1.0"
    "normalize-package-data" "2.5.0"
    "path-type" "1.1.0"

"read-pkg@2.0.0":
  "integrity" "sha1-jvHAYjxqbbDcZxPEv6xGMysjaPg="
  "resolved" "https://registry.npmjs.org/read-pkg/-/read-pkg-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "load-json-file" "2.0.0"
    "normalize-package-data" "2.5.0"
    "path-type" "2.0.0"

"readable-stream@2.3.7":
  "integrity" "sha512-Ebho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw=="
  "resolved" "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.7.tgz"
  "version" "2.3.7"
  dependencies:
    "core-util-is" "1.0.2"
    "inherits" "2.0.4"
    "isarray" "1.0.0"
    "process-nextick-args" "2.0.1"
    "safe-buffer" "5.1.2"
    "string_decoder" "1.1.1"
    "util-deprecate" "1.0.2"

"readable-stream@3.6.0":
  "integrity" "sha512-BViHy7LKeTz4oNnkcLJ+lVSL6vpiFeX6/d3oSH8zCW7UxP2onchk+vTGB143xuFjHS3deTgkKoXXymXqymiIdA=="
  "resolved" "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "inherits" "2.0.4"
    "string_decoder" "1.1.1"
    "util-deprecate" "1.0.2"

"readdirp@2.2.1":
  "integrity" "sha512-1JU/8q+VgFZyxwrJ+SVIOsh+KywWGpds3NTqikiKpDMZWScmAYyKIgqkO+ARvNWJfXeXR1zxz7aHF4u4CyH6vQ=="
  "resolved" "https://registry.npmjs.org/readdirp/-/readdirp-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "graceful-fs" "4.2.3"
    "micromatch" "3.1.10"
    "readable-stream" "2.3.7"

"realpath-native@1.1.0":
  "integrity" "sha512-wlgPA6cCIIg9gKz0fgAPjnzh4yR/LnXovwuo9hvyGvx3h8nX4+/iLZplfUWasXpqD8BdnGnP5njOFjkUwPzvjA=="
  "resolved" "https://registry.npmjs.org/realpath-native/-/realpath-native-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "util.promisify" "1.0.1"

"recursive-readdir@2.2.2":
  "integrity" "sha512-nRCcW9Sj7NuZwa2XvH9co8NPeXUBhZP7CRKJtU+cS6PW9FpCIFoI5ib0NT1ZrbNuPoRy0ylyCaUL8Gih4LSyFg=="
  "resolved" "https://registry.npmjs.org/recursive-readdir/-/recursive-readdir-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "minimatch" "3.0.4"

"redux-devtools-extension@^2.13.8":
  "integrity" "sha512-cNJ8Q/EtjhQaZ71c8I9+BPySIBVEKssbPpskBfsXqb8HJ002A3KRVHfeRzwRo6mGPqsm7XuHTqNSNeS1Khig0A=="
  "resolved" "https://registry.npmjs.org/redux-devtools-extension/-/redux-devtools-extension-2.13.9.tgz"
  "version" "2.13.9"

"redux@^3.1.0 || ^4.0.0", "redux@^4.0.0", "redux@^4.0.5":
  "integrity" "sha512-uI2dQN43zqLWCt6B/BMGRMY6db7TTY4qeHHfGeKb3EOhmOKjU3KdWvNLJyqaHRksv/ErdNH7cFZWg9jXtewy4g=="
  "resolved" "https://registry.npmjs.org/redux/-/redux-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "@babel/runtime" "^7.9.2"

"regenerate-unicode-properties@8.1.0":
  "integrity" "sha512-LGZzkgtLY79GeXLm8Dp0BVLdQlWICzBnJz/ipWUgo59qBaZ+BHtq51P2q1uVZlppMuUAT37SDk39qUbjTWB7bA=="
  "resolved" "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-8.1.0.tgz"
  "version" "8.1.0"
  dependencies:
    "regenerate" "1.4.0"

"regenerate@1.4.0":
  "integrity" "sha512-1G6jJVDWrt0rK99kBjvEtziZNCICAuvIPkSiUFIQxVP06RCVpq3dmDo2oi6ABpYaDYaTRr67BEhL8r1wgEZZKg=="
  "resolved" "https://registry.npmjs.org/regenerate/-/regenerate-1.4.0.tgz"
  "version" "1.4.0"

"regenerator-runtime@^0.13.4":
  "integrity" "sha512-a54FxoJDIr27pgf7IgeQGxmqUNYrcV338lf/6gH456HZ/PhX+5BcwHXG9ajESmwe6WRO0tAzRUrRmNONWgkrew=="
  "resolved" "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.7.tgz"
  "version" "0.13.7"

"regenerator-runtime@0.10.5":
  "integrity" "sha1-M2w+/BIgrc7dosn6tntaeVWjNlg="
  "resolved" "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.10.5.tgz"
  "version" "0.10.5"

"regenerator-runtime@0.11.1":
  "integrity" "sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg=="
  "resolved" "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz"
  "version" "0.11.1"

"regenerator-runtime@0.12.1":
  "integrity" "sha512-odxIc1/vDlo4iZcfXqRYFj0vpXFNoGdKMAUieAlFYO6m/nl5e9KR/beGf41z4a1FI+aQgtjhuaSlDxQ0hmkrHg=="
  "resolved" "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.12.1.tgz"
  "version" "0.12.1"

"regenerator-runtime@0.13.4":
  "integrity" "sha512-plpwicqEzfEyTQohIKktWigcLzmNStMGwbOUbykx51/29Z3JOGYldaaNGK7ngNXV+UcoqvIMmloZ48Sr74sd+g=="
  "resolved" "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.4.tgz"
  "version" "0.13.4"

"regenerator-transform@0.14.2":
  "integrity" "sha512-V4+lGplCM/ikqi5/mkkpJ06e9Bujq1NFmNLvsCs56zg3ZbzrnUzAtizZ24TXxtRX/W2jcdScwQCnbL0CICTFkQ=="
  "resolved" "https://registry.npmjs.org/regenerator-transform/-/regenerator-transform-0.14.2.tgz"
  "version" "0.14.2"
  dependencies:
    "@babel/runtime" "7.8.7"
    "private" "0.1.8"

"regex-cache@0.4.4":
  "integrity" "sha512-nVIZwtCjkC9YgvWkpM55B5rBhBYRZhAaJbgcFYXXsHnbZ9UZI9nnVWYZpBlCqv9ho2eZryPnWrZGsOdPwVWXWQ=="
  "resolved" "https://registry.npmjs.org/regex-cache/-/regex-cache-0.4.4.tgz"
  "version" "0.4.4"
  dependencies:
    "is-equal-shallow" "0.1.3"

"regex-not@1.0.2":
  "integrity" "sha512-J6SDjUgDxQj5NusnOtdFxDwN/+HWykR8GELwctJ7mdqhcyy1xEc4SRFHUXvxTp661YaVKAjfRLZ9cCqS6tn32A=="
  "resolved" "https://registry.npmjs.org/regex-not/-/regex-not-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "extend-shallow" "3.0.2"
    "safe-regex" "1.1.0"

"regexp.prototype.flags@1.3.0":
  "integrity" "sha512-2+Q0C5g951OlYlJz6yu5/M33IcsESLlLfsyIaLJaG4FA2r4yP8MvVMJUUP/fVBkSpbbbZlS5gynbEWLipiiXiQ=="
  "resolved" "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "define-properties" "1.1.3"
    "es-abstract" "1.17.4"

"regexpp@2.0.1":
  "integrity" "sha512-lv0M6+TkDVniA3aD1Eg0DVpfU/booSu7Eev3TDO/mZKHBfVjgCGTV4t4buppESEYDtkArYFOxTJWv6S5C+iaNw=="
  "resolved" "https://registry.npmjs.org/regexpp/-/regexpp-2.0.1.tgz"
  "version" "2.0.1"

"regexpu-core@4.6.0":
  "integrity" "sha512-YlVaefl8P5BnFYOITTNzDvan1ulLOiXJzCNZxduTIosN17b87h3bvG9yHMoHaRuo88H4mQ06Aodj5VtYGGGiTg=="
  "resolved" "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.6.0.tgz"
  "version" "4.6.0"
  dependencies:
    "regenerate" "1.4.0"
    "regenerate-unicode-properties" "8.1.0"
    "regjsgen" "0.5.1"
    "regjsparser" "0.6.3"
    "unicode-match-property-ecmascript" "1.0.4"
    "unicode-match-property-value-ecmascript" "1.1.0"

"regjsgen@0.5.1":
  "integrity" "sha512-5qxzGZjDs9w4tzT3TPhCJqWdCc3RLYwy9J2NB0nm5Lz+S273lvWcpjaTGHsT1dc6Hhfq41uSEOw8wBmxrKOuyg=="
  "resolved" "https://registry.npmjs.org/regjsgen/-/regjsgen-0.5.1.tgz"
  "version" "0.5.1"

"regjsparser@0.6.3":
  "integrity" "sha512-8uZvYbnfAtEm9Ab8NTb3hdLwL4g/LQzEYP7Xs27T96abJCCE2d6r3cPZPQEsLKy0vRSGVNG+/zVGtLr86HQduA=="
  "resolved" "https://registry.npmjs.org/regjsparser/-/regjsparser-0.6.3.tgz"
  "version" "0.6.3"
  dependencies:
    "jsesc" "0.5.0"

"relateurl@0.2.7":
  "integrity" "sha1-VNvzd+UUQKypCkzSdGANP/LYiKk="
  "resolved" "https://registry.npmjs.org/relateurl/-/relateurl-0.2.7.tgz"
  "version" "0.2.7"

"remove-trailing-separator@1.1.0":
  "integrity" "sha1-wkvOKig62tW8P1jg1IJJuSN52O8="
  "resolved" "https://registry.npmjs.org/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz"
  "version" "1.1.0"

"renderkid@2.0.3":
  "integrity" "sha512-z8CLQp7EZBPCwCnncgf9C4XAi3WR0dv+uWu/PjIyhhAb5d6IJ/QZqlHFprHeKT+59//V6BNUsLbvN8+2LarxGA=="
  "resolved" "https://registry.npmjs.org/renderkid/-/renderkid-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "css-select" "1.2.0"
    "dom-converter" "0.2.0"
    "htmlparser2" "3.10.1"
    "strip-ansi" "3.0.1"
    "utila" "0.4.0"

"repeat-element@1.1.3":
  "integrity" "sha512-ahGq0ZnV5m5XtZLMb+vP76kcAM5nkLqk0lpqAuojSKGgQtn4eRi4ZZGm2olo2zKFH+sMsWaqOCW1dqAnOru72g=="
  "resolved" "https://registry.npmjs.org/repeat-element/-/repeat-element-1.1.3.tgz"
  "version" "1.1.3"

"repeat-string@1.6.1":
  "integrity" "sha1-jcrkcOHIirwtYA//Sndihtp15jc="
  "resolved" "https://registry.npmjs.org/repeat-string/-/repeat-string-1.6.1.tgz"
  "version" "1.6.1"

"repeating@2.0.1":
  "integrity" "sha1-UhTFOpJtNVJwdSf7q0FdvAjQbdo="
  "resolved" "https://registry.npmjs.org/repeating/-/repeating-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-finite" "1.1.0"

"request-promise-core@1.1.3":
  "integrity" "sha512-QIs2+ArIGQVp5ZYbWD5ZLCY29D5CfWizP8eWnm8FoGD1TX61veauETVQbrV60662V0oFBkrDOuaBI8XgtuyYAQ=="
  "resolved" "https://registry.npmjs.org/request-promise-core/-/request-promise-core-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "lodash" "4.17.15"

"request-promise-native@1.0.8":
  "integrity" "sha512-dapwLGqkHtwL5AEbfenuzjTYg35Jd6KPytsC2/TLkVMz8rm+tNt72MGUWT1RP/aYawMpN6HqbNGBQaRcBtjQMQ=="
  "resolved" "https://registry.npmjs.org/request-promise-native/-/request-promise-native-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "request-promise-core" "1.1.3"
    "stealthy-require" "1.1.1"
    "tough-cookie" "2.5.0"

"request@2.88.2":
  "integrity" "sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw=="
  "resolved" "https://registry.npmjs.org/request/-/request-2.88.2.tgz"
  "version" "2.88.2"
  dependencies:
    "aws-sign2" "0.7.0"
    "aws4" "1.9.1"
    "caseless" "0.12.0"
    "combined-stream" "1.0.8"
    "extend" "3.0.2"
    "forever-agent" "0.6.1"
    "form-data" "2.3.3"
    "har-validator" "5.1.3"
    "http-signature" "1.2.0"
    "is-typedarray" "1.0.0"
    "isstream" "0.1.2"
    "json-stringify-safe" "5.0.1"
    "mime-types" "2.1.26"
    "oauth-sign" "0.9.0"
    "performance-now" "2.1.0"
    "qs" "6.5.2"
    "safe-buffer" "5.2.0"
    "tough-cookie" "2.5.0"
    "tunnel-agent" "0.6.0"
    "uuid" "3.4.0"

"require-directory@2.1.1":
  "integrity" "sha1-jGStX9MNqxyXbiNE/+f3kqam30I="
  "resolved" "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz"
  "version" "2.1.1"

"require-main-filename@1.0.1":
  "integrity" "sha1-l/cXtp1IeE9fUmpsWqj/3aBVpNE="
  "resolved" "https://registry.npmjs.org/require-main-filename/-/require-main-filename-1.0.1.tgz"
  "version" "1.0.1"

"requires-port@1.0.0":
  "integrity" "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8="
  "resolved" "https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz"
  "version" "1.0.0"

"resize-observer-polyfill@1.5.1":
  "integrity" "sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg=="
  "resolved" "https://registry.npmjs.org/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz"
  "version" "1.5.1"

"resolve-cwd@2.0.0":
  "integrity" "sha1-AKn3OHVW4nA46uIyyqNypqWbZlo="
  "resolved" "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "resolve-from" "3.0.0"

"resolve-from@3.0.0":
  "integrity" "sha1-six699nWiBvItuZTM17rywoYh0g="
  "resolved" "https://registry.npmjs.org/resolve-from/-/resolve-from-3.0.0.tgz"
  "version" "3.0.0"

"resolve-from@4.0.0":
  "integrity" "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g=="
  "resolved" "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz"
  "version" "4.0.0"

"resolve-pathname@3.0.0":
  "integrity" "sha512-C7rARubxI8bXFNB/hqcp/4iUeIXJhJZvFPFPiSPRnhU5UPxzMFIl+2E6yY6c4k9giDJAhtV+enfA+G89N6Csng=="
  "resolved" "https://registry.npmjs.org/resolve-pathname/-/resolve-pathname-3.0.0.tgz"
  "version" "3.0.0"

"resolve-url@0.2.1":
  "integrity" "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo="
  "resolved" "https://registry.npmjs.org/resolve-url/-/resolve-url-0.2.1.tgz"
  "version" "0.2.1"

"resolve@1.1.7":
  "integrity" "sha1-IDEU2CrSxe2ejgQRs5ModeiJ6Xs="
  "resolved" "https://registry.npmjs.org/resolve/-/resolve-1.1.7.tgz"
  "version" "1.1.7"

"resolve@1.10.0":
  "integrity" "sha512-3sUr9aq5OfSg2S9pNtPA9hL1FVEAjvfOC4leW0SNf/mpnaakz2a9femSd6LqAww2RaFctwyf1lCqnTHuF1rxDg=="
  "resolved" "https://registry.npmjs.org/resolve/-/resolve-1.10.0.tgz"
  "version" "1.10.0"
  dependencies:
    "path-parse" "1.0.6"

"resolve@1.15.1":
  "integrity" "sha512-84oo6ZTtoTUpjgNEr5SJyzQhzL72gaRodsSfyxC/AXRvwu0Yse9H8eF9IpGo7b8YetZhlI6v7ZQ6bKBFV/6S7w=="
  "resolved" "https://registry.npmjs.org/resolve/-/resolve-1.15.1.tgz"
  "version" "1.15.1"
  dependencies:
    "path-parse" "1.0.6"

"restore-cursor@2.0.0":
  "integrity" "sha1-n37ih/gv0ybU/RYpI9YhKe7g368="
  "resolved" "https://registry.npmjs.org/restore-cursor/-/restore-cursor-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "onetime" "2.0.1"
    "signal-exit" "3.0.2"

"ret@0.1.15":
  "integrity" "sha512-TTlYpa+OL+vMMNG24xSlQGEJ3B/RzEfUlLct7b5G/ytav+wPrplCpVMFuwzXbkecJrb6IYo1iFb0S9v37754mg=="
  "resolved" "https://registry.npmjs.org/ret/-/ret-0.1.15.tgz"
  "version" "0.1.15"

"rgb-regex@1.0.1":
  "integrity" "sha1-wODWiC3w4jviVKR16O3UGRX+rrE="
  "resolved" "https://registry.npmjs.org/rgb-regex/-/rgb-regex-1.0.1.tgz"
  "version" "1.0.1"

"rgba-regex@1.0.0":
  "integrity" "sha1-QzdOLiyglosO8VI0YLfXMP8i7rM="
  "resolved" "https://registry.npmjs.org/rgba-regex/-/rgba-regex-1.0.0.tgz"
  "version" "1.0.0"

"rimraf@2.6.3":
  "integrity" "sha512-mwqeW5XsA2qAejG46gYdENaxXjx9onRNCfn7L0duuP4hCuTIi/QO7PDK07KJfp1d+izWPrzEJDcSqBa0OZQriA=="
  "resolved" "https://registry.npmjs.org/rimraf/-/rimraf-2.6.3.tgz"
  "version" "2.6.3"
  dependencies:
    "glob" "7.1.6"

"ripemd160@2.0.2":
  "integrity" "sha512-ii4iagi25WusVoiC4B4lq7pbXfAp3D9v5CwfkY33vffw2+pkDjY1D8GaN7spsxvCSx8dkPqOZCEZyfxcmJG2IA=="
  "resolved" "https://registry.npmjs.org/ripemd160/-/ripemd160-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "hash-base" "3.0.4"
    "inherits" "2.0.4"

"rmc-feedback@2.0.0":
  "integrity" "sha512-5PWOGOW7VXks/l3JzlOU9NIxRpuaSS8d9zA3UULUCuTKnpwBHNvv1jSJzxgbbCQeYzROWUpgKI4za3X4C/mKmQ=="
  "resolved" "https://registry.npmjs.org/rmc-feedback/-/rmc-feedback-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "babel-runtime" "6.26.0"
    "classnames" "2.2.6"

"rsvp@3.6.2":
  "integrity" "sha512-OfWGQTb9vnwRjwtA2QwpG2ICclHC3pgXZO5xt8H2EfgDquO0qVdSb5T88L4qJVAEugbS56pAuV4XZM58UX8ulw=="
  "resolved" "https://registry.npmjs.org/rsvp/-/rsvp-3.6.2.tgz"
  "version" "3.6.2"

"run-async@2.4.0":
  "integrity" "sha512-xJTbh/d7Lm7SBhc1tNvTpeCHaEzoyxPrqNlvSdMfBTYwaY++UJFyXUOxAtsRUXjlqOfj8luNaR9vjCh4KeV+pg=="
  "resolved" "https://registry.npmjs.org/run-async/-/run-async-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "is-promise" "2.1.0"

"run-queue@1.0.3":
  "integrity" "sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec="
  "resolved" "https://registry.npmjs.org/run-queue/-/run-queue-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "aproba" "1.2.0"

"rxjs@6.5.4":
  "integrity" "sha512-naMQXcgEo3csAEGvw/NydRA0fuS2nDZJiw1YUWFKU7aPPAPGZEsD4Iimit96qwCieH6y614MCLYwdkrWx7z/7Q=="
  "resolved" "https://registry.npmjs.org/rxjs/-/rxjs-6.5.4.tgz"
  "version" "6.5.4"
  dependencies:
    "tslib" "1.11.1"

"safe-buffer@5.1.2":
  "integrity" "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g=="
  "resolved" "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safe-buffer@5.2.0":
  "integrity" "sha512-fZEwUGbVl7kouZs1jCdMLdt95hdIv0ZeHg6L7qPeciMZhZ+/gdesW4wgTARkrFWEpspjEATAzUGPG8N2jJiwbg=="
  "resolved" "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.0.tgz"
  "version" "5.2.0"

"safe-regex@1.1.0":
  "integrity" "sha1-QKNmnzsHfR6UPURinhV91IAjvy4="
  "resolved" "https://registry.npmjs.org/safe-regex/-/safe-regex-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "ret" "0.1.15"

"safer-buffer@2.1.2":
  "integrity" "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="
  "resolved" "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz"
  "version" "2.1.2"

"sane@2.5.2":
  "integrity" "sha1-tNwYYcIbQn6SlQej51HiosuKs/o="
  "resolved" "https://registry.npmjs.org/sane/-/sane-2.5.2.tgz"
  "version" "2.5.2"
  dependencies:
    "anymatch" "2.0.0"
    "capture-exit" "1.2.0"
    "exec-sh" "0.2.2"
    "fb-watchman" "2.0.1"
    "micromatch" "3.1.10"
    "minimist" "1.2.0"
    "walker" "1.0.7"
    "watch" "0.18.0"

"sass-loader@7.1.0":
  "integrity" "sha512-+G+BKGglmZM2GUSfT9TLuEp6tzehHPjAMoRRItOojWIqIGPloVCMhNIQuG639eJ+y033PaGTSjLaTHts8Kw79w=="
  "resolved" "https://registry.npmjs.org/sass-loader/-/sass-loader-7.1.0.tgz"
  "version" "7.1.0"
  dependencies:
    "clone-deep" "2.0.2"
    "loader-utils" "1.4.0"
    "lodash.tail" "4.1.1"
    "neo-async" "2.6.1"
    "pify" "3.0.0"
    "semver" "5.7.1"

"sax@1.2.4":
  "integrity" "sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw=="
  "resolved" "https://registry.npmjs.org/sax/-/sax-1.2.4.tgz"
  "version" "1.2.4"

"scheduler@0.19.0":
  "integrity" "sha512-xowbVaTPe9r7y7RUejcK73/j8tt2jfiyTednOvHbA8JoClvMYCp+r8QegLwK/n8zWQAtZb1fFnER4XLBZXrCxA=="
  "resolved" "https://registry.npmjs.org/scheduler/-/scheduler-0.19.0.tgz"
  "version" "0.19.0"
  dependencies:
    "loose-envify" "1.4.0"
    "object-assign" "4.1.1"

"schema-utils@0.4.7":
  "integrity" "sha512-v/iwU6wvwGK8HbU9yi3/nhGzP0yGSuhQMzL6ySiec1FSrZZDkhm4noOSWzrNFo/jEc+SJY6jRTwuwbSXJPDUnQ=="
  "resolved" "https://registry.npmjs.org/schema-utils/-/schema-utils-0.4.7.tgz"
  "version" "0.4.7"
  dependencies:
    "ajv" "6.12.0"
    "ajv-keywords" "3.4.1"

"schema-utils@1.0.0":
  "integrity" "sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g=="
  "resolved" "https://registry.npmjs.org/schema-utils/-/schema-utils-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "ajv" "6.12.0"
    "ajv-errors" "1.0.1"
    "ajv-keywords" "3.4.1"

"select-hose@2.0.0":
  "integrity" "sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo="
  "resolved" "https://registry.npmjs.org/select-hose/-/select-hose-2.0.0.tgz"
  "version" "2.0.0"

"selfsigned@1.10.7":
  "integrity" "sha512-8M3wBCzeWIJnQfl43IKwOmC4H/RAp50S8DF60znzjW5GVqTcSe2vWclt7hmYVPkKPlHWOu5EaWOMZ2Y6W8ZXTA=="
  "resolved" "https://registry.npmjs.org/selfsigned/-/selfsigned-1.10.7.tgz"
  "version" "1.10.7"
  dependencies:
    "node-forge" "0.9.0"

"semver@5.7.1":
  "integrity" "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz"
  "version" "5.7.1"

"semver@6.3.0":
  "integrity" "sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz"
  "version" "6.3.0"

"semver@7.0.0":
  "integrity" "sha512-+GB6zVA9LWh6zovYQLALHwv5rb2PHGlJi3lfiqIHxR0uuwCgefcOJc59v9fv1w8GbStwxuuqqAjI9NMAOOgq1A=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-7.0.0.tgz"
  "version" "7.0.0"

"send@0.17.1":
  "integrity" "sha512-BsVKsiGcQMFwT8UxypobUKyv7irCNRHk1T0G680vk88yf6LBByGcZJOTJCrTP2xVN6yI+XjPJcNuE3V4fT9sAg=="
  "resolved" "https://registry.npmjs.org/send/-/send-0.17.1.tgz"
  "version" "0.17.1"
  dependencies:
    "debug" "2.6.9"
    "depd" "1.1.2"
    "destroy" "1.0.4"
    "encodeurl" "1.0.2"
    "escape-html" "1.0.3"
    "etag" "1.8.1"
    "fresh" "0.5.2"
    "http-errors" "1.7.2"
    "mime" "1.6.0"
    "ms" "2.1.1"
    "on-finished" "2.3.0"
    "range-parser" "1.2.1"
    "statuses" "1.5.0"

"serialize-javascript@1.9.1":
  "integrity" "sha512-0Vb/54WJ6k5v8sSWN09S0ora+Hnr+cX40r9F170nT+mSkaxltoE/7R3OrIdBSUv1OoiobH1QoWQbCnAO+e8J1A=="
  "resolved" "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-1.9.1.tgz"
  "version" "1.9.1"

"serve-index@1.9.1":
  "integrity" "sha1-03aNabHn2C5c4FD/9bRTvqEqkjk="
  "resolved" "https://registry.npmjs.org/serve-index/-/serve-index-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "accepts" "1.3.7"
    "batch" "0.6.1"
    "debug" "2.6.9"
    "escape-html" "1.0.3"
    "http-errors" "1.6.3"
    "mime-types" "2.1.26"
    "parseurl" "1.3.3"

"serve-static@1.14.1":
  "integrity" "sha512-JMrvUwE54emCYWlTI+hGrGv5I8dEwmco/00EvkzIIsR7MqrHonbD9pO2MOfFnpFntl7ecpZs+3mW+XbQZu9QCg=="
  "resolved" "https://registry.npmjs.org/serve-static/-/serve-static-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "encodeurl" "1.0.2"
    "escape-html" "1.0.3"
    "parseurl" "1.3.3"
    "send" "0.17.1"

"set-blocking@2.0.0":
  "integrity" "sha1-BF+XgtARrppoA93TgrJDkrPYkPc="
  "resolved" "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz"
  "version" "2.0.0"

"set-value@2.0.1":
  "integrity" "sha512-JxHc1weCN68wRY0fhCoXpyK55m/XPHafOmK4UWD7m2CI14GMcFypt4w/0+NV5f/ZMby2F6S2wwA7fgynh9gWSw=="
  "resolved" "https://registry.npmjs.org/set-value/-/set-value-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "extend-shallow" "2.0.1"
    "is-extendable" "0.1.1"
    "is-plain-object" "2.0.4"
    "split-string" "3.1.0"

"setimmediate@1.0.5":
  "integrity" "sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU="
  "resolved" "https://registry.npmjs.org/setimmediate/-/setimmediate-1.0.5.tgz"
  "version" "1.0.5"

"setprototypeof@1.1.0":
  "integrity" "sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ=="
  "resolved" "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.1.0.tgz"
  "version" "1.1.0"

"setprototypeof@1.1.1":
  "integrity" "sha512-JvdAWfbXeIGaZ9cILp38HntZSFSo3mWg6xGcJJsd+d4aRMOqauag1C63dJfDw7OaMYwEbHMOxEZ1lqVRYP2OAw=="
  "resolved" "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.1.1.tgz"
  "version" "1.1.1"

"sha.js@2.4.11":
  "integrity" "sha512-QMEp5B7cftE7APOjk5Y6xgrbWu+WkLVQwk8JNjZ8nKRciZaByEW6MubieAiToS7+dwvrjGhH8jRXz3MVd0AYqQ=="
  "resolved" "https://registry.npmjs.org/sha.js/-/sha.js-2.4.11.tgz"
  "version" "2.4.11"
  dependencies:
    "inherits" "2.0.4"
    "safe-buffer" "5.2.0"

"shallow-clone@0.1.2":
  "integrity" "sha1-WQnodLp3EG1zrEFM/sH/yofZcGA="
  "resolved" "https://registry.npmjs.org/shallow-clone/-/shallow-clone-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "is-extendable" "0.1.1"
    "kind-of" "2.0.1"
    "lazy-cache" "0.2.7"
    "mixin-object" "2.0.1"

"shallow-clone@1.0.0":
  "integrity" "sha512-oeXreoKR/SyNJtRJMAKPDSvd28OqEwG4eR/xc856cRGBII7gX9lvAqDxusPm0846z/w/hWYjI1NpKwJ00NHzRA=="
  "resolved" "https://registry.npmjs.org/shallow-clone/-/shallow-clone-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-extendable" "0.1.1"
    "kind-of" "5.1.0"
    "mixin-object" "2.0.1"

"shallow-equal@1.2.1":
  "integrity" "sha512-S4vJDjHHMBaiZuT9NPb616CSmLf618jawtv3sufLl6ivK8WocjAo58cXwbRV1cgqxH0Qbv+iUt6m05eqEa2IRA=="
  "resolved" "https://registry.npmjs.org/shallow-equal/-/shallow-equal-1.2.1.tgz"
  "version" "1.2.1"

"shallowequal@1.1.0":
  "integrity" "sha512-y0m1JoUZSlPAjXVtPPW70aZWfIL/dSP7AFkRnniLCrK/8MDKog3TySTBmckD+RObVxH0v4Tox67+F14PdED2oQ=="
  "resolved" "https://registry.npmjs.org/shallowequal/-/shallowequal-1.1.0.tgz"
  "version" "1.1.0"

"shebang-command@1.2.0":
  "integrity" "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo="
  "resolved" "https://registry.npmjs.org/shebang-command/-/shebang-command-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "shebang-regex" "1.0.0"

"shebang-regex@1.0.0":
  "integrity" "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM="
  "resolved" "https://registry.npmjs.org/shebang-regex/-/shebang-regex-1.0.0.tgz"
  "version" "1.0.0"

"shell-quote@1.6.1":
  "integrity" "sha1-9HgZSczkAmlxJ0MOo7PFR29IF2c="
  "resolved" "https://registry.npmjs.org/shell-quote/-/shell-quote-1.6.1.tgz"
  "version" "1.6.1"
  dependencies:
    "array-filter" "0.0.1"
    "array-map" "0.0.0"
    "array-reduce" "0.0.0"
    "jsonify" "0.0.0"

"shellwords@0.1.1":
  "integrity" "sha512-vFwSUfQvqybiICwZY5+DAWIPLKsWO31Q91JSKl3UYv+K5c2QRPzn0qzec6QPu1Qc9eHYItiP3NdJqNVqetYAww=="
  "resolved" "https://registry.npmjs.org/shellwords/-/shellwords-0.1.1.tgz"
  "version" "0.1.1"

"signal-exit@3.0.2":
  "integrity" "sha1-tf3AjxKH6hF4Yo5BXiUTK3NkbG0="
  "resolved" "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.2.tgz"
  "version" "3.0.2"

"simple-swizzle@0.2.2":
  "integrity" "sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo="
  "resolved" "https://registry.npmjs.org/simple-swizzle/-/simple-swizzle-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "is-arrayish" "0.3.2"

"sisteransi@0.1.1":
  "integrity" "sha512-PmGOd02bM9YO5ifxpw36nrNMBTptEtfRl4qUYl9SndkolplkrZZOW7PGHjrZL53QvMVj9nQ+TKqUnRsw4tJa4g=="
  "resolved" "https://registry.npmjs.org/sisteransi/-/sisteransi-0.1.1.tgz"
  "version" "0.1.1"

"slash@1.0.0":
  "integrity" "sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU="
  "resolved" "https://registry.npmjs.org/slash/-/slash-1.0.0.tgz"
  "version" "1.0.0"

"slash@2.0.0":
  "integrity" "sha512-ZYKh3Wh2z1PpEXWr0MpSBZ0V6mZHAQfYevttO11c51CaWjGTaadiKZ+wVt1PbMlDV5qhMFslpZCemhwOK7C89A=="
  "resolved" "https://registry.npmjs.org/slash/-/slash-2.0.0.tgz"
  "version" "2.0.0"

"slice-ansi@2.1.0":
  "integrity" "sha512-Qu+VC3EwYLldKa1fCxuuvULvSJOKEgk9pi8dZeCVK7TqBfUNTH4sFkk4joj8afVSfAYgJoSOetjx9QWOJ5mYoQ=="
  "resolved" "https://registry.npmjs.org/slice-ansi/-/slice-ansi-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "ansi-styles" "3.2.1"
    "astral-regex" "1.0.0"
    "is-fullwidth-code-point" "2.0.0"

"snapdragon-node@2.1.1":
  "integrity" "sha512-O27l4xaMYt/RSQ5TR3vpWCAB5Kb/czIcqUFOM/C4fYcLnbZUc1PkjTAMjof2pBWaSTwOUd6qUHcFGVGj7aIwnw=="
  "resolved" "https://registry.npmjs.org/snapdragon-node/-/snapdragon-node-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "define-property" "1.0.0"
    "isobject" "3.0.1"
    "snapdragon-util" "3.0.1"

"snapdragon-util@3.0.1":
  "integrity" "sha512-mbKkMdQKsjX4BAL4bRYTj21edOf8cN7XHdYUJEe+Zn99hVEYcMvKPct1IqNe7+AZPirn8BCDOQBHQZknqmKlZQ=="
  "resolved" "https://registry.npmjs.org/snapdragon-util/-/snapdragon-util-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "kind-of" "3.2.2"

"snapdragon@0.8.2":
  "integrity" "sha512-FtyOnWN/wCHTVXOMwvSv26d+ko5vWlIDD6zoUJ7LW8vh+ZBC8QdljveRP+crNrtBwioEUWy/4dMtbBjA4ioNlg=="
  "resolved" "https://registry.npmjs.org/snapdragon/-/snapdragon-0.8.2.tgz"
  "version" "0.8.2"
  dependencies:
    "base" "0.11.2"
    "debug" "2.6.9"
    "define-property" "0.2.5"
    "extend-shallow" "2.0.1"
    "map-cache" "0.2.2"
    "source-map" "0.5.7"
    "source-map-resolve" "0.5.3"
    "use" "3.1.1"

"sockjs-client@1.3.0":
  "integrity" "sha512-R9jxEzhnnrdxLCNln0xg5uGHqMnkhPSTzUZH2eXcR03S/On9Yvoq2wyUZILRUhZCNVu2PmwWVoyuiPz8th8zbg=="
  "resolved" "https://registry.npmjs.org/sockjs-client/-/sockjs-client-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "debug" "3.2.6"
    "eventsource" "1.0.7"
    "faye-websocket" "0.11.3"
    "inherits" "2.0.4"
    "json3" "3.3.3"
    "url-parse" "1.4.7"

"sockjs@0.3.19":
  "integrity" "sha512-V48klKZl8T6MzatbLlzzRNhMepEys9Y4oGFpypBFFn1gLI/QQ9HtLLyWJNbPlwGLelOVOEijUbTTJeLLI59jLw=="
  "resolved" "https://registry.npmjs.org/sockjs/-/sockjs-0.3.19.tgz"
  "version" "0.3.19"
  dependencies:
    "faye-websocket" "0.10.0"
    "uuid" "3.4.0"

"source-list-map@2.0.1":
  "integrity" "sha512-qnQ7gVMxGNxsiL4lEuJwe/To8UnK7fAnmbGEEH8RpLouuKbeEm0lhbQVFIrNSuB+G7tVrAlVsZgETT5nljf+Iw=="
  "resolved" "https://registry.npmjs.org/source-list-map/-/source-list-map-2.0.1.tgz"
  "version" "2.0.1"

"source-map-resolve@0.5.3":
  "integrity" "sha512-Htz+RnsXWk5+P2slx5Jh3Q66vhQj1Cllm0zvnaY98+NFx+Dv2CF/f5O/t8x+KaNdrdIAsruNzoh/KpialbqAnw=="
  "resolved" "https://registry.npmjs.org/source-map-resolve/-/source-map-resolve-0.5.3.tgz"
  "version" "0.5.3"
  dependencies:
    "atob" "2.1.2"
    "decode-uri-component" "0.2.0"
    "resolve-url" "0.2.1"
    "source-map-url" "0.4.0"
    "urix" "0.1.0"

"source-map-support@0.4.18":
  "integrity" "sha512-try0/JqxPLF9nOjvSta7tVondkP5dwgyLDjVoyMDlmjugT2lRZ1OfsrYTkCd2hkDnJTKRbO/Rl3orm8vlsUzbA=="
  "resolved" "https://registry.npmjs.org/source-map-support/-/source-map-support-0.4.18.tgz"
  "version" "0.4.18"
  dependencies:
    "source-map" "0.5.7"

"source-map-support@0.5.16":
  "integrity" "sha512-efyLRJDr68D9hBBNIPWFjhpFzURh+KJykQwvMyW5UiZzYwoF6l4YMMDIJJEyFWxWCqfyxLzz6tSfUFR+kXXsVQ=="
  "resolved" "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.16.tgz"
  "version" "0.5.16"
  dependencies:
    "buffer-from" "1.1.1"
    "source-map" "0.6.1"

"source-map-url@0.4.0":
  "integrity" "sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM="
  "resolved" "https://registry.npmjs.org/source-map-url/-/source-map-url-0.4.0.tgz"
  "version" "0.4.0"

"source-map@0.5.7":
  "integrity" "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@0.6.1":
  "integrity" "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"spdx-correct@3.1.0":
  "integrity" "sha512-lr2EZCctC2BNR7j7WzJ2FpDznxky1sjfxvvYEyzxNyb6lZXHODmEoJeFu4JupYlkfha1KZpJyoqiJ7pgA1qq8Q=="
  "resolved" "https://registry.npmjs.org/spdx-correct/-/spdx-correct-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "spdx-expression-parse" "3.0.0"
    "spdx-license-ids" "3.0.5"

"spdx-exceptions@2.2.0":
  "integrity" "sha512-2XQACfElKi9SlVb1CYadKDXvoajPgBVPn/gOQLrTvHdElaVhr7ZEbqJaRnJLVNeaI4cMEAgVCeBMKF6MWRDCRA=="
  "resolved" "https://registry.npmjs.org/spdx-exceptions/-/spdx-exceptions-2.2.0.tgz"
  "version" "2.2.0"

"spdx-expression-parse@3.0.0":
  "integrity" "sha512-Yg6D3XpRD4kkOmTpdgbUiEJFKghJH03fiC1OPll5h/0sO6neh2jqRDVHOQ4o/LMea0tgCkbMgea5ip/e+MkWyg=="
  "resolved" "https://registry.npmjs.org/spdx-expression-parse/-/spdx-expression-parse-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "spdx-exceptions" "2.2.0"
    "spdx-license-ids" "3.0.5"

"spdx-license-ids@3.0.5":
  "integrity" "sha512-J+FWzZoynJEXGphVIS+XEh3kFSjZX/1i9gFBaWQcB+/tmpe2qUsSBABpcxqxnAxFdiUFEgAX1bjYGQvIZmoz9Q=="
  "resolved" "https://registry.npmjs.org/spdx-license-ids/-/spdx-license-ids-3.0.5.tgz"
  "version" "3.0.5"

"spdy-transport@3.0.0":
  "integrity" "sha512-hsLVFE5SjA6TCisWeJXFKniGGOpBgMLmerfO2aCyCU5s7nJ/rpAepqmFifv/GCbSbueEeAJJnmSQ2rKC/g8Fcw=="
  "resolved" "https://registry.npmjs.org/spdy-transport/-/spdy-transport-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "debug" "4.1.1"
    "detect-node" "2.0.4"
    "hpack.js" "2.1.6"
    "obuf" "1.1.2"
    "readable-stream" "3.6.0"
    "wbuf" "1.7.3"

"spdy@4.0.1":
  "integrity" "sha512-HeZS3PBdMA+sZSu0qwpCxl3DeALD5ASx8pAX0jZdKXSpPWbQ6SYGnlg3BBmYLx5LtiZrmkAZfErCm2oECBcioA=="
  "resolved" "https://registry.npmjs.org/spdy/-/spdy-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "debug" "4.1.1"
    "handle-thing" "2.0.0"
    "http-deceiver" "1.2.7"
    "select-hose" "2.0.0"
    "spdy-transport" "3.0.0"

"split-string@3.1.0":
  "integrity" "sha512-NzNVhJDYpwceVVii8/Hu6DKfD2G+NrQHlS/V/qgv763EYudVwEcMQNxd2lh+0VrUByXN/oJkl5grOhYWvQUYiw=="
  "resolved" "https://registry.npmjs.org/split-string/-/split-string-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "extend-shallow" "3.0.2"

"sprintf-js@1.0.3":
  "integrity" "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw="
  "resolved" "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz"
  "version" "1.0.3"

"sshpk@1.16.1":
  "integrity" "sha512-HXXqVUq7+pcKeLqqZj6mHFUMvXtOJt1uoUx09pFW6011inTMxqI8BA8PM95myrIyyKwdnzjdFjLiE6KBPVtJIg=="
  "resolved" "https://registry.npmjs.org/sshpk/-/sshpk-1.16.1.tgz"
  "version" "1.16.1"
  dependencies:
    "asn1" "0.2.4"
    "assert-plus" "1.0.0"
    "bcrypt-pbkdf" "1.0.2"
    "dashdash" "1.14.1"
    "ecc-jsbn" "0.1.2"
    "getpass" "0.1.7"
    "jsbn" "0.1.1"
    "safer-buffer" "2.1.2"
    "tweetnacl" "0.14.5"

"ssri@6.0.1":
  "integrity" "sha512-3Wge10hNcT1Kur4PDFwEieXSCMCJs/7WvSACcrMYrNp+b8kDL1/0wJch5Ni2WrtwEa2IO8OsVfeKIciKCDx/QA=="
  "resolved" "https://registry.npmjs.org/ssri/-/ssri-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "figgy-pudding" "3.5.1"

"stable@0.1.8":
  "integrity" "sha512-ji9qxRnOVfcuLDySj9qzhGSEFVobyt1kIOSkj1qZzYLzq7Tos/oUUWvotUPQLlrsidqsK6tBH89Bc9kL5zHA6w=="
  "resolved" "https://registry.npmjs.org/stable/-/stable-0.1.8.tgz"
  "version" "0.1.8"

"stack-utils@1.0.2":
  "integrity" "sha512-MTX+MeG5U994cazkjd/9KNAapsHnibjMLnfXodlkXw76JEea0UiNzrqidzo1emMwk7w5Qhc9jd4Bn9TBb1MFwA=="
  "resolved" "https://registry.npmjs.org/stack-utils/-/stack-utils-1.0.2.tgz"
  "version" "1.0.2"

"static-extend@0.1.2":
  "integrity" "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY="
  "resolved" "https://registry.npmjs.org/static-extend/-/static-extend-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "define-property" "0.2.5"
    "object-copy" "0.1.0"

"statuses@1.5.0":
  "integrity" "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow="
  "resolved" "https://registry.npmjs.org/statuses/-/statuses-1.5.0.tgz"
  "version" "1.5.0"

"stealthy-require@1.1.1":
  "integrity" "sha1-NbCYdbT/SfJqd35QmzCQoyJr8ks="
  "resolved" "https://registry.npmjs.org/stealthy-require/-/stealthy-require-1.1.1.tgz"
  "version" "1.1.1"

"stream-browserify@2.0.2":
  "integrity" "sha512-nX6hmklHs/gr2FuxYDltq8fJA1GDlxKQCz8O/IM4atRqBH8OORmBNgfvW5gG10GT/qQ9u0CzIvr2X5Pkt6ntqg=="
  "resolved" "https://registry.npmjs.org/stream-browserify/-/stream-browserify-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "inherits" "2.0.4"
    "readable-stream" "2.3.7"

"stream-each@1.2.3":
  "integrity" "sha512-vlMC2f8I2u/bZGqkdfLQW/13Zihpej/7PmSiMQsbYddxuTsJp8vRe2x2FvVExZg7FaOds43ROAuFJwPR4MTZLw=="
  "resolved" "https://registry.npmjs.org/stream-each/-/stream-each-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "end-of-stream" "1.4.4"
    "stream-shift" "1.0.1"

"stream-http@2.8.3":
  "integrity" "sha512-+TSkfINHDo4J+ZobQLWiMouQYB+UVYFttRA94FpEzzJ7ZdqcL4uUUQ7WkdkI4DSozGmgBUE/a47L+38PenXhUw=="
  "resolved" "https://registry.npmjs.org/stream-http/-/stream-http-2.8.3.tgz"
  "version" "2.8.3"
  dependencies:
    "builtin-status-codes" "3.0.0"
    "inherits" "2.0.4"
    "readable-stream" "2.3.7"
    "to-arraybuffer" "1.0.1"
    "xtend" "4.0.2"

"stream-shift@1.0.1":
  "integrity" "sha512-AiisoFqQ0vbGcZgQPY1cdP2I76glaVA/RauYR4G4thNFgkTqr90yXTo4LYX60Jl+sIlPNHHdGSwo01AvbKUSVQ=="
  "resolved" "https://registry.npmjs.org/stream-shift/-/stream-shift-1.0.1.tgz"
  "version" "1.0.1"

"string_decoder@1.1.1":
  "integrity" "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg=="
  "resolved" "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "safe-buffer" "5.1.2"

"string-convert@0.2.1":
  "integrity" "sha1-aYLMMEn7tM2F+LJFaLnZvznu/5c="
  "resolved" "https://registry.npmjs.org/string-convert/-/string-convert-0.2.1.tgz"
  "version" "0.2.1"

"string-length@2.0.0":
  "integrity" "sha1-1A27aGo6zpYMHP/KVivyxF+DY+0="
  "resolved" "https://registry.npmjs.org/string-length/-/string-length-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "astral-regex" "1.0.0"
    "strip-ansi" "4.0.0"

"string-width@1.0.2":
  "integrity" "sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M="
  "resolved" "https://registry.npmjs.org/string-width/-/string-width-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "code-point-at" "1.1.0"
    "is-fullwidth-code-point" "1.0.0"
    "strip-ansi" "3.0.1"

"string-width@2.1.1":
  "integrity" "sha512-nOqH59deCq9SRHlxq1Aw85Jnt4w6KvLKqWVik6oA9ZklXLNIOlqg4F2yrT1MVaTjAqvVwdfeZ7w7aCvJD7ugkw=="
  "resolved" "https://registry.npmjs.org/string-width/-/string-width-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-fullwidth-code-point" "2.0.0"
    "strip-ansi" "4.0.0"

"string-width@3.1.0":
  "integrity" "sha512-vafcv6KjVZKSgz06oM/H6GDBrAtz8vdhQakGjFIvNrHA6y3HCF1CInLy+QLq8dTJPQ1b+KDUqDFctkdRW44e1w=="
  "resolved" "https://registry.npmjs.org/string-width/-/string-width-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "emoji-regex" "7.0.3"
    "is-fullwidth-code-point" "2.0.0"
    "strip-ansi" "5.2.0"

"string.prototype.trimleft@2.1.1":
  "integrity" "sha512-iu2AGd3PuP5Rp7x2kEZCrB2Nf41ehzh+goo8TV7z8/XDBbsvc6HQIlUl9RjkZ4oyrW1XM5UwlGl1oVEaDjg6Ag=="
  "resolved" "https://registry.npmjs.org/string.prototype.trimleft/-/string.prototype.trimleft-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "define-properties" "1.1.3"
    "function-bind" "1.1.1"

"string.prototype.trimright@2.1.1":
  "integrity" "sha512-qFvWL3/+QIgZXVmJBfpHmxLB7xsUXz6HsUmP8+5dRaC3Q7oKUv9Vo6aMCRZC1smrtyECFsIT30PqBJ1gTjAs+g=="
  "resolved" "https://registry.npmjs.org/string.prototype.trimright/-/string.prototype.trimright-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "define-properties" "1.1.3"
    "function-bind" "1.1.1"

"stringify-object@3.3.0":
  "integrity" "sha512-rHqiFh1elqCQ9WPLIC8I0Q/g/wj5J1eMkyoiD6eoQApWHP0FtlK7rqnhmabL5VUY9JQCcqwwvlOaSuutekgyrw=="
  "resolved" "https://registry.npmjs.org/stringify-object/-/stringify-object-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "get-own-enumerable-property-symbols" "3.0.2"
    "is-obj" "1.0.1"
    "is-regexp" "1.0.0"

"strip-ansi@3.0.1":
  "integrity" "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8="
  "resolved" "https://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "ansi-regex" "2.1.1"

"strip-ansi@4.0.0":
  "integrity" "sha1-qEeQIusaw2iocTibY1JixQXuNo8="
  "resolved" "https://registry.npmjs.org/strip-ansi/-/strip-ansi-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "ansi-regex" "3.0.0"

"strip-ansi@5.0.0":
  "integrity" "sha512-Uu7gQyZI7J7gn5qLn1Np3G9vcYGTVqB+lFTytnDJv83dd8T22aGH451P3jueT2/QemInJDfxHB5Tde5OzgG1Ow=="
  "resolved" "https://registry.npmjs.org/strip-ansi/-/strip-ansi-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "ansi-regex" "4.1.0"

"strip-ansi@5.2.0":
  "integrity" "sha512-DuRs1gKbBqsMKIZlrffwlug8MHkcnpjs5VPmL1PAh+mA30U0DTotfDZ0d2UUsXpPmPmMMJ6W773MaA3J+lbiWA=="
  "resolved" "https://registry.npmjs.org/strip-ansi/-/strip-ansi-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "ansi-regex" "4.1.0"

"strip-bom@2.0.0":
  "integrity" "sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4="
  "resolved" "https://registry.npmjs.org/strip-bom/-/strip-bom-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "is-utf8" "0.2.1"

"strip-bom@3.0.0":
  "integrity" "sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM="
  "resolved" "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz"
  "version" "3.0.0"

"strip-comments@1.0.2":
  "integrity" "sha512-kL97alc47hoyIQSV165tTt9rG5dn4w1dNnBhOQ3bOU1Nc1hel09jnXANaHJ7vzHLd4Ju8kseDGzlev96pghLFw=="
  "resolved" "https://registry.npmjs.org/strip-comments/-/strip-comments-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "babel-extract-comments" "1.0.0"
    "babel-plugin-transform-object-rest-spread" "6.26.0"

"strip-eof@1.0.0":
  "integrity" "sha1-u0P/VZim6wXYm1n80SnJgzE2Br8="
  "resolved" "https://registry.npmjs.org/strip-eof/-/strip-eof-1.0.0.tgz"
  "version" "1.0.0"

"strip-json-comments@2.0.1":
  "integrity" "sha1-PFMZQukIwml8DsNEhYwobHygpgo="
  "resolved" "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-2.0.1.tgz"
  "version" "2.0.1"

"style-loader@0.23.1":
  "integrity" "sha512-XK+uv9kWwhZMZ1y7mysB+zoihsEj4wneFWAS5qoiLwzW0WzSqMrrsIy+a3zkQJq0ipFtBpX5W3MqyRIBF/WFGg=="
  "resolved" "https://registry.npmjs.org/style-loader/-/style-loader-0.23.1.tgz"
  "version" "0.23.1"
  dependencies:
    "loader-utils" "1.4.0"
    "schema-utils" "1.0.0"

"stylehacks@4.0.3":
  "integrity" "sha512-7GlLk9JwlElY4Y6a/rmbH2MhVlTyVmiJd1PfTCqFaIBEGMYNsrO/v3SeGTdhBThLg4Z+NbOk/qFMwCa+J+3p/g=="
  "resolved" "https://registry.npmjs.org/stylehacks/-/stylehacks-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "4.9.1"
    "postcss" "7.0.27"
    "postcss-selector-parser" "3.1.2"

"supports-color@2.0.0":
  "integrity" "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-2.0.0.tgz"
  "version" "2.0.0"

"supports-color@3.2.3":
  "integrity" "sha1-ZawFBLOVQXHYpklGsq48u4pfVPY="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-3.2.3.tgz"
  "version" "3.2.3"
  dependencies:
    "has-flag" "1.0.0"

"supports-color@5.5.0":
  "integrity" "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "has-flag" "3.0.0"

"supports-color@6.1.0":
  "integrity" "sha512-qe1jfm1Mg7Nq/NSh6XE24gPXROEVsWHxC1LIx//XNlD9iw7YZQGjZNjYN7xGaEG6iKdA8EtNFW6R0gjnVXp+wQ=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "has-flag" "3.0.0"

"svg-parser@2.0.4":
  "integrity" "sha512-e4hG1hRwoOdRb37cIMSgzNsxyzKfayW6VOflrwvR+/bzrkyxY/31WkbgnQpgtrNp1SdpJvpUAGTa/ZoiPNDuRQ=="
  "resolved" "https://registry.npmjs.org/svg-parser/-/svg-parser-2.0.4.tgz"
  "version" "2.0.4"

"svgo@1.3.2":
  "integrity" "sha512-yhy/sQYxR5BkC98CY7o31VGsg014AKLEPxdfhora76l36hD9Rdy5NZA/Ocn6yayNPgSamYdtX2rFJdcv07AYVw=="
  "resolved" "https://registry.npmjs.org/svgo/-/svgo-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "chalk" "2.4.2"
    "coa" "2.0.2"
    "css-select" "2.1.0"
    "css-select-base-adapter" "0.1.1"
    "css-tree" "1.0.0-alpha.37"
    "csso" "4.0.2"
    "js-yaml" "3.13.1"
    "mkdirp" "0.5.1"
    "object.values" "1.1.1"
    "sax" "1.2.4"
    "stable" "0.1.8"
    "unquote" "1.1.1"
    "util.promisify" "1.0.1"

"symbol-tree@3.2.4":
  "integrity" "sha512-9QNk5KwDF+Bvz+PyObkmSYjI5ksVUYtjW7AU22r2NKcfLJcXp96hkDWU3+XndOsUb+AQ9QhfzfCT2O+CNWT5Tw=="
  "resolved" "https://registry.npmjs.org/symbol-tree/-/symbol-tree-3.2.4.tgz"
  "version" "3.2.4"

"table@5.4.6":
  "integrity" "sha512-wmEc8m4fjnob4gt5riFRtTu/6+4rSe12TpAELNSqHMfF3IqnA+CH37USM6/YR3qRZv7e56kAEAtd6nKZaxe0Ug=="
  "resolved" "https://registry.npmjs.org/table/-/table-5.4.6.tgz"
  "version" "5.4.6"
  dependencies:
    "ajv" "6.12.0"
    "lodash" "4.17.15"
    "slice-ansi" "2.1.0"
    "string-width" "3.1.0"

"tapable@1.1.3":
  "integrity" "sha512-4WK/bYZmj8xLr+HUCODHGF1ZFzsYffasLUgEiMBY4fgtltdO6B4WJtlSbPaDTLpYTcGVwM2qLnFTICEcNxs3kA=="
  "resolved" "https://registry.npmjs.org/tapable/-/tapable-1.1.3.tgz"
  "version" "1.1.3"

"terser-webpack-plugin@1.2.2":
  "integrity" "sha512-1DMkTk286BzmfylAvLXwpJrI7dWa5BnFmscV/2dCr8+c56egFcbaeFAl7+sujAjdmpLam21XRdhA4oifLyiWWg=="
  "resolved" "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "cacache" "11.3.3"
    "find-cache-dir" "2.1.0"
    "schema-utils" "1.0.0"
    "serialize-javascript" "1.9.1"
    "source-map" "0.6.1"
    "terser" "3.17.0"
    "webpack-sources" "1.4.3"
    "worker-farm" "1.7.0"

"terser@3.17.0":
  "integrity" "sha512-/FQzzPJmCpjAH9Xvk2paiWrFq+5M6aVOf+2KRbwhByISDX/EujxsK+BAvrhb6H+2rtrLCHK9N01wO014vrIwVQ=="
  "resolved" "https://registry.npmjs.org/terser/-/terser-3.17.0.tgz"
  "version" "3.17.0"
  dependencies:
    "commander" "2.20.3"
    "source-map" "0.6.1"
    "source-map-support" "0.5.16"

"test-exclude@4.2.3":
  "integrity" "sha512-SYbXgY64PT+4GAL2ocI3HwPa4Q4TBKm0cwAVeKOt/Aoc0gSpNRjJX8w0pA1LMKZ3LBmd8pYBqApFNQLII9kavA=="
  "resolved" "https://registry.npmjs.org/test-exclude/-/test-exclude-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "arrify" "1.0.1"
    "micromatch" "2.3.11"
    "object-assign" "4.1.1"
    "read-pkg-up" "1.0.1"
    "require-main-filename" "1.0.1"

"text-table@0.2.0":
  "integrity" "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ="
  "resolved" "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz"
  "version" "0.2.0"

"throat@4.1.0":
  "integrity" "sha1-iQN8vJLFarGJJua6TLsgDhVnKmo="
  "resolved" "https://registry.npmjs.org/throat/-/throat-4.1.0.tgz"
  "version" "4.1.0"

"through@2.3.8":
  "integrity" "sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU="
  "resolved" "https://registry.npmjs.org/through/-/through-2.3.8.tgz"
  "version" "2.3.8"

"through2@2.0.5":
  "integrity" "sha512-/mrRod8xqpA+IHSLyGCQ2s8SPHiCDEeQJSep1jqLYeEUClOFG2Qsh+4FU6G9VeqpZnGW/Su8LQGc4YKni5rYSQ=="
  "resolved" "https://registry.npmjs.org/through2/-/through2-2.0.5.tgz"
  "version" "2.0.5"
  dependencies:
    "readable-stream" "2.3.7"
    "xtend" "4.0.2"

"thunky@1.1.0":
  "integrity" "sha512-eHY7nBftgThBqOyHGVN+l8gF0BucP09fMo0oO/Lb0w1OF80dJv+lDVpXG60WMQvkcxAkNybKsrEIE3ZtKGmPrA=="
  "resolved" "https://registry.npmjs.org/thunky/-/thunky-1.1.0.tgz"
  "version" "1.1.0"

"timers-browserify@2.0.11":
  "integrity" "sha512-60aV6sgJ5YEbzUdn9c8kYGIqOubPoUdqQCul3SBAsRCZ40s6Y5cMcrW4dt3/k/EsbLVJNl9n6Vz3fTc+k2GeKQ=="
  "resolved" "https://registry.npmjs.org/timers-browserify/-/timers-browserify-2.0.11.tgz"
  "version" "2.0.11"
  dependencies:
    "setimmediate" "1.0.5"

"timsort@0.3.0":
  "integrity" "sha1-QFQRqOfmM5/mTbmiNN4R3DHgK9Q="
  "resolved" "https://registry.npmjs.org/timsort/-/timsort-0.3.0.tgz"
  "version" "0.3.0"

"tiny-invariant@1.1.0":
  "integrity" "sha512-ytxQvrb1cPc9WBEI/HSeYYoGD0kWnGEOR8RY6KomWLBVhqz0RgTwVO9dLrGz7dC+nN9llyI7OKAgRq8Vq4ZBSw=="
  "resolved" "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.1.0.tgz"
  "version" "1.1.0"

"tiny-warning@1.0.3":
  "integrity" "sha512-lBN9zLN/oAf68o3zNXYrdCt1kP8WsiGW8Oo2ka41b2IM5JL/S1CTyX1rW0mb/zSuJun0ZUrDxx4sqvYS2FWzPA=="
  "resolved" "https://registry.npmjs.org/tiny-warning/-/tiny-warning-1.0.3.tgz"
  "version" "1.0.3"

"tinycolor2@1.4.1":
  "integrity" "sha1-9PrTM0R7wLB9TcjpIJ2POaisd+g="
  "resolved" "https://registry.npmjs.org/tinycolor2/-/tinycolor2-1.4.1.tgz"
  "version" "1.4.1"

"tmp@0.0.33":
  "integrity" "sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw=="
  "resolved" "https://registry.npmjs.org/tmp/-/tmp-0.0.33.tgz"
  "version" "0.0.33"
  dependencies:
    "os-tmpdir" "1.0.2"

"tmpl@1.0.4":
  "integrity" "sha1-I2QN17QtAEM5ERQIIOXPRA5SHdE="
  "resolved" "https://registry.npmjs.org/tmpl/-/tmpl-1.0.4.tgz"
  "version" "1.0.4"

"to-arraybuffer@1.0.1":
  "integrity" "sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M="
  "resolved" "https://registry.npmjs.org/to-arraybuffer/-/to-arraybuffer-1.0.1.tgz"
  "version" "1.0.1"

"to-fast-properties@1.0.3":
  "integrity" "sha1-uDVx+k2MJbguIxsG46MFXeTKGkc="
  "resolved" "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-1.0.3.tgz"
  "version" "1.0.3"

"to-fast-properties@2.0.0":
  "integrity" "sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4="
  "resolved" "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-2.0.0.tgz"
  "version" "2.0.0"

"to-object-path@0.3.0":
  "integrity" "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68="
  "resolved" "https://registry.npmjs.org/to-object-path/-/to-object-path-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "kind-of" "3.2.2"

"to-regex-range@2.1.1":
  "integrity" "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg="
  "resolved" "https://registry.npmjs.org/to-regex-range/-/to-regex-range-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-number" "3.0.0"
    "repeat-string" "1.6.1"

"to-regex@3.0.2":
  "integrity" "sha512-FWtleNAtZ/Ki2qtqej2CXTOayOH9bHDQF+Q48VpWyDXjbYxA4Yz8iDB31zXOBUlOHHKidDbqGVrTUvQMPmBGBw=="
  "resolved" "https://registry.npmjs.org/to-regex/-/to-regex-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "define-property" "2.0.2"
    "extend-shallow" "3.0.2"
    "regex-not" "1.0.2"
    "safe-regex" "1.1.0"

"toggle-selection@1.0.6":
  "integrity" "sha1-bkWxJj8gF/oKzH2J14sVuL932jI="
  "resolved" "https://registry.npmjs.org/toggle-selection/-/toggle-selection-1.0.6.tgz"
  "version" "1.0.6"

"toidentifier@1.0.0":
  "integrity" "sha512-yaOH/Pk/VEhBWWTlhI+qXxDFXlejDGcQipMlyxda9nthulaxLZUNcUqFxokp0vcYnvteJln5FNQDRrxj3YcbVw=="
  "resolved" "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.0.tgz"
  "version" "1.0.0"

"topo@2.0.2":
  "integrity" "sha1-zVYVdSU5BXwNwEkaYhw7xvvh0YI="
  "resolved" "https://registry.npmjs.org/topo/-/topo-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "hoek" "4.2.1"

"tough-cookie@2.5.0":
  "integrity" "sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g=="
  "resolved" "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "psl" "1.7.0"
    "punycode" "2.1.1"

"tr46@1.0.1":
  "integrity" "sha1-qLE/1r/SSJUZZ0zN5VujaTtwbQk="
  "resolved" "https://registry.npmjs.org/tr46/-/tr46-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "punycode" "2.1.1"

"trim-right@1.0.1":
  "integrity" "sha1-yy4SAwZ+DI3h9hQJS5/kVwTqYAM="
  "resolved" "https://registry.npmjs.org/trim-right/-/trim-right-1.0.1.tgz"
  "version" "1.0.1"

"tryer@1.0.1":
  "integrity" "sha512-c3zayb8/kWWpycWYg87P71E1S1ZL6b6IJxfb5fvsUgsf0S2MVGaDhDXXjDMpdCpfWXqptc+4mXwmiy1ypXqRAA=="
  "resolved" "https://registry.npmjs.org/tryer/-/tryer-1.0.1.tgz"
  "version" "1.0.1"

"ts-pnp@1.1.6":
  "integrity" "sha512-CrG5GqAAzMT7144Cl+UIFP7mz/iIhiy+xQ6GGcnjTezhALT02uPMRw7tgDSESgB5MsfKt55+GPWw4ir1kVtMIQ=="
  "resolved" "https://registry.npmjs.org/ts-pnp/-/ts-pnp-1.1.6.tgz"
  "version" "1.1.6"

"tslib@1.11.1":
  "integrity" "sha512-aZW88SY8kQbU7gpV19lN24LtXh/yD4ZZg6qieAJDDg+YBsJcSmLGK9QpnUjAKVG/xefmvJGd1WUmfpT/g6AJGA=="
  "resolved" "https://registry.npmjs.org/tslib/-/tslib-1.11.1.tgz"
  "version" "1.11.1"

"tty-browserify@0.0.0":
  "integrity" "sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY="
  "resolved" "https://registry.npmjs.org/tty-browserify/-/tty-browserify-0.0.0.tgz"
  "version" "0.0.0"

"tunnel-agent@0.6.0":
  "integrity" "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0="
  "resolved" "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.6.0.tgz"
  "version" "0.6.0"
  dependencies:
    "safe-buffer" "5.2.0"

"tweetnacl@0.14.5":
  "integrity" "sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q="
  "resolved" "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz"
  "version" "0.14.5"

"type-check@0.3.2":
  "integrity" "sha1-WITKtRLPHTVeP7eE8wgEsrUg23I="
  "resolved" "https://registry.npmjs.org/type-check/-/type-check-0.3.2.tgz"
  "version" "0.3.2"
  dependencies:
    "prelude-ls" "1.1.2"

"type-is@1.6.18":
  "integrity" "sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g=="
  "resolved" "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz"
  "version" "1.6.18"
  dependencies:
    "media-typer" "0.3.0"
    "mime-types" "2.1.26"

"typedarray@0.0.6":
  "integrity" "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c="
  "resolved" "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz"
  "version" "0.0.6"

"ua-parser-js@0.7.21":
  "integrity" "sha512-+O8/qh/Qj8CgC6eYBVBykMrNtp5Gebn4dlGD/kKXVkJNDwyrAwSIqwz8CDf+tsAIWVycKcku6gIXJ0qwx/ZXaQ=="
  "resolved" "https://registry.npmjs.org/ua-parser-js/-/ua-parser-js-0.7.21.tgz"
  "version" "0.7.21"

"uglify-js@3.4.10":
  "integrity" "sha512-Y2VsbPVs0FIshJztycsO2SfPk7/KAF/T72qzv9u5EpQ4kB2hQoHlhNQTsNyy6ul7lQtqJN/AoWeS23OzEiEFxw=="
  "resolved" "https://registry.npmjs.org/uglify-js/-/uglify-js-3.4.10.tgz"
  "version" "3.4.10"
  dependencies:
    "commander" "2.19.0"
    "source-map" "0.6.1"

"unicode-canonical-property-names-ecmascript@1.0.4":
  "integrity" "sha512-jDrNnXWHd4oHiTZnx/ZG7gtUTVp+gCcTTKr8L0HjlwphROEW3+Him+IpvC+xcJEFegapiMZyZe02CyuOnRmbnQ=="
  "resolved" "https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-1.0.4.tgz"
  "version" "1.0.4"

"unicode-match-property-ecmascript@1.0.4":
  "integrity" "sha512-L4Qoh15vTfntsn4P1zqnHulG0LdXgjSO035fEpdtp6YxXhMT51Q6vgM5lYdG/5X3MjS+k/Y9Xw4SFCY9IkR0rg=="
  "resolved" "https://registry.npmjs.org/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "unicode-canonical-property-names-ecmascript" "1.0.4"
    "unicode-property-aliases-ecmascript" "1.0.5"

"unicode-match-property-value-ecmascript@1.1.0":
  "integrity" "sha512-hDTHvaBk3RmFzvSl0UVrUmC3PuW9wKVnpoUDYH0JDkSIovzw+J5viQmeYHxVSBptubnr7PbH2e0fnpDRQnQl5g=="
  "resolved" "https://registry.npmjs.org/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-1.1.0.tgz"
  "version" "1.1.0"

"unicode-property-aliases-ecmascript@1.0.5":
  "integrity" "sha512-L5RAqCfXqAwR3RriF8pM0lU0w4Ryf/GgzONwi6KnL1taJQa7x1TCxdJnILX59WIGOwR57IVxn7Nej0fz1Ny6fw=="
  "resolved" "https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-1.0.5.tgz"
  "version" "1.0.5"

"union-value@1.0.1":
  "integrity" "sha512-tJfXmxMeWYnczCVs7XAEvIV7ieppALdyepWMkHkwciRpZraG/xwT+s2JN8+pr1+8jCRf80FFzvr+MpQeeoF4Xg=="
  "resolved" "https://registry.npmjs.org/union-value/-/union-value-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "arr-union" "3.1.0"
    "get-value" "2.0.6"
    "is-extendable" "0.1.1"
    "set-value" "2.0.1"

"uniq@1.0.1":
  "integrity" "sha1-sxxa6CVIRKOoKBVBzisEuGWnNP8="
  "resolved" "https://registry.npmjs.org/uniq/-/uniq-1.0.1.tgz"
  "version" "1.0.1"

"uniqs@2.0.0":
  "integrity" "sha1-/+3ks2slKQaW5uFl1KWe25mOawI="
  "resolved" "https://registry.npmjs.org/uniqs/-/uniqs-2.0.0.tgz"
  "version" "2.0.0"

"unique-filename@1.1.1":
  "integrity" "sha512-Vmp0jIp2ln35UTXuryvjzkjGdRyf9b2lTXuSYUiPmzRcl3FDtYqAwOnTJkAngD9SWhnoJzDbTKwaOrZ+STtxNQ=="
  "resolved" "https://registry.npmjs.org/unique-filename/-/unique-filename-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "unique-slug" "2.0.2"

"unique-slug@2.0.2":
  "integrity" "sha512-zoWr9ObaxALD3DOPfjPSqxt4fnZiWblxHIgeWqW8x7UqDzEtHEQLzji2cuJYQFCU6KmoJikOYAZlrTHHebjx2w=="
  "resolved" "https://registry.npmjs.org/unique-slug/-/unique-slug-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "imurmurhash" "0.1.4"

"universalify@0.1.2":
  "integrity" "sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg=="
  "resolved" "https://registry.npmjs.org/universalify/-/universalify-0.1.2.tgz"
  "version" "0.1.2"

"unpipe@1.0.0":
  "integrity" "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw="
  "resolved" "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz"
  "version" "1.0.0"

"unquote@1.1.1":
  "integrity" "sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ="
  "resolved" "https://registry.npmjs.org/unquote/-/unquote-1.1.1.tgz"
  "version" "1.1.1"

"unset-value@1.0.0":
  "integrity" "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk="
  "resolved" "https://registry.npmjs.org/unset-value/-/unset-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-value" "0.3.1"
    "isobject" "3.0.1"

"upath@1.2.0":
  "integrity" "sha512-aZwGpamFO61g3OlfT7OQCHqhGnW43ieH9WZeP7QxN/G/jS4jfqUkZxoryvJgVPEcrl5NL/ggHsSmLMHuH64Lhg=="
  "resolved" "https://registry.npmjs.org/upath/-/upath-1.2.0.tgz"
  "version" "1.2.0"

"upper-case@1.1.3":
  "integrity" "sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg="
  "resolved" "https://registry.npmjs.org/upper-case/-/upper-case-1.1.3.tgz"
  "version" "1.1.3"

"uri-js@4.2.2":
  "integrity" "sha512-KY9Frmirql91X2Qgjry0Wd4Y+YTdrdZheS8TFwvkbLWf/G5KNJDCh6pKL5OZctEW4+0Baa5idK2ZQuELRwPznQ=="
  "resolved" "https://registry.npmjs.org/uri-js/-/uri-js-4.2.2.tgz"
  "version" "4.2.2"
  dependencies:
    "punycode" "2.1.1"

"urix@0.1.0":
  "integrity" "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI="
  "resolved" "https://registry.npmjs.org/urix/-/urix-0.1.0.tgz"
  "version" "0.1.0"

"url-loader@1.1.2":
  "integrity" "sha512-dXHkKmw8FhPqu8asTc1puBfe3TehOCo2+RmOOev5suNCIYBcT626kxiWg1NBVkwc4rO8BGa7gP70W7VXuqHrjg=="
  "resolved" "https://registry.npmjs.org/url-loader/-/url-loader-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "loader-utils" "1.4.0"
    "mime" "2.4.4"
    "schema-utils" "1.0.0"

"url-parse@1.4.7":
  "integrity" "sha512-d3uaVyzDB9tQoSXFvuSUNFibTd9zxd2bkVrDRvF5TmvWWQwqE4lgYJ5m+x1DbecWkw+LK4RNl2CU1hHuOKPVlg=="
  "resolved" "https://registry.npmjs.org/url-parse/-/url-parse-1.4.7.tgz"
  "version" "1.4.7"
  dependencies:
    "querystringify" "2.1.1"
    "requires-port" "1.0.0"

"url@0.11.0":
  "integrity" "sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE="
  "resolved" "https://registry.npmjs.org/url/-/url-0.11.0.tgz"
  "version" "0.11.0"
  dependencies:
    "punycode" "1.3.2"
    "querystring" "0.2.0"

"use@3.1.1":
  "integrity" "sha512-cwESVXlO3url9YWlFW/TA9cshCEhtu7IKJ/p5soJ/gGpj7vbvFrAY/eIioQ6Dw23KjZhYgiIo8HOs1nQ2vr/oQ=="
  "resolved" "https://registry.npmjs.org/use/-/use-3.1.1.tgz"
  "version" "3.1.1"

"util-deprecate@1.0.2":
  "integrity" "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="
  "resolved" "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"util.promisify@1.0.0":
  "integrity" "sha512-i+6qA2MPhvoKLuxnJNpXAGhg7HphQOSUq2LKMZD0m15EiskXUkMvKdF4Uui0WYeCUGea+o2cw/ZuwehtfsrNkA=="
  "resolved" "https://registry.npmjs.org/util.promisify/-/util.promisify-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "define-properties" "1.1.3"
    "object.getownpropertydescriptors" "2.1.0"

"util.promisify@1.0.1":
  "integrity" "sha512-g9JpC/3He3bm38zsLupWryXHoEcS22YHthuPQSJdMy6KNrzIRzWqcsHzD/WUnqe45whVou4VIsPew37DoXWNrA=="
  "resolved" "https://registry.npmjs.org/util.promisify/-/util.promisify-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "define-properties" "1.1.3"
    "es-abstract" "1.17.4"
    "has-symbols" "1.0.1"
    "object.getownpropertydescriptors" "2.1.0"

"util@0.10.3":
  "integrity" "sha1-evsa/lCAUkZInj23/g7TeTNqwPk="
  "resolved" "https://registry.npmjs.org/util/-/util-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "inherits" "2.0.1"

"util@0.11.1":
  "integrity" "sha512-HShAsny+zS2TZfaXxD9tYj4HQGlBezXZMZuM/S5PKLLoZkShZiGk9o5CzukI1LVHZvjdvZ2Sj1aW/Ndn2NB/HQ=="
  "resolved" "https://registry.npmjs.org/util/-/util-0.11.1.tgz"
  "version" "0.11.1"
  dependencies:
    "inherits" "2.0.3"

"utila@0.4.0":
  "integrity" "sha1-ihagXURWV6Oupe7MWxKk+lN5dyw="
  "resolved" "https://registry.npmjs.org/utila/-/utila-0.4.0.tgz"
  "version" "0.4.0"

"utils-merge@1.0.1":
  "integrity" "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM="
  "resolved" "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz"
  "version" "1.0.1"

"uuid@3.4.0":
  "integrity" "sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A=="
  "resolved" "https://registry.npmjs.org/uuid/-/uuid-3.4.0.tgz"
  "version" "3.4.0"

"validate-npm-package-license@3.0.4":
  "integrity" "sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew=="
  "resolved" "https://registry.npmjs.org/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "spdx-correct" "3.1.0"
    "spdx-expression-parse" "3.0.0"

"value-equal@1.0.1":
  "integrity" "sha512-NOJ6JZCAWr0zlxZt+xqCHNTEKOsrks2HQd4MqhP1qy4z1SkbEP467eNx6TgDKXMvUOb+OENfJCZwM+16n7fRfw=="
  "resolved" "https://registry.npmjs.org/value-equal/-/value-equal-1.0.1.tgz"
  "version" "1.0.1"

"vary@1.1.2":
  "integrity" "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw="
  "resolved" "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz"
  "version" "1.1.2"

"vendors@1.0.4":
  "integrity" "sha512-/juG65kTL4Cy2su4P8HjtkTxk6VmJDiOPBufWniqQ6wknac6jNiXS9vU+hO3wgusiyqWlzTbVHi0dyJqRONg3w=="
  "resolved" "https://registry.npmjs.org/vendors/-/vendors-1.0.4.tgz"
  "version" "1.0.4"

"verror@1.10.0":
  "integrity" "sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA="
  "resolved" "https://registry.npmjs.org/verror/-/verror-1.10.0.tgz"
  "version" "1.10.0"
  dependencies:
    "assert-plus" "1.0.0"
    "core-util-is" "1.0.2"
    "extsprintf" "1.3.0"

"vm-browserify@1.1.2":
  "integrity" "sha512-2ham8XPWTONajOR0ohOKOHXkm3+gaBmGut3SRuu75xLd/RRaY6vqgh8NBYYk7+RW3u5AtzPQZG8F10LHkl0lAQ=="
  "resolved" "https://registry.npmjs.org/vm-browserify/-/vm-browserify-1.1.2.tgz"
  "version" "1.1.2"

"w3c-hr-time@1.0.2":
  "integrity" "sha512-z8P5DvDNjKDoFIHK7q8r8lackT6l+jo/Ye3HOle7l9nICP9lf1Ci25fy9vHd0JOWewkIFzXIEig3TdKT7JQ5fQ=="
  "resolved" "https://registry.npmjs.org/w3c-hr-time/-/w3c-hr-time-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "browser-process-hrtime" "1.0.0"

"walker@1.0.7":
  "integrity" "sha1-L3+bj9ENZ3JisYqITijRlhjgKPs="
  "resolved" "https://registry.npmjs.org/walker/-/walker-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "makeerror" "1.0.11"

"warning@4.0.3":
  "integrity" "sha512-rpJyN222KWIvHJ/F53XSZv0Zl/accqHR8et1kpaMTD/fLCRxtV8iX8czMzY7sVZupTI3zcUTg8eycS2kNF9l6w=="
  "resolved" "https://registry.npmjs.org/warning/-/warning-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "loose-envify" "1.4.0"

"watch@0.18.0":
  "integrity" "sha1-KAlUdsbffJDJYxOJkMClQj60uYY="
  "resolved" "https://registry.npmjs.org/watch/-/watch-0.18.0.tgz"
  "version" "0.18.0"
  dependencies:
    "exec-sh" "0.2.2"
    "minimist" "1.2.0"

"watchpack@1.6.0":
  "integrity" "sha512-i6dHe3EyLjMmDlU1/bGQpEw25XSjkJULPuAVKCbNRefQVq48yXKUpwg538F7AZTf9kyr57zj++pQFltUa5H7yA=="
  "resolved" "https://registry.npmjs.org/watchpack/-/watchpack-1.6.0.tgz"
  "version" "1.6.0"
  dependencies:
    "chokidar" "2.1.8"
    "graceful-fs" "4.2.3"
    "neo-async" "2.6.1"

"wbuf@1.7.3":
  "integrity" "sha512-O84QOnr0icsbFGLS0O3bI5FswxzRr8/gHwWkDlQFskhSPryQXvrTMxjxGP4+iWYoauLoBvfDpkrOauZ+0iZpDA=="
  "resolved" "https://registry.npmjs.org/wbuf/-/wbuf-1.7.3.tgz"
  "version" "1.7.3"
  dependencies:
    "minimalistic-assert" "1.0.1"

"webidl-conversions@4.0.2":
  "integrity" "sha512-YQ+BmxuTgd6UXZW3+ICGfyqRyHXVlD5GtQr5+qjiNW7bF0cqrzX500HVXPBOvgXb5YnzDd+h0zqyv61KUD7+Sg=="
  "resolved" "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-4.0.2.tgz"
  "version" "4.0.2"

"webpack-dev-middleware@3.4.0":
  "integrity" "sha512-Q9Iyc0X9dP9bAsYskAVJ/hmIZZQwf/3Sy4xCAZgL5cUkjZmUZLt4l5HpbST/Pdgjn3u6pE7u5OdGd1apgzRujA=="
  "resolved" "https://registry.npmjs.org/webpack-dev-middleware/-/webpack-dev-middleware-3.4.0.tgz"
  "version" "3.4.0"
  dependencies:
    "memory-fs" "0.4.1"
    "mime" "2.4.4"
    "range-parser" "1.2.1"
    "webpack-log" "2.0.0"

"webpack-dev-server@3.1.14":
  "integrity" "sha512-mGXDgz5SlTxcF3hUpfC8hrQ11yhAttuUQWf1Wmb+6zo3x6rb7b9mIfuQvAPLdfDRCGRGvakBWHdHOa0I9p/EVQ=="
  "resolved" "https://registry.npmjs.org/webpack-dev-server/-/webpack-dev-server-3.1.14.tgz"
  "version" "3.1.14"
  dependencies:
    "ansi-html" "0.0.7"
    "bonjour" "3.5.0"
    "chokidar" "2.1.8"
    "compression" "1.7.4"
    "connect-history-api-fallback" "1.6.0"
    "debug" "3.1.0"
    "del" "3.0.0"
    "express" "4.17.1"
    "html-entities" "1.2.1"
    "http-proxy-middleware" "0.18.0"
    "import-local" "2.0.0"
    "internal-ip" "3.0.1"
    "ip" "1.1.5"
    "killable" "1.0.1"
    "loglevel" "1.6.7"
    "opn" "5.4.0"
    "portfinder" "1.0.25"
    "schema-utils" "1.0.0"
    "selfsigned" "1.10.7"
    "semver" "5.7.1"
    "serve-index" "1.9.1"
    "sockjs" "0.3.19"
    "sockjs-client" "1.3.0"
    "spdy" "4.0.1"
    "strip-ansi" "3.0.1"
    "supports-color" "5.5.0"
    "url" "0.11.0"
    "webpack-dev-middleware" "3.4.0"
    "webpack-log" "2.0.0"
    "yargs" "12.0.2"

"webpack-log@2.0.0":
  "integrity" "sha512-cX8G2vR/85UYG59FgkoMamwHUIkSSlV3bBMRsbxVXVUk2j6NleCKjQ/WE9eYg9WY4w25O9w8wKP4rzNZFmUcUg=="
  "resolved" "https://registry.npmjs.org/webpack-log/-/webpack-log-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "ansi-colors" "3.2.4"
    "uuid" "3.4.0"

"webpack-manifest-plugin@2.0.4":
  "integrity" "sha512-nejhOHexXDBKQOj/5v5IZSfCeTO3x1Dt1RZEcGfBSul891X/eLIcIVH31gwxPDdsi2Z8LKKFGpM4w9+oTBOSCg=="
  "resolved" "https://registry.npmjs.org/webpack-manifest-plugin/-/webpack-manifest-plugin-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "fs-extra" "7.0.1"
    "lodash" "4.17.15"
    "tapable" "1.1.3"

"webpack-sources@1.4.3":
  "integrity" "sha512-lgTS3Xhv1lCOKo7SA5TjKXMjpSM4sBjNV5+q2bqesbSPs5FjGmU6jjtBSkX9b4qW87vDIsCIlUPOEhbZrMdjeQ=="
  "resolved" "https://registry.npmjs.org/webpack-sources/-/webpack-sources-1.4.3.tgz"
  "version" "1.4.3"
  dependencies:
    "source-list-map" "2.0.1"
    "source-map" "0.6.1"

"webpack@4.28.3":
  "integrity" "sha512-vLZN9k5I7Nr/XB1IDG9GbZB4yQd1sPuvufMFgJkx0b31fi2LD97KQIjwjxE7xytdruAYfu5S0FLBLjdxmwGJCg=="
  "resolved" "https://registry.npmjs.org/webpack/-/webpack-4.28.3.tgz"
  "version" "4.28.3"
  dependencies:
    "@webassemblyjs/ast" "1.7.11"
    "@webassemblyjs/helper-module-context" "1.7.11"
    "@webassemblyjs/wasm-edit" "1.7.11"
    "@webassemblyjs/wasm-parser" "1.7.11"
    "acorn" "5.7.3"
    "acorn-dynamic-import" "3.0.0"
    "ajv" "6.12.0"
    "ajv-keywords" "3.4.1"
    "chrome-trace-event" "1.0.2"
    "enhanced-resolve" "4.1.1"
    "eslint-scope" "4.0.3"
    "json-parse-better-errors" "1.0.2"
    "loader-runner" "2.4.0"
    "loader-utils" "1.4.0"
    "memory-fs" "0.4.1"
    "micromatch" "3.1.10"
    "mkdirp" "0.5.1"
    "neo-async" "2.6.1"
    "node-libs-browser" "2.2.1"
    "schema-utils" "0.4.7"
    "tapable" "1.1.3"
    "terser-webpack-plugin" "1.2.2"
    "watchpack" "1.6.0"
    "webpack-sources" "1.4.3"

"websocket-driver@0.7.3":
  "integrity" "sha512-bpxWlvbbB459Mlipc5GBzzZwhoZgGEZLuqPaR0INBGnPAY1vdBX6hPnoFXiw+3yWxDuHyQjO2oXTMyS8A5haFg=="
  "resolved" "https://registry.npmjs.org/websocket-driver/-/websocket-driver-0.7.3.tgz"
  "version" "0.7.3"
  dependencies:
    "http-parser-js" "0.4.10"
    "safe-buffer" "5.2.0"
    "websocket-extensions" "0.1.3"

"websocket-extensions@0.1.3":
  "integrity" "sha512-nqHUnMXmBzT0w570r2JpJxfiSD1IzoI+HGVdd3aZ0yNi3ngvQ4jv1dtHt5VGxfI2yj5yqImPhOK4vmIh2xMbGg=="
  "resolved" "https://registry.npmjs.org/websocket-extensions/-/websocket-extensions-0.1.3.tgz"
  "version" "0.1.3"

"whatwg-encoding@1.0.5":
  "integrity" "sha512-b5lim54JOPN9HtzvK9HFXvBma/rnfFeqsic0hSpjtDbVxR3dJKLc+KB4V6GgiGOvl7CY/KNh8rxSo9DKQrnUEw=="
  "resolved" "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "iconv-lite" "0.4.24"

"whatwg-fetch@3.0.0":
  "integrity" "sha512-9GSJUgz1D4MfyKU7KRqwOjXCXTqWdFNvEr7eUBYchQiVc744mqK/MzXPNR2WsPkmkOa4ywfg8C2n8h+13Bey1Q=="
  "resolved" "https://registry.npmjs.org/whatwg-fetch/-/whatwg-fetch-3.0.0.tgz"
  "version" "3.0.0"

"whatwg-mimetype@2.3.0":
  "integrity" "sha512-M4yMwr6mAnQz76TbJm914+gPpB/nCwvZbJU28cUD6dR004SAxDLOOSUaB1JDRqLtaOV/vi0IC5lEAGFgrjGv/g=="
  "resolved" "https://registry.npmjs.org/whatwg-mimetype/-/whatwg-mimetype-2.3.0.tgz"
  "version" "2.3.0"

"whatwg-url@6.5.0":
  "integrity" "sha512-rhRZRqx/TLJQWUpQ6bmrt2UV4f0HCQ463yQuONJqC6fO2VoEb1pTYddbe59SkYq87aoM5A3bdhMZiUiVws+fzQ=="
  "resolved" "https://registry.npmjs.org/whatwg-url/-/whatwg-url-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "lodash.sortby" "4.7.0"
    "tr46" "1.0.1"
    "webidl-conversions" "4.0.2"

"whatwg-url@7.1.0":
  "integrity" "sha512-WUu7Rg1DroM7oQvGWfOiAK21n74Gg+T4elXEQYkOhtyLeWiJFoOGLXPKI/9gzIie9CtwVLm8wtw6YJdKyxSjeg=="
  "resolved" "https://registry.npmjs.org/whatwg-url/-/whatwg-url-7.1.0.tgz"
  "version" "7.1.0"
  dependencies:
    "lodash.sortby" "4.7.0"
    "tr46" "1.0.1"
    "webidl-conversions" "4.0.2"

"which-module@2.0.0":
  "integrity" "sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho="
  "resolved" "https://registry.npmjs.org/which-module/-/which-module-2.0.0.tgz"
  "version" "2.0.0"

"which@1.3.1":
  "integrity" "sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ=="
  "resolved" "https://registry.npmjs.org/which/-/which-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "isexe" "2.0.0"

"word-wrap@1.2.3":
  "integrity" "sha512-Hz/mrNwitNRh/HUAtM/VT/5VH+ygD6DV7mYKZAtHOrbs8U7lvPS6xf7EJKMF0uW1KJCl0H701g3ZGus+muE5vQ=="
  "resolved" "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.3.tgz"
  "version" "1.2.3"

"wordwrap@0.0.3":
  "integrity" "sha1-o9XabNXAvAAI03I0u68b7WMFkQc="
  "resolved" "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.3.tgz"
  "version" "0.0.3"

"workbox-background-sync@3.6.3":
  "integrity" "sha512-ypLo0B6dces4gSpaslmDg5wuoUWrHHVJfFWwl1udvSylLdXvnrfhFfriCS42SNEe5lsZtcNZF27W/SMzBlva7Q=="
  "resolved" "https://registry.npmjs.org/workbox-background-sync/-/workbox-background-sync-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "workbox-core" "3.6.3"

"workbox-broadcast-cache-update@3.6.3":
  "integrity" "sha512-pJl4lbClQcvp0SyTiEw0zLSsVYE1RDlCPtpKnpMjxFtu8lCFTAEuVyzxp9w7GF4/b3P4h5nyQ+q7V9mIR7YzGg=="
  "resolved" "https://registry.npmjs.org/workbox-broadcast-cache-update/-/workbox-broadcast-cache-update-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "workbox-core" "3.6.3"

"workbox-build@3.6.3":
  "integrity" "sha512-w0clZ/pVjL8VXy6GfthefxpEXs0T8uiRuopZSFVQ8ovfbH6c6kUpEh6DcYwm/Y6dyWPiCucdyAZotgjz+nRz8g=="
  "resolved" "https://registry.npmjs.org/workbox-build/-/workbox-build-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "babel-runtime" "6.26.0"
    "common-tags" "1.8.0"
    "fs-extra" "4.0.3"
    "glob" "7.1.6"
    "joi" "11.4.0"
    "lodash.template" "4.5.0"
    "pretty-bytes" "4.0.2"
    "stringify-object" "3.3.0"
    "strip-comments" "1.0.2"
    "workbox-background-sync" "3.6.3"
    "workbox-broadcast-cache-update" "3.6.3"
    "workbox-cache-expiration" "3.6.3"
    "workbox-cacheable-response" "3.6.3"
    "workbox-core" "3.6.3"
    "workbox-google-analytics" "3.6.3"
    "workbox-navigation-preload" "3.6.3"
    "workbox-precaching" "3.6.3"
    "workbox-range-requests" "3.6.3"
    "workbox-routing" "3.6.3"
    "workbox-strategies" "3.6.3"
    "workbox-streams" "3.6.3"
    "workbox-sw" "3.6.3"

"workbox-cache-expiration@3.6.3":
  "integrity" "sha512-+ECNph/6doYx89oopO/UolYdDmQtGUgo8KCgluwBF/RieyA1ZOFKfrSiNjztxOrGJoyBB7raTIOlEEwZ1LaHoA=="
  "resolved" "https://registry.npmjs.org/workbox-cache-expiration/-/workbox-cache-expiration-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "workbox-core" "3.6.3"

"workbox-cacheable-response@3.6.3":
  "integrity" "sha512-QpmbGA9SLcA7fklBLm06C4zFg577Dt8u3QgLM0eMnnbaVv3rhm4vbmDpBkyTqvgK/Ly8MBDQzlXDtUCswQwqqg=="
  "resolved" "https://registry.npmjs.org/workbox-cacheable-response/-/workbox-cacheable-response-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "workbox-core" "3.6.3"

"workbox-core@3.6.3":
  "integrity" "sha512-cx9cx0nscPkIWs8Pt98HGrS9/aORuUcSkWjG25GqNWdvD/pSe7/5Oh3BKs0fC+rUshCiyLbxW54q0hA+GqZeSQ=="
  "resolved" "https://registry.npmjs.org/workbox-core/-/workbox-core-3.6.3.tgz"
  "version" "3.6.3"

"workbox-google-analytics@3.6.3":
  "integrity" "sha512-RQBUo/6SXtIaQTRFj4RQZ9e1gAl7D8oS5S+Hi173Kk70/BgJjzPwXpC5A249Jv5YfkCOLMQCeF9A27BiD0b0ig=="
  "resolved" "https://registry.npmjs.org/workbox-google-analytics/-/workbox-google-analytics-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "workbox-background-sync" "3.6.3"
    "workbox-core" "3.6.3"
    "workbox-routing" "3.6.3"
    "workbox-strategies" "3.6.3"

"workbox-navigation-preload@3.6.3":
  "integrity" "sha512-dd26xTX16DUu0i+MhqZK/jQXgfIitu0yATM4jhRXEmpMqQ4MxEeNvl2CgjDMOHBnCVMax+CFZQWwxMx/X/PqCw=="
  "resolved" "https://registry.npmjs.org/workbox-navigation-preload/-/workbox-navigation-preload-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "workbox-core" "3.6.3"

"workbox-precaching@3.6.3":
  "integrity" "sha512-aBqT66BuMFviPTW6IpccZZHzpA8xzvZU2OM1AdhmSlYDXOJyb1+Z6blVD7z2Q8VNtV1UVwQIdImIX+hH3C3PIw=="
  "resolved" "https://registry.npmjs.org/workbox-precaching/-/workbox-precaching-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "workbox-core" "3.6.3"

"workbox-range-requests@3.6.3":
  "integrity" "sha512-R+yLWQy7D9aRF9yJ3QzwYnGFnGDhMUij4jVBUVtkl67oaVoP1ymZ81AfCmfZro2kpPRI+vmNMfxxW531cqdx8A=="
  "resolved" "https://registry.npmjs.org/workbox-range-requests/-/workbox-range-requests-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "workbox-core" "3.6.3"

"workbox-routing@3.6.3":
  "integrity" "sha512-bX20i95OKXXQovXhFOViOK63HYmXvsIwZXKWbSpVeKToxMrp0G/6LZXnhg82ijj/S5yhKNRf9LeGDzaqxzAwMQ=="
  "resolved" "https://registry.npmjs.org/workbox-routing/-/workbox-routing-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "workbox-core" "3.6.3"

"workbox-strategies@3.6.3":
  "integrity" "sha512-Pg5eulqeKet2y8j73Yw6xTgLdElktcWExGkzDVCGqfV9JCvnGuEpz5eVsCIK70+k4oJcBCin9qEg3g3CwEIH3g=="
  "resolved" "https://registry.npmjs.org/workbox-strategies/-/workbox-strategies-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "workbox-core" "3.6.3"

"workbox-streams@3.6.3":
  "integrity" "sha512-rqDuS4duj+3aZUYI1LsrD2t9hHOjwPqnUIfrXSOxSVjVn83W2MisDF2Bj+dFUZv4GalL9xqErcFW++9gH+Z27w=="
  "resolved" "https://registry.npmjs.org/workbox-streams/-/workbox-streams-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "workbox-core" "3.6.3"

"workbox-sw@3.6.3":
  "integrity" "sha512-IQOUi+RLhvYCiv80RP23KBW/NTtIvzvjex28B8NW1jOm+iV4VIu3VXKXTA6er5/wjjuhmtB28qEAUqADLAyOSg=="
  "resolved" "https://registry.npmjs.org/workbox-sw/-/workbox-sw-3.6.3.tgz"
  "version" "3.6.3"

"workbox-webpack-plugin@3.6.3":
  "integrity" "sha512-RwmKjc7HFHUFHoOlKoZUq9349u0QN3F8W5tZZU0vc1qsBZDINWXRiIBCAKvo/Njgay5sWz7z4I2adnyTo97qIQ=="
  "resolved" "https://registry.npmjs.org/workbox-webpack-plugin/-/workbox-webpack-plugin-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "babel-runtime" "6.26.0"
    "json-stable-stringify" "1.0.1"
    "workbox-build" "3.6.3"

"worker-farm@1.7.0":
  "integrity" "sha512-rvw3QTZc8lAxyVrqcSGVm5yP/IJ2UcB3U0graE3LCFoZ0Yn2x4EoVSqJKdB/T5M+FLcRPjz4TDacRf3OCfNUzw=="
  "resolved" "https://registry.npmjs.org/worker-farm/-/worker-farm-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "errno" "0.1.7"

"wrap-ansi@2.1.0":
  "integrity" "sha1-2Pw9KE3QV5T+hJc8rs3Rz4JP3YU="
  "resolved" "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "string-width" "1.0.2"
    "strip-ansi" "3.0.1"

"wrappy@1.0.2":
  "integrity" "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="
  "resolved" "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
  "version" "1.0.2"

"write-file-atomic@2.4.3":
  "integrity" "sha512-GaETH5wwsX+GcnzhPgKcKjJ6M2Cq3/iZp1WyY/X1CSqrW+jVNM9Y7D8EC2sM4ZG/V8wZlSniJnCKWPmBYAucRQ=="
  "resolved" "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-2.4.3.tgz"
  "version" "2.4.3"
  dependencies:
    "graceful-fs" "4.2.3"
    "imurmurhash" "0.1.4"
    "signal-exit" "3.0.2"

"write@0.2.1":
  "integrity" "sha1-X8A4KOJkzqP+kUVUdvejxWbLB1c="
  "resolved" "https://registry.npmjs.org/write/-/write-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "mkdirp" "0.5.1"

"ws@5.2.2":
  "integrity" "sha512-jaHFD6PFv6UgoIVda6qZllptQsMlDEJkTQcybzzXDYM1XO9Y8em691FGMPmM46WGyLU4z9KMgQN+qrux/nhlHA=="
  "resolved" "https://registry.npmjs.org/ws/-/ws-5.2.2.tgz"
  "version" "5.2.2"
  dependencies:
    "async-limiter" "1.0.1"

"xml-name-validator@3.0.0":
  "integrity" "sha512-A5CUptxDsvxKJEU3yO6DuWBSJz/qizqzJKOMIfUJHETbBw/sFaDxgd6fxm1ewUaM0jZ444Fc5vC5ROYurg/4Pw=="
  "resolved" "https://registry.npmjs.org/xml-name-validator/-/xml-name-validator-3.0.0.tgz"
  "version" "3.0.0"

"xregexp@4.0.0":
  "integrity" "sha512-PHyM+sQouu7xspQQwELlGwwd05mXUFqwFYfqPO0cC7x4fxyHnnuetmQr6CjJiafIDoH4MogHb9dOoJzR/Y4rFg=="
  "resolved" "https://registry.npmjs.org/xregexp/-/xregexp-4.0.0.tgz"
  "version" "4.0.0"

"xtend@4.0.2":
  "integrity" "sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ=="
  "resolved" "https://registry.npmjs.org/xtend/-/xtend-4.0.2.tgz"
  "version" "4.0.2"

"y18n@3.2.1":
  "integrity" "sha1-bRX7qITAhnnA136I53WegR4H+kE="
  "resolved" "https://registry.npmjs.org/y18n/-/y18n-3.2.1.tgz"
  "version" "3.2.1"

"y18n@4.0.0":
  "integrity" "sha512-r9S/ZyXu/Xu9q1tYlpsLIsa3EeLXXk0VwlxqTcFRfg9EhMW+17kbt9G0NrgCmhGb5vT2hyhJZLfDGx+7+5Uj/w=="
  "resolved" "https://registry.npmjs.org/y18n/-/y18n-4.0.0.tgz"
  "version" "4.0.0"

"yallist@3.1.1":
  "integrity" "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g=="
  "resolved" "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz"
  "version" "3.1.1"

"yargs-parser@10.1.0":
  "integrity" "sha512-VCIyR1wJoEBZUqk5PA+oOBF6ypbwh5aNB3I50guxAL/quggdfs4TtNHQrSazFA3fYZ+tEqfs0zIGlv0c/rgjbQ=="
  "resolved" "https://registry.npmjs.org/yargs-parser/-/yargs-parser-10.1.0.tgz"
  "version" "10.1.0"
  dependencies:
    "camelcase" "4.1.0"

"yargs-parser@9.0.2":
  "integrity" "sha1-nM9qQ0YP5O1Aqbto9I1DuKaMwHc="
  "resolved" "https://registry.npmjs.org/yargs-parser/-/yargs-parser-9.0.2.tgz"
  "version" "9.0.2"
  dependencies:
    "camelcase" "4.1.0"

"yargs@11.1.1":
  "integrity" "sha512-PRU7gJrJaXv3q3yQZ/+/X6KBswZiaQ+zOmdprZcouPYtQgvNU35i+68M4b1ZHLZtYFT5QObFLV+ZkmJYcwKdiw=="
  "resolved" "https://registry.npmjs.org/yargs/-/yargs-11.1.1.tgz"
  "version" "11.1.1"
  dependencies:
    "cliui" "4.1.0"
    "decamelize" "1.2.0"
    "find-up" "2.1.0"
    "get-caller-file" "1.0.3"
    "os-locale" "3.1.0"
    "require-directory" "2.1.1"
    "require-main-filename" "1.0.1"
    "set-blocking" "2.0.0"
    "string-width" "2.1.1"
    "which-module" "2.0.0"
    "y18n" "3.2.1"
    "yargs-parser" "9.0.2"

"yargs@12.0.2":
  "integrity" "sha512-e7SkEx6N6SIZ5c5H22RTZae61qtn3PYUE8JYbBFlK9sYmh3DMQ6E5ygtaG/2BW0JZi4WGgTR2IV5ChqlqrDGVQ=="
  "resolved" "https://registry.npmjs.org/yargs/-/yargs-12.0.2.tgz"
  "version" "12.0.2"
  dependencies:
    "cliui" "4.1.0"
    "decamelize" "2.0.0"
    "find-up" "3.0.0"
    "get-caller-file" "1.0.3"
    "os-locale" "3.1.0"
    "require-directory" "2.1.1"
    "require-main-filename" "1.0.1"
    "set-blocking" "2.0.0"
    "string-width" "2.1.1"
    "which-module" "2.0.0"
    "y18n" "3.2.1"
    "yargs-parser" "10.1.0"

"zrender@4.3.2":
  "integrity" "sha512-bIusJLS8c4DkIcdiK+s13HiQ/zjQQVgpNohtd8d94Y2DnJqgM1yjh/jpDb8DoL6hd7r8Awagw8e3qK/oLaWr3g=="
  "resolved" "https://registry.npmjs.org/zrender/-/zrender-4.3.2.tgz"
  "version" "4.3.2"
