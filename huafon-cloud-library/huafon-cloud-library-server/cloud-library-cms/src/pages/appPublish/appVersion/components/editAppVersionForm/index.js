import React, { Component } from 'react';
import { Form, Input, Select, Modal, DatePicker, message, Upload, Button, Icon, Switch } from "antd";
import moment from "moment";
import axios from "../../../../../axios";
import { compose } from 'redux';
import { apply } from 'file-loader';
const FormItem = Form.Item;
const { Option } = Select;
const { TextArea } = Input;

let isAllow = true;
let size = 0;
let originSize = 0;
export default class EditAppVersionForm extends Component {

    state = {
        fileList: [],
    };
    beforeUpload(file) {
        const isJpgOrPng = file.type === 'application/vnd.android.package-archive'
        if (!isJpgOrPng) {
            message.error('只支持apk文件格式');
            isAllow = false;
        } else {
            size=Math.round(file.size / 1024 / 1024);
            originSize = file.size;
            isAllow = true
        }
    }

    handleChange = info => {
        if (isAllow === true) {

            if (info.file.status !== 'uploading') {
                console.log(info.file, info.fileList)
            }
            if (info.file.status === 'done') {
                console.log(`${info.file.name} 文件上传成功`)
            } else if (info.file.status === 'error') {
                message.error(`${info.file.name} 文件上传失败`)
            }
            let fileList = [...info.fileList];

            fileList = fileList.slice(-1);

            // 2. Read from response and show file link
            fileList = fileList.map(file => {
                if (file.response) {
                    // Component will show file.url as link
                    file.url = file.response.data;
                }
                let newFile = {
                    uid: file.uid,
                    name: file.name,
                    status: 'done',
                    url: file.url,
                }
                return newFile;
            });

            this.setState({ fileList });
        }
    };

    handleCancel = () => {
        let fileList = [];
        this.setState({ fileList });
        this.props.handleCancel();
        size=0;
        originSize=0;
    }
    render() {
        const props = {
            action: window.location.origin + '/file/upload',
            onChange: this.handleChange,
            beforeUpload: this.beforeUpload,
            listType: 'picture',
            multiple: true,
        };
        const { visible, handleCancel } = this.props;
        const { getFieldDecorator } = this.props.form;

        const record = this.props.record || {};
        const formItemLayout = {
            labelCol: { span: 5 },
            wrapperCol: { span: 19 },
        };
        const { startValue, endValue, endOpen } = this.state;
        return (
            <Modal
                title="编辑版本"
                visible={visible}
                onCancel={this.handleCancel}
                onOk={this.editData}
                okText="确认"
                cancelText="取消"
                width={900}
            >
                <Form layout="horizontal">
                    <FormItem
                        style={{ display: "none" }}
                        label="隐藏版本id"
                        {...formItemLayout}
                    >
                        {getFieldDecorator("id", { initialValue: record.id || undefined })(
                            <Input type="text" placeholder="" />
                        )}
                    </FormItem>

                    <FormItem
                        style={{ display: "none" }}
                        label="隐藏版本size"
                        {...formItemLayout}
                    >
                        {getFieldDecorator("size", { initialValue: record.size || undefined })(
                            <Input type="text" placeholder="" />
                        )}
                    </FormItem>

                    <FormItem
                        style={{ display: "none" }}
                        label="隐藏版本origin"
                        {...formItemLayout}
                    >
                        {getFieldDecorator("originSize", { initialValue: record.originSize || undefined })(
                            <Input type="text" placeholder="" />
                        )}
                    </FormItem>
                    <FormItem
                        style={{ display: "none" }}
                        label="隐藏版本applicationId"
                        {...formItemLayout}
                    >
                        {getFieldDecorator("applicationId", { initialValue: record.applicationId || undefined })(
                            <Input type="text" placeholder="" />
                        )}
                    </FormItem>

                    <FormItem label="选择应用" {...formItemLayout}>
                        {getFieldDecorator("appName", {
                            initialValue: record.appName || undefined,
                            rules: [
                                {
                                    required: true,
                                    message: "应用不能为空",
                                },
                            ],
                        })(<Input disabled type="text" placeholder="" />)}
                    </FormItem>

                    <Form.Item label="上传安装包" {...formItemLayout}>
                        {
                            getFieldDecorator('apkUrl', {
                                initialValue: ""
                            })(
                                <Upload  {...props} fileList={this.state.fileList.length === 0 ? this.props.fileList : this.state.fileList}>
                                    <Button>
                                        <Icon type="upload" /> Upload
                                    </Button>
                                </Upload>
                            )
                        }
                    </Form.Item>

                    <FormItem label="版本号" {...formItemLayout}>
                        {getFieldDecorator("versionNum", {
                            initialValue: record.versionNum || undefined,
                            rules: [
                                {
                                    required: true,
                                    message: "版本号不能为空",
                                },
                            ],
                        })(<Input type="text" placeholder="请输入版本号" />)}
                    </FormItem>

                    <FormItem label="build" {...formItemLayout}>
                        {getFieldDecorator("buildNum", {
                            initialValue: record.buildNum || undefined,
                            rules: [
                                {
                                    required: true,
                                    message: "构建次数不能为空",
                                },
                            ],
                        })(<Input disabled type="text" placeholder="请输入构建次数" />)}
                    </FormItem>

                    <FormItem label="更新描述" {...formItemLayout}>
                        {getFieldDecorator("describe", {
                            initialValue: record.describe || undefined,
                            rules: [
                                {
                                    required: true,
                                    message: "更新描述不能为空",
                                },
                            ],
                        })(<TextArea type="text" placeholder="请输入更新描述" />)}
                    </FormItem>
                    <FormItem label="" {...formItemLayout}>
                        {getFieldDecorator("isCurrent", {
                            valuePropName: "checked",
                            initialValue: record.isCurrent === 1
                        })(<Switch
                            onChange={this.onChange}
                            style={{ float: "left", marginLeft: 100, marginTop: 10 }}
                            checkedChildren={<Icon type="check" />}
                            unCheckedChildren={<Icon type="close" />}

                        />)}<span style={{ fontSize: 16, marginLeft: 3 }}>版本创建成功后，即设置为当前版本</span>
                    </FormItem>
                </Form>
            </Modal>
        );
    }
    editData = () => {
        this.props.form.validateFields((err, values) => {
            if ((this.state.fileList.length !== 0
                && this.state.fileList[0].url !== undefined)
                || (this.props.fileList.length !== 0 &&
                    this.props.fileList[0].url !== undefined)) {
                let params = {
                    "id": values.id,
                    "applicationId": values.applicationId,
                    "versionNum": values.versionNum,
                    "apkUrl": JSON.stringify(this.state.fileList.length === 0 ? this.props.fileList : this.state.fileList),
                    "describe": values.describe,
                    "isCurrent": values.isCurrent === true ? 1 : 0,
                    "size":size!==0?size:values.size,
                    "originSize":originSize!==0?originSize:values.originSize
                }
            
                if (!err) {
                    axios
                        .ajax({
                            url: "app/version/modify",
                            data: {
                                params: params,
                                isShowLoading: false,
                            },
                            method: "post",
                        })
                        .then((res) => {
                            if (res.code === 0) {
                                message.success(`编辑成功`);
                                this.handleCancel();
                                this.props.requestList();
                            } else {
                                this.handleCancel();
                                message.error(`编辑失败`);
                            }
                        });
                }
            } else {
                message.error(`文件还没上传完毕`);
            }


        });
    };
}
EditAppVersionForm = Form.create({})(EditAppVersionForm);
