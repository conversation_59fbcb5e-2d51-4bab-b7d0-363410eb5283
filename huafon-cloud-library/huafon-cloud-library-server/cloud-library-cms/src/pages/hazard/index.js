import React from 'react';
import axios from "../../axios/index";
import Utils from "../../utils/utils";
import AddHazardForm from "./components/addHazardForm";
import EditHazardForm from "./components/editHazardForm";
import {
    <PERSON>ton,
    Card,
    Col,
    Divider,
    Form,
    Input,
    message,
    Modal,
    Popconfirm,
    Row,
    Select,
    Table,
} from "antd";

const { Option } = Select;
const { TextArea } = Input;

/***
 *  查询参数
 * ***/
let query = {
    pageNo: 1,
    pageSize: 10,
    condition: ""
};
export default class HazardData extends React.Component {

    state = {
        list: [],
        editFormVisible: false,
        editRecord: null,
        addFormVisible: false,
        hazardLevels: [],
        strengths: [],
        trades: []
    };

    componentDidMount() {
        query.pageNo = 1;
        query.pageSize = 10;
        this.requestList();
        this.requestFilters();
    }

    //请求列表
    requestList = () => {
        let _this = this;
        axios.ajax({
            url: '/hazard/page',
            data: {
                params: query,
                isShowLoading: false
            },
            method: "post"
        }).then((res) => {
            let list = res.data.list.map((item, index) => {
                item.key = index;
                return item;
            });
            this.setState({
                list: list,
                pagination: Utils.pagination(res.data, (current) => {
                    query.pageNo = current;
                    _this.requestList();
                })
            })
        });
    };

    handleDelete = (record) => {
        axios.ajax({
            url: '/hazard/delete/' + record.id,
            method: "delete"
        }).then((res) => {
            if (res.code === 0) {
                message.success(`record delete successfully`);
            } else {
                message.error(`record delete failed`);
            }
            this.requestList();
        });
    };

    render() {
        const columns = [
            {
                title: '隐患编码',
                dataIndex: 'code',
                width: 120,
            },
            {
                title: '隐患描述',
                dataIndex: 'brief',
                width: 120,
            }, {
                title: '判定依据',
                dataIndex: 'judg',
                width: 300,
            }, {
                title: '适合行业',
                dataIndex: 'trade',
                width: 50,
            }, {
                title: '依据强度',
                dataIndex: 'strength',
                width: 50,
            }, {
                title: '隐患等级',
                dataIndex: 'hazardLevel',
                width: 50,
            }, {
                title: '操作',
                dataIndex: 'operation',
                width: 120,
                render: (text, record) => {
                    return <div>
                        <Button type="primary" size="small" onClick={() => {
                            this.setState({
                                editFormVisible: true,
                                editRecord: record
                            })
                        }}>编辑</Button>
                        <Divider type="vertical" />
                        <Popconfirm onConfirm={this.handleDelete.bind(this, record)} title="确认删除该记录？" okText="是" cancelText="否"
                        >
                            <Button type="primary" size="small" style={{ backgroundColor: 'red' }}>删除</Button>
                        </Popconfirm>
                    </div>
                },
            }];
        return (
            <div>
                <Card>
                    <FilterForm
                        hazardLevels={this.state.hazardLevels}
                        strengths={this.state.strengths}
                        trades={this.state.trades}
                        requestList={this.requestList}
                        showAddForm={() => {
                            this.setState({
                                addFormVisible: true
                            })
                        }} />
                </Card>
                <Card style={{ marginTop: 10 }} className="operate-wrap">
                    <Table
                        size='small'
                        bordered={true}
                        columns={columns}
                        dataSource={this.state.list}
                        pagination={this.state.pagination}
                    />
                </Card>

                <AddHazardForm
                    visible={this.state.addFormVisible}
                    handleCancel={this.handleAddFormClose}
                    requestList={this.requestList}
                    wrappedComponentRef={this.addForm}
                    hazardLevels={this.state.hazardLevels}
                    strengths={this.state.strengths}
                    trades={this.state.trades}
                >
                </AddHazardForm>
                <EditHazardForm visible={this.state.editFormVisible}
                    handleCancel={this.handleEditFormClose}
                    requestList={this.requestList}
                    wrappedComponentRef={this.editForm}
                    record={this.state.editRecord}
                    hazardLevels={this.state.hazardLevels}
                    strengths={this.state.strengths}
                    trades={this.state.trades}>
                </EditHazardForm>
            </div>
        )
    }
    addForm = (addFormRef) => {
        this.addFormRef = addFormRef;
    };
    editForm = (editFormRef) => {
        this.editFormRef = editFormRef;
    };

    handleAddFormClose = () => {
        this.addFormRef.props.form.resetFields();
        this.setState({
            addFormVisible: false,
        });
    };
    handleEditFormClose = () => {
        this.editFormRef.props.form.resetFields();
        this.setState({
            editFormVisible: false,
        });
    };
    requestFilters = () => {
        axios.ajax({
            url: '/hazard/filters',
            data: {
                isShowLoading: false
            },
            method: "get"
        }).then((res) => {
            this.setState({
                hazardLevels: this.handleToOption(res.data.hazardLevels),
                strengths: this.handleToOption(res.data.strengths),
                trades: this.handleToOption(res.data.trades),
                dataNames: this.handleToOption(res.data.dataNames)
            });
        });
    };

    handleToOption = (data) => {
        let children = [];
        for (let i = 0; i < data.length; i++) {
            children.push(<Option key={data[i]}>{data[i].replace("隐患依据库-", "")}</Option>);
        }
        return children;
    };

}

const FilterForm = Form.create({ name: 'filterForm' })(

    class extends React.Component {
        handleSearch = (data, fieldname) => {
            if (fieldname !== 'search') {
                this.props.form.setFieldsValue({ [fieldname]: data });
            }
            let fieldsValue = this.props.form.getFieldsValue();
            query.pageNo = 1;
            query.condition = this.handleChangeVal(fieldsValue.condition);
            query.hazardLevel = this.handleChangeVal(fieldsValue.hazardLevel);
            query.strength = this.handleChangeVal(fieldsValue.strength);
            query.trade = this.handleChangeVal(fieldsValue.trade);
            this.props.requestList();
        };
        handleChangeVal = (value) => {
            return this.verdictUndefinedOrEmpty(value) ? null : value.trim();
        };

        verdictUndefinedOrEmpty = (value) => {
            return value === undefined || value === "";
        };

        render() {
            const { getFieldDecorator } = this.props.form;
            return (
                <Form layout='inline'>
                    <Row>
                        <Col span={22}>
                            <Form.Item label="隐患描述">
                                {
                                    getFieldDecorator('condition', {})(
                                        <Input className='myInput' type="text" />
                                    )
                                }
                            </Form.Item>
                            <Form.Item label="隐患等级">
                                {
                                    getFieldDecorator('hazardLevel', {
                                        initialValue: ""
                                    })(
                                        <Select style={{ width: 80 }} onChange={(data) => this.handleSearch(data, 'hazardLevel')}>
                                            <Select.Option value="" >全部</Select.Option>
                                            {this.props.hazardLevels}
                                        </Select>
                                    )
                                }
                            </Form.Item>
                            <Form.Item label="依据强度">
                                {
                                    getFieldDecorator('strength', {
                                        initialValue: ""
                                    })(
                                        <Select style={{ width: 150 }} onChange={(data) => this.handleSearch(data, 'strength')}>
                                            <Select.Option value="" >全部</Select.Option>
                                            {this.props.strengths}
                                        </Select>
                                    )
                                }
                            </Form.Item>
                            <Form.Item label="适合行业">
                                {
                                    getFieldDecorator('trade', {
                                        initialValue: ""
                                    })(
                                        <Select style={{ width: 210 }} onChange={(data) => this.handleSearch(data, 'trade')}>
                                            <Select.Option value="" >全部</Select.Option>
                                            {this.props.trades}
                                        </Select>
                                    )
                                }
                            </Form.Item>
                            <Form.Item>
                                <Button type="primary" style={{ margin: '0 10px' }}
                                    onClick={(data) => this.handleSearch(data, 'search')}>查询</Button>
                                <Button onClick={() => {
                                    this.props.form.resetFields();
                                }}>重置</Button>
                            </Form.Item>
                        </Col>

                        <Col span={2}>
                            <Form.Item>
                                <Button type="primary" icon="plus"
                                    onClick={this.props.showAddForm}>新增</Button>
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            );
        }
    }
);
