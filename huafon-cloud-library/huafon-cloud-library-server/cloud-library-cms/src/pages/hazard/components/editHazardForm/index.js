import React, { Component } from "react";
import { Form, Input, Select, Modal, DatePicker, message } from "antd";
import moment from "moment";
import axios from "../../../../axios";
const FormItem = Form.Item;
const { Option } = Select;
const { TextArea } = Input;
export default class EditHazardForm extends Component {
    render() {
        const { visible, handleCancel } = this.props;
        const { getFieldDecorator } = this.props.form;
        const record = this.props.record || {};
        const formItemLayout = {
            labelCol: { span: 5 },
            wrapperCol: { span: 19 },
        };
        return (
            <Modal
                title="编辑隐患库"
                visible={visible}
                onCancel={handleCancel}
                onOk={this.editData}
                okText="确认"
                cancelText="取消"
                width={900}
            >
                <Form layout="horizontal">
                    <FormItem
                        style={{ display: "none" }}
                        label="隐藏隐患id"
                        {...formItemLayout}
                    >
                        {getFieldDecorator("id", { initialValue: record.id || undefined })(
                            <Input type="text" placeholder="" />
                        )}
                    </FormItem>
                    <Form.Item label="隐患等级" {...formItemLayout}>
                        {
                            getFieldDecorator('hazardLevel', {
                                initialValue: record.hazardLevel || undefined,
                                rules: [
                                    {
                                        required: true,
                                        message: "隐患等级不能为空",
                                    },
                                ],
                            })(
                                <Select style={{ width: 250 }}>
                                    {this.props.hazardLevels}
                                </Select>
                            )
                        }
                    </Form.Item>
                    <Form.Item label="依据强度" {...formItemLayout}>
                        {
                            getFieldDecorator('strength', {
                                initialValue: record.strength || undefined,
                                rules: [
                                    {
                                        required: true,
                                        message: "依据强度不能为空",
                                    },
                                ],
                            })(
                                <Select style={{ width: 250 }} >
                                    {this.props.strengths}
                                </Select>
                            )
                        }
                    </Form.Item>
                    <Form.Item label="适合行业" {...formItemLayout}>
                        {
                            getFieldDecorator('trade', {
                                initialValue: record.trade || undefined,
                                rules: [
                                    {
                                        required: true,
                                        message: "适合行业不能为空",
                                    },
                                ],
                            })(
                                <Select style={{ width: 250 }}>
                                    {this.props.trades}
                                </Select>
                            )
                        }
                    </Form.Item>
                    <FormItem label="隐患描述" {...formItemLayout}>
                        {getFieldDecorator("brief", {
                            initialValue: record.brief || undefined,
                            rules: [
                                {
                                    required: true,
                                    message: "隐患描述不能为空",
                                },
                            ],
                        })(<TextArea type="text" placeholder="请输入隐患描述" />)}
                    </FormItem>
                    <FormItem label="判定依据" {...formItemLayout}>
                        {getFieldDecorator("judg", {
                            initialValue: record.judg || undefined,
                            rules: [
                                {
                                    required: true,
                                    message: "判定依据不能为空",
                                },
                            ],
                        })(<TextArea style={{ height: 300 }} type="text" placeholder="请输入判定依据" />)}
                    </FormItem>
                </Form>
            </Modal>
        );
    }
    editData = () => {
        this.props.form.validateFields((err, values) => {
            if (!err) {
                axios
                    .ajax({
                        url: "/hazard/edit",
                        data: {
                            params: values,
                            isShowLoading: false,
                        },
                        method: "post",
                    })
                    .then((res) => {
                        if (res.code === 0) {
                            message.success(`record edit successfully`);
                            this.props.handleCancel();
                            this.props.requestList();
                        } else {
                            message.error(`record edit failed`);
                        }
                    });
            }
        });
    };
}
EditHazardForm = Form.create({})(EditHazardForm);
