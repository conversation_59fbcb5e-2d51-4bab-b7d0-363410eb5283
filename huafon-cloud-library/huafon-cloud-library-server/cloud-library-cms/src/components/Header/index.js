import './index.less';

import React from 'react'
import {Row, Col, message} from "antd";

import Util from '../../utils/utils';
import axios from '../../axios';
import { connect } from 'react-redux'
class Header extends React.Component{

    state = {
        menuName:""
    };

    componentWillMount(){
        setInterval(()=>{
            let sysTime = Util.formateDate(new Date().getTime());
            this.setState({
                sysTime
            })
        },1000);
        this.getMenuName();
    }

    getMenuName=()=>{
        // let url = Util.getUrlRelativePath();
        // if(url == "/"){
        //     this.setState({
        //         menuName:'首页'
        //     })
        // }else{
        //     axios.ajax({
        //         url: '/base-admin/api/user/routeName/?url='+url,
        //         data: {
        //             isShowLoading: false
        //         },
        //         method: "get",
        //         visible: false
        //     }).then((res) => {
        //         this.setState({
        //             menuName:res.data
        //         })
        //     });
        // }
    }

    handleLoginOut = () =>{
        axios.ajax({
            url: '/user/logout',
            data: {
                isShowLoading: false
            },
            method: "post",
            visible: false
        }).then((res) => {
            if (res.code === 0) {
                window.location.href = "http://"+window.location.host+"/login"
            } else {
                message.error(`系统错误,请稍后再试`)
            }
        });
    };

    render(){
        const menuType = this.props.menuType;
        const userInfo = this.props.userInfo || {};
        return (
            <div className="header">
                <Row className="header-top">
                    <Col span={menuType?18:24}>
                        <span>欢迎，{userInfo.userName}</span>
                        <a  onClick={this.handleLoginOut}>退出</a>
                    </Col>
                </Row>
                <Row className="breadcrumb">
                    <Col span={3} className="breadcrumb-title">
                    <span style={{textAlign:'center'}}>{this.props.menuName || this.state.menuName}</span>
                    </Col>
                    <Col span={21} className="weather">
                        <span className="date">{this.state.sysTime}</span>
                        <span className="weather-img">
                            <img src={this.state.dayPictureUrl} alt="" />
                        </span>
                        <span className="weather-detail">
                            {this.state.weather}
                        </span>
                    </Col>
                </Row>
            </div>
        );
    }
}
const mapStateToProps = state => {
    return {
        menuName: state.menuName
    }
}
export default connect(mapStateToProps)(Header)