import React from "react";

import { Form, Input, Row, Modal, Select } from "antd";
const { Option } = Select;

export default class AddRouteComponent extends React.Component {

    handleConfirm=()=>{
        this.props.form.validateFields((err, value)=>{
            console.log(value);
            
            if(!err){
                //修改
                if(parseInt(value.pid,10) != 0 && !Number(parseInt(value.pid,10))){
                    this.props.confirm({...value,pid:this.props.list.filter(item => (item.routeName == value.pid))[0].id,id: this.props.record.id});
                    return;
                }
                //新增
                this.props.confirm({...value,sort: value.sort ? value.sort : "0" });
                this.props.form.resetFields();
            }
        })
    }

    isExitedUserName=(name)=>{
        const record = this.props.record;
        if(record){
           return record.some(item => item.account === name)
        }
        return false;
    }

    handleCancle=()=>{
        this.props.form.resetFields();
        this.props.cancel();
    }

    render() {

        const {isShow,record,list,title} = this.props;
        const {getFieldDecorator} = this.props.form;
        const formItemLayout1 = {
            labelCol: { span: 5 },
            wrapperCol: { span: 19 },
        };

        return (
            <Modal  title={title}
                    okText="确定"
                    cancelText="取消"
                    visible={isShow}
                    onOk={()=>this.handleConfirm()}
                    onCancel={this.handleCancle}
                    >
                    <Form>
                        <Row style={{display: 'none'}}>
                            <Form.Item label="路由ID" {...formItemLayout1}>
                                {getFieldDecorator('id', {initialValue: record ? record.id : 0,})(
                                    <Input placeholder="路由ID" />,
                                )}
                            </Form.Item>
                        </Row>
                        <Row>
                            <Form.Item label="路由名称" {...formItemLayout1}>
                                {getFieldDecorator('routeName', {
                                    initialValue: record ? record.routeName : "",
                                    rules: [
                                        {required: true, message: '请输入路由名称'},
                                        {whitespace: true, message: '路由名称不能为空'}
                                    ],
                                })(
                                    <Input placeholder="路由名称" />,
                                )}
                            </Form.Item>
                        </Row>
                        <Row>
                            <Form.Item label="上级路由" {...formItemLayout1}>
                                {getFieldDecorator('pid', {
                                    initialValue: record ? record.pid == 0 ? "0" : list.filter(item=>item.id == record.pid)[0].routeName : "0",
                                    rules: [],
                                })(
                                    <Select>
                                        <Option value="0">/</Option>
                                        {list && list.length > 0 ?
                                            list.map((item,index) => {
                                                return <Option key={index} value={record ? item.routeName : `${item.id}`}>{item.routeName}</Option>
                                            })
                                        :
                                            null
                                        }
                                    </Select>
                                )}
                            </Form.Item>
                        </Row>
                        <Row>
                            <Form.Item label="路由地址" {...formItemLayout1}>
                                {getFieldDecorator('routeUrl', {
                                    initialValue: record ? record.routeUrl : "",
                                })(
                                    <Input placeholder="路由地址" />,
                                )}
                            </Form.Item>
                        </Row>
                        <Row>
                            <Form.Item label="排序" {...formItemLayout1}>
                                {getFieldDecorator('sort', {
                                    initialValue: record ? record.sort : "0",
                                })(
                                    <Input type="number" min="1" max="999999" pattern="\d+" placeholder="排序" />,
                                )}
                            </Form.Item>
                        </Row>
                    </Form>
            </Modal>
        );
    };
}

AddRouteComponent = Form.create({})(AddRouteComponent);