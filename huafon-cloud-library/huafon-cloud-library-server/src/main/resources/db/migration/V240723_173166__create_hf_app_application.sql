CREATE SEQUENCE "hf_app_application_id_seq" INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1;
ALTER SEQUENCE "hf_app_application_id_seq" OWNER TO "postgres";
CREATE TABLE "hf_app_application"
(
    "id"          int8                                        NOT NULL DEFAULT nextval('hf_app_application_id_seq' :: regclass),
    "icon"   varchar(512) COLLATE "pg_catalog"."default" NOT NULL DEFAULT '':: character varying,
    "app_name"   varchar(512) COLLATE "pg_catalog"."default" NOT NULL DEFAULT '':: character varying,
    "download_url"   varchar(512) COLLATE "pg_catalog"."default" NOT NULL DEFAULT '':: character varying,
    "describe"   varchar(10240) COLLATE "pg_catalog"."default" NOT NULL DEFAULT '':: character varying,
    "create_time" timestamp(0)                                NOT NULL DEFAULT NULL:: timestamp without time zone,
    "modify_time" timestamp(0)                                NOT NULL DEFAULT NULL:: timestamp without time zone,
    "is_del"      int4                                        NOT NULL DEFAULT 0,
    CONSTRAINT "hf_app_application_pkey" PRIMARY KEY ("id")
)
;
ALTER TABLE "hf_app_application"
    OWNER TO "postgres";

COMMENT ON COLUMN "hf_app_application"."id" IS '主键';
COMMENT ON COLUMN "hf_app_application"."icon" IS '图标';
COMMENT ON COLUMN "hf_app_application"."app_name" IS 'app名称';
COMMENT ON COLUMN "hf_app_application"."download_url" IS '下载地址';
COMMENT ON COLUMN "hf_app_application"."describe" IS '描述';
COMMENT ON COLUMN "hf_app_application"."create_time" IS '创建时间';
COMMENT ON COLUMN "hf_app_application"."modify_time" IS '修改时间';
COMMENT ON COLUMN "hf_app_application"."is_del" IS '是否删除 0 正常 1 删除';


