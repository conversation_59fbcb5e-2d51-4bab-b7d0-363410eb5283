CREATE SEQUENCE "hf_app_version_id_seq" INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1;
ALTER SEQUENCE "hf_app_version_id_seq" OWNER TO "postgres";
CREATE TABLE "hf_app_version" (
                                  "id" int8 NOT NULL DEFAULT nextval( 'hf_app_version_id_seq' :: regclass ),
                                  "application_id" VARCHAR ( 512 ) COLLATE "pg_catalog"."default" NOT NULL DEFAULT '' :: CHARACTER VARYING,
                                  "version_num" VARCHAR ( 512 ) COLLATE "pg_catalog"."default" NOT NULL DEFAULT '' :: CHARACTER VARYING,
                                  "build_num" VARCHAR ( 512 ) COLLATE "pg_catalog"."default" NOT NULL DEFAULT '' :: CHARACTER VARYING,
                                  "apk_url" VARCHAR ( 512 ) COLLATE "pg_catalog"."default" NOT NULL DEFAULT '' :: CHARACTER VARYING,
                                  "size" int4 NOT NULL DEFAULT 0,
                                  "describe" VARCHAR ( 10240 ) COLLATE "pg_catalog"."default" NOT NULL DEFAULT '' :: CHARACTER VARYING,
                                  "download_num" int4 NOT NULL DEFAULT 0,
                                  "is_current" int2   NOT NULL DEFAULT 0,
                                  "create_time" TIMESTAMP ( 0 ) NOT NULL DEFAULT NULL :: TIMESTAMP WITHOUT TIME ZONE,
                                  "modify_time" TIMESTAMP ( 0 ) NOT NULL DEFAULT NULL :: TIMESTAMP WITHOUT TIME ZONE,
                                  "is_del" int4 NOT NULL DEFAULT 0,
                                  CONSTRAINT "hf_app_version_pkey" PRIMARY KEY ( "id" )
);
ALTER TABLE "hf_app_version" OWNER TO "postgres";
COMMENT ON COLUMN "hf_app_version"."id" IS '主键';
COMMENT ON COLUMN "hf_app_version"."application_id" IS 'app应用id 关联hf_app_application.id';
COMMENT ON COLUMN "hf_app_version"."version_num" IS '版本号';
COMMENT ON COLUMN "hf_app_version"."build_num" IS '构建次数';
COMMENT ON COLUMN "hf_app_version"."apk_url" IS 'apk地址';
COMMENT ON COLUMN "hf_app_version"."size" IS 'app大小';
COMMENT ON COLUMN "hf_app_version"."describe" IS '描述信息';
COMMENT ON COLUMN "hf_app_version"."download_num" IS '下载次数';
COMMENT ON COLUMN "hf_app_version"."is_current" IS '是否是当前版本';
COMMENT ON COLUMN "hf_app_version"."create_time" IS '创建时间';
COMMENT ON COLUMN "hf_app_version"."modify_time" IS '修改时间';
COMMENT ON COLUMN "hf_app_version"."is_del" IS '是否删除 0 正常 1 删除';