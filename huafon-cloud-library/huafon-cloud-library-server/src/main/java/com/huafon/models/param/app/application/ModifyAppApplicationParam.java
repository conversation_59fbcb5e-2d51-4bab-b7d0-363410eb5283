package com.huafon.models.param.app.application;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: huafon-base
 * @description:
 * @author: slowy
 * @create: 2024-07-23 22:33
 **/
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel(value = "ModifyAppApplicationParam", description = "修改app应用")
public class ModifyAppApplicationParam {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "图标")
    private String icon;

    @ApiModelProperty(value = "app名称")
    private String appName;

    @ApiModelProperty(value = "下载地址")
    private String downloadUrl;

    @ApiModelProperty(value = "描述")
    private String describe;
}
