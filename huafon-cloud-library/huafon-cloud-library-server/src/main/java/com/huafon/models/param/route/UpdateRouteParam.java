package com.huafon.models.param.route;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2024-01-21 14:37:18
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel(value = "UpdateRouteParam", description = "编辑路由")
public class UpdateRouteParam {

    @ApiModelProperty(value = "路由id")
    private Long id;

    @ApiModelProperty(value = "路由名称")
    private String routeName;

    @ApiModelProperty(value = "路由url")
    private String routeUrl;

    @ApiModelProperty(value = "父id")
    private String pid;

    @ApiModelProperty(value = "排序")
    private String sort;
}