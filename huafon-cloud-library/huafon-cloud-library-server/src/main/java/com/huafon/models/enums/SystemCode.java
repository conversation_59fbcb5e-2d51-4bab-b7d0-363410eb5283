package com.huafon.models.enums;

import com.huafon.support.core.pojo.IResultCode;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2022-12-13 16:41:38
 */
public enum SystemCode implements IResultCode {
    LOGIN_FAIL(20001, "登录失败");

    final int code;
    final String msg;

    private SystemCode(final int code, final String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }
}
