package com.huafon.models.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2024-03-05 13:07:55
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel(value = "LawFilterVO", description = "法律法规筛选数据")
public class LawFilterVO {

    @ApiModelProperty(value = "法律效力")
    private List<String> lawForces;

    @ApiModelProperty(value = "标准")
    private List<String> standards;

    @ApiModelProperty(value = "行业")
    private List<String> trades;

}