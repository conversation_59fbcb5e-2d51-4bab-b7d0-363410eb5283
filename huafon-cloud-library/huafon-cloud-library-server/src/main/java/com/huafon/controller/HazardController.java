package com.huafon.controller;

import com.huafon.common.utils.CodeBuilder;
import com.huafon.configs.AccessPermission;
import com.huafon.convert.HazardConvert;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.models.enums.CodeEnum;
import com.huafon.models.param.hazard.AddHazardParam;
import com.huafon.models.param.hazard.EditHazardParam;
import com.huafon.models.param.hazard.HazardPageParam;
import com.huafon.models.vo.HazardDataVO;
import com.huafon.models.vo.HazardFilterVO;
import com.huafon.service.HazardService;
import com.huafon.support.core.pojo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2024-01-22 20:46:03
 */
@Api(tags = "隐患资料库")
@RestController
@RequestMapping("/hazard")
@Slf4j
public class HazardController {

    private final HazardService hazardService;

    private final CodeBuilder codeBuilder;

    @Autowired
    public HazardController(HazardService hazardService,
                            CodeBuilder codeBuilder) {
        this.hazardService = hazardService;
        this.codeBuilder = codeBuilder;
    }

    @PostMapping("/page")
    @ApiOperation(value = "隐患资料库分页列表")
    public R<CommonPage<HazardDataVO>> getPage(@RequestBody @Valid HazardPageParam param) {
        return R.ok(hazardService.getPage(param));
    }

    @PostMapping("/edit")
    @ApiOperation(value = "编辑隐患资料库")
    @AccessPermission
    public R edit(@RequestBody @Valid EditHazardParam param) {
        hazardService.update(HazardConvert.toHazardData(param));
        return R.ok();
    }

    @PostMapping("/save")
    @ApiOperation(value = "保存隐患资料库")
    @AccessPermission
    public R add(@RequestBody @Valid AddHazardParam param) {
        hazardService.save(HazardConvert.toHazardData(param));
        return R.ok();
    }

    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除隐患")
    @AccessPermission
    public R delete(@PathVariable Long id) {
        hazardService.delete(id);
        return R.ok();
    }

    @GetMapping("/filters")
    @ApiOperation(value = "隐患筛选")
    @AccessPermission
    public R<HazardFilterVO> getFilters() {
        return R.ok(hazardService.getFilters());
    }

//    @GetMapping("/deal/nocode")
//    @ApiOperation(value = "处理没有隐患编码的数据")
//    @AccessPermission
//    public R dealNoCode() {
//        hazardService.dealNoCode();
//        return R.ok();
//    }


}