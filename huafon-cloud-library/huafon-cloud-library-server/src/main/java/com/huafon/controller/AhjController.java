package com.huafon.controller;

import com.huafon.configs.AccessPermission;
import com.huafon.convert.RouteConvert;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.models.entity.AhjOriginData;
import com.huafon.models.entity.UserPermission;
import com.huafon.models.param.ahj.AddAhjOriginDataParam;
import com.huafon.models.param.ahj.AhjOriginDataPageParam;
import com.huafon.models.param.route.AddRouteParam;
import com.huafon.models.param.user.UserPageParam;
import com.huafon.models.vo.AhjOriginDataVO;
import com.huafon.models.vo.UserInfoVO;
import com.huafon.service.AhjService;
import com.huafon.support.core.pojo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2024-01-21 18:41:22
 */
@Api(tags = "安环健资料库原始数据处理")
@RestController
@RequestMapping("/ahj")
@Slf4j
public class AhjController {

    private final AhjService service;

    @Autowired
    public AhjController(AhjService service) {
        this.service = service;
    }

    @PostMapping("/add")
    @ApiOperation(value = "新增安环健原始数据")
    @AccessPermission
    public R add(@RequestBody @Valid AddAhjOriginDataParam param) {
        service.save(AhjOriginData.builder()
                .dataName(param.getDataName())
                .jsonData(param.getJsonData())
                .createTime(new Date())
                .modifyTime(new Date())
                .build());
        return R.ok();
    }

    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除原始安环数据")
    @AccessPermission
    public R add(@PathVariable Long id) {
        service.delete(id);
        return R.ok();
    }


    @PostMapping("/page")
    @ApiOperation(value = "返回安环健列列表")
    @AccessPermission
    public R<CommonPage<AhjOriginDataVO>> getPage(@RequestBody @Valid AhjOriginDataPageParam param) {
        return R.ok(service.getPage(param));
    }

}