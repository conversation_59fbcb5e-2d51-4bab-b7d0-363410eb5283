package com.huafon.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.models.entity.AppVersion;
import com.huafon.models.param.app.distribute.CountParam;
import com.huafon.models.param.app.version.SetCurrentVersionParam;
import com.huafon.models.param.app.version.VersionPageParam;
import com.huafon.models.vo.app.AllAppVersionVO;
import com.huafon.models.vo.app.AppVersionVO;
import com.huafon.models.vo.app.VersionPageVO;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-23
 */
public interface AppVersionService extends IService<AppVersion> {

    void add(AppVersion appVersion);

    void modify(AppVersion appVersion);

    void delete(AppVersion appVersion);

    void deleteByApplicationId(Long applicationId);

    CommonPage<VersionPageVO> getVersionPage(VersionPageParam param);

    void setCurrentVersion(SetCurrentVersionParam param);

    List<AllAppVersionVO> getAllVersionList(Long applicationId);

    List<AppVersion> getAppVersionList(Long applicationId);

    AppVersion getOne(Long applicationId, Long versionId);

    List<AppVersion> getAppVersions(List<Long> versionIds);

    AppVersion getLastAppVersion(List<Long> versionIds);

    void downLoadCount(CountParam param);
}
