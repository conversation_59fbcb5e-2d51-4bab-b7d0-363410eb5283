package com.huafon.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.huafon.models.entity.AppChannelVersionLink;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-28
 */
public interface AppChannelVersionLinkService extends IService<AppChannelVersionLink> {

    void add (AppChannelVersionLink appChannelVersionLink);

    List<AppChannelVersionLink> getVersions(Long channelId);

    void delete(Long channelId);

}
