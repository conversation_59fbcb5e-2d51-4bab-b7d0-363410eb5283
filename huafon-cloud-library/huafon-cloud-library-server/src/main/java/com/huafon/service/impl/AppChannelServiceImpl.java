package com.huafon.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huafon.common.utils.BaseUtils;
import com.huafon.convert.AppConvert;
import com.huafon.framework.mybatis.enums.DelFlag;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.mapper.AppChannelMapper;
import com.huafon.models.entity.*;
import com.huafon.models.param.app.channel.AddAppChannelParam;
import com.huafon.models.param.app.channel.ChannelPageParam;
import com.huafon.models.param.app.channel.ModifyAppChannelParam;
import com.huafon.models.param.app.distribute.DistributeParam;
import com.huafon.models.vo.app.ChannelPageVO;
import com.huafon.models.vo.app.DistributeVO;
import com.huafon.models.vo.app.SelectAllVersionVO;
import com.huafon.service.AppApplicationService;
import com.huafon.service.AppChannelService;
import com.huafon.service.AppChannelVersionLinkService;
import com.huafon.service.AppVersionService;
import com.huafon.support.exceptions.ServiceException;
import com.huafon.utils.encoder.ClEncoder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.temporal.Temporal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-23
 */
@Service
public class AppChannelServiceImpl extends ServiceImpl<AppChannelMapper, AppChannel> implements AppChannelService {

    private final AppChannelMapper appChannelMapper;

    private final AppApplicationService appApplicationService;

    private final AppChannelVersionLinkService appChannelVersionLinkService;

    private final AppVersionService appVersionService;

    public AppChannelServiceImpl(AppChannelMapper appChannelMapper,
                                 AppApplicationService appApplicationService,
                                 AppChannelVersionLinkService appChannelVersionLinkService,
                                 AppVersionService appVersionService) {
        this.appChannelMapper = appChannelMapper;
        this.appApplicationService = appApplicationService;
        this.appChannelVersionLinkService = appChannelVersionLinkService;
        this.appVersionService = appVersionService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(AddAppChannelParam param) {

        AppChannel one = this.getOne(param.getChannelName());
        if (Objects.nonNull(one)) {
            throw new ServiceException("app应用渠道已经存在");
        }
        AppChannel appChannel = AppConvert.toDO(param);
        appChannelMapper.insert(appChannel);
        param.getSelectVersionIds().stream().forEach(versionId -> {
            AppVersion appVersion = appVersionService.getOne(param.getApplicationId(), versionId);
            if (Objects.nonNull(appVersion)) {
                appChannelVersionLinkService.add(AppChannelVersionLink.builder()
                        .channelId(appChannel.getId())
                        .versionId(versionId)
                        .build());
            }
        });

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(ModifyAppChannelParam param) {
        AppChannel one = this.getOne(param.getChannelName(), param.getId());
        if (Objects.nonNull(one)) {
            throw new ServiceException("app应用渠道已经存在");
        }
        appChannelMapper.updateById(AppConvert.toDO(param));

        appChannelVersionLinkService.delete(param.getId());

        param.getSelectVersionIds().stream().forEach(versionId -> {
            AppVersion appVersion = appVersionService.getOne(param.getApplicationId(), versionId);
            if (Objects.nonNull(appVersion)) {
                appChannelVersionLinkService.add(AppChannelVersionLink.builder()
                        .channelId(param.getId())
                        .versionId(versionId)
                        .build());
            }
        });
    }

    @Override
    public void delete(Long id) {
        appChannelMapper.updateById(AppChannel.builder()
                .id(id)
                .isDel(DelFlag.DELELTED.getValue())
                .modifyTime(new Date())
                .build());
    }

    @Override
    public void deleteByApplicationId(Long applicationId) {
        appChannelMapper.update(AppChannel.builder()
                .isDel(DelFlag.DELELTED.getValue())
                .modifyTime(new Date())
                .build(), new LambdaQueryWrapper<AppChannel>()
                .eq(AppChannel::getApplicationId, applicationId));
    }

    @Override
    public CommonPage<ChannelPageVO> getChannelPage(ChannelPageParam param) {

        Page<AppChannel> page = new Page<>(param.getPageNo(), param.getPageSize());
        List<OrderItem> orders = CollectionUtils.isEmpty(param.getOrders())
                ? Collections.singletonList(OrderItem.desc("create_time"))
                : param.getOrders();
        page.setOrders(orders);
        IPage<AppChannel> listPage = appChannelMapper.selectPage(page, new LambdaQueryWrapper<AppChannel>()
                .eq(AppChannel::getIsDel, DelFlag.SAVE.getValue())
                .eq(Objects.nonNull(param.getApplicationId()), AppChannel::getApplicationId, param.getApplicationId())
                .and(StringUtils.isNotBlank(param.getCondition()), wrapper ->
                        wrapper.like(AppChannel::getChannelName, param.getCondition()))
                .between(BaseUtils.isNotNull(param.getStartTime()) && BaseUtils.isNotNull(param.getEndTime()),
                        AppChannel::getModifyTime, param.getStartTime(), param.getEndTime()));
        IPage<ChannelPageVO> vo = listPage.convert(this::convertToChannelPageVO);
        return new CommonPage<>(vo);
    }

    @Override
    public List<SelectAllVersionVO> getAllSelectVersion(Long channelId) {
        List<AppChannelVersionLink> appChannelVersionLinks = appChannelVersionLinkService.getVersions(channelId);
        List<AppVersion> appVersions = new ArrayList<>();
        if (!CollectionUtils.isEmpty(appChannelVersionLinks)) {
            appVersions = appVersionService.getAppVersions(appChannelVersionLinks.stream()
                    .map(item -> item.getVersionId())
                    .collect(Collectors.toList()));
        }
        List<SelectAllVersionVO> vos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(appVersions)) {
            vos = appVersions.stream()
                    .map(item -> SelectAllVersionVO.builder()
                            .id(item.getId())
                            .versionNum(item.getVersionNum())
                            .buildNum(item.getBuildNum())
                            .build())
                    .collect(Collectors.toList());
        }
        return vos;
    }

    @Override
    public DistributeVO getDistribute(DistributeParam param) {
        DistributeVO vo = new DistributeVO();
        if (param.getDistributeType().equals("app")) {

            AppApplication appApplication = appApplicationService.getOne(ClEncoder.decode(param.getHashId()));
            List<Attachment> attachments = JSON.parseArray(appApplication.getIcon(), Attachment.class);
            vo.setAppName(appApplication.getAppName());
            vo.setIcon(attachments.get(0).getUrl());
            List<DistributeVO.Version> versions = appVersionService.getAppVersionList(appApplication.getId()).stream().map(item -> DistributeVO.Version.builder()
                    .versionNum(item.getVersionNum())
                    .id(item.getId())
                    .buildNum(item.getBuildNum())
                    .isCurrent(item.getIsCurrent())
                    .apkUrl(JSON.parseArray(item.getApkUrl(), Attachment.class).get(0).getUrl())
                    .modifyTime(item.getModifyTime())
                    .size(item.getSize())
                    .originSize(item.getOriginSize())
                    .build())
                    .collect(Collectors.toList());
            vo.setVersions(versions);

        } else if (param.getDistributeType().equals("channel")) {

            AppChannel appChannel = this.getOne(ClEncoder.decode(param.getHashId()));
            AppApplication appApplication = appApplicationService.getOne(appChannel.getApplicationId());
            List<Attachment> attachments = JSON.parseArray(appApplication.getIcon(), Attachment.class);
            vo.setAppName(appApplication.getAppName());
            vo.setIcon(attachments.get(0).getUrl());
            List<AppChannelVersionLink> links = appChannelVersionLinkService.getVersions(appChannel.getId());
            List<DistributeVO.Version> versions = appVersionService.getAppVersions(links.stream()
                    .map(item -> item.getVersionId())
                    .collect(Collectors.toList())).stream().map(item -> DistributeVO.Version.builder()
                    .versionNum(item.getVersionNum())
                    .buildNum(item.getBuildNum())
                    .isCurrent(item.getIsCurrent())
                    .apkUrl(JSON.parseArray(item.getApkUrl(), Attachment.class).get(0).getUrl())
                    .modifyTime(item.getModifyTime())
                    .size(item.getSize())
                    .originSize(item.getOriginSize())
                    .build())
                    .collect(Collectors.toList());
            vo.setVersions(versions);

        }
        return vo;
    }

    public ChannelPageVO convertToChannelPageVO(AppChannel channel) {
        ChannelPageVO vo = AppConvert.toVO(channel);
        AppApplication appApplication = appApplicationService.getOne(channel.getApplicationId());

        List<AppChannelVersionLink> appChannelVersionLinks = appChannelVersionLinkService.getVersions(channel.getId());
        AppVersion appVersion = new AppVersion();
        if (!CollectionUtils.isEmpty(appChannelVersionLinks)) {
            appVersion = appVersionService.getLastAppVersion(appChannelVersionLinks.stream()
                    .map(item -> item.getVersionId())
                    .collect(Collectors.toList()));
        }
        vo.setIcon(appApplication.getIcon());
        vo.setAppName(appApplication.getAppName());
        vo.setApplicationId(appApplication.getId());
        vo.setLatestVersion(Objects.isNull(appVersion) ? "无" : appVersion.getVersionNum() + "（Build：" + appVersion.getBuildNum() + "）");
        return vo;
    }


    private AppChannel getOne(String channelName) {
        AppChannel one = new LambdaQueryChainWrapper<>(appChannelMapper)
                .eq(AppChannel::getChannelName, channelName)
                .eq(AppChannel::getIsDel, DelFlag.SAVE.getValue())
                .one();
        return one;
    }

    private AppChannel getOne(String channelName, Long id) {
        AppChannel one = new LambdaQueryChainWrapper<>(appChannelMapper)
                .eq(AppChannel::getChannelName, channelName)
                .eq(AppChannel::getIsDel, DelFlag.SAVE.getValue())
                .ne(AppChannel::getId, id)
                .one();
        return one;
    }

    private AppChannel getOne(Long id) {
        AppChannel one = new LambdaQueryChainWrapper<>(appChannelMapper)
                .eq(AppChannel::getId, id)
                .eq(AppChannel::getIsDel, DelFlag.SAVE.getValue())
                .one();
        return one;
    }


}
