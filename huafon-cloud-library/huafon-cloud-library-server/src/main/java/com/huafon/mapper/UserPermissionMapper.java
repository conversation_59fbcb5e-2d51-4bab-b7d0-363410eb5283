package com.huafon.mapper;

import com.huafon.models.entity.UserPermission;

import java.util.List;

public interface UserPermissionMapper {
    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int deleteByPrimaryKey(Long id);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int insert(UserPermission row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int insertSelective(UserPermission row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    UserPermission selectByPrimaryKeyWithLock(Long id);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    UserPermission selectByPrimaryKey(Long id);

    UserPermission selectByRouteUrl(String RouteUrl);

    List<UserPermission> selectByPid(Long pid);

    List<UserPermission> selectAllPermission();

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByPrimaryKeySelective(UserPermission row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByPrimaryKey(UserPermission row);
}