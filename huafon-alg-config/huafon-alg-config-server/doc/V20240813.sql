-- SEQUENCE: ai_platform.hf_ai_area_camera_id_seq

CREATE SEQUENCE IF NOT EXISTS ai_platform.hf_ai_area_camera_id_seq
    INCREMENT 1
    START 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;

ALTER SEQUENCE ai_platform.hf_ai_area_camera_id_seq
    OWNER TO postgres;

-- Table: ai_platform.hf_ai_area_camera
CREATE TABLE IF NOT EXISTS "ai_platform"."hf_ai_area_camera" (
   "id" int8 NOT NULL DEFAULT nextval('"ai_platform".hf_ai_area_camera_id_seq'::regclass),
   "create_time" timestamp(0) NOT NULL DEFAULT NULL::timestamp without time zone,
   "modify_time" timestamp(0) NOT NULL DEFAULT NULL::timestamp without time zone,
   "is_del" int2 NOT NULL DEFAULT 0,
   "area_id" int8 NOT NULL DEFAULT '-1'::integer,
   "camera_id" int8 NOT NULL DEFAULT '-1'::integer
) TABLESPACE pg_default;

-- ----------------------------
-- Uniques structure for table hf_ai_area_camera
-- ----------------------------
ALTER TABLE "ai_platform"."hf_ai_area_camera" ADD CONSTRAINT "hf_ai_area_camera_uniquekey" UNIQUE ("area_id", "camera_id");

-- ----------------------------
-- Primary Key structure for table hf_ai_area_camera
-- ----------------------------
ALTER TABLE "ai_platform"."hf_ai_area_camera" ADD CONSTRAINT "hf_ai_area_camera_pkey" PRIMARY KEY ("id");

ALTER TABLE IF EXISTS ai_platform.hf_ai_area_camera
    OWNER to postgres;

COMMENT ON COLUMN ai_platform.hf_ai_area_camera.id IS '唯一id';
COMMENT ON COLUMN ai_platform.hf_ai_area_camera.area_id IS '区域id';
COMMENT ON COLUMN ai_platform.hf_ai_area_camera.camera_id IS '摄像头id';
COMMENT ON COLUMN ai_platform.hf_ai_area_camera.create_time IS '创建时间';
COMMENT ON COLUMN ai_platform.hf_ai_area_camera.modify_time IS '更新时间';
COMMENT ON COLUMN ai_platform.hf_ai_area_camera.is_del IS '是否删除';


-- 历史数据处理
DO $$
DECLARE
    camera_record RECORD;
BEGIN
    FOR camera_record IN SELECT * FROM ai_platform.hf_camera
    LOOP
        if not exists (select 1 from ai_platform.hf_ai_area_camera where area_id = camera_record.area_id and camera_id = camera_record.id) AND camera_record.area_id is not null
        then
            insert into ai_platform.hf_ai_area_camera (create_time, modify_time, is_del, area_id, camera_id)
            values (LOCALTIMESTAMP, LOCALTIMESTAMP, 0, camera_record.area_id, camera_record.id);
        end if;
    END LOOP;
END $$ LANGUAGE plpgsql;