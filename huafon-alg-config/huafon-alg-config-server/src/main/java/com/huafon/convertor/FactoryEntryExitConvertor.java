package com.huafon.convertor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huafon.models.entity.FactoryEntryExitRecord;
import com.huafon.models.vo.req.footfall.FactoryEntryExitAddReqVO;
import com.huafon.models.vo.resp.footfall.FactoryEntryExitRespVO;
import com.huafon.models.vo.resp.footfall.FactoryFootfallLiveStatRespVO;
import com.huafon.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;

@Slf4j
public class FactoryEntryExitConvertor {

    public static IPage<FactoryEntryExitRespVO> convert(IPage<FactoryEntryExitRecord> listPage) {
        if (Objects.isNull(listPage)) {
            return null;
        }

        return listPage.convert(FactoryEntryExitConvertor::convert);
    }

    public static FactoryEntryExitRespVO convert(FactoryEntryExitRecord factoryEntryExitRecord) {
        FactoryEntryExitRespVO result = new FactoryEntryExitRespVO();
        result.setId(factoryEntryExitRecord.getId());
        result.setRecordTime(factoryEntryExitRecord.getRecordTime());
        result.setEntryExitType(factoryEntryExitRecord.getEntryExitType());
        result.setDetail(factoryEntryExitRecord.getDetail());
        result.setNum(factoryEntryExitRecord.getNum());

        return result;
    }

    public static FactoryEntryExitRecord convert(FactoryEntryExitAddReqVO reqVO) {
        FactoryEntryExitRecord result = new FactoryEntryExitRecord();
        result.setIsDel(0);
        result.setEntryExitType(reqVO.getEntryExitType());
        result.setDetail(reqVO.getDetail());
        result.setNum(reqVO.getNum());

        // 定义日期时间格式
        /* DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime localDateTime = LocalDateTime.parse(reqVO.getRecordTime(), formatter);
        ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneId.of("Asia/Shanghai")); */

        result.setRecordTime(
                DateUtils.convertTo(reqVO.getRecordTime(), "yyyy-MM-dd HH:mm:ss", "Asia/Shanghai"));

        result.setCreateTime(new Date());
        result.setModifyTime(new Date());

        return result;
    }

    public static List<String> convert(String rtpsJsonArrayString) {
        if (StringUtils.isBlank(rtpsJsonArrayString)) {
            return Collections.emptyList();
        }

        try {
            JSONArray jsonArray = JSON.parseArray(rtpsJsonArrayString);
            if (Objects.isNull(jsonArray)) {
                return Collections.emptyList();
            }

            List<String> rtspList = new ArrayList<>(jsonArray.size());
            for (int i = 0; i < jsonArray.size(); i++) {
                rtspList.add(jsonArray.getString(i));
            }

            return rtspList;
        } catch (Exception ex) {
            log.error("convert rtpsJsonArrayString error, rtpsJsonArrayString:{}", rtpsJsonArrayString, ex);
        }

        return Collections.emptyList();
    }

    public static FactoryFootfallLiveStatRespVO convert(Integer entryExitPeopleNum, List<String> rtspList, Integer livePeopleNum) {
        FactoryFootfallLiveStatRespVO result = new FactoryFootfallLiveStatRespVO();
        result.setAiCameraNum(CollectionUtils.isEmpty(rtspList) ? 0 : rtspList.size());

        Integer realTimePeopleNum = livePeopleNum + entryExitPeopleNum;
        result.setLivePeopleNum(realTimePeopleNum < 0 ? 0 : realTimePeopleNum);

        return result;
    }
}
