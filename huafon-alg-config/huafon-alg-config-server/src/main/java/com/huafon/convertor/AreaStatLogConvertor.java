package com.huafon.convertor;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huafon.framework.mybatis.enums.DelFlag;
import com.huafon.models.entity.AiAreaStatLog;
import com.huafon.models.vo.req.area.AlgAreaStatV2ReqVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

public class AreaStatLogConvertor {

    public static List<AiAreaStatLog> convertToAreaStatLog(AlgAreaStatV2ReqVO req, Date now) {
        if (Objects.isNull(req) ||
                StringUtils.isBlank(req.getAiType()) ||
                CollectionUtils.isEmpty(req.getAreaInfo())) {
            return Collections.emptyList();
        }

        List<AiAreaStatLog> allResult = new ArrayList<>(req.getAreaInfo().size());
        for (AlgAreaStatV2ReqVO.AlgAreaInfo algAreaInfo : req.getAreaInfo()) {
            if (Objects.isNull(algAreaInfo) || Objects.isNull(algAreaInfo.getAlarmNum())) {
                continue;
            }

            AiAreaStatLog result = convertToAiAreaStatLog(algAreaInfo, now);
            allResult.add(result);
        }

        return allResult;
    }

    private static AiAreaStatLog convertToAiAreaStatLog(AlgAreaStatV2ReqVO.AlgAreaInfo algAreaInfo, Date now) {
        AiAreaStatLog result = new AiAreaStatLog();
        result.setAreaId(algAreaInfo.getAreaId());
        result.setNum(algAreaInfo.getAreaNum());
        result.setLimitNum(algAreaInfo.getAreaLimitNum());
        result.setAreaId(algAreaInfo.getAreaId());
        result.setCreateTime(now);
        result.setModifyTime(now);
        result.setIsDel(DelFlag.SAVE.getValue());
        return result;
    }

    public static List<Long> convertToLogIdList(IPage<AiAreaStatLog> statLogPage) {
        if (Objects.isNull(statLogPage) || CollectionUtils.isEmpty(statLogPage.getRecords())) {
            return Collections.emptyList();
        }

        return statLogPage.getRecords().stream()
                .filter(Objects::nonNull)
                .map(AiAreaStatLog::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
