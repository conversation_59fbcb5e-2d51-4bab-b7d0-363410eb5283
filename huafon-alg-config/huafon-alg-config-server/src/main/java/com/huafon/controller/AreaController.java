package com.huafon.controller;

import com.huafon.constant.AccessPermission;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.models.dto.AlgCameraStatInfo;
import com.huafon.models.entity.AiArea;
import com.huafon.models.entity.AiBoxManage;
import com.huafon.models.vo.req.area.*;
import com.huafon.models.vo.resp.area.*;
import com.huafon.service.AiAreaGroupService;
import com.huafon.service.AiAreaService;
import com.huafon.service.AiAreaStatLogService;
import com.huafon.service.AiBoxManageService;
import com.huafon.support.core.pojo.R;
import com.huafon.support.exceptions.ServiceException;
import com.huafon.thrd.BoxApiClient;
import com.huafon.thrd.config.BoxFeignConfig;
import com.huafon.thrd.dto.BoxCommonRsp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2023-04-27 10:13:18
 */
@Api(tags = "区域")
@RestController
@RequestMapping("/area")
@Slf4j
public class AreaController {

    private final AiAreaService aiAreaService;
    private final AiAreaGroupService aiAreaGroupService;
    private final AiBoxManageService aiBoxManageService;
    private final AiAreaStatLogService aiAreaStatLogService;

    @Autowired
    public AreaController(AiAreaService aiAreaService,
                          AiAreaGroupService aiAreaGroupService,
                          AiBoxManageService aiBoxManageService,
                          AiAreaStatLogService aiAreaStatLogService) {
        this.aiAreaService = aiAreaService;
        this.aiAreaGroupService = aiAreaGroupService;
        this.aiBoxManageService = aiBoxManageService;
        this.aiAreaStatLogService = aiAreaStatLogService;
    }

    @PostMapping("/add")
    @ApiOperation(value = "添加区域")
    public R<AiArea> add(@RequestBody @Valid AreaAddReqVO req) {

        return R.ok(aiAreaService.add(req));
    }

    @PostMapping("/add/{sequence}")
    @ApiOperation(value = "远程调用-添加区域")
    public R<AiArea> add(@PathVariable String sequence, @RequestBody @Valid AreaAddReqVO req) {

        try {
            AiBoxManage aiBoxManage = aiBoxManageService.getAiBoxManage(sequence);
            BoxApiClient boxApiClient = BoxFeignConfig.createApiClient("http://" + aiBoxManage.getIp() + ":" + aiBoxManage.getPort() + "");
            BoxCommonRsp<AiArea> commonRsp = boxApiClient.addArea(req);
            if (commonRsp.getCode().equals(1)) {
                throw new ServiceException(commonRsp.getMsg());
            } else {
                return R.ok(commonRsp.getData());
            }
        } catch (Exception exception) {
            throw new ServiceException(exception.getMessage());
        }
    }

    @PostMapping("/edit")
    @ApiOperation(value = "编辑区域")
    public R<AiArea> edit(@RequestBody @Valid AreaEditReqVO req) {

        return R.ok(aiAreaService.edit(req));
    }

    @PostMapping("/edit/{sequence}")
    @ApiOperation(value = "远程调用-编辑区域")
    public R<AiArea> edit(@PathVariable String sequence, @RequestBody @Valid AreaEditReqVO req) {

        try {
            AiBoxManage aiBoxManage = aiBoxManageService.getAiBoxManage(sequence);
            BoxApiClient boxApiClient = BoxFeignConfig.createApiClient("http://" + aiBoxManage.getIp() + ":" + aiBoxManage.getPort() + "");
            BoxCommonRsp<AiArea> commonRsp = boxApiClient.editArea(req);
            if (commonRsp.getCode().equals(1)) {
                throw new ServiceException(commonRsp.getMsg());
            } else {
                return R.ok(commonRsp.getData());
            }
        } catch (Exception exception) {
            throw new ServiceException(exception.getMessage());
        }
    }


    @DeleteMapping("/delete/{areaId}")
    @ApiOperation(value = "删除区域")
    public R delete(@PathVariable Long areaId) {
        aiAreaService.delete(areaId);
        return R.ok();
    }

    @DeleteMapping("/delete/{areaId}/{sequence}")
    @ApiOperation(value = "远程调用-删除区域")
    public R delete(@PathVariable String sequence, @PathVariable Long areaId) {

        try {
            AiBoxManage aiBoxManage = aiBoxManageService.getAiBoxManage(sequence);
            BoxApiClient boxApiClient = BoxFeignConfig.createApiClient("http://" + aiBoxManage.getIp() + ":" + aiBoxManage.getPort() + "");
            BoxCommonRsp commonRsp = boxApiClient.deleteArea(areaId);
            if (commonRsp.getCode().equals(1)) {
                throw new ServiceException(commonRsp.getMsg());
            } else {
                return R.ok(commonRsp.getData());
            }
        } catch (Exception exception) {
            throw new ServiceException(exception.getMessage());
        }
    }


    @PostMapping("/list")
    @ApiOperation(value = "区域列表")
    public R<CommonPage<AreaListResVO>> getAreaPage(@RequestBody @Valid AreaListReqVO req) {
        return R.ok(aiAreaService.getAreaPage(req));
    }

    @PostMapping("/list/{sequence}")
    @ApiOperation(value = "远程调用-区域列表")
    public R<CommonPage<AreaListResVO>> getAreaPage(@PathVariable String sequence, @RequestBody @Valid AreaListReqVO req) {
        try {
            AiBoxManage aiBoxManage = aiBoxManageService.getAiBoxManage(sequence);
            BoxApiClient boxApiClient = BoxFeignConfig.createApiClient("http://" + aiBoxManage.getIp() + ":" + aiBoxManage.getPort() + "");
            BoxCommonRsp<CommonPage<AreaListResVO>> commonRsp = boxApiClient.getAreaPage(req);
            if (commonRsp.getCode().equals(1)) {
                throw new ServiceException(commonRsp.getMsg());
            } else {
                return R.ok(commonRsp.getData());
            }
        } catch (Exception exception) {
            throw new ServiceException(exception.getMessage());
        }
    }

    @PostMapping("/group/list")
    @ApiOperation(value = "一级区域分组列表")
    public R<CommonPage<AreaGroupListResVO>> getAreaGroupPage(@RequestBody @Valid AreaGroupListReqVO req) {
        return R.ok(aiAreaGroupService.getAreaGroupPage(req));
    }

    @PostMapping("/group/list/{sequence}")
    @ApiOperation(value = "远程调用-区域分组列表")
    @AccessPermission
    public R<CommonPage<AreaGroupListResVO>> getAreaGroupPage(@PathVariable String sequence, @RequestBody @Valid AreaGroupListReqVO req) {
        try {
            AiBoxManage aiBoxManage = aiBoxManageService.getAiBoxManage(sequence);
            BoxApiClient boxApiClient = BoxFeignConfig.createApiClient("http://" + aiBoxManage.getIp() + ":" + aiBoxManage.getPort() + "");
            BoxCommonRsp<CommonPage<AreaGroupListResVO>> commonRsp = boxApiClient.getAreaGroupPage(req);
            if (commonRsp.getCode().equals(1)) {
                throw new ServiceException(commonRsp.getMsg());
            } else {
                return R.ok(commonRsp.getData());
            }
        } catch (Exception exception) {
            throw new ServiceException(exception.getMessage());
        }
    }

    // 后端sql无法对二级进行过滤后、然后再对一级区域进行分页，区域总数量不多，一次性查询出来所有区域基本信息 在前端进行分页 start
    @PostMapping("/group/all")
    @ApiOperation(value = "获取所有aibox关联的一级区域列表")
    @AccessPermission
    public R<List<AreaGroupResVO>> getAreaGroupAll(@RequestBody @Valid AreaGroupAllReqVO req) {
        return R.ok(aiAreaGroupService.getAreaGroupAll(req));
    }

    @PostMapping("/group/all/current")
    @ApiOperation(value = "获取当前aibox关联的一级区域列表")
    public R<List<AreaGroupResVO>> getCurrentAreaGroupAll(@RequestBody @Valid CurrentAreaGroupAllReqVO req) {
        return R.ok(aiAreaGroupService.getCurrentAreaGroupAll(req));
    }

    @PostMapping("/group/all/{sequence}")
    @ApiOperation(value = "远程调用-获取指定aibox关联的一级区域列表")
    @AccessPermission
    public R<List<AreaGroupResVO>> getAreaGroupAll(@PathVariable String sequence, @RequestBody @Valid AreaGroupAllReqVO req) {
        try {
            AiBoxManage aiBoxManage = aiBoxManageService.getAiBoxManage(sequence);
            BoxApiClient boxApiClient = BoxFeignConfig.createApiClient("http://" + aiBoxManage.getIp() + ":" + aiBoxManage.getPort() + "");
            CurrentAreaGroupAllReqVO redirectReq = new CurrentAreaGroupAllReqVO();
            redirectReq.setSequence(sequence);
            redirectReq.setAreaName(req.getAreaName());
            redirectReq.setPageSize(req.getPageSize());
            redirectReq.setPageNo(req.getPageNo());
            redirectReq.setOrders(req.getOrders());
            redirectReq.setShowCameraInfo(req.getShowCameraInfo());
            BoxCommonRsp<List<AreaGroupResVO>> commonRsp = boxApiClient.getCurrentAreaGroupAll(redirectReq);
            if (commonRsp.getCode().equals(1)) {
                throw new ServiceException(commonRsp.getMsg());
            } else {
                return R.ok(commonRsp.getData());
            }
        } catch (Exception exception) {
            throw new ServiceException(exception.getMessage());
        }
    }
    // 后端sql无法对二级进行过滤后、然后再对一级区域进行分页，区域总数量不多，一次性查询出来所有区域基本信息 在前端进行分页 end

    @PostMapping("/stat/list")
    @ApiOperation(value = "区域统计列表")
    public R<CommonPage<AreaStatPageResVO>> getAreaStatPage(@RequestBody @Valid AreaStatPageReqVO req) {
        return R.ok(aiAreaService.getAreaStatPage(req));
    }


    @PostMapping("/stat/list/{sequence}")
    @ApiOperation(value = "远程调用-区域统计列表")
    public R<CommonPage<AreaStatPageResVO>> getAreaStatPage(@PathVariable String sequence, @RequestBody @Valid AreaStatPageReqVO req) {
        try {
            AiBoxManage aiBoxManage = aiBoxManageService.getAiBoxManage(sequence);
            BoxApiClient boxApiClient = BoxFeignConfig.createApiClient("http://" + aiBoxManage.getIp() + ":" + aiBoxManage.getPort() + "");
            BoxCommonRsp<CommonPage<AreaStatPageResVO>> commonRsp = boxApiClient.getAreaStatPage(req);
            if (commonRsp.getCode().equals(1)) {
                throw new ServiceException(commonRsp.getMsg());
            } else {
                return R.ok(commonRsp.getData());
            }
        } catch (Exception exception) {
            throw new ServiceException(exception.getMessage());
        }
    }

    @PostMapping("/stat/batch/query")
    @ApiOperation(value = "批量获取aibox关联的区域统计信息")
    @AccessPermission
    public R<Map<String, List<AreaStatBatchQueryResVO>>> statBatchQuery(@RequestBody @Valid AreaStatBatchQueryReqVO req) {
        return R.ok(aiAreaService.statBatchQuery(req));
    }

    @PostMapping("/stat/batch/query/current")
    @ApiOperation(value = "获取当前aibox关联的区域统计信息")
    public R<List<AreaStatBatchQueryResVO>> currentStatBatchQuery(@RequestBody @Valid CurrentAreaStatBatchQueryReqVO req) {
        return R.ok(aiAreaService.currentStatBatchQuery(req));
    }


    @PostMapping("/stat/batch/query/{sequence}")
    @ApiOperation(value = "远程调用-获取指定aibox关联的区域统计信息")
    @AccessPermission
    public R<List<AreaStatBatchQueryResVO>> statBatchQuery(@PathVariable String sequence, @RequestBody @Valid CurrentAreaStatBatchQueryReqVO req) {
        try {
            AiBoxManage aiBoxManage = aiBoxManageService.getAiBoxManage(sequence);
            BoxApiClient boxApiClient = BoxFeignConfig.createApiClient("http://" + aiBoxManage.getIp() + ":" + aiBoxManage.getPort() + "");
            BoxCommonRsp<List<AreaStatBatchQueryResVO>> commonRsp = boxApiClient.currentStatBatchQuery(req);
            if (commonRsp.getCode().equals(1)) {
                throw new ServiceException(commonRsp.getMsg());
            } else {
                return R.ok(commonRsp.getData());
            }
        } catch (Exception exception) {
            throw new ServiceException(exception.getMessage());
        }
    }

    // 查询历史log 日志 start
    @PostMapping("/stat/log/query")
    @ApiOperation(value = "获取区域统计历史log")
    public R<CommonPage<AreaStatLogResVO>> statLogQuery(@RequestBody @Valid AreaStatLogQueryReqVO req) {
        CommonPage<AreaStatLogResVO> result = aiAreaStatLogService.getStatLogPage(req);
        return R.ok(result);
    }

    @PostMapping("/stat/log/query/{sequence}")
    @ApiOperation(value = "远程调用-获取区域统计历史log")
    public R<CommonPage<AreaStatLogResVO>> statLogQuery(@PathVariable String sequence, @RequestBody @Valid AreaStatLogQueryReqVO req) {
        try {
            AiBoxManage aiBoxManage = aiBoxManageService.getAiBoxManage(sequence);
            BoxApiClient boxApiClient = BoxFeignConfig.createApiClient("http://" + aiBoxManage.getIp() + ":" + aiBoxManage.getPort());
            BoxCommonRsp<CommonPage<AreaStatLogResVO>> commonRsp = boxApiClient.statLogQuery(req);
            if (commonRsp.getCode().equals(1)) {
                throw new ServiceException(commonRsp.getMsg());
            } else {
                return R.ok(commonRsp.getData());
            }
        } catch (Exception exception) {
            throw new ServiceException(exception.getMessage());
        }
    }
    // 查询历史log 日志 end

    @PostMapping("/pull/list")
    @ApiOperation(value = "返回区域的下拉框")
    public R<List<AreaPullListResVO>> getAreaPullList() {
        return R.ok(aiAreaService.getAreaPullList());
    }

    @PostMapping("/pull/list/{sequence}")
    @ApiOperation(value = "远程调用-返回区域的下拉框")
    @AccessPermission
    public R<List<AreaPullListResVO>> getAreaPullList(@PathVariable String sequence) {
        try {
            AiBoxManage aiBoxManage = aiBoxManageService.getAiBoxManage(sequence);
            BoxApiClient boxApiClient = BoxFeignConfig.createApiClient("http://" + aiBoxManage.getIp() + ":" + aiBoxManage.getPort() + "");
            BoxCommonRsp<List<AreaPullListResVO>> commonRsp = boxApiClient.getAreaPullList();
            if (commonRsp.getCode().equals(1)) {
                throw new ServiceException(commonRsp.getMsg());
            } else {
                return R.ok(commonRsp.getData());
            }
        } catch (Exception exception) {
            throw new ServiceException(exception.getMessage());
        }
    }

    @PostMapping("/stat/v2")
    @ApiOperation(value = "算法推送-v2区域统计")
    public R areaStatV2(@RequestBody @Valid AlgAreaStatV2ReqVO req) {
        aiAreaService.areaStatV2(req);
        aiAreaService.notifyLed();
        return R.ok();
    }

    @PostMapping("/stat/v3")
    @ApiOperation(value = "算法推送-v3区域统计")
    public R<AiAreaStatV3ResVO> areaStatV3(@RequestBody @Valid AlgAreaStatV3ReqVO req) {
        long start = System.currentTimeMillis();
        List<AlgCameraStatInfo> failList = aiAreaService.areaStatV3(req);
        AiAreaStatV3ResVO aiAreaStatV3ResVO = new AiAreaStatV3ResVO();
        aiAreaStatV3ResVO.setFailCameraStatInfoList(failList);
        log.info("areaStatV3 耗时:{}", System.currentTimeMillis() - start);
        aiAreaService.notifyLed();
        return R.ok(aiAreaStatV3ResVO);
    }

    @PostMapping("/stat/num/fix")
    @ApiOperation(value = "区域统计强制修正")
    public R areaStatFix(@RequestBody @Valid AlgAreaStatFixReqVO req) {
        aiAreaService.areaStatFix(req);
        return R.ok();
    }

    @PostMapping("/stat/num/fix/{sequence}")
    @ApiOperation(value = "远程调用-区域统计强制修正")
    @AccessPermission
    public R areaStatFix(@PathVariable String sequence, @RequestBody @Valid AlgAreaStatFixReqVO req) {
        try {
            AiBoxManage aiBoxManage = aiBoxManageService.getAiBoxManage(sequence);
            BoxApiClient boxApiClient = BoxFeignConfig.createApiClient("http://" + aiBoxManage.getIp() + ":" + aiBoxManage.getPort());
            BoxCommonRsp commonRsp = boxApiClient.areaStatFix(req);
            if (commonRsp.getCode().equals(1)) {
                throw new ServiceException(commonRsp.getMsg());
            } else {
                return R.ok(commonRsp.getData());
            }
        } catch (Exception exception) {
            throw new ServiceException(exception.getMessage());
        }
    }

    @PostMapping("/stat")
    @ApiOperation(value = "算法推送-区域统计")
    public R areaStat(@RequestBody @Valid AreaStatReqVO req) {
        aiAreaService.areaStat(req);
        return R.ok();
    }

    @PostMapping("/push/alarm")
    @ApiOperation(value = "算法推送-区域告警")
    public R pushAreaAlarm(@RequestBody @Valid AiAreaAlarmPushVO req) {
        aiAreaService.pushAreaAlarm(req);
        return R.ok();
    }

    @PostMapping("/camera/stat")
    @ApiOperation(value = "区域的摄像头统计")
    public R areaCameraStat(@RequestBody @Valid AreaCameraStatReqVO req) {
        aiAreaService.areaCameraStat(req);
        return R.ok();
    }

    @GetMapping("/alarm/popup")
    @ApiOperation(value = "当前aibox区域告警的弹窗的信息--前端不需要调用")
    public R<AreaAlarmPopUpVO> areaAlarmPopUp() {
        return R.ok(aiAreaService.areaAlarmPopUp());
    }

    @GetMapping("/box/alarm/popup")
    @ApiOperation(value = "全部aibox区域告警的弹窗信息--前端调用")
    public R<List<AreaAlarmPopUpVO>> areaBoxAlarmPopUp() {

        List<AreaAlarmPopUpVO> vos = new ArrayList<>();
        try {
            List<AiBoxManage> aiBoxManages = aiBoxManageService.readAiBoxManages();
            aiBoxManages.forEach(item -> {
                BoxApiClient boxApiClient = BoxFeignConfig.createApiClient("http://" + item.getIp() + ":" + item.getPort() + "");
                BoxCommonRsp<AreaAlarmPopUpVO> commonRsp = boxApiClient.areaAlarmPopUp();
                if (commonRsp.getCode().equals(0)) {
                    AreaAlarmPopUpVO vo = commonRsp.getData();
                    vo.setName(item.getName());
                    vo.setSequenceNumber(item.getSequenceNumber());
                    vos.add(vo);
                }
            });

        } catch (Exception exception) {
            return R.ok(vos);
        }
        return R.ok(vos.stream().sorted(Comparator.comparing(AreaAlarmPopUpVO::getAlarmTime).reversed()).collect(Collectors.toList()));
    }
}