package com.huafon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huafon.models.entity.AiBoxRisk;
import com.huafon.models.vo.req.analysis.RiskCountReqVO;
import com.huafon.models.vo.resp.analysis.AllBoxRiskCountRespVO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
public interface AiBoxRiskMapper extends BaseMapper<AiBoxRisk> {

    IPage<AllBoxRiskCountRespVO> getBoxRiskPage(@Param("page") Page<AllBoxRiskCountRespVO> page, @Param("req") RiskCountReqVO req);
}
