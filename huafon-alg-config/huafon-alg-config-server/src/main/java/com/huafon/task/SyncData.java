package com.huafon.task;

import com.alibaba.fastjson.JSON;
import com.huafon.models.entity.AiBoxManage;
import com.huafon.models.entity.AiBoxRisk;
import com.huafon.models.vo.req.analysis.AiboxRiskReqVO;
import com.huafon.service.AiBoxManageService;
import com.huafon.service.AiBoxRiskService;
import com.huafon.thrd.BoxApiClient;
import com.huafon.thrd.config.BoxFeignConfig;
import com.huafon.thrd.dto.AIBoxInfoRsp;
import com.huafon.thrd.dto.BoxCommonRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2023-10-24 14:28:51
 */
@Slf4j
@Component
public class SyncData {

    private final AiBoxManageService aiBoxManageService;
    private final AiBoxRiskService aiBoxRiskService;

    public SyncData(AiBoxManageService aiBoxManageService,
                    AiBoxRiskService aiBoxRiskService) {
        this.aiBoxManageService = aiBoxManageService;
        this.aiBoxRiskService = aiBoxRiskService;
    }


    @Scheduled(initialDelay=1000, fixedDelay=6000)
    public void doRiskTask() {

        log.info("存在的风险和近7天告警统计同步开始");
        List<AiBoxManage> aiBoxManages = aiBoxManageService.readAiBoxManages();
        LocalDate today = LocalDate.now();
        String date = today.getYear() + "-" + today.getMonthValue() + "-" + today.getDayOfMonth();
        aiBoxManages.stream().forEach(item -> {
            try {
                AiBoxManage aiBoxManage = aiBoxManageService.getAiBoxManage(item.getSequenceNumber());
                BoxApiClient boxApiClient = BoxFeignConfig.createApiClient("http://" + aiBoxManage.getIp() + ":" + aiBoxManage.getPort() + "");
                BoxCommonRsp<List<AiBoxRisk>> commonRsp = boxApiClient.getRemoteAlarmRisk(AiboxRiskReqVO.builder()
                        .sequence(item.getSequenceNumber())
                        .boxName(item.getName())
                        .date(date)
                        .build());
                log.info(commonRsp.getData().toString());
                if (commonRsp.getCode().equals(1)) {
                    log.info(commonRsp.getMsg());
                } else {
                    aiBoxRiskService.batchSave(commonRsp.getData());
                }
            } catch (Exception exception) {
                log.info(exception.getMessage());
            }
        });
        log.info("存在的风险和近7天告警统计同步结束");
    }

    @Scheduled(initialDelay=1000, fixedDelay=20000)
    public void doBoxInfoTask() {

        log.info("aibox技能，摄像头和告警信息同步开始");
        List<AiBoxManage> aiBoxManages = aiBoxManageService.readAiBoxManages();
        aiBoxManages.stream().forEach(item -> {
            try {
                BoxApiClient boxApiClient = BoxFeignConfig.createApiClient("http://" + item.getIp() + ":" + item.getPort() + "");
                AIBoxInfoRsp dto = boxApiClient.getAIBoxInfo().getData();
                log.info(JSON.toJSONString(dto));
                aiBoxManageService.updateAiBoxManage(AiBoxManage.builder()
                        .id(item.getId())
                        .offlineNumber(dto.getOfflineNumber())
                        .onlineNumber(dto.getOnlineNumber())
                        .todayNumber(dto.getTodayNumber())
                        .yesterdayNumber(dto.getYesterdayNumber())
                        .capacity(JSON.toJSONString(dto.getCapacity()))
                        .enableNumber(dto.getEnableAiNum())
                        .build());
            } catch (Exception ex) {
                log.info(ex.getMessage());
            }
        });

        log.info("aibox技能，摄像头和告警信息同步结束");
    }

}