package com.huafon.task;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.huafon.dao.mapper.AiAreaStatMapper;
import com.huafon.framework.mybatis.enums.DelFlag;
import com.huafon.models.entity.*;
import com.huafon.models.enums.NumTypeEnum;
import com.huafon.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 计算区域人数
 *
 */
@Slf4j
public class AreaNumFixTask implements Runnable {

    private Long areaId;
    private AiAreaStatMapper aiAreaStatMapper;
    // private AiCameraStatSyncLogMapper aiCameraStatSyncLogMapper;
    private CameraService cameraService;
    private AiAreaStatService aiAreaStatService;
    private AreaAiEventLogService areaAiEventLogService;
    private AiAreaStatLogService aiAreaStatLogService;
    private AiAreaService aiAreaService;
    private AiCameraStatSyncLogService aiCameraStatSyncLogService;
    private PlatformTransactionManager transactionManager;

    public AreaNumFixTask(Long areaId,
                          AiAreaStatMapper aiAreaStatMapper,
                          AiAreaStatService aiAreaStatService,
                          CameraService cameraService,
                          AreaAiEventLogService areaAiEventLogService,
                          AiAreaStatLogService aiAreaStatLogService,
                          AiAreaService aiAreaService,
                          AiCameraStatSyncLogService aiCameraStatSyncLogService,
                          PlatformTransactionManager transactionManager) {
        this.areaId = areaId;
        this.aiAreaStatMapper = aiAreaStatMapper;
        // this.aiCameraStatSyncLogMapper = aiCameraStatSyncLogMapper;
        this.cameraService = cameraService;
        this.areaAiEventLogService = areaAiEventLogService;
        this.aiAreaStatService = aiAreaStatService;
        this.aiAreaStatLogService = aiAreaStatLogService;
        this.aiAreaService = aiAreaService;
        this.aiCameraStatSyncLogService = aiCameraStatSyncLogService;
        this.transactionManager = transactionManager;
    }

    @Override
    public void run() {
        // 更新数据
        log.info("AreaNumStatTask start, areaId:{}", areaId);
        TransactionStatus transactionStatus = this.transactionManager.getTransaction(TransactionDefinition.withDefaults());
        try {
            boolean lockSuccess = aiCameraStatSyncLogService.tryGetAreaLogLock(areaId);
            if (! lockSuccess) {
                this.transactionManager.rollback(transactionStatus);
                return;
            }

            LambdaQueryWrapper<AiAreaStat> areaStatLockQuery = new LambdaQueryWrapper<>();
            areaStatLockQuery.eq(AiAreaStat::getAreaId, areaId);
            AiAreaStat aiAreaStat = aiAreaStatMapper.selectOne(areaStatLockQuery);
            AiArea aiArea = aiAreaService.getAiArea(areaId);

            // 查询所有未消费的日志记录
            List<AiCameraStatSyncLog> waitConsumeLogList =
                    aiCameraStatSyncLogService.getWaitConsumeSyncLogList(areaId);

            log.info("AreaNumStatTask waitConsumeSyncLog, areaId:{}, logListSize:{}", areaId,
                    waitConsumeLogList.size());

            // 数据排序
            waitConsumeLogList = waitConsumeLogList.stream()
                    .filter(Objects::nonNull)
                    .sorted(Comparator.comparingLong(AiCameraStatSyncLog::getId))
                    .collect(Collectors.toList());

            // 开始窗口计算：查询窗口内日志记录，更新区域总人数，触发告警
            AiCameraStatSyncLog lastSyncLog = null, lastAlarmSyncLog = null;
            int totalNum = Objects.nonNull(aiAreaStat) ? aiAreaStat.getNum() : 0;
            for (AiCameraStatSyncLog syncLog : waitConsumeLogList) {
                if (Objects.isNull(syncLog)) {
                    continue;
                }

                lastSyncLog = syncLog;
                log.info("AreaNumStatTask waitConsumeSyncLog, areaId:{}, logId:{}, lastWaitConsumeSyncLog:{}",
                        areaId,
                        JSON.toJSONString(syncLog)
                );

                if (!Objects.equals(syncLog.getFlagStatStatus(), 0)) {
                    continue;
                }

                totalNum = this.computeAreaPersonNum(syncLog, totalNum);

                boolean success = aiCameraStatSyncLogService.setSyncLogConsumeSuccess(syncLog);
                log.info("AreaNumStatTask consumeSyncLogSuccess finish, areaId:{}, success:{}, syncLogId:{}", areaId,
                        success,
                        syncLog.getId()
                );

                boolean needAlarm = needAlarm(totalNum, aiArea, lastSyncLog);
                if (needAlarm && Objects.isNull(lastAlarmSyncLog)) {
                    lastAlarmSyncLog = syncLog;
                }
            }

            // fix total num
            if (totalNum < 0) {
                totalNum = 0;
            }

            if (Objects.isNull(lastSyncLog)) {
                transactionManager.rollback(transactionStatus);
                return;
            }

            // 数据 -> 同步 区域统计表
            this.updateAreaNum(totalNum, lastSyncLog);
            log.info("AreaNumStatTask updateAreaNum finish, areaId:{}, totalNum:{}, syncLogId:{}", areaId,
                    totalNum,
                    lastSyncLog.getId()
            );
            // this.updateCameraNum(totalNum, lastWaitConsumeSyncLog);

            this.updateAreaStatLog(totalNum, aiArea, lastSyncLog);
            log.info("AreaNumStatTask updateAreaStatLog finish, areaId:{}, totalNum:{}, syncLogId:{}", areaId,
                    totalNum,
                    lastSyncLog.getId()
            );
            this.generateAlarm(totalNum, aiArea, lastAlarmSyncLog);
            log.info("AreaNumStatTask generateAlarm finish, areaId:{}, totalNum:{}, syncLogId:{}", areaId,
                    totalNum,
                    lastSyncLog.getId()
            );

            this.transactionManager.commit(transactionStatus);
        } catch (Exception e) {
            log.error("area num stat task error, areaId:{}", this.areaId, e);
            this.transactionManager.rollback(transactionStatus);
        }
    }

    public Integer computeAreaPersonNum(AiCameraStatSyncLog lastSyncLog, int baseNum) {
        Integer totalNum = baseNum;
        if (Objects.equals(lastSyncLog.getNumType(), NumTypeEnum.full.getCode())) {
            // 重置基数
            totalNum = lastSyncLog.getNum();
        } else {
            // 在基数基础之上进行累加
            totalNum = baseNum + lastSyncLog.getNum();
        }

        // 判负
        if (totalNum < 0) {
            totalNum = 0;
        }

        return totalNum;
    }

    public void updateAreaNum(int totalNum, AiCameraStatSyncLog lastWaitConsumeSyncLog) {
        AiAreaStat newAiAreaStat = new AiAreaStat();
        newAiAreaStat.setAreaId(lastWaitConsumeSyncLog.getAreaId());
        newAiAreaStat.setModifyTime(lastWaitConsumeSyncLog.getModifyTime());
        newAiAreaStat.setCreateTime(lastWaitConsumeSyncLog.getCreateTime());
        newAiAreaStat.setNum(totalNum);
        newAiAreaStat.setNumSyncVersion(lastWaitConsumeSyncLog.getId());
        aiAreaStatService.areaStat(newAiAreaStat, lastWaitConsumeSyncLog.getId());
    }

    public void updateCameraNum(int totalNum, AiCameraStatSyncLog lastWaitConsumeSyncLog) {
        // 数据 -> 同步 摄像头统计表
        // 数据 -> 同步 摄像头历史统计表
        Camera camera = new Camera();
        camera.setId(lastWaitConsumeSyncLog.getCameraId());
        camera.setAreaId(lastWaitConsumeSyncLog.getAreaId());
        camera.setNum(totalNum);
        camera.setModifyTime(lastWaitConsumeSyncLog.getModifyTime());
        cameraService.statCameraNum(camera);
    }

    private void updateAreaStatLog(int totalNum, AiArea aiArea, AiCameraStatSyncLog lastWaitConsumeSyncLog) {
        // 数据 -> 同步 区域历史统计表
        if (Objects.isNull(aiArea)) {
            return;
        }

        if (! Objects.equals(aiArea.getLimitOnState(), 1)) {
            return;
        }

        if (Objects.isNull(aiArea.getLimitNum())) {
            return;
        }

        AiAreaStatLog aiAreaStatLog = new AiAreaStatLog();
        aiAreaStatLog.setAreaId(lastWaitConsumeSyncLog.getAreaId());
        aiAreaStatLog.setLimitNum(aiArea.getLimitNum());
        aiAreaStatLog.setNum(totalNum);
        aiAreaStatLog.setIsDel(DelFlag.SAVE.getValue());
        aiAreaStatLog.setCreateTime(lastWaitConsumeSyncLog.getModifyTime());
        aiAreaStatLog.setModifyTime(lastWaitConsumeSyncLog.getModifyTime());
        aiAreaStatLogService.addStatLog(aiAreaStatLog);
    }

    public void generateAlarm(int totalNum, AiArea aiArea, AiCameraStatSyncLog lastWaitConsumeSyncLog) {
        if (Objects.isNull(lastWaitConsumeSyncLog)) {
            return;
        }

        Integer limitNum = aiArea.getLimitNum();
        AreaAiEventLog areaAiEventLog = AreaAiEventLog.builder()
                .areaId(lastWaitConsumeSyncLog.getAreaId())
                .aiType(lastWaitConsumeSyncLog.getAiType())
                .alarmNum(totalNum - limitNum)
                .limitNum(limitNum)
                .imageUrl(lastWaitConsumeSyncLog.getImgUrl())
                .catalog(lastWaitConsumeSyncLog.getCatalog())
                .createTime(lastWaitConsumeSyncLog.getModifyTime())
                .modifyTime(lastWaitConsumeSyncLog.getModifyTime())
                .isDel(DelFlag.SAVE.getValue())
                .build();
        areaAiEventLogService.saveAreaAiEventLog(areaAiEventLog);
    }

    /**
     * 判断是否需要进行告警
     *
     * @param totalNum
     * @param aiArea
     * @param lastWaitSyncLog
     * @return
     */
    public boolean needAlarm(int totalNum, AiArea aiArea, AiCameraStatSyncLog lastWaitSyncLog) {
        // 是否需要进行告警
        if (Objects.isNull(aiArea)) {
            return false;
        }

        if (! Objects.equals(aiArea.getLimitOnState(), 1)) {
            return false;
        }

        if (Objects.isNull(aiArea.getLimitNum())) {
            return false;
        }

        int limitNum = aiArea.getLimitNum();
        if (limitNum >= totalNum) {
            return false;
        }

        // 产品约定，人数减少 -> 不产生告警
        if (lastWaitSyncLog.getNum() < 0) {
            return false;
        }

        return true;
    }
}
