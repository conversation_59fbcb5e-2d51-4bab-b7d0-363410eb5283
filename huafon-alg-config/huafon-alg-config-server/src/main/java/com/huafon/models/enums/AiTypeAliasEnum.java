package com.huafon.models.enums;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2023-10-24 10:42:15
 */
public enum AiTypeAliasEnum {

    HELMET_CHECK("safety_helmet", "helmetCheckSum"),
    SAFETY_BELT("safety_harness_wearing", "safetyBeltSum"),
    BARE_SKIN_LEAKAGE("bare_skin_leakage", "bareSkinLeakageSum"),
    MASK("mask", "maskSum"),
    REFLECTIVE_VEST("reflective_vest", "reflectiveVestSum"),
    AREA_INTRUDE("area_intrude", "areaIntrudeSum"),
    WORK_CLOTHES("work_clothes", "workClothesSum"),
    ANTI_STATIC_CLOTHING("anti_static_clothing", "antiStaticClothingSum"),
    GOGGLE("goggle", "goggleSum"),
    SMOKE("smoke", "smokeSum"),
    RING_UP("ring_up", "ringUpSum"),
    PLAY_PHONE("play_phone", "playPhoneSum"),
    TUMBLE("tumble", "tumbleSum"),
    FACE_RECOGNITION("face_recognition", "faceRecognitionSum"),
    RETENTION("retention", "retentionSum"),
    INTRUSION("personnel_trespassing", "intrusionSum"),
    OVER_MAN("overcrowding", "overManSum"),
    UNDER_STAFFING("understaffing", "underStaffingSum"),
    SMOKE_FIRE("smoke_fire", "smokeFireSum"),
    AREA_STATISTICS("area_personnel_statistics", "areaStatisticsSum"),
    PERSONNEL_WALKING("personnel_walking", "personnelWalkingSum")
    ;

    public static final AiTypeAliasEnum[] copyOfValues = values();
    private String type;
    private String alias;

    AiTypeAliasEnum(String type, String alias) {
        this.type = type;
        this.alias = alias;
    }

    public static String getAlias(String type) {
        for (AiTypeAliasEnum value : copyOfValues) {
            if (value.getType().equals(type)) {
                return value.getAlias();
            }
        }
        return "";
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }
}