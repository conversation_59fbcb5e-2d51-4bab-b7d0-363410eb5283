package com.huafon.models.vo.resp.analysis;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2023-10-31 11:04:23
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel(value = "AllBoxRecentAlarmVO", description = "统计所有aibox最近告警")
public class AllBoxRecentAlarmVO {

    @ApiModelProperty(value = "aibox名称")
    private String boxName;

    @ApiModelProperty(value = "ip地址")
    private String ip;

    @ApiModelProperty(value = "端口")
    private String port;

    @ApiModelProperty(value = "AI能力类型")
    private String type;

    @ApiModelProperty(value = "AI能力类型备注")
    private String remark;

    @ApiModelProperty(value = "图片地址")
    private String imageUrl;

    @ApiModelProperty(value = "渲染图片地址")
    private String drawImageUrl;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}