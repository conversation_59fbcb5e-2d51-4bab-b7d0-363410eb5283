package com.huafon.models.vo.req.area;

import com.huafon.models.dto.AlgCameraStatInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel(value = "AlgAreaStatV3ReqVO", description = "算法区域统计V3")
public class AlgAreaStatV3ReqVO {

    @ApiModelProperty(value = "摄像头统计数组")
    private List<AlgCameraStatInfo> cameraStatInfoList;
}
