package com.huafon.models.vo.req.camera;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2023-06-28 14:34:31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "CameraPushStateVO", description = "ai服务器推送摄像头状态")
public class CameraPushStateVO {

    @ApiModelProperty(value = "摄像头rtsp")
    private String rtsp;

    @ApiModelProperty(value = "状态 1可用 0 不可用")
    @JsonIgnore
    private Integer state;

}