package com.huafon.models.vo.resp.camera;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.huafon.models.vo.resp.area.AiAreaSimpleResVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.jackson.JsonComponent;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> zhang
 * @program: huafon-security-environment
 * @description:
 * @date 2022-10-11 10:51:03
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "CameraAiConfigInfoVO", description = "摄像头ai配置信息")
@JsonComponent
public class CameraAiConfigInfoVO {

    @ApiModelProperty(value = "摄像头id")
    private Long cameraId;

    @ApiModelProperty(value = "摄像头序列号")
    private String serialNumber;

    @ApiModelProperty(value = "摄像头名称")
    private String cameraName;

    @ApiModelProperty(value = "rtsp地址")
    private String rtsp;

    @ApiModelProperty(value = "状态 1可用 0 不可用")
    private Integer state;

    @ApiModelProperty(value = "ai配置项")
    @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
    private List<AiConfig> configs;

    @Deprecated
    @ApiModelProperty(value = "区域id")
    private Long areaId;

    @ApiModelProperty(value = "区域列表")
    private List<AiAreaSimpleResVO> areaList;

    @ApiModelProperty(value = "类型分为demo 和 real；demo为demo视频流，real为真实的视频流")
    private String cameraType;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel(value = "AiConfig", description = "AiConfig")
    @JsonComponent
    public static class AiConfig {
        @ApiModelProperty(value = "AI能力类型（helmet_check：安全帽侦查 area_intrude：区域闯入 over_man：超员 under_staffing：缺员）")
        private String type;

        @ApiModelProperty(value = "AI能力类型名称")
        private String name;

        @ApiModelProperty(value = "是否启用")
        private Boolean enable;

        @ApiModelProperty(value = "灵敏度")
        private Integer sensitivity;

        @ApiModelProperty(value = "布防区域范围")
        private Object scopes;

        @ApiModelProperty(value = "进入轮廓")
        private Object inContour;

        @ApiModelProperty(value = "外出轮廓")
        private Object outContour;

        @ApiModelProperty(value = "布防时间")
        @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
        private List<DeployTime> deployTimes;

        public void setDeployTimes(String deployTime) {
            JSONArray array = JSON.parseArray(deployTime);
            List<DeployTime> deployTimes = new ArrayList<>();
            array.forEach(item -> {
                JSONObject jsonObject = JSONObject.parseObject(item.toString());
                deployTimes.add(DeployTime
                        .builder()
                        .week(jsonObject.get("week").toString())
                        .range(JSONArray.parseArray(jsonObject.get("range").toString(), String.class))
                        .build());
            });
            this.deployTimes = deployTimes;
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel(value = "DeployTime", description = "DeployTime")
    @JsonComponent
    public static class DeployTime {
        @ApiModelProperty(value = "星期几")
        private String week;
        @ApiModelProperty(value = "时间范围")
        private List<String> range;
    }


}