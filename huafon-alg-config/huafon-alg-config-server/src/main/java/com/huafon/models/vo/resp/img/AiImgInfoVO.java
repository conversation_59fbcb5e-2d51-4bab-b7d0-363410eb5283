package com.huafon.models.vo.resp.img;

import com.huafon.models.dto.tag.TagDisplayInfoDto;
import com.huafon.models.dto.tag.TagInfoDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 图元数据
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "AiImgInfoVO", description = "图元数据信息")
public class AiImgInfoVO implements Serializable {
    private static final long serialVersionUID = -8899598059649037545L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("摄像头名称")
    private String cameraName;

    @ApiModelProperty("图片推送时间 yyyy-MM-dd HH:mm:ss")
    private String pushTime;

    @ApiModelProperty("处理结果标记")
    private TagInfoDto resTagInfo;

    @ApiModelProperty("ai技能标签")
    private List<TagInfoDto> aiCapacityTagList;

    @ApiModelProperty("图片路径")
    private String imgUrl;

    @ApiModelProperty("图片目录")
    private String catalog;

    @ApiModelProperty("算法识别区域")
    private String scopes;

    @ApiModelProperty("推送识别数据")
    private String pushIdentifyData;

    @ApiModelProperty("图片渲染路径")
    private String drawImageUrl;
}
