package com.huafon.models.vo.req.area;

import com.huafon.framework.mybatis.pojo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2023-04-27 19:34:08
 */

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel(value = "AreaStatLogQueryReqVO", description = "区域统计历史记录查询")
public class AreaStatLogQueryReqVO extends PageRequest {

    @ApiModelProperty(value = "区域名称")
    private String areaName;

    @ApiModelProperty(value = "开始时间, yyyy-MM-dd HH:mm:ss")
    private String startTime;

    @ApiModelProperty(value = "结束时间, yyyy-MM-dd HH:mm:ss, 必填. 日志数据更新比较快，同一批数据翻页操作需要保证值相同",
            required = true)
    private String endTime;
}