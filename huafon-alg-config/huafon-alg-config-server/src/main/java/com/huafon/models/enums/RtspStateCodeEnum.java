package com.huafon.models.enums;

/**
 * <AUTHOR> zhang
 * @program: huafon-security-environment
 * @description:
 * @date 2022-03-30 19:12:09
 */
public enum RtspStateCodeEnum {
    RTSP_NORMAL(1004, "rtsp流读取状态正常"),
    RTSP_ABNORMAL(1005, "rtsp流读取状态异常");

    private Integer code;
    private String remark;

    RtspStateCodeEnum(Integer code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
