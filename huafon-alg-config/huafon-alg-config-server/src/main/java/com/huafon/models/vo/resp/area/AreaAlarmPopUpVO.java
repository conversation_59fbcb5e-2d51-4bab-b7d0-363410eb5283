package com.huafon.models.vo.resp.area;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huafon.models.enums.CameraAiTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2023-12-14 11:34:30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "AreaAlarmPopUpVO", description = "区域告警事件弹窗")
public class AreaAlarmPopUpVO {

    @ApiModelProperty(value = "aibox 名称")
    private String name;

    @ApiModelProperty(value = "aibox 序列号")
    private String sequenceNumber;

    @ApiModelProperty(value = "区域名称")
    private String AreaName;

    @ApiModelProperty(value = "告警内容")
    private String AlarmContent;

    @ApiModelProperty(value = "告警时间")
    private String alarmTime;

    public void setAlarmContent(String alarmContent) {
        this.AlarmContent = "发生" + CameraAiTypeEnum.getRemark(alarmContent) + "事件";
    }
}