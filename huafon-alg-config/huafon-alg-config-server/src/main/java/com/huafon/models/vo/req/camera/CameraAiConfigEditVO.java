package com.huafon.models.vo.req.camera;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.huafon.models.entity.CameraAiConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> zhang
 * @program: huafon-security-environment
 * @description:
 * @date 2022-10-11 19:28:14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "CameraAiConfigEditVO", description = "编辑摄像头信息")
public class CameraAiConfigEditVO {

    @ApiModelProperty(value = "摄像头id")
    @NotNull(message = "摄像头id不能为空")
    private Long cameraId;

    @ApiModelProperty(value = "AI能力类型（helmet_check：安全帽侦查 area_intrude：区域闯入 over_man：超员 under_staffing：缺员 foot_traffic_statistics: 人流量统计）")
    @NotBlank(message = "AI能力类型不能为空")
    private String type;

    @ApiModelProperty(value = "是否启用")
    private Boolean enable;

    @ApiModelProperty(value = "灵敏度")
    @Size(min = 0, max = 100)
    private Integer sensitivity;

    @ApiModelProperty(value = "布防区域范围")
    @JsonIgnore
    private String scope;

    @ApiModelProperty(value = "布防区域范围")
    private Object scopes;

    @ApiModelProperty(value = "进入轮廓")
    private Object inContour;

    @ApiModelProperty(value = "外出轮廓")
    private Object outContour;

    @ApiModelProperty(value = "布防时间 json格式为：[{\\\"week\\\":\\\"Monday\\\",\\\"range\\\":[\\\"0:00-13:00\\\",\\\"17:00-18:00\\\"]},{\\\"week\\\":\\\"Tuesday\\\",\\\"range\\\":[\\\"0:00-13:00\\\",\\\"17:00-18:00\\\"]},{\\\"week\\\":\\\"Wednesday\\\",\\\"range\\\":[\\\"0:00-13:00\\\",\\\"17:00-18:00\\\"]},{\\\"week\\\":\\\"Thursday\\\",\\\"range\\\":[\\\"0:00-13:00\\\",\\\"17:00-18:00\\\"]},{\\\"week\\\":\\\"Friday\\\",\\\"range\\\":[\\\"0:00-13:00\\\",\\\"17:00-18:00\\\"]},{\\\"week\\\":\\\"Saturday\\\",\\\"range\\\":[\\\"0:00-13:00\\\",\\\"17:00-18:00\\\"]},{\\\"week\\\":\\\"Sunday\\\",\\\"range\\\":[\\\"0:00-13:00\\\",\\\"17:00-18:00\\\"]}]")
    @NotEmpty(message = "布防时间不能为空")
    private List<DeployTime> deployTimes;


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DeployTime {
        @ApiModelProperty(value = "星期几")
        private String week;
        @ApiModelProperty(value = "时间范围")
        private List<String> range;
    }


    public static CameraAiConfig convertToCameraAiConfig(CameraAiConfigEditVO item) {
        if (item == null) {
            return null;
        }
        CameraAiConfig cameraAiConfig = new CameraAiConfig();
        cameraAiConfig.setCameraId(item.getCameraId());
        cameraAiConfig.setType(item.getType());
        cameraAiConfig.setEnable(item.getEnable());
        cameraAiConfig.setSensitivity(item.getSensitivity());
        cameraAiConfig.setScope(JSON.toJSONString(item.getScopes()));
        if (Objects.nonNull(item.getInContour())){
            cameraAiConfig.setInContour(JSON.toJSONString(item.getInContour()));
        }
        if (Objects.nonNull(item.getOutContour())){
            cameraAiConfig.setOutContour(JSON.toJSONString(item.getOutContour()));
        }
        cameraAiConfig.setDeployTime(JSON.toJSONString(item.getDeployTimes()));
        cameraAiConfig.setCreateTime(new Date());
        cameraAiConfig.setModifyTime(new Date());
        return cameraAiConfig;
    }

//    UPDATE hf_camera_ai_config SET deploy_time = replace(deploy_time,'"range":','"range":[');
//    UPDATE hf_camera_ai_config SET deploy_time = replace(deploy_time,',"week":','],"week":');
}