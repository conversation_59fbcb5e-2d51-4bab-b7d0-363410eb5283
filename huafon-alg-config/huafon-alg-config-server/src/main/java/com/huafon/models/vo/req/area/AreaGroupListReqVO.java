package com.huafon.models.vo.req.area;

import com.huafon.framework.mybatis.pojo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2023-04-27 10:53:08
 */

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel(value = "AreaGroupListReqVO", description = "查询区域分组列表")
public class AreaGroupListReqVO extends PageRequest {

    @ApiModelProperty(value = "区域分组名称")
    private String name;

}