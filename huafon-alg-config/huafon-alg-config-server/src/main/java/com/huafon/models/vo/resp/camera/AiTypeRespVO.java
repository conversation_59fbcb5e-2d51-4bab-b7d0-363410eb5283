package com.huafon.models.vo.resp.camera;

import com.huafon.models.entity.CameraAiConfig;
import com.huafon.models.enums.CameraAiTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2022-12-05 19:48:57
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "AiTypeRespVO", description = "ai类型返回")
public class AiTypeRespVO {

    @ApiModelProperty(value = "AI能力类型（helmet_check：安全帽侦查 area_intrude：区域闯入 over_man：超员 under_staffing：缺员）")
    private String type;

    @ApiModelProperty(value = "AI能力类型名称")
    private String name;

    @ApiModelProperty(value = "Ai能力是否启用 1启用 0 不启用")
    private Boolean enable;

    public static AiTypeRespVO convertToAiTypeRespVo(CameraAiConfig cameraAiConfig) {
        if (cameraAiConfig == null) {
            return null;
        }
        AiTypeRespVO aiTypeRespVo = new AiTypeRespVO();
        aiTypeRespVo.setType(cameraAiConfig.getType());
        aiTypeRespVo.setName(CameraAiTypeEnum.getRemark(cameraAiConfig.getType()));
        aiTypeRespVo.setEnable(cameraAiConfig.getEnable());
        return aiTypeRespVo;
    }
}