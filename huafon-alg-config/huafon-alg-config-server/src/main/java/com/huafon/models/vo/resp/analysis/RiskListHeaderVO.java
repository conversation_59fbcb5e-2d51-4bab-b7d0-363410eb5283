package com.huafon.models.vo.resp.analysis;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2023-10-19 11:10:12
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel(value = "RiskListHeaderVO", description = "数据统计分析列表表头")
public class RiskListHeaderVO {

    @ApiModelProperty(value = "列表第一列")
    private String column0;

    @ApiModelProperty(value = "列表第二列")
    private String column1;

    @ApiModelProperty(value = "列表第三列")
    private String column2;

    @ApiModelProperty(value = "列表其他列动态渲染")
    private List<HeaderColumn> headerColumns;


    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    @ApiModel(value = "HeaderColumn", description = "动态表头")
    public static class HeaderColumn {

        private String fieldType;

        private String aliasType;

        private String columnName;
    }
}