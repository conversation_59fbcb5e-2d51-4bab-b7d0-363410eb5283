package com.huafon.models.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.huafon.models.entity.Camera;
import com.huafon.models.enums.CameraTypeEnum;
import com.huafon.service.impl.support.CustomRow;
import lombok.*;

import java.util.Date;


/**
 * 摄像头导入对象
 *
 * <AUTHOR>
 * @since 2022-10-20 15:24
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@EqualsAndHashCode
public class CameraUploadDTO extends CustomRow {

    @ExcelProperty(index = 0, value = "序列ID")
    private String serialNumber;

    @ExcelProperty(index = 1, value = "摄像头名称")
    private String cameraName;

    @ExcelProperty(index = 2, value = "视频流地址")
    private String rtsp;

    public static Camera convertToCamera(CameraUploadDTO cameraUploadDTO) {
        if (cameraUploadDTO == null) {
            return null;
        }
        Camera camera = new Camera();
        camera.setSerialNumber(cameraUploadDTO.getSerialNumber());
        camera.setCameraName(cameraUploadDTO.getCameraName());
        camera.setRtsp(cameraUploadDTO.getRtsp());
        camera.setState(1);
        camera.setCameraType(CameraTypeEnum.REAL.getCode());
        camera.setCreateTime(new Date());
        camera.setModifyTime(new Date());
        return camera;
    }

}
