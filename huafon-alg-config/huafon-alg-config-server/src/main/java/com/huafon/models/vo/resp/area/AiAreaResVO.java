package com.huafon.models.vo.resp.area;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.huafon.models.entity.Camera;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2023-04-27 10:55:38
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "AiAreaResVO", description = "区域信息返回")
public class AiAreaResVO {

    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "区域名称")
    private String name;

    @ApiModelProperty(value = "区域人数")
    private Integer num;

    @ApiModelProperty(value = "限制人数")
    private Integer limitNum;

    @ApiModelProperty(value = "限制人数开启状态：0为未开启；1为开启；")
    private Integer limitOnState;

    @ApiModelProperty(value = "摄像头列表")
    private List<AreaListResVO.CameraList> cameraLists;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel(value = "CameraList", description = "摄像头列表")
    public static class CameraList {

        @ApiModelProperty(value = "摄像头id")
        private Long cameraId;

        @ApiModelProperty(value = "摄像头名称")
        private String cameraName;

        @ApiModelProperty(value = "限制人数开启状态：0为未开启；1为开启；")
        private Integer limitOnState;

        @ApiModelProperty(value = "状态 1可用 0 不可用")
        private Integer state;

        @ApiModelProperty(value = "摄像头数量")
        private Integer num;
    }
}