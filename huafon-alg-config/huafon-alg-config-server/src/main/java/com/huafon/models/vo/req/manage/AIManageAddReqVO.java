package com.huafon.models.vo.req.manage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2023-10-08 10:20:21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "AIManageAddReqVO", description = "AI管理添加请求对象")
public class AIManageAddReqVO {

    @ApiModelProperty(value = "aibox名称")
    private String name;

    @ApiModelProperty(value = "序列号")
    private String sequenceNumber;

    @ApiModelProperty(value = "ip地址")
    private String ip;

    @ApiModelProperty(value = "端口")
    private String port;

}