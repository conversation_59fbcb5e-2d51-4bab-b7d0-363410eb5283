package com.huafon.models.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2023-04-27 10:08:31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("hf_ai_area")
@ApiModel(value="AiArea对象", description="")
public class AiArea implements Serializable {

    private static final long serialVersionUID = 7750961394390933429L;

    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "区域名称")
    private String name;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date modifyTime;

    @ApiModelProperty(value = "是否删除 0 正常 1 删除")
    private Integer isDel;

    @ApiModelProperty(value = "限制人数")
    private Integer limitNum;

    @ApiModelProperty(value = "限制人数开启状态：0为未开启；1为开启；")
    private Integer limitOnState;
}
