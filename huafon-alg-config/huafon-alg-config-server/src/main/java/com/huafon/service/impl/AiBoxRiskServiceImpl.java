package com.huafon.service.impl;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huafon.dao.mapper.AiBoxRiskMapper;
import com.huafon.framework.mybatis.enums.DelFlag;
import com.huafon.models.entity.AiBoxRisk;
import com.huafon.service.AiBoxRiskService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
@Service
public class AiBoxRiskServiceImpl extends ServiceImpl<AiBoxRiskMapper, AiBoxRisk> implements AiBoxRiskService {

    private final AiBoxRiskMapper aiBoxRiskMapper;

    public AiBoxRiskServiceImpl(AiBoxRiskMapper aiBoxRiskMapper) {
        this.aiBoxRiskMapper = aiBoxRiskMapper;
    }

    @Override
    public List<AiBoxRisk> getAiBoxRisks(DateTime startTime, DateTime endTime) {
        List<AiBoxRisk> aiBoxRisks = new LambdaQueryChainWrapper<>(aiBoxRiskMapper)
                .eq(AiBoxRisk::getIsDel, DelFlag.SAVE.getValue())
                .between(AiBoxRisk::getTime, startTime, endTime)
                .list();
        return aiBoxRisks;
    }

    @Override
    public void batchSave(List<AiBoxRisk> aiBoxRisks) {

        aiBoxRisks.stream().forEach(item -> {
            if (Objects.isNull(this.getAiBoxRisk(item))) {
                this.saveAiBoxRisk(item);
            } else {
                this.updateAiBoxRisk(item);
            }
        });
    }


    /**
     * @param aiBoxRisk
     * @return void
     * <AUTHOR>
     * @describe: 插入aibox统计数据
     * @date 2023/10/24 16:47
     */
    private void saveAiBoxRisk(AiBoxRisk aiBoxRisk) {
        aiBoxRisk.setCreateTime(new Date());
        aiBoxRisk.setModifyTime(new Date());
        aiBoxRiskMapper.insert(aiBoxRisk);
    }


    /**
     * @param aiBoxRisk
     * @return void
     * <AUTHOR>
     * @describe: 更新aibox统计数据
     * @date 2023/10/24 16:50
     */
    private void updateAiBoxRisk(AiBoxRisk aiBoxRisk) {
        aiBoxRisk.setModifyTime(new Date());
        aiBoxRiskMapper.update(aiBoxRisk, new LambdaQueryWrapper<AiBoxRisk>()
                .eq(AiBoxRisk::getBoxSequence, aiBoxRisk.getBoxSequence())
                .eq(AiBoxRisk::getCameraSequence, aiBoxRisk.getCameraSequence())
                .eq(AiBoxRisk::getAiType, aiBoxRisk.getAiType())
                .eq(AiBoxRisk::getTime, aiBoxRisk.getTime())
        );
    }

    /**
     * @param aiBoxRisk
     * @return com.huafon.models.entity.AiBoxRisk
     * <AUTHOR>
     * @describe: 查找是否存在这个记录
     * @date 2023/10/24 18:51
     */
    private AiBoxRisk getAiBoxRisk(AiBoxRisk aiBoxRisk) {
        AiBoxRisk one = new LambdaQueryChainWrapper<>(aiBoxRiskMapper)
                .eq(AiBoxRisk::getBoxSequence, aiBoxRisk.getBoxSequence())
                .eq(AiBoxRisk::getCameraSequence, aiBoxRisk.getCameraSequence())
                .eq(AiBoxRisk::getAiType, aiBoxRisk.getAiType())
                .eq(AiBoxRisk::getTime, aiBoxRisk.getTime())
                .eq(AiBoxRisk::getIsDel, DelFlag.SAVE.getValue())
                .one();
        return one;
    }

}
