package com.huafon.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huafon.enums.AiFootfallSceneTypeEnum;
import com.huafon.convertor.FactoryEntryExitConvertor;
import com.huafon.dao.mapper.FactoryEntryExitRecordMapper;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.models.dto.FactoryEntryExitFilterParam;
import com.huafon.models.entity.AiFootfallStat;
import com.huafon.models.entity.FactoryEntryExitRecord;
import com.huafon.models.vo.req.footfall.FactoryEntryExitAddReqVO;
import com.huafon.models.vo.req.footfall.FactoryEntryExitPageReqVO;
import com.huafon.models.vo.req.footfall.FactoryFootfallLiveStatReqVO;
import com.huafon.models.vo.req.footfall.FactoryEntryExitUpdateReqVO;
import com.huafon.models.vo.resp.footfall.FactoryEntryExitRespVO;
import com.huafon.models.vo.resp.footfall.FactoryFootfallLiveStatRespVO;
import com.huafon.service.AiFootfallStatService;
import com.huafon.service.FactoryEntryExitService;
import com.huafon.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class FactoryEntryExitServiceImpl implements FactoryEntryExitService {

    @Resource
    private FactoryEntryExitRecordMapper factoryEntryExitRecordMapper;

    @Resource
    private AiFootfallStatService aiFootfallStatService;

    @Override
    public CommonPage<FactoryEntryExitRespVO> getRecordPage(FactoryEntryExitPageReqVO reqVO) {
        // 组装分页查询参数
        Page<FactoryEntryExitRecord> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        FactoryEntryExitFilterParam filterParam = new FactoryEntryExitFilterParam();
        filterParam.setEntryExitType(reqVO.getEntryExitType());
        if (StringUtils.isNoneBlank(reqVO.getStartTime())) {
            filterParam.setStartTime(
                    DateUtils.convertTo(reqVO.getStartTime(), "yyyy-MM-dd HH:mm:ss", "Asia/Shanghai")
            );
        }

        if (StringUtils.isNoneBlank(reqVO.getEndTime())) {
            filterParam.setEndTime(
                    DateUtils.convertTo(reqVO.getEndTime(), "yyyy-MM-dd HH:mm:ss", "Asia/Shanghai")
            );
        }

        IPage<FactoryEntryExitRecord> listPage = factoryEntryExitRecordMapper.getRecordPage(page, filterParam);
        IPage<FactoryEntryExitRespVO> listResVO = FactoryEntryExitConvertor.convert(listPage);
        return new CommonPage<>(listResVO);
    }

    @Override
    public boolean addRecord(@Valid FactoryEntryExitAddReqVO reqVO) {
        FactoryEntryExitRecord record = FactoryEntryExitConvertor.convert(reqVO);
        int count = factoryEntryExitRecordMapper.insert(record);
        return count > 0;
    }

    @Override
    public boolean updateRecord(@Valid FactoryEntryExitUpdateReqVO reqVO) {
        // 软删除
        LambdaUpdateWrapper<FactoryEntryExitRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(FactoryEntryExitRecord::getId, reqVO.getId()) // 设定条件：通过 ID 查找
                .set(FactoryEntryExitRecord::getDetail, reqVO.getDetail())
                .set(FactoryEntryExitRecord::getRecordTime,
                        DateUtils.convertTo(reqVO.getRecordTime(), "yyyy-MM-dd HH:mm:ss", "Asia/Shanghai"))
                .set(FactoryEntryExitRecord::getNum, reqVO.getNum())
                .set(FactoryEntryExitRecord::getEntryExitType, reqVO.getEntryExitType());

        int rows = factoryEntryExitRecordMapper.update(null, updateWrapper);
        return rows > 0;
    }

    @Override
    public boolean deleteRecord(Long id) {
        // 软删除
        LambdaUpdateWrapper<FactoryEntryExitRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(FactoryEntryExitRecord::getId, id) // 设定条件：通过 ID 查找
                .set(FactoryEntryExitRecord::getIsDel, 1); // 更新的字段和值

        int rows = factoryEntryExitRecordMapper.update(null, updateWrapper);
        return rows > 0;
    }

    @Override
    public FactoryFootfallLiveStatRespVO getLiveStat(FactoryFootfallLiveStatReqVO reqVO) {
        // 人工登记人数
        Date startTime = DateUtils.convertTo(reqVO.getStartTime(), "yyyy-MM-dd HH:mm:ss", "Asia/Shanghai");
        Date endTime = DateUtils.convertTo(reqVO.getEndTime(), "yyyy-MM-dd HH:mm:ss", "Asia/Shanghai");
        Integer entryExitPeopleNum = factoryEntryExitRecordMapper.getPeopleNum(startTime, endTime);

        // 算法实时统计人数 + 摄像头数量
        AiFootfallStat aiLiveStat = aiFootfallStatService.getAiLiveStat(AiFootfallSceneTypeEnum.FACTORY_ENTRY_EXIT.getCode());
        Integer aiLivePeopleNum = 0;
        List<String> rtspList = Collections.emptyList();
        if (Objects.nonNull(aiLiveStat)) {
            aiLivePeopleNum = aiLiveStat.getLivePeopleNum();
            rtspList = FactoryEntryExitConvertor.convert(aiLiveStat.getRtsps());
        }

        // 组装出入厂区人流量统计结果
        if (Objects.isNull(entryExitPeopleNum)) {
            entryExitPeopleNum = 0;
        }
        if (Objects.isNull(aiLivePeopleNum)) {
            aiLivePeopleNum = 0;
        }
        return FactoryEntryExitConvertor.convert(entryExitPeopleNum, rtspList, aiLivePeopleNum);
    }
}
