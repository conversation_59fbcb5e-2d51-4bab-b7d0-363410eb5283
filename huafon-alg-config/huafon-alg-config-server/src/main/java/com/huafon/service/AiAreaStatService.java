package com.huafon.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.huafon.models.entity.AiAreaStat;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
public interface AiAreaStatService extends IService<AiAreaStat> {

    /**
     * 区域统计
     *
     * @param aiAreaStat
     */
    void areaStat(AiAreaStat aiAreaStat);

    /**
     * 区域统计
     *
     * @param aiAreaStat
     * @param numSyncVersion
     */
    void areaStat(AiAreaStat aiAreaStat, Long numSyncVersion);

    /**
     * 获取ai 区域统计
     * @param areaId
     * @return
     */
    AiAreaStat getAiAreaStat(Long areaId);

    List<AiAreaStat> getBatchAiAreaStat(List<Long> areaId);
}
