package com.huafon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.huafon.models.entity.annual.ExamAnnualPlanDetail;
import com.huafon.models.vo.resp.annual.ExamAnnualPlanDetailRespVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ${comments}
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-15
 */
public interface ExamAnnualPlanDetailMapper extends BaseMapper<ExamAnnualPlanDetail> {

    List<ExamAnnualPlanDetailRespVO> queryByPlanIds(@Param("planIds") List<Long> planIds);
	
}