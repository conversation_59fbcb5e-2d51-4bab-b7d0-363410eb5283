package com.huafon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.huafon.models.entity.ExamBacklogJobStudent;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-09
 */
public interface ExamBacklogJobStudentMapper extends BaseMapper<ExamBacklogJobStudent> {

    List<ExamBacklogJobStudent> selectByThreeLevel(@Param("threeLevelType") Integer threeLevelType,
                                                   @Param("threeLevelSubject") String threeLevelSubject,
                                                   @Param("userIds") List<Long> userIds,
                                                   @Param("tenantId") Integer tenantId);

    List<ExamBacklogJobStudent> selectByUserId(@Param("userId") Long userId,
                                               @Param("tenantId") Integer tenantId);
}
