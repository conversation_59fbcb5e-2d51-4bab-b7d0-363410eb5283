package com.huafon.service;

import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.models.entity.ExamEvaluation;
import com.huafon.models.vo.req.evaluation.ExamEvaluationAddReqVO;
import com.huafon.models.vo.req.evaluation.ExamEvaluationListLinkReqVO;
import com.huafon.models.vo.req.evaluation.ExamEvaluationListReqVO;
import com.huafon.models.vo.resp.evaluation.ExamEvaluationInfoRespVO;
import com.huafon.models.vo.resp.evaluation.ExamEvaluationListForActivityRespVO;
import com.huafon.models.vo.resp.evaluation.ExamEvaluationListForPersonRespVO;
import com.huafon.models.vo.resp.evaluation.ExamEvaluationListRespVO;
import com.huafon.models.vo.resp.evaluation.ExamEvaluationScoreRespVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/19 9:41:10
 * @description 服务类
 */
public interface ExamEvaluationService {

    void add(ExamEvaluationAddReqVO req);

    ExamEvaluationInfoRespVO getInfo(Long id);

    CommonPage<ExamEvaluationListForActivityRespVO> listForActivity(ExamEvaluationListLinkReqVO req);

    CommonPage<ExamEvaluationListForPersonRespVO> listForStudent(ExamEvaluationListLinkReqVO req);

    CommonPage<ExamEvaluationListRespVO> queryEvaluationList(ExamEvaluationListReqVO req);

    List<ExamEvaluation> readExamEvaluate(Long userId, Long activityId, Long courseId);

    Map<Long, List<ExamEvaluationScoreRespVO>> readScoreByActivityIds(List<Long> activityIds);

    Map<Long, List<ExamEvaluationScoreRespVO>> readScoreByLecturerIds(List<Long> lecturerIds);

    Map<Long, List<ExamEvaluationScoreRespVO>> readScoreByCourseIds(List<Long> courseIds);

    List<ExamEvaluation> readExamEvaluate(Long activityId);

}
