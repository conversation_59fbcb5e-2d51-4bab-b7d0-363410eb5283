package com.huafon.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.huafon.common.config.TenantContext;
import com.huafon.file.center.api.dto.FileInfoRpcDto;
import com.huafon.file.center.api.service.IFileRpcService;
import com.huafon.license.apis.dto.innerLicense.LicenseTypeDTO;
import com.huafon.license.apis.dto.innerLicense.StaffInnerLicenseDTO;
import com.huafon.license.apis.remote.StaffInnerLicenseRpcService;
import com.huafon.models.dto.excel.OfflineOperationRecordUploadDTO;
import com.huafon.models.entity.ExamLecturer;
import com.huafon.models.entity.ExamOfflineOperationRecord;
import com.huafon.models.vo.req.history.LicenseTypeAddReqVO;
import com.huafon.models.vo.req.history.OfflineOperationLecturerReqVO;
import com.huafon.portal.api.dto.UserDto;
import com.huafon.portal.api.service.UserRpcV2Service;
import com.huafon.service.ExamLecturerService;
import com.huafon.service.ExamOfflineOperationRecordService;
import com.huafon.service.ExamSubjectService;
import com.huafon.service.OfflineOperationRecordImportService;
import com.huafon.service.support.ExcelValidationError;
import com.huafon.service.support.ExcelValidationResult;
import com.huafon.support.exceptions.ServiceException;
import com.huafon.utils.CodeUtils;
import com.huafon.utils.FreemarkerUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * <p>
 * 线下实操记录导入 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Service
public class OfflineOperationRecordImportServiceImpl implements OfflineOperationRecordImportService {

    @DubboReference
    private UserRpcV2Service userRpcV2Service;
    @DubboReference
    private StaffInnerLicenseRpcService staffInnerLicenseRpcService;
    @DubboReference
    private IFileRpcService iFileRpcService;
    @Autowired
    private ExamLecturerService examLecturerService;
    @Autowired
    private ExamOfflineOperationRecordService examOfflineOperationRecordService;
    @Autowired
    private ExamSubjectService examSubjectService;
    @Autowired
    private CodeUtils codeUtils;
    @Value("${minio.bucket.common}")
    private String bucket;

    @Override
    public ExcelValidationResult<OfflineOperationRecordUploadDTO> validationImportExcel(List<OfflineOperationRecordUploadDTO> source) {
        List<OfflineOperationRecordUploadDTO> successData = CollUtil.newArrayList();
        List<ExcelValidationError<OfflineOperationRecordUploadDTO>> errorData = CollUtil.newArrayList();
        if (CollUtil.isEmpty(source)){
            return new ExcelValidationResult<>(successData,errorData);
        }
        Integer tenantId = TenantContext.getOne();
        Map<String, List<Integer>> subjectMap = examSubjectService.getAllNodeForImport();
        for (OfflineOperationRecordUploadDTO uploadDTO : source) {
            StringJoiner errorMsg = new StringJoiner(";");
            List<Integer> subjectIds = subjectMap.get(uploadDTO.getSubjectName());
            if (!CollectionUtils.isEmpty(subjectIds)) {
                uploadDTO.setSubjectId(subjectIds.get(0).longValue());
            } else {
                errorMsg.add("培训类别不存在");
            }
            if (uploadDTO.getEndTime().before(uploadDTO.getStartTime())) {
                errorMsg.add("开始时间不能晚于结束时间");
            }
            List<OfflineOperationLecturerReqVO> lecturers = new ArrayList<>();
            String[] lecturerNames = uploadDTO.getLecturerName().split("\\|");
            String[] lecturerUserNames = uploadDTO.getLecturerUserName().split("\\|");
            if (lecturerNames.length != lecturerUserNames.length) {
                errorMsg.add("讲师格式错误");
            } else {
                for (int i=0;i<lecturerNames.length;i++) {
                    Long lecturerId = getLecturerId(lecturerNames[i],lecturerUserNames[i]);
                    if (lecturerId == null) {
                        errorMsg.add("讲师不存在");
                        break;
                    } else {
                        lecturers.add(OfflineOperationLecturerReqVO.builder()
                                        .lecturerId(lecturerId)
                                        .lecturerName(lecturerNames[i])
                                        .build());
                    }
                }
            }
            uploadDTO.setLecturer(JSONArray.toJSONString(lecturers));
            if (Objects.equals(uploadDTO.getIsAward(),"是")) {
                LicenseTypeDTO license = null;
                List<LicenseTypeDTO> licenseList = staffInnerLicenseRpcService.queryLicenseTypeByNameLike(uploadDTO.getLicenseName(), tenantId);
                if (!CollectionUtils.isEmpty(licenseList)) {
                    for (LicenseTypeDTO licenseTypeDTO: licenseList) {
                        if (licenseTypeDTO.getLicenseTypeName().equals(uploadDTO.getLicenseName())) {
                            license = licenseTypeDTO;
                            break;
                        }
                    }
                }
                if (license == null) {
                    errorMsg.add("证书不存在");
                } else {
                    uploadDTO.setLicenseTypeId(license.getLicenseTypeId());
                    uploadDTO.setTemplateBackground(license.getTemplateBackground());
                    uploadDTO.setTemplateLicenseName(license.getTemplateLicenseName());
                    uploadDTO.setTemplateLicenseContent(license.getTemplateLicenseContent());
                    uploadDTO.setTemplateCompanyName(license.getTemplateCompanyName());
                }
            }
            UserDto userDto = userRpcV2Service.getByUsername(uploadDTO.getUsername(), TenantContext.getOne());
            if (userDto != null && userDto.getName().equals(uploadDTO.getName())) {
                uploadDTO.setUserId(userDto.getUserId());
                uploadDTO.setWorkNum(userDto.getWorkNum());
                uploadDTO.setMobile(userDto.getMobile());
            } else {
                errorMsg.add("学员不存在");
            }
            if (errorMsg.length()>0){
                errorData.add(new ExcelValidationError<>(uploadDTO,errorMsg.toString()));
            }else {
                successData.add(uploadDTO);
            }
        }
        return new ExcelValidationResult<>(successData,errorData);
    }

    private Long getLecturerId(String name, String username) {
        Long lecturerId = null;
        List<ExamLecturer> examLecturers = examLecturerService.list(new LambdaQueryWrapper<ExamLecturer>()
                .eq(ExamLecturer::getName, name)
                .eq(ExamLecturer::getTenantId, TenantContext.getOne())
                .eq(ExamLecturer::getIsDel, 0));
        if (!CollectionUtils.isEmpty(examLecturers)) {
            List<Integer> userIds = examLecturers.stream().map(v -> v.getUserId().intValue()).collect(Collectors.toList());
            Map<Integer,UserDto> userDtoMap = userRpcV2Service.queryMappingByIds(userIds,TenantContext.getOne());
            for (ExamLecturer examLecturer: examLecturers) {
                UserDto userDto = userDtoMap.get(examLecturer.getUserId().intValue());
                if (userDto != null && userDto.getUsername().equals(username)) {
                    lecturerId = examLecturer.getId();
                    break;
                }
            }
        }
        return lecturerId;
    }

    @Override
    public void importIntoDatabase(List<OfflineOperationRecordUploadDTO> source) {
        if (!CollectionUtils.isEmpty(source)) {
            List<ExamOfflineOperationRecord> entityList = new ArrayList<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String templateTime = sdf.format(new Date());
            for (OfflineOperationRecordUploadDTO item: source) {
                ExamOfflineOperationRecord entity = OfflineOperationRecordUploadDTO.convert(item);
                entity.setCode(codeUtils.createOfflineOperationRecordCode(TenantContext.getOne()));
                if (Objects.equals(entity.getIsPass(),1) && Objects.equals(entity.getIsAward(),1)) {
                    String licenseNo = getLicenseNo();
                    LicenseTypeAddReqVO licenseTemplate = new LicenseTypeAddReqVO();
                    licenseTemplate.setTemplateBackground(item.getTemplateBackground());
                    licenseTemplate.setTemplateLicenseName(item.getTemplateLicenseName());
                    licenseTemplate.setTemplateStaffName(entity.getUserName());
                    licenseTemplate.setTemplateLicenseContent(item.getTemplateLicenseContent());
                    licenseTemplate.setTemplateCompanyName(item.getTemplateCompanyName());
                    licenseTemplate.setTemplateTime(templateTime);
                    // 调用远程rpc生成证书图片url
                    HashMap map = new HashMap();
                    map.put("data", licenseTemplate);
                    ByteArrayOutputStream baos = null;
                    try {
                        baos = FreemarkerUtils.createPng("certificate/index.ftl", map, FreemarkerUtils.getCertificatePage());
                        Calendar calendar = Calendar.getInstance();
                        String fileName = "license_" + UuidUtils.generateUuid() + ".png";
                        String folder = "license/" + calendar.get(Calendar.YEAR) + "/" + (calendar.get(Calendar.MONTH)+1) + "/" + calendar.get(Calendar.DATE);
                        FileInfoRpcDto fileInfoRpcDto = iFileRpcService.updateFile(baos.toByteArray(), fileName, null, folder, bucket);
                        entity.setLicense(fileInfoRpcDto.getUrl());
                        entity.setLicenseNumber(licenseNo);
                    }catch (Exception e) {
                        throw new ServiceException(e.getMessage());
                    }finally {
                        if (null != baos) {
                            try {
                                baos.close();
                            } catch (IOException e) {
                                throw new RuntimeException(e);
                            }
                        }
                    }
                    // 调用远程rpc插入证书
                    StaffInnerLicenseDTO staffInnerLicenseDTO = StaffInnerLicenseDTO
                            .builder()
                            .licenseTypeId(item.getLicenseTypeId())
                            .licenseUserId(entity.getUserId())
                            .staffWorkNum(entity.getWorkNum())
                            .licenseNo(licenseNo)
                            .licenseDate(entity.getCreateTime())
                            .licenseFile(entity.getLicense())
                            .tenantId(TenantContext.getOne())
                            .build();
                    staffInnerLicenseRpcService.createStaffInnerLicense(staffInnerLicenseDTO);
                }
                entityList.add(entity);
            }
            examOfflineOperationRecordService.saveBatch(entityList);
        }
    }

    private String getLicenseNo() {
        Date dt = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyMMddHHmmssSS");
        String data = sdf.format(dt);
        String random = ((int) (Math.random() * 9000) + 1000) + "";
        return "CERT" + data + random;
    }

}
