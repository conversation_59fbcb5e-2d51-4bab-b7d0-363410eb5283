package com.huafon.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.huafon.api.service.ConsoleRpcService;
import com.huafon.api.service.dto.StatisticsV2ConfigDTO;
import com.huafon.api.service.dto.StatisticsV2RespDTO;
import com.huafon.api.service.enums.StatisticsTrainAndTestEnum;
import com.huafon.common.config.TenantContext;
import com.huafon.dao.mapper.ExamStatisticsCacheMapper;
import com.huafon.dao.mapper.StatisticsMapper;
import com.huafon.framework.mybatis.enums.DelFlag;
import com.huafon.models.entity.ExamActivity;
import com.huafon.models.entity.ExamActivityStudentLink;
import com.huafon.models.entity.ExamCourse;
import com.huafon.models.entity.ExamStatisticsCache;
import com.huafon.models.entity.annual.ExamAnnualTrainPlan;
import com.huafon.models.enums.StatisticsCacheEnum;
import com.huafon.models.vo.req.statistics.*;
import com.huafon.models.vo.resp.statistics.*;
import com.huafon.portal.api.dto.UserDto;
import com.huafon.portal.api.service.DeptRpcService;
import com.huafon.portal.api.service.UserRpcV2Service;
import com.huafon.service.ExamActivityStudentLinkService;
import com.huafon.service.ExamCourseService;
import com.huafon.service.StatisticsService;
import com.huafon.support.config.UserContext;
import com.huafon.utils.DateUtil;
import com.huafon.visitor.api.dto.ContractorInfoDTO;
import com.huafon.visitor.api.service.VCRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 数据统计 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Service
@Slf4j
public class StatisticsServiceImpl implements StatisticsService {

    private final StatisticsMapper statisticsMapper;

    private final ExamActivityStudentLinkService examActivityStudentLinkService;

    private final ExamCourseService examCourseService;

    private final ExamStatisticsCacheMapper examStatisticsCacheMapper;

    @DubboReference
    private DeptRpcService deptRpcService;
    @DubboReference
    private VCRpcService vcRpcService;
    @DubboReference
    private UserRpcV2Service userRpcV2Service;
    @DubboReference
    private ConsoleRpcService consoleRpcService;

    public StatisticsServiceImpl(StatisticsMapper statisticsMapper,
                                 ExamActivityStudentLinkService examActivityStudentLinkService,
                                 ExamCourseService examCourseService,
                                 ExamStatisticsCacheMapper examStatisticsCacheMapper) {
        this.statisticsMapper = statisticsMapper;
        this.examActivityStudentLinkService = examActivityStudentLinkService;
        this.examCourseService = examCourseService;
        this.examStatisticsCacheMapper = examStatisticsCacheMapper;
    }

    @Override
    public List<ActivityStatisticsRespVO> activityAnalysis(ActivityStatisticsReqVO reqVO) {
        reqVO.setTenantId(TenantContext.getOne());
        if (Objects.nonNull(reqVO.getTrainDepartmentId())) {
            reqVO.setTrainDepartmentIds(deptRpcService.getChildNodeIdsById(reqVO.getTrainDepartmentId()));
        }
        List<ActivityStatisticsRespVO> statisticsList = ActivityStatisticsRespVO.initializeList(reqVO.getStartDate(), reqVO.getEndDate());
        List<ExamActivity> activities = statisticsMapper.selectActivityList(reqVO);
        if (!CollectionUtils.isEmpty(activities)) {
            List<ExamActivityStudentLink> examActivityStudentLinks = new ArrayList<>();
            List<Long> activityIds = activities.stream().map(ExamActivity::getId).collect(Collectors.toList());
            LambdaQueryWrapper<ExamActivityStudentLink> queryWrapper = new LambdaQueryWrapper<ExamActivityStudentLink>()
                    .in(ExamActivityStudentLink::getActivityId, activityIds)
                    .ne(ExamActivityStudentLink::getIsAppeal, Boolean.TRUE)
                    .eq(ExamActivityStudentLink::getIsDel, 0);
            if (Objects.nonNull(reqVO.getTrainDepartmentId())) {
                Map<Integer, UserDto> userDtoMap = userRpcV2Service.queryMappingByDepartmentIds(reqVO.getTrainDepartmentId(),true,TenantContext.getOne());
                if (!CollectionUtils.isEmpty(userDtoMap)) {
                    queryWrapper.in(ExamActivityStudentLink::getUserId, userDtoMap.keySet());
                    examActivityStudentLinks = examActivityStudentLinkService.list(queryWrapper);
                }
            } else {
                examActivityStudentLinks = examActivityStudentLinkService.list(queryWrapper);
            }
            // 活动总人次
            Map<Long, Long> peopleCount = examActivityStudentLinks.stream().collect(Collectors.groupingBy(ExamActivityStudentLink::getActivityId, Collectors.counting()));
            // 活动考评情况合格人员数量
            Map<Long, Long> passPeopleCount = examActivityStudentLinks.stream().filter(v->Objects.equals(v.getAssessmentSituation(),1)).collect(Collectors.groupingBy(ExamActivityStudentLink::getActivityId, Collectors.counting()));

            List<Long> courseIds = activities.stream().map(ExamActivity::getCourseId).collect(Collectors.toList());
            List<ExamCourse> examCourses = examCourseService.list(new LambdaQueryWrapper<ExamCourse>()
                    .isNotNull(ExamCourse::getPeriod)
                    .in(ExamCourse::getId, courseIds)
                    .eq(ExamCourse::getIsDel, 0));
            // 课程课时数量
            Map<Long, Double> periodCount = examCourses.stream().collect(Collectors.toMap(ExamCourse::getId, ExamCourse::getPeriod));

            // 合计数据
            Long sumActivity = 0L;
            Long sumPeople = 0L;
            BigDecimal sumPeriod = BigDecimal.ZERO;
            Long sumPassPeople = 0L;
            for (ExamActivity examActivity: activities) {
                String yearMonth = DateUtil.formatYearMonth(examActivity.getStartTime());
                for (ActivityStatisticsRespVO statistics: statisticsList) {
                    if (statistics.getTime().equals(yearMonth)) {
                        Long totalPeople = peopleCount.containsKey(examActivity.getId()) ? peopleCount.get(examActivity.getId()) : 0L;
                        Double period = periodCount.containsKey(examActivity.getCourseId()) ? periodCount.get(examActivity.getCourseId()) : 0D;
                        Long totalPassPeople = passPeopleCount.containsKey(examActivity.getId()) ? passPeopleCount.get(examActivity.getId()) : 0L;
                        BigDecimal totalPeriod = new BigDecimal(period.toString());
                        statistics.setTotalActivity(statistics.getTotalActivity()+1);
                        statistics.setTotalPeople(statistics.getTotalPeople()+totalPeople);
                        statistics.setTotalPeriod(statistics.getTotalPeriod().add(totalPeriod));
                        statistics.setTotalPassPeople(statistics.getTotalPassPeople()+totalPassPeople);
                        statistics.calculatePassRate();
                        sumActivity += 1;
                        sumPeople += totalPeople;
                        sumPeriod = sumPeriod.add(totalPeriod);
                        sumPassPeople += totalPassPeople;
                    }
                }
            }
            ActivityStatisticsRespVO sum = new ActivityStatisticsRespVO();
            sum.setTime("合计");
            sum.setTotalActivity(sumActivity);
            sum.setTotalPeople(sumPeople);
            sum.setTotalPeriod(sumPeriod);
            sum.setTotalPassPeople(sumPassPeople);
            sum.calculatePassRate();
            statisticsList.add(sum);
        }
        return statisticsList;
    }

    @Override
    public List<PlanStatisticsRespVO> planAnalysis(PlanStatisticsReqVO reqVO) throws Exception {
        reqVO.setTenantId(TenantContext.getOne());
        if (Objects.nonNull(reqVO.getFormulateDeptId())) {
            reqVO.setFormulateDeptIds(deptRpcService.getChildNodeIdsById(reqVO.getFormulateDeptId()));
        }
        List<PlanStatisticsRespVO> statisticsList = PlanStatisticsRespVO.initializeList(reqVO.getDate());
        List<ExamAnnualTrainPlan> plans = statisticsMapper.selectPlanList(reqVO);
        if (!CollectionUtils.isEmpty(plans)) {
            List<Long> planIds = plans.stream().map(ExamAnnualTrainPlan::getId).collect(Collectors.toList());
            // 合计
            Long sumContext = 0L;
            Long sumActivity = 0L;
            SimpleDateFormat ym = new SimpleDateFormat("yyyy-MM");
            for (PlanStatisticsRespVO statistics: statisticsList) {
                Date yearMonth = ym.parse(statistics.getTime());
                // 计划内容数量
                Long contextCount = contextCount(planIds,yearMonth);
                // 培训活动数量
                Long activityCount = activityCount(planIds,yearMonth,reqVO.getFormulateDeptIds());
                if (activityCount > contextCount) {
                    activityCount = contextCount;
                }
                statistics.setTotal(statistics.getTotal()+contextCount);
                statistics.setTotalActivity(statistics.getTotalActivity()+activityCount);
                statistics.calculateFinishRate();
                sumContext += contextCount;
                sumActivity += activityCount;
            }
            PlanStatisticsRespVO sum = new PlanStatisticsRespVO();
            sum.setTime("合计");
            sum.setTotal(sumContext);
            sum.setTotalActivity(sumActivity);
            sum.calculateFinishRate();
            statisticsList.add(sum);
        }
        return statisticsList;
    }

    @Override
    public List<ContractorStatisticsRespVO> contractorAnalysis(ContractStatisticsReqVO reqVO) {
        Integer tenantId = TenantContext.getOne();
        List<ContractorInfoDTO> contractors = vcRpcService.queryContractorInfo(null, Arrays.asList(tenantId));
        List<ContractorStatisticsRespVO> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(contractors)) {
            List<String> contractorNames = contractors.stream().map(ContractorInfoDTO::getContractorName).collect(Collectors.toList());
            reqVO.setTenantId(TenantContext.getOne());
            reqVO.setContractorNames(contractorNames);
            result.addAll(statisticsMapper.contractorAnalysis(reqVO));
        }
        result.add(getTotalContractorStatistics(result));
        return result;
    }

    private ContractorStatisticsRespVO getTotalContractorStatistics(List<ContractorStatisticsRespVO> source) {
        ContractorStatisticsRespVO totalItem = new ContractorStatisticsRespVO();
        totalItem.setOrganization("合计");
        if (!CollectionUtils.isEmpty(source)) {
            BigDecimal totalPeriod = source.stream().map(ContractorStatisticsRespVO::getPeriod).reduce(BigDecimal.ZERO, BigDecimal::add);
            Integer totalPeopleNum = source.stream().map(ContractorStatisticsRespVO::getPeopleNum).reduce(0, Integer::sum);

            totalItem.setPeriod(totalPeriod);
            totalItem.setPeopleNum(totalPeopleNum);
            BigDecimal totalAvgPeriod = totalPeriod.divide(BigDecimal.valueOf(totalPeopleNum), 1, RoundingMode.HALF_UP);
            totalItem.setAveragePeriod(totalAvgPeriod);
        }

        return totalItem;
    }

    @Override
    public ClassHourStatisticsRespVO classHour(ClassHourStatisticsReqVO reqVO) {
        setCache(JSONObject.toJSONString(reqVO), StatisticsCacheEnum.CLASS_HOUR.getType());
        Integer tenantId = TenantContext.getOne();
        ClassHourStatisticsRespVO vo = ClassHourStatisticsRespVO.init(reqVO.getYear());
        List<Integer> userIds = userRpcV2Service.getByDeptIdsAndPostIds(reqVO.getDeptIds(),reqVO.getPostIds(),tenantId);
        if (!CollectionUtils.isEmpty(userIds)) {
            vo.setTotalNum(userIds.size());
            vo.setCompleteNum(statisticsMapper.classHour(reqVO.getYear(),reqVO.getPeriod(),userIds,tenantId));
            vo.setNotCompleteNum(vo.getTotalNum()-vo.getCompleteNum());
            vo.setCompleteRate(new BigDecimal(vo.getCompleteNum()*100).divide(new BigDecimal(vo.getTotalNum()),2,BigDecimal.ROUND_HALF_UP));
        }
        return vo;
    }

    @Override
    public List<DeptPassRateStatisticsRespVO> deptPassRate(DeptPassRateStatisticsReqVO reqVO) {
        List<DeptPassRateStatisticsRespVO> resultList = new ArrayList<>();
        setCache(JSONObject.toJSONString(reqVO), StatisticsCacheEnum.DEPT_PASS_RATE.getType());
        if (!CollectionUtils.isEmpty(reqVO.getDepts())) {
            reqVO.setTenantId(TenantContext.getOne());
            List<DeptPassRateStatisticsRespVO.User> dataList = new ArrayList<>();
            List<Integer> deptIds = reqVO.getDepts().stream().map(DeptPassRateStatisticsReqVO.Dept::getDeptId).collect(Collectors.toList());
            Map<Integer,List<Integer>> deptMap = userRpcV2Service.queryGroupByDepartmentIdsWithChild(deptIds);
            if (!CollectionUtils.isEmpty(deptMap)) {
                Set<Integer> userIdSet = new HashSet<>();
                deptMap.forEach((k,v)->{
                    if (!CollectionUtils.isEmpty(v)) {
                        userIdSet.addAll(v);
                    }
                });
                if (!CollectionUtils.isEmpty(userIdSet)) {
                    reqVO.setUserIds(userIdSet);
                    dataList = statisticsMapper.deptPassRate(reqVO);
                }
            }
            for (DeptPassRateStatisticsReqVO.Dept dept: reqVO.getDepts()) {
                DeptPassRateStatisticsRespVO vo = new DeptPassRateStatisticsRespVO(dept.getDeptId(), dept.getDeptName());
                List<Integer> userIds = deptMap.get(vo.getDeptId());
                if (!CollectionUtils.isEmpty(userIds)) {
                    List<DeptPassRateStatisticsRespVO.User> datas = dataList.stream().filter(v->userIds.contains(v.getUserId())).collect(Collectors.toList());
                    vo.setTotalNum(datas.size());
                    for (DeptPassRateStatisticsRespVO.User data : datas) {
                        if (Objects.equals(data.getAssessmentSituation(),1)) {
                            vo.setPassNum(vo.getPassNum()+1);
                        } else {
                            vo.setUnPassNum(vo.getUnPassNum()+1);
                        }
                        if (data.getSignatureNum() > 0) {
                            vo.setFinishNum(vo.getFinishNum()+1);
                        } else {
                            vo.setUnFinishNum(vo.getUnFinishNum()+1);
                        }
                    }
                    vo.calculateRate();
                    resultList.add(vo);
                }
            }
        }
        return resultList;
    }

    @Override
    public List<DailyTrainingPassRateStatisticsRespVO> dailyTrainingPassRate(DailyTrainingPassRateStatisticsReqVO reqVO) {
        List<DailyTrainingPassRateStatisticsRespVO> resultList = new ArrayList<>();
        setCache(JSONObject.toJSONString(reqVO), StatisticsCacheEnum.DAILY_TRAINING_PASS_RATE.getType());
        if (!CollectionUtils.isEmpty(reqVO.getDepts())) {
            reqVO.setTenantId(TenantContext.getOne());
            List<DailyTrainingPassRateStatisticsRespVO.User> dataList = new ArrayList<>();
            List<Integer> deptIds = reqVO.getDepts().stream().map(DailyTrainingPassRateStatisticsReqVO.Dept::getDeptId).collect(Collectors.toList());
            Map<Integer,List<Integer>> deptMap = userRpcV2Service.queryGroupByDepartmentIdsWithChild(deptIds);
            if (!CollectionUtils.isEmpty(deptMap)) {
                Set<Integer> userIdSet = new HashSet<>();
                deptMap.forEach((k,v)->{
                    if (!CollectionUtils.isEmpty(v)) {
                        userIdSet.addAll(v);
                    }
                });
                if (!CollectionUtils.isEmpty(userIdSet)) {
                    reqVO.setUserIds(userIdSet);
                    dataList = statisticsMapper.dailyTrainingPassRate(reqVO);
                }
            }
            for (DailyTrainingPassRateStatisticsReqVO.Dept dept: reqVO.getDepts()) {
                DailyTrainingPassRateStatisticsRespVO vo = new DailyTrainingPassRateStatisticsRespVO(dept.getDeptId(), dept.getDeptName());
                List<Integer> userIds = deptMap.get(vo.getDeptId());
                if (!CollectionUtils.isEmpty(userIds)) {
                    List<DailyTrainingPassRateStatisticsRespVO.User> datas = dataList.stream().filter(v->userIds.contains(v.getUserId())).collect(Collectors.toList());
                    vo.setTaskNum(datas.size());
                    for (DailyTrainingPassRateStatisticsRespVO.User data : datas) {
                        if (Objects.equals(data.getStatus(),1)) {
                            vo.setCompleteTaskNum(vo.getCompleteTaskNum()+1);
                        }
                        if (Objects.equals(data.getIsPass(),1)) {
                            vo.setPassNum(vo.getPassNum()+1);
                        }
                    }
                    vo.calculateRate();
                    resultList.add(vo);
                }
            }
        }
        return resultList;
    }

    @Override
    public List<StatisticsV2RespDTO> console(ConsoleStatisticsReqVO reqVO) {
        List<StatisticsV2RespDTO> list = new ArrayList<>();
        List<StatisticsV2ConfigDTO> configList = consoleRpcService.getStatisticsV2ConfigByKey("STATISTICS_V2_TRAIN_AND_TEST",UserContext.getId());
        if (!CollectionUtils.isEmpty(configList)) {
            ActivityStatisticsRespVO joinTrain = this.sum(activityAnalysis(ConsoleStatisticsReqVO.convertJoinTrain(reqVO)));
            ActivityStatisticsRespVO transferTrain = this.sum(activityAnalysis(ConsoleStatisticsReqVO.convertTransferTrain(reqVO)));
            ActivityStatisticsRespVO dailyTrain = this.sum(activityAnalysis(ConsoleStatisticsReqVO.convertDailyTrain(reqVO)));
            for (StatisticsV2ConfigDTO dto : configList) {
                if (Objects.equals(dto.getCode(), StatisticsTrainAndTestEnum.JOIN_TRAIN_STUDENT.getCode())) {
                    list.add(new StatisticsV2RespDTO(StatisticsTrainAndTestEnum.JOIN_TRAIN_STUDENT.getCode(),StatisticsTrainAndTestEnum.JOIN_TRAIN_STUDENT.getName(), String.valueOf(joinTrain.getTotalPeople()),""));
                } else if (Objects.equals(dto.getCode(), StatisticsTrainAndTestEnum.JOIN_TRAIN_CREDIT.getCode())) {
                    list.add(new StatisticsV2RespDTO(StatisticsTrainAndTestEnum.JOIN_TRAIN_CREDIT.getCode(),StatisticsTrainAndTestEnum.JOIN_TRAIN_CREDIT.getName(), joinTrain.getTotalPeriod().stripTrailingZeros().toPlainString(),""));
                } else if (Objects.equals(dto.getCode(), StatisticsTrainAndTestEnum.JOIN_TRAIN_ACTIVITY.getCode())) {
                    list.add(new StatisticsV2RespDTO(StatisticsTrainAndTestEnum.JOIN_TRAIN_ACTIVITY.getCode(),StatisticsTrainAndTestEnum.JOIN_TRAIN_ACTIVITY.getName(), String.valueOf(joinTrain.getTotalActivity()),""));
                } else if (Objects.equals(dto.getCode(), StatisticsTrainAndTestEnum.JOIN_TRAIN_PASS_RATE.getCode())) {
                    list.add(new StatisticsV2RespDTO(StatisticsTrainAndTestEnum.JOIN_TRAIN_PASS_RATE.getCode(),StatisticsTrainAndTestEnum.JOIN_TRAIN_PASS_RATE.getName(), joinTrain.getPassRate().stripTrailingZeros().toPlainString(),"%"));
                } else if (Objects.equals(dto.getCode(), StatisticsTrainAndTestEnum.TRANSFER_TRAIN_STUDENT.getCode())) {
                    list.add(new StatisticsV2RespDTO(StatisticsTrainAndTestEnum.TRANSFER_TRAIN_STUDENT.getCode(),StatisticsTrainAndTestEnum.TRANSFER_TRAIN_STUDENT.getName(), String.valueOf(transferTrain.getTotalPeople()),""));
                } else if (Objects.equals(dto.getCode(), StatisticsTrainAndTestEnum.TRANSFER_TRAIN_CREDIT.getCode())) {
                    list.add(new StatisticsV2RespDTO(StatisticsTrainAndTestEnum.TRANSFER_TRAIN_CREDIT.getCode(),StatisticsTrainAndTestEnum.TRANSFER_TRAIN_CREDIT.getName(), transferTrain.getTotalPeriod().stripTrailingZeros().toPlainString(),""));
                } else if (Objects.equals(dto.getCode(), StatisticsTrainAndTestEnum.TRANSFER_TRAIN_ACTIVITY.getCode())) {
                    list.add(new StatisticsV2RespDTO(StatisticsTrainAndTestEnum.TRANSFER_TRAIN_ACTIVITY.getCode(),StatisticsTrainAndTestEnum.TRANSFER_TRAIN_ACTIVITY.getName(), String.valueOf(transferTrain.getTotalActivity()),""));
                } else if (Objects.equals(dto.getCode(), StatisticsTrainAndTestEnum.TRANSFER_TRAIN_PASS_RATE.getCode())) {
                    list.add(new StatisticsV2RespDTO(StatisticsTrainAndTestEnum.TRANSFER_TRAIN_PASS_RATE.getCode(),StatisticsTrainAndTestEnum.TRANSFER_TRAIN_PASS_RATE.getName(), transferTrain.getPassRate().stripTrailingZeros().toPlainString(),"%"));
                } else if (Objects.equals(dto.getCode(), StatisticsTrainAndTestEnum.DAILY_TRAIN_STUDENT.getCode())) {
                    list.add(new StatisticsV2RespDTO(StatisticsTrainAndTestEnum.DAILY_TRAIN_STUDENT.getCode(),StatisticsTrainAndTestEnum.DAILY_TRAIN_STUDENT.getName(), String.valueOf(dailyTrain.getTotalPeople()),""));
                } else if (Objects.equals(dto.getCode(), StatisticsTrainAndTestEnum.DAILY_TRAIN_CREDIT.getCode())) {
                    list.add(new StatisticsV2RespDTO(StatisticsTrainAndTestEnum.DAILY_TRAIN_CREDIT.getCode(),StatisticsTrainAndTestEnum.DAILY_TRAIN_CREDIT.getName(), dailyTrain.getTotalPeriod().stripTrailingZeros().toPlainString(),""));
                } else if (Objects.equals(dto.getCode(), StatisticsTrainAndTestEnum.DAILY_TRAIN_ACTIVITY.getCode())) {
                    list.add(new StatisticsV2RespDTO(StatisticsTrainAndTestEnum.DAILY_TRAIN_ACTIVITY.getCode(),StatisticsTrainAndTestEnum.DAILY_TRAIN_ACTIVITY.getName(), String.valueOf(dailyTrain.getTotalActivity()),""));
                } else if (Objects.equals(dto.getCode(), StatisticsTrainAndTestEnum.DAILY_TRAIN_PASS_RATE.getCode())) {
                    list.add(new StatisticsV2RespDTO(StatisticsTrainAndTestEnum.DAILY_TRAIN_PASS_RATE.getCode(),StatisticsTrainAndTestEnum.DAILY_TRAIN_PASS_RATE.getName(), dailyTrain.getPassRate().stripTrailingZeros().toPlainString(),"%"));
                }
            }
        }
        return list;
    }

    private void setCache(String text, Integer type) {
        ExamStatisticsCache cache = examStatisticsCacheMapper.selectOne(new LambdaQueryWrapper<ExamStatisticsCache>()
                .eq(ExamStatisticsCache::getType, type)
                .eq(ExamStatisticsCache::getUserId, UserContext.getId())
                .eq(ExamStatisticsCache::getTenantId, TenantContext.getOne())
                .eq(ExamStatisticsCache::getIsDel, DelFlag.SAVE.getValue()));
        if (cache == null) {
            cache = new ExamStatisticsCache();
            cache.setType(type);
            cache.setText(text);
            cache.setUserId(UserContext.getId());
            cache.setTenantId(TenantContext.getOne());
            cache.setCreate(UserContext.getId());
            examStatisticsCacheMapper.insert(cache);
        } else {
            cache.setText(text);
            cache.setModify(UserContext.getId());
            examStatisticsCacheMapper.updateById(cache);
        }
    }

    @Override
    public String cacheParam(Integer type) {
        ExamStatisticsCache cache = examStatisticsCacheMapper.selectOne(new LambdaQueryWrapper<ExamStatisticsCache>()
                .eq(ExamStatisticsCache::getType, type)
                .eq(ExamStatisticsCache::getUserId, UserContext.getId())
                .eq(ExamStatisticsCache::getTenantId, TenantContext.getOne())
                .eq(ExamStatisticsCache::getIsDel, DelFlag.SAVE.getValue()));
        return Objects.isNull(cache) ? null : cache.getText();
    }

    /**
     * 统计计划下当前年月的内容数量
     * @param planIds 计划ids
     * @param yearMonth 年月
     * @return 内容数量
     */
    private Long contextCount(List<Long> planIds,Date yearMonth) {
        return statisticsMapper.contextCount(planIds,yearMonth);
    }

    /**
     * 统计计划下当前年月的培训活动数量
     * @param planIds 计划ids
     * @param yearMonth 年月
     * @param trainDepartmentIds 组织单位ids
     * @return 培训活动数量
     */
    private Long activityCount(List<Long> planIds,Date yearMonth,List<Integer> trainDepartmentIds) {
        return statisticsMapper.activityCount(planIds,yearMonth,trainDepartmentIds);
    }

    private ActivityStatisticsRespVO sum(List<ActivityStatisticsRespVO> list) {
        if (!CollectionUtils.isEmpty(list)) {
            return list.get(list.size()-1);
        }
        return ActivityStatisticsRespVO.init();
    }
}
