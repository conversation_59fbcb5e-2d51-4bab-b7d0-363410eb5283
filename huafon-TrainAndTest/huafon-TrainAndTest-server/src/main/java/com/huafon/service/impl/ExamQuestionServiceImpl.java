package com.huafon.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huafon.admin.api.service.ISysDictRpcService;
import com.huafon.common.config.TenantContext;
import com.huafon.dao.mapper.ExamQuestionItemMapper;
import com.huafon.dao.mapper.ExamQuestionMapper;
import com.huafon.datascope.annotation.AdditionalExpression;
import com.huafon.datascope.annotation.CustomMapping;
import com.huafon.datascope.annotation.DataScopeControl;
import com.huafon.framework.mybatis.enums.DelFlag;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.models.entity.ExamQuestion;
import com.huafon.models.entity.ExamQuestionItem;
import com.huafon.models.enums.QuestionTypeEnum;
import com.huafon.models.enums.TrainTypeEnum;
import com.huafon.models.vo.req.question.ExamQuestionAddReqVO;
import com.huafon.models.vo.req.question.ExamQuestionBatchOpenReqVO;
import com.huafon.models.vo.req.question.ExamQuestionBatchRemoveReqVO;
import com.huafon.models.vo.req.question.ExamQuestionCountReqVO;
import com.huafon.models.vo.req.question.ExamQuestionDifficultNumReqVO;
import com.huafon.models.vo.req.question.ExamQuestionEditReqVO;
import com.huafon.models.vo.req.question.ExamQuestionItemAddReqVO;
import com.huafon.models.vo.req.question.ExamQuestionItemEditReqVO;
import com.huafon.models.vo.req.question.ExamQuestionListReqVO;
import com.huafon.models.vo.resp.question.ExamQuestionDifficultNumRespVO;
import com.huafon.models.vo.resp.question.ExamQuestionInfoRespVO;
import com.huafon.models.vo.resp.question.ExamQuestionItemInfoRespVO;
import com.huafon.models.vo.resp.question.ExamQuestionListRespVO;
import com.huafon.models.vo.resp.question.ExamQuestionNumGroupRespVO;
import com.huafon.models.vo.resp.question.ExamQuestionNumRespVO;
import com.huafon.models.vo.resp.subject.ExamSubjectInfoRespVO;
import com.huafon.models.vo.resp.subject.ExamSubjectNodeCountVO;
import com.huafon.portal.api.service.DeptRpcService;
import com.huafon.service.ExamQuestionService;
import com.huafon.service.ExamSubjectService;
import com.huafon.support.config.UserContext;
import com.huafon.support.exceptions.ServiceException;
import com.huafon.support.utils.StringUtils;
import com.huafon.utils.CodeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @describe: 考试题目service层
 * @date 2022/5/17 14:05
 */
@Service
@Slf4j
public class ExamQuestionServiceImpl extends ServiceImpl<ExamQuestionMapper, ExamQuestion> implements ExamQuestionService {

    private final ExamQuestionMapper examQuestionMapper;

    private final ExamQuestionItemMapper examQuestionItemMapper;

    private final ExamSubjectService examSubjectService;

    private final CodeUtils codeUtils;

    @DubboReference
    private ISysDictRpcService iSysDictRpcService;
    @DubboReference
    private DeptRpcService deptRpcService;

    @Autowired
    public ExamQuestionServiceImpl(ExamQuestionMapper examQuestionMapper,
                                   ExamQuestionItemMapper examQuestionItemMapper,
                                   @Lazy ExamSubjectService examSubjectService,
                                   CodeUtils codeUtils) {
        this.examQuestionMapper = examQuestionMapper;
        this.examQuestionItemMapper = examQuestionItemMapper;
        this.examSubjectService = examSubjectService;
        this.codeUtils = codeUtils;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addQuestion(ExamQuestionAddReqVO examQuestionAddReqVO) {
        ExamQuestion examQuestion = ExamQuestionAddReqVO.convertToExamQuestion(examQuestionAddReqVO);
        if (TrainTypeEnum.internal.getType().equals(examQuestion.getType())) {
            if (Objects.isNull(examQuestion.getSubjectId())) {
                ExamSubjectInfoRespVO noClassifyNode = examSubjectService.getNoClassifyNode();
                examQuestion.setSubjectId(noClassifyNode.getId());
            }
            if (isExistQuestionByAdd(examQuestion.getQuestionCode(), examQuestion.getSubjectId(), examQuestion.getShortTitle())) {
                throw new ServiceException("题库已存在相同题目");
            }
        } else {
            if (isExistQuestionByAdd(examQuestion.getQuestionCode(), examQuestion.getSubjectCode(), examQuestion.getShortTitle())) {
                throw new ServiceException("题库已存在相同题目");
            }
        }
        examQuestion.setCode(codeUtils.createQuestionCode(TenantContext.getOne()));
        examQuestionMapper.insert(examQuestion);
        examQuestionAddReqVO.getExamQuestionItem().forEach(item -> {
            ExamQuestionItem examQuestionItem = ExamQuestionItemAddReqVO.convertToExamQuestionItem(item);
            examQuestionItem.setExamQuestionId(examQuestion.getId());
            examQuestionItemMapper.insert(examQuestionItem);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editQuestion(ExamQuestionEditReqVO examQuestionEditReqVO) {
        ExamQuestion examQuestion = ExamQuestionEditReqVO.convertToExamQuestion(examQuestionEditReqVO);
        if (TrainTypeEnum.internal.getType().equals(examQuestion.getType())) {
            if (Objects.isNull(examQuestion.getSubjectId())) {
                ExamSubjectInfoRespVO noClassifyNode = examSubjectService.getNoClassifyNode();
                examQuestion.setSubjectId(noClassifyNode.getId());
            }
        }
        examQuestionMapper.updateById(examQuestion);
        QueryWrapper<ExamQuestionItem> examQuestionItemQueryWrapper = new QueryWrapper<>();
        examQuestionItemQueryWrapper.lambda()
                .eq(ExamQuestionItem::getExamQuestionId, examQuestionEditReqVO.getId())
                .eq(ExamQuestionItem::getIsDel, 0);
        ExamQuestionItem examQuestionItem = new ExamQuestionItem();
        examQuestionItem.setModifyTime(new Date()).setIsDel(1);
        examQuestionItemMapper.update(examQuestionItem, examQuestionItemQueryWrapper);
        examQuestionEditReqVO.getExamQuestionItem().forEach(item -> {
            ExamQuestionItem questionItem = ExamQuestionItemEditReqVO.convertToExamQuestionItem(item);
            questionItem.setExamQuestionId(examQuestion.getId());
            examQuestionItemMapper.insert(questionItem);
        });
    }

    @Override
    public ExamQuestionInfoRespVO readQuestionInfo(Long id) {
        QueryWrapper<ExamQuestion> examQuestionQueryWrapper = new QueryWrapper<>();
        examQuestionQueryWrapper.lambda()
                .eq(ExamQuestion::getId, id)
                .eq(ExamQuestion::getIsDel, 0);
        ExamQuestion examQuestion = examQuestionMapper.selectOne(examQuestionQueryWrapper);
        if (examQuestion == null) {
            throw new ServiceException("未找到该题目信息。");
        }
        ExamQuestionInfoRespVO examQuestionInfoRespVO = ExamQuestionInfoRespVO.convertToExamQuestionInfoRespVO(examQuestion);
        examQuestionInfoRespVO.setSubjectName(examSubjectService.readExamSubject(examQuestion.getSubjectId()).getSubjectName());
        QueryWrapper<ExamQuestionItem> examQuestionItemQueryWrapper = new QueryWrapper<>();
        examQuestionItemQueryWrapper.lambda()
                .eq(ExamQuestionItem::getExamQuestionId, id)
                .eq(ExamQuestionItem::getIsDel, 0);
        List<ExamQuestionItem> examQuestionItems = examQuestionItemMapper.selectList(examQuestionItemQueryWrapper);
        List<ExamQuestionItemInfoRespVO> examQuestionItemInfoRespVoS = new ArrayList<>();
        examQuestionItems.forEach(examQuestionItem -> {
            ExamQuestionItemInfoRespVO examQuestionItemInfoRespVO = ExamQuestionItemInfoRespVO.convertToExamQuestionItemInfoRespVO(examQuestionItem);
            examQuestionItemInfoRespVoS.add(examQuestionItemInfoRespVO);
        });
        examQuestionInfoRespVO.setExamQuestionItemInfoRespVOS(examQuestionItemInfoRespVoS);
        return examQuestionInfoRespVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeQuestion(Long id) {
        ExamQuestion examQuestion = new ExamQuestion();
        examQuestion.setModifyById(UserContext.getId())
                    .setModifyByName(UserContext.get().getName())
                    .setModifyTime(new Date())
                    .setIsDel(1);
        QueryWrapper<ExamQuestion> examQuestionWrapper = new QueryWrapper<>();
        examQuestionWrapper.lambda().eq(ExamQuestion::getId, id);
        examQuestionMapper.update(examQuestion, examQuestionWrapper);
    }

    @Override
    public void batchRemoveQuestion(List<Long> ids) {
        if (!CollectionUtils.isEmpty(ids)) {
            ExamQuestion examQuestion = new ExamQuestion();
            examQuestion.setModifyById(UserContext.getId())
                        .setModifyByName(UserContext.get().getName())
                        .setModifyTime(new Date())
                        .setIsDel(1);
            QueryWrapper<ExamQuestion> examQuestionWrapper = new QueryWrapper<>();
            examQuestionWrapper.lambda().in(ExamQuestion::getId, ids).eq(ExamQuestion::getTenantId, TenantContext.getOne());
            examQuestionMapper.update(examQuestion, examQuestionWrapper);
        }
    }

    @Override
    @DataScopeControl(
            includeProperty = {
                    "examQuestionCreateDepartment",
                    "create_by"
            },
            customMapping = {
                    @CustomMapping(origin = "examQuestionCreateDepartment", target = "create_department", description = "题库创建人所属部门"),
                    @CustomMapping(origin = "create_by", target = "create_by_id", description = "创建人")
            },
            dataScopeAdditionalExpressions = {
                    @AdditionalExpression(conditionExpression = "open = 1")
            },
            dataScopeExtraExpressions = {
                    @AdditionalExpression(isAnd = false, conditionExpression = "open = 0")
            })
    public CommonPage<ExamQuestionListRespVO> queryQuestionList(ExamQuestionListReqVO examQuestionListReqVO) {
        Integer tenantId = TenantContext.getOne();
        examQuestionListReqVO.setTenantId(tenantId);
        //如果是内部培训考试，通过他的subjectId 获取到他下面的所有子id,内部考试判断是否公开
        if (examQuestionListReqVO.getType().equals(TrainTypeEnum.internal.getType())) {
            examQuestionListReqVO.setSubjectIds(examSubjectService.readSubjectIds(examQuestionListReqVO.getSubjectId(), tenantId));
            examQuestionListReqVO.setCreateById(UserContext.getId());
            if (!CollectionUtils.isEmpty(examQuestionListReqVO.getQuestionSubjectIds())) {
                List<Long> allSubjectIds = new ArrayList<>();
                for (Long subjectId : examQuestionListReqVO.getQuestionSubjectIds()) {
                    allSubjectIds.addAll(examSubjectService.readSubjectIds(subjectId, tenantId));
                }
                examQuestionListReqVO.setQuestionSubjectIds(allSubjectIds);
            }
        }
        if (Objects.nonNull(examQuestionListReqVO.getCreateDepartment())) {
            examQuestionListReqVO.setCreateDepartments(deptRpcService.getChildNodeIdsById(examQuestionListReqVO.getCreateDepartment()));
        }
        Page<ExamQuestionListRespVO> page = new Page(examQuestionListReqVO.getPageNo(), examQuestionListReqVO.getPageSize());
        IPage<ExamQuestionListRespVO> listPage = examQuestionMapper.getQuestionListPage(page, examQuestionListReqVO);
        IPage<ExamQuestionListRespVO> examQuestionListRespVOIPage = listPage.convert(this::convertToExamQuestionListRespVO);
        return new CommonPage<>(examQuestionListRespVOIPage);
    }

    private ExamQuestionListRespVO convertToExamQuestionListRespVO(ExamQuestionListRespVO examQuestionListRespVO) {
        examQuestionListRespVO.setSubjectName(examSubjectService.readExamSubject(examQuestionListRespVO.getSubjectId()).getSubjectName());
        return examQuestionListRespVO;
    }

    @Override
    public List<ExamQuestionNumRespVO> getQuestionTypeNum(List<Integer> tenantIds) {
        QueryWrapper<ExamQuestion> examQuestionQueryWrapper = new QueryWrapper<>();
        examQuestionQueryWrapper.in("tenant_id", tenantIds)
                .lambda().eq(ExamQuestion::getIsDel, 0);
        List<ExamQuestion> examQuestions = examQuestionMapper.selectList(examQuestionQueryWrapper);

        Map<String, Long> collect =
                examQuestions.stream()
                        .collect(Collectors.groupingBy(ExamQuestion::getQuestionCode, Collectors.counting()));

        List<ExamQuestionNumRespVO> examQuestionNumRespVOS = new ArrayList<>();
        for (Map.Entry<String, Long> map : collect.entrySet()) {
            ExamQuestionNumRespVO examQuestionNumRespVO = new ExamQuestionNumRespVO();
            examQuestionNumRespVO.setQuestionCode(map.getKey()).setNum(Math.toIntExact(map.getValue()));
            examQuestionNumRespVOS.add(examQuestionNumRespVO);
        }
        return examQuestionNumRespVOS;
    }

    @Override
    public List<ExamQuestionNumRespVO> getQuestionTypeNumByCode(String code) {
        QueryWrapper<ExamQuestion> examQuestionQueryWrapper = new QueryWrapper<>();
        examQuestionQueryWrapper.lambda()
                .eq(ExamQuestion::getTenantId, TenantContext.getOne())
                .eq(ExamQuestion::getIsDel, 0)
                .eq(ExamQuestion::getSubjectCode, code);
        List<ExamQuestion> examQuestions = examQuestionMapper.selectList(examQuestionQueryWrapper);
        Map<String, Long> collect = examQuestions.stream().collect(Collectors.groupingBy(ExamQuestion::getQuestionCode, Collectors.counting()));
        List<ExamQuestionNumRespVO> examQuestionNumRespVOS = new ArrayList<>();
        for (Map.Entry<String, Long> map : collect.entrySet()) {
            ExamQuestionNumRespVO examQuestionNumRespVO = new ExamQuestionNumRespVO();
            examQuestionNumRespVO.setQuestionCode(map.getKey()).setNum(Math.toIntExact(map.getValue()));
            examQuestionNumRespVOS.add(examQuestionNumRespVO);
        }
        return examQuestionNumRespVOS;
    }

    @Override
    public List<ExamQuestionNumRespVO> getQuestionTypeNumBySubjectIds(List<Long> subjectIds) {
        Integer tenantId = TenantContext.getOne();
        List<Long> allSubjectIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(subjectIds)) {
            for (Long subject: subjectIds) {
                allSubjectIds.addAll(examSubjectService.readSubjectIds(subject,tenantId));
            }
        }
        QueryWrapper<ExamQuestion> examQuestionQueryWrapper = new QueryWrapper<>();
        examQuestionQueryWrapper.lambda()
                .eq(ExamQuestion::getTenantId, tenantId)
                .in(!CollectionUtils.isEmpty(allSubjectIds), ExamQuestion::getSubjectId, allSubjectIds)
                .eq(ExamQuestion::getIsDel, 0);
        List<ExamQuestion> examQuestions = examQuestionMapper.selectList(examQuestionQueryWrapper);
        Map<String, Long> collect = examQuestions.stream().collect(Collectors.groupingBy(ExamQuestion::getQuestionCode, Collectors.counting()));
        List<ExamQuestionNumRespVO> examQuestionNumRespVOS = new ArrayList<>();
        for (Map.Entry<String, Long> map : collect.entrySet()) {
            ExamQuestionNumRespVO examQuestionNumRespVO = new ExamQuestionNumRespVO();
            examQuestionNumRespVO.setQuestionCode(map.getKey()).setNum(Math.toIntExact(map.getValue()));
            examQuestionNumRespVOS.add(examQuestionNumRespVO);
        }
        return examQuestionNumRespVOS;
    }

    @Override
    public Boolean isExistQuestion(String questionCode, String subjectCode, String shortTitle) {
        Integer tenantId = TenantContext.getOne();
        int count = new LambdaQueryChainWrapper<>(examQuestionMapper)
                .eq(ExamQuestion::getQuestionCode, QuestionTypeEnum.getCode(questionCode))
                .eq(ExamQuestion::getSubjectCode, iSysDictRpcService.getCodeByName(subjectCode, tenantId))
                .eq(ExamQuestion::getShortTitle, shortTitle)
                .eq(ExamQuestion::getTenantId, tenantId)
                .eq(ExamQuestion::getIsDel, 0)
                .count();
        return count > 0;
    }

    @Override
    public Boolean isExistQuestion(String questionCode, Long subjectId, String shortTitle) {
        Integer tenantId = TenantContext.getOne();
        int count = new LambdaQueryChainWrapper<>(examQuestionMapper)
                .eq(ExamQuestion::getQuestionCode, QuestionTypeEnum.getCode(questionCode))
                .eq(ExamQuestion::getSubjectId, subjectId)
                .eq(ExamQuestion::getShortTitle, shortTitle)
                .eq(ExamQuestion::getTenantId, tenantId)
                .eq(ExamQuestion::getIsDel, 0)
                .count();
        return count > 0;
    }

    @Override
    public List<ExamQuestion> readExamQuestions(List<Long> subjectIds) {
        if (!CollectionUtils.isEmpty(subjectIds)) {
            List<ExamQuestion> examQuestions = new LambdaQueryChainWrapper<>(examQuestionMapper)
                    .in(ExamQuestion::getSubjectId, subjectIds)
                    .eq(ExamQuestion::getIsDel, 0)
                    .list();
            return CollectionUtils.isEmpty(examQuestions) ? Collections.emptyList() : examQuestions;
        }
        return Collections.emptyList();
    }

    @Override
    @DataScopeControl(
            includeProperty = {
                    "examQuestionCreateDepartment",
                    "create_by"
            },
            customMapping = {
                    @CustomMapping(origin = "examQuestionCreateDepartment", target = "create_department", description = "题库创建人所属部门"),
                    @CustomMapping(origin = "create_by", target = "create_by_id", description = "创建人")
            },
            dataScopeAdditionalExpressions = {
                    @AdditionalExpression(conditionExpression = "open = 1")
            },
            dataScopeExtraExpressions = {
                    @AdditionalExpression(isAnd = false, conditionExpression = "open = 0")
            })
    public List<ExamSubjectNodeCountVO> getNodeNums(ExamQuestionCountReqVO reqVO) {
        Integer tenantId = TenantContext.getOne();
        List<ExamSubjectNodeCountVO> nodes = examSubjectService.initSubjectNodeCountList(reqVO.getNodeIds());
        if (!CollectionUtils.isEmpty(nodes)) {
            for (ExamSubjectNodeCountVO node : nodes) {
                List<Long> subjectIds = examSubjectService.readSubjectIds(node.getId(), tenantId);
                QueryWrapper<ExamQuestion> queryWrapper = new QueryWrapper<ExamQuestion>();
                queryWrapper.lambda()
                        .eq(StringUtils.isNotBlank(reqVO.getQuestionCode()), ExamQuestion::getQuestionCode, reqVO.getQuestionCode())
                        .eq(ExamQuestion::getTenantId, tenantId)
                        .eq(ExamQuestion::getIsDel, 0)
                        .in(ExamQuestion::getSubjectId, subjectIds)
                        .apply("((open = 1) OR (open = 0 AND create_by_id = " + UserContext.get().getUserId() + "))");
                Integer num = examQuestionMapper.selectCount(queryWrapper);
                node.setNum(num);
            }
        }
        return nodes;
    }

    @Override
    public void batchUpdateSubjectId(List<ExamQuestion> examQuestions, Long subjectId) {
        if (!CollectionUtils.isEmpty(examQuestions)) {
            List<Long> questionIds = examQuestions.stream().map(ExamQuestion::getId).collect(Collectors.toList());
            UpdateWrapper<ExamQuestion> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda()
                    .set(ExamQuestion::getSubjectId, subjectId)
                    .in(ExamQuestion::getId, questionIds);
            examQuestionMapper.update(null, updateWrapper);
        }
    }

    @Override
    public List<ExamQuestionDifficultNumRespVO> getQuestionTypeNumByDifficult(ExamQuestionDifficultNumReqVO reqVO) {
        Integer tenantId = TenantContext.getOne();
        List<Long> allSubjectIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(reqVO.getSubjectIds())) {
            for (Long subjectId : reqVO.getSubjectIds()) {
                allSubjectIds.addAll(examSubjectService.readSubjectIds(subjectId,tenantId));
            }
        }
        List<ExamQuestion> examQuestions = new LambdaQueryChainWrapper<>(examQuestionMapper)
                .eq(StringUtils.isNotBlank(reqVO.getSubjectCode()), ExamQuestion::getSubjectCode, reqVO.getSubjectCode())
                .in(!CollectionUtils.isEmpty(allSubjectIds), ExamQuestion::getSubjectId, allSubjectIds)
                .eq(ExamQuestion::getTenantId, TenantContext.getOne())
                .eq(ExamQuestion::getIsDel, 0)
                .list();
        Map<String, List<ExamQuestion>> collect = examQuestions.stream().collect(Collectors.groupingBy(ExamQuestion::getQuestionCode));
        List<ExamQuestionDifficultNumRespVO> examQuestionDifficultNumRespVOS = new ArrayList<>();
        collect.forEach((questionType,questions) -> {
            ExamQuestionDifficultNumRespVO examQuestionDifficultNumRespVO = new ExamQuestionDifficultNumRespVO();
            examQuestionDifficultNumRespVO.setQuestionCode(questionType);
            List<ExamQuestionDifficultNumRespVO.Difficult.Info> difficult = new ArrayList<>();
            Map<String, Long> difficultMap = questions.stream().collect(Collectors.groupingBy(ExamQuestion::getDifficult,Collectors.counting()));
            for (Map.Entry<String,Long> map: difficultMap.entrySet()) {
                ExamQuestionDifficultNumRespVO.Difficult.Info info = new ExamQuestionDifficultNumRespVO.Difficult.Info();
                info.setDifficult(map.getKey());
                info.setNum(Math.toIntExact(map.getValue()));
                difficult.add(info);
            }
            examQuestionDifficultNumRespVO.setDifficult(difficult);
            examQuestionDifficultNumRespVOS.add(examQuestionDifficultNumRespVO);
        });
        return examQuestionDifficultNumRespVOS;
    }

    @Override
    public void batchOpen(ExamQuestionBatchOpenReqVO reqVO) {
        this.lambdaUpdate()
                .set(ExamQuestion::getOpen, reqVO.getOpen())
                .set(ExamQuestion::getModifyById, UserContext.getId())
                .set(ExamQuestion::getModifyByName, UserContext.get().getName())
                .set(ExamQuestion::getModifyTime, new Date())
                .in(ExamQuestion::getId, reqVO.getIds())
                .update();
    }

    @Override
    public List<ExamQuestionNumGroupRespVO> getQuestionTypeNumGroupBySubjectIds(List<Long> subjectIds) {
        List<ExamQuestionNumGroupRespVO> voList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(subjectIds)) {
            Integer tenantId = TenantContext.getOne();
            for (Long subjectId : subjectIds) {
                List<ExamQuestion> examQuestions = examQuestionMapper.selectList(new LambdaQueryWrapper<ExamQuestion>()
                        .in(ExamQuestion::getSubjectId, examSubjectService.readSubjectIds(subjectId,tenantId))
                        .eq(ExamQuestion::getTenantId, tenantId)
                        .eq(ExamQuestion::getIsDel, DelFlag.SAVE.getValue()));
                Map<String, Long> collect = examQuestions.stream().collect(Collectors.groupingBy(ExamQuestion::getQuestionCode, Collectors.counting()));
                List<ExamQuestionNumRespVO> examQuestionNumRespVOS = new ArrayList<>();
                for (Map.Entry<String, Long> map : collect.entrySet()) {
                    ExamQuestionNumRespVO examQuestionNumRespVO = new ExamQuestionNumRespVO();
                    examQuestionNumRespVO.setQuestionCode(map.getKey()).setNum(Math.toIntExact(map.getValue()));
                    examQuestionNumRespVOS.add(examQuestionNumRespVO);
                }
                voList.add(new ExamQuestionNumGroupRespVO(subjectId, examQuestionNumRespVOS));
            }
        }
        return voList;
    }

    @Override
    public void batchRemove(ExamQuestionBatchRemoveReqVO reqVO) {
        this.lambdaUpdate()
                .set(ExamQuestion::getSubjectId, reqVO.getSubjectId())
                .set(ExamQuestion::getModifyById, UserContext.getId())
                .set(ExamQuestion::getModifyByName, UserContext.get().getName())
                .set(ExamQuestion::getModifyTime, new Date())
                .in(ExamQuestion::getId, reqVO.getIds())
                .update();
    }

    private Boolean isExistQuestionByAdd(String questionCode, String subjectCode, String shortTitle) {
        Integer tenantId = TenantContext.getOne();
        int count = new LambdaQueryChainWrapper<>(examQuestionMapper)
                .eq(ExamQuestion::getQuestionCode, questionCode)
                .eq(ExamQuestion::getSubjectCode, subjectCode)
                .eq(ExamQuestion::getShortTitle, shortTitle)
                .eq(ExamQuestion::getTenantId, tenantId)
                .eq(ExamQuestion::getIsDel, 0)
                .count();
        return count > 0;
    }

    private Boolean isExistQuestionByAdd(String questionCode, Long subjectId, String shortTitle) {
        Integer tenantId = TenantContext.getOne();
        int count = new LambdaQueryChainWrapper<>(examQuestionMapper)
                .eq(ExamQuestion::getQuestionCode, questionCode)
                .eq(ExamQuestion::getSubjectId, subjectId)
                .eq(ExamQuestion::getShortTitle, shortTitle)
                .eq(ExamQuestion::getTenantId, tenantId)
                .eq(ExamQuestion::getIsDel, 0)
                .count();
        return count > 0;
    }

}
