package com.huafon.service.support;

import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.excel.entity.params.ExcelForEachParams;
import cn.afterturn.easypoi.excel.export.styler.IExcelExportStyler;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.DefaultIndexedColorMap;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;

import static com.huafon.utils.excel.ExcelStyleConstant.HEX_COLOR;
import static com.huafon.utils.excel.ExcelStyleConstant.getColor;

/**
 * <p>
 * Easypoi 自定义样式类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-22
 */
public class ExcelExportStyler implements IExcelExportStyler {

    // 表头样式
    private CellStyle headerStyle;

    // 标题样式
    private CellStyle titleStyle;

    // 数据内容样式
    private CellStyle styles;

    public ExcelExportStyler(Workbook workbook) {
        this.headerStyle = initHeaderStyle(workbook);
        this.titleStyle = initTitleStyle(workbook);
        this.styles = initStyles(workbook);
    }

    @Override
    public CellStyle getHeaderStyle(short i) {
        return headerStyle;
    }

    @Override
    public CellStyle getTitleStyle(short i) {
        return titleStyle;
    }

    @Override
    public CellStyle getStyles(boolean b, ExcelExportEntity excelExportEntity) {
        return styles;
    }

    @Override
    public CellStyle getStyles(Cell cell, int i, ExcelExportEntity excelExportEntity, Object o, Object o1) {
        return getStyles(true, excelExportEntity);
    }

    @Override
    public CellStyle getTemplateStyles(boolean b, ExcelForEachParams excelForEachParams) {
        return null;
    }

    private CellStyle initHeaderStyle(Workbook workbook) {
        XSSFCellStyle style = getBassCellStyle(workbook);
        style.setFont(getFont(workbook, (short) 14,true));
        java.awt.Color color = getColor(HEX_COLOR);
        XSSFColor myColor = new XSSFColor(color, new DefaultIndexedColorMap());
        style.setFillForegroundColor(myColor);
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return style;
    }

    private CellStyle initTitleStyle(Workbook workbook) {
        return initHeaderStyle(workbook);
    }

    private CellStyle initStyles(Workbook workbook) {
        CellStyle style = getBassCellStyle(workbook);
        style.setFont(getFont(workbook, (short) 12,false));
        return style;
    }

    private XSSFCellStyle getBassCellStyle(Workbook workbook) {
        XSSFCellStyle style = (XSSFCellStyle) workbook.createCellStyle();
        // 添加边框
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        // 水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        // 垂直左对齐
        style.setAlignment(HorizontalAlignment.LEFT);
        // 自动换行
        style.setWrapText(true);
        return style;
    }

    private Font getFont(Workbook workbook, short size, boolean isBold) {
        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setBold(isBold);
        font.setFontHeightInPoints(size);
        return font;
    }

    /**
     * 设置高度 标题行高20 正文行高18
     * @param workbook
     * @param headRow 表头行数
     */
    public static void setHeight(Workbook workbook, Integer headRow) {
        if (headRow == null) {
            headRow = 1;
        }
        Sheet sheet = workbook.getSheetAt(0);
        for (int i=0;i<headRow;i++) {
            sheet.getRow(i).setHeightInPoints(20);
        }
        for (int i=headRow;i<=sheet.getLastRowNum();i++) {
            sheet.getRow(i).setHeightInPoints(18);
        }
    }

    public static void setWidth(Workbook workbook) {
        Sheet sheet = workbook.getSheetAt(0);
        for (int i=0;i<sheet.getRow(0).getLastCellNum();i++) {
            sheet.setColumnWidth(i,32*256);
        }
    }

    /**
     * 覆盖二级标题样式
     * @param workbook
     */
    public static void setSecondTitleStyle(Workbook workbook) {
        Sheet sheet = workbook.getSheetAt(0);
        // 一级标题样式
        CellStyle style = sheet.getRow(0).getCell(0).getCellStyle();
        Row row = sheet.getRow(1);
        for (int i=0;i<row.getLastCellNum();i++) {
            row.getCell(i).setCellStyle(style);
        }
    }
}
