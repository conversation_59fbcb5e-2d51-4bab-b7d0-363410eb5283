package com.huafon.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.huafon.models.dto.excel.SubjectDownloadDTO;
import com.huafon.models.entity.ExamSubject;
import com.huafon.models.vo.req.subject.ExamSubjectAddReqVO;
import com.huafon.models.vo.req.subject.ExamSubjectEditReqVO;
import com.huafon.models.vo.req.subject.MoveReq;
import com.huafon.models.vo.resp.subject.ExamSubjectInfoRespVO;
import com.huafon.models.vo.resp.subject.ExamSubjectNodeCountVO;
import com.huafon.models.vo.resp.subject.ExamSubjectTreeRespVO;
import com.huafon.portal.api.dto.TenantSyncMqDto;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-03
 */
public interface ExamSubjectService extends IService<ExamSubject> {

    void addSubject(ExamSubjectAddReqVO examSubjectAddReqVo);

    void editSubject(ExamSubjectEditReqVO examSubjectEditReqVO);

    ExamSubjectInfoRespVO readInfo(Integer id);

    ExamSubjectInfoRespVO getNoClassifyNode();

    ExamSubjectTreeRespVO readTree();

    ExamSubject readExamSubject(Long id);

    void delete(Long id);

    List<Long> readSubjectIds(Long pid, Integer tenantId);

    String readLink(Long id);

    void move(MoveReq req);

    List<ExamSubjectNodeCountVO> initSubjectNodeCountList(List<Long> nodeIds);

    void init(TenantSyncMqDto tenantSyncMqDto);

    Map<Long, String> readMapSubjectIds(List<Long> subjectIds);

    Map<String, List<Integer>> getAllNodeForImport();

    ExamSubject getTemporaryNode();

    List<SubjectDownloadDTO> findForDownload();

    List<String> readExamSubjectNames(List<Long> subjectIds);
}
