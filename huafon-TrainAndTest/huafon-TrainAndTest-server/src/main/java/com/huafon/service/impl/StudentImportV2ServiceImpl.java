package com.huafon.service.impl;

import com.huafon.common.config.TenantContext;
import com.huafon.models.dto.excel.StudentUploadV2DTO;
import com.huafon.portal.api.dto.UserDto;
import com.huafon.service.StudentImportV2Service;
import com.huafon.service.support.ExcelValidationError;
import com.huafon.service.support.ExcelValidationResult;
import com.huafon.utils.UserRpcUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 学员导入 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-28
 */
@Service
@Slf4j
public class StudentImportV2ServiceImpl implements StudentImportV2Service {

    private final UserRpcUtil userRpcUtil;

    @Autowired
    public StudentImportV2ServiceImpl(UserRpcUtil userRpcUtil) {
        this.userRpcUtil = userRpcUtil;
    }

    @Override
    public ExcelValidationResult<StudentUploadV2DTO> validationImportExcel(List<StudentUploadV2DTO> source) {
        if (CollectionUtils.isEmpty(source)) {
            log.warn("[导入的数据为空]");
            return new ExcelValidationResult<>(new ArrayList<>());
        }
        List<StudentUploadV2DTO> success = new ArrayList<>();
        List<ExcelValidationError<StudentUploadV2DTO>> error = new ArrayList<>();
        Set<String> duplicate = new HashSet<>();
        List<UserDto> userDtos = userRpcUtil.getByUsernames(source.stream().map(StudentUploadV2DTO::getUsername).collect(Collectors.toList()), TenantContext.getOne());
        Map<String, UserDto> userDtoMap = userDtos.stream().collect(Collectors.toMap(UserDto::getUsername, Function.identity()));
        for (StudentUploadV2DTO item : source) {
            StringJoiner errorMessage = new StringJoiner(";");
            if (StringUtils.isBlank(item.getStaffName())) {
                errorMessage.add("姓名不能为空");
            }
            if (StringUtils.isBlank(item.getUsername())) {
                errorMessage.add("用户名不能为空");
            }
            if (errorMessage.length() == 0) {
                String duplicateKey = item.getStaffName() + "-" + item.getUsername();
                if (duplicate.contains(duplicateKey)) {
                    errorMessage.add("表格中存在相同的数据");
                } else {
                    UserDto userDto = userDtoMap.get(item.getUsername());
                    if (userDto == null) {
                        errorMessage.add("不存在该用户");
                    } else {
                        if (!Objects.equals(item.getStaffName(),userDto.getName())) {
                            errorMessage.add("不存在该用户");
                        }
                        item.setUserId(userDto.getUserId());
                        item.setMobile(userDto.getMobile());
                        item.setEmail(userDto.getEmail());
                        item.setGender(userDto.getGender());
                        item.setIdNumber(userDto.getIdNumber());
                        item.setAvatar(userDto.getAvatar());
                        item.setWorkNum(userDto.getWorkNum());
                        item.setDeptPost(userDto.getDepartPostInfo());
                    }
                    duplicate.add(duplicateKey);
                }
            }
            if (errorMessage.length() > 0) {
                error.add(new ExcelValidationError<>(item, errorMessage.toString()));
            } else {
                success.add(item);
            }
        }
        return new ExcelValidationResult<>(success, error);
    }

}