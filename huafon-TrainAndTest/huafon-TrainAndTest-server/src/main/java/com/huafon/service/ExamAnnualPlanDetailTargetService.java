package com.huafon.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.huafon.models.entity.annual.ExamAnnualPlanDetailTarget;
import com.huafon.models.vo.req.annual.ExamAnnualPlanDetailTargetReqVO;
import com.huafon.models.vo.resp.annual.ExamAnnualPlanDetailTargetRespVO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 年度培训计划明细责任对象 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-29
 */
public interface ExamAnnualPlanDetailTargetService extends IService<ExamAnnualPlanDetailTarget> {

    void save(Long detailId, List<ExamAnnualPlanDetailTargetReqVO> targetList);

    void modify(Long detailId, List<ExamAnnualPlanDetailTargetReqVO> targetList);

    Map<Long,List<ExamAnnualPlanDetailTargetRespVO>> groupByDetailIds(List<Long> detailIds);

}
