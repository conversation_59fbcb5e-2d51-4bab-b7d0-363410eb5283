package com.huafon.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huafon.common.config.TenantContext;
import com.huafon.dao.mapper.ExamActivityStudentLinkMapper;
import com.huafon.framework.mybatis.enums.DelFlag;
import com.huafon.models.dto.StudentStatisticsDTO;
import com.huafon.models.entity.ExamActivity;
import com.huafon.models.entity.ExamActivityStudentLink;
import com.huafon.models.vo.req.activity.AssessmentSituationReqVO;
import com.huafon.models.vo.req.student.ThreeLevelStudentStatusCountReqVO;
import com.huafon.models.vo.resp.activity.RelatedSignListRespVO;
import com.huafon.service.ExamActivityService;
import com.huafon.service.ExamActivityStudentLinkService;
import com.huafon.support.config.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-09
 */
@Slf4j
@Service
public class ExamActivityStudentLinkServiceImpl extends ServiceImpl<ExamActivityStudentLinkMapper, ExamActivityStudentLink> implements ExamActivityStudentLinkService {

    private final ExamActivityStudentLinkMapper examActivityStudentLinkMapper;
    private final ExamActivityService examActivityService;

    @Autowired
    public ExamActivityStudentLinkServiceImpl(ExamActivityStudentLinkMapper examActivityStudentLinkMapper,
                                              @Lazy ExamActivityService examActivityService) {
        this.examActivityStudentLinkMapper = examActivityStudentLinkMapper;
        this.examActivityService = examActivityService;
    }

    @Override
    public List<ExamActivityStudentLink> readExamActivityStudentLinksByActivityId(Long activityId) {
        List<ExamActivityStudentLink> examActivityStudentLinks = new LambdaQueryChainWrapper<>(examActivityStudentLinkMapper)
                .eq(ExamActivityStudentLink::getActivityId, activityId)
                .eq(ExamActivityStudentLink::getIsDel, DelFlag.SAVE.getValue())
                .list();
        return CollectionUtils.isEmpty(examActivityStudentLinks) ? Collections.emptyList() : examActivityStudentLinks;
    }

    @Override
    public List<ExamActivityStudentLink> readExamActivityStudentLinks(List<Long> userIds, Long activityId) {
        if (Objects.nonNull(activityId) && !CollectionUtils.isEmpty(userIds)) {
            List<ExamActivityStudentLink> examActivityStudentLinks = new LambdaQueryChainWrapper<>(examActivityStudentLinkMapper)
                    .eq(ExamActivityStudentLink::getActivityId, activityId)
                    .in(ExamActivityStudentLink::getUserId, userIds)
                    .eq(ExamActivityStudentLink::getIsDel, DelFlag.SAVE.getValue())
                    .list();
            return examActivityStudentLinks;
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public ExamActivityStudentLink readExamActivityStudentLink(Long userId, Long activityId) {
        LambdaQueryWrapper<ExamActivityStudentLink> queryWrapper = new LambdaQueryWrapper<ExamActivityStudentLink>().eq(ExamActivityStudentLink::getUserId, userId)
                .eq(ExamActivityStudentLink::getActivityId, activityId)
                .eq(ExamActivityStudentLink::getIsDel, DelFlag.SAVE.getValue())
                .last("limit 1");
        ExamActivityStudentLink examActivityStudentLink = examActivityStudentLinkMapper.selectOne(queryWrapper);
        return Objects.isNull(examActivityStudentLink) ? new ExamActivityStudentLink() : examActivityStudentLink;
    }

    @Override
    public void addExamActivityStudentLink(ExamActivityStudentLink examActivityStudentLink) {
        ExamActivityStudentLink readExamActivityStudentLink = this.readExamActivityStudentLink(examActivityStudentLink.getUserId(), examActivityStudentLink.getActivityId());
        if (Objects.isNull(readExamActivityStudentLink.getId())) {
            examActivityStudentLinkMapper.insert(examActivityStudentLink);
        }
    }

    @Override
    public void deleteExamActivityStudentLink(ExamActivityStudentLink examActivityStudentLink) {
        examActivityStudentLinkMapper.update(examActivityStudentLink, new LambdaQueryWrapper<ExamActivityStudentLink>()
                .eq(ExamActivityStudentLink::getActivityId, examActivityStudentLink.getActivityId())
                .eq(ExamActivityStudentLink::getUserId, examActivityStudentLink.getUserId())
                .eq(ExamActivityStudentLink::getIsDel, DelFlag.SAVE.getValue()));
    }

    @Override
    public void batchDeleteExamActivityStudentLink(List<ExamActivityStudentLink> examActivityStudentLinks) {
        if (!CollectionUtils.isEmpty(examActivityStudentLinks)) {
            List<Long> ids = examActivityStudentLinks.stream().map(ExamActivityStudentLink::getId).collect(Collectors.toList());
            ExamActivityStudentLink examActivityStudentLink = new ExamActivityStudentLink();
            examActivityStudentLink.setModifyBy(UserContext.get().getUserId());
            examActivityStudentLink.setModifyTime(new Date());
            examActivityStudentLink.setIsDel(DelFlag.DELELTED.getValue());
            examActivityStudentLinkMapper.update(examActivityStudentLink, new LambdaQueryWrapper<ExamActivityStudentLink>()
                    .in(ExamActivityStudentLink::getId, ids)
                    .eq(ExamActivityStudentLink::getIsDel, DelFlag.SAVE.getValue()));
        }
    }

    @Override
    public void updateExamActivityStudentLink(ExamActivityStudentLink examActivityStudentLink) {
        examActivityStudentLinkMapper.update(examActivityStudentLink, new LambdaQueryWrapper<ExamActivityStudentLink>()
                .eq(ExamActivityStudentLink::getActivityId, examActivityStudentLink.getActivityId())
                .eq(ExamActivityStudentLink::getUserId, examActivityStudentLink.getUserId())
                .eq(ExamActivityStudentLink::getIsDel, DelFlag.SAVE.getValue()));
    }

    @Override
    public Integer countThreeLevel(ThreeLevelStudentStatusCountReqVO req, Integer status) {
        req.setStatus(status);
        return examActivityStudentLinkMapper.countThreeLevel(req);
    }

    @Override
    public List<RelatedSignListRespVO> readSignByActivityId(Long activityId) {
        return examActivityStudentLinkMapper.readSignByActivityId(activityId);
    }

    @Override
    public void updateAssessmentSituation(List<AssessmentSituationReqVO> reqVOS) {
        if (!CollectionUtils.isEmpty(reqVOS)) {
            reqVOS.forEach(reqVO -> {
                this.lambdaUpdate().set(ExamActivityStudentLink::getAssessmentSituation, reqVO.getAssessmentSituation())
                        .eq(ExamActivityStudentLink::getId, reqVO.getId())
                        .eq(ExamActivityStudentLink::getIsDel, 0)
                        .update();
            });
        }
    }

    @Override
    public Map<Long,Integer> countStudentActivityNum(List<Long> userIds,Date year) {
        if (!CollectionUtils.isEmpty(userIds)) {
            List<StudentStatisticsDTO> list = examActivityStudentLinkMapper.countStudentActivityNum(userIds,year,TenantContext.getOne());
            return list.stream().collect(Collectors.toMap(StudentStatisticsDTO::getUserId,StudentStatisticsDTO::getIntegerValue));
        } else {
            return new HashMap<>();
        }
    }

    @Override
    public void completeTrainByCourseModify(Long courseId) {
        List<ExamActivity> activities = examActivityService.lambdaQuery()
                .eq(ExamActivity::getCourseId,courseId)
                .eq(ExamActivity::getIsDel,DelFlag.SAVE.getValue())
                .eq(ExamActivity::getTenantId,TenantContext.getOne())
                .list();
        if (!CollectionUtils.isEmpty(activities)) {
            List<Long> activityIds = activities.stream().map(ExamActivity::getId).collect(Collectors.toList());
            this.lambdaUpdate().set(ExamActivityStudentLink::getTrainState,2)
                    .in(ExamActivityStudentLink::getTrainState, Arrays.asList(0,1))
                    .eq(ExamActivityStudentLink::getIsDel,DelFlag.SAVE.getValue())
                    .in(ExamActivityStudentLink::getActivityId,activityIds)
                    .isNotNull(ExamActivityStudentLink::getSignTime)
                    .update();
        }
    }

    @Override
    public void completeAppeal(Map<Long, String> map) {
        if (!CollectionUtils.isEmpty(map)) {
            ExamActivityStudentLink update = new ExamActivityStudentLink();
            update.setIsAppeal(true);
            for (Map.Entry<Long,String> entry: map.entrySet()) {
                update.setAppealReason(entry.getValue());
                examActivityStudentLinkMapper.update(update, new LambdaQueryWrapper<ExamActivityStudentLink>()
                        .eq(ExamActivityStudentLink::getId,entry.getKey())
                        .eq(ExamActivityStudentLink::getIsDel, 0));
            }
        }
    }
}
