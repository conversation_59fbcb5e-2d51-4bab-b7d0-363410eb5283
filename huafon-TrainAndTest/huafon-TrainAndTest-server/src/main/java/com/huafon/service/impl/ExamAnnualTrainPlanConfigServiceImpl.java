package com.huafon.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huafon.common.config.TenantContext;
import com.huafon.dao.mapper.ExamAnnualTrainPlanConfigMapper;
import com.huafon.models.entity.annual.ExamAnnualTrainPlanConfig;
import com.huafon.models.vo.req.annual.ExamAnnualTrainPlanConfigReqVO;
import com.huafon.models.vo.resp.annual.ExamAnnualTrainPlanConfigRespVO;
import com.huafon.service.ExamAnnualTrainPlanConfigService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 培训计划配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class ExamAnnualTrainPlanConfigServiceImpl extends ServiceImpl<ExamAnnualTrainPlanConfigMapper,ExamAnnualTrainPlanConfig> implements ExamAnnualTrainPlanConfigService {

    @Override
    public void set(ExamAnnualTrainPlanConfigReqVO reqVO) {
        ExamAnnualTrainPlanConfig entity = ExamAnnualTrainPlanConfigReqVO.convert(reqVO);
        saveOrUpdate(entity);
    }

    @Override
    public ExamAnnualTrainPlanConfigRespVO get() {
        ExamAnnualTrainPlanConfig entity = getOne(new LambdaQueryWrapper<ExamAnnualTrainPlanConfig>().eq(ExamAnnualTrainPlanConfig::getTenantId, TenantContext.getOne()));
        return ExamAnnualTrainPlanConfigRespVO.convert(entity);
    }

}
