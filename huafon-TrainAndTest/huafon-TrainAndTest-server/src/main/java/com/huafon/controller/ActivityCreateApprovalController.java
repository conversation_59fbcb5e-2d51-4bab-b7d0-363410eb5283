package com.huafon.controller;

import com.huafon.admin.api.annotation.OperationLog;
import com.huafon.admin.api.enums.OperationLogType;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.models.vo.req.activityCreateApproval.ActivityCreateApprovalPageReqVO;
import com.huafon.models.vo.req.activityCreateApproval.ActivityCreateApprovalReqVO;
import com.huafon.models.vo.req.process.ProcessFinishReqVO;
import com.huafon.models.vo.resp.activityCreateApproval.ActivityCreateApprovalPageRespVO;
import com.huafon.models.vo.resp.activityCreateApproval.ActivityCreateApprovalRespVO;
import com.huafon.models.vo.resp.process.ProcessStatusNumRespVO;
import com.huafon.service.ExamActivityCreateApprovalService;
import com.huafon.support.core.pojo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 活动创建审批
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11
 */
@Api(tags = "活动创建审批")
@RestController
@RequestMapping("/activity/create/approval")
public class ActivityCreateApprovalController {

    private final ExamActivityCreateApprovalService approvalService;

    public ActivityCreateApprovalController(ExamActivityCreateApprovalService approvalService) {
        this.approvalService = approvalService;
    }

    @PostMapping("/complete")
    @ApiOperation(value = "审批")
    @OperationLog(type = OperationLogType.MODITY, notes = "活动创建审批(审批)")
    public void complete(@RequestBody ActivityCreateApprovalReqVO reqVO) {
        approvalService.complete(reqVO);
    }

    @PostMapping("/finish")
    @ApiOperation(value = "结束流程")
    @OperationLog(type = OperationLogType.MODITY, notes = "活动创建审批(结束流程)")
    public void finish(@Valid @RequestBody ProcessFinishReqVO reqVO) {
        approvalService.finish(reqVO);
    }

    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除(流程中)")
    @OperationLog(type = OperationLogType.DELETE, notes = "活动创建审批(删除流程中)")
    public void deleteById(@PathVariable Long id) {
        approvalService.delete(id);
    }

    @DeleteMapping("/batchDelete")
    @ApiOperation(value = "批量删除(已取消)")
    @OperationLog(type = OperationLogType.DELETE, notes = "活动创建审批(批量删除已取消)")
    public void batchDelete(@RequestBody List<Long> ids) {
        approvalService.batchDelete(ids);
    }

    @GetMapping("/statusNum")
    @ApiOperation(value = "流程数量统计")
    public R<ProcessStatusNumRespVO> statusNum() {
        return R.ok(approvalService.statusNum());
    }

    @PostMapping("/page")
    @ApiOperation(value = "分页")
    public R<CommonPage<ActivityCreateApprovalPageRespVO>> page(@RequestBody ActivityCreateApprovalPageReqVO reqVO) {
        return R.ok(approvalService.page(reqVO));
    }

    @GetMapping("/info/{id}")
    @ApiOperation(value = "详情")
    public R<ActivityCreateApprovalRespVO> info(@PathVariable Long id) {
        return R.ok(approvalService.info(id));
    }

    @PostMapping("/modify")
    @ApiOperation(value = "编辑")
    @OperationLog(type = OperationLogType.MODITY, notes = "活动创建审批(编辑)")
    public void modify(@RequestBody ActivityCreateApprovalReqVO reqVO) {
        approvalService.modify(reqVO);
    }

}
