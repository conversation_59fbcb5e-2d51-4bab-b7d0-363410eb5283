package com.huafon.controller;

import com.huafon.service.PdfService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description: pdf导出
 * @date 2023-06-26 16:46:09
 */
@RestController
@RequestMapping("/pdf")
@Api(tags = "pdf处理导出")
@Slf4j
public class PdfController {

    private final PdfService pdfService;

    @Autowired
    public PdfController(PdfService pdfService) {
        this.pdfService = pdfService;
    }

    @PostMapping("/testResult/download")
    @ApiOperation(value = "考试试卷结果下载")
    public void testResultDownload(@RequestBody List<Long> historyIds, HttpServletResponse response) throws Exception {
        pdfService.internalTestResultDownload(historyIds, response);
    }

    @PostMapping("/paper/download")
    @ApiOperation(value = "试卷导出")
    public void paperDownload(@RequestBody List<Long> paperIds, HttpServletResponse response) throws Exception {
        pdfService.paperDownload(paperIds, response);
    }

    @PostMapping("/threeLevelCard/download")
    @ApiOperation(value = "三级培训卡导出")
    public void threeLevelCardDownload(@RequestBody List<Long> userIds, HttpServletResponse response) throws Exception {
        pdfService.threeLevelCardDownload(userIds, response);
    }

    @PostMapping("/external/testResult/download")
    @ApiOperation(value = "外部考试试卷结果下载")
    public void externalTestResultDownload(@RequestBody List<Long> historyIds, HttpServletResponse response) throws Exception {
        pdfService.externalTestResultDownload(historyIds, response);
    }

}