package com.huafon.controller;

import com.huafon.admin.api.annotation.OperationLog;
import com.huafon.admin.api.enums.OperationLogType;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.models.dto.ExamActivitySequenceDTO;
import com.huafon.models.vo.common.PersonVO;
import com.huafon.models.vo.req.activity.ActivityProcessConfigReqVO;
import com.huafon.models.vo.req.activity.ActivityStudentListReqVO;
import com.huafon.models.vo.req.activity.ActivityStudentPageReqVO;
import com.huafon.models.vo.req.activity.ActivityTestRecordListReqVO;
import com.huafon.models.vo.req.activity.ExamActivityAddReqVO;
import com.huafon.models.vo.req.activity.ExamActivityDateReqVO;
import com.huafon.models.vo.req.activity.ExamActivityEditReqVO;
import com.huafon.models.vo.req.activity.ExamActivityFinishReqVO;
import com.huafon.models.vo.req.activity.ExamActivityListReqVO;
import com.huafon.models.vo.req.activity.ExamActivityStatusCountReqVO;
import com.huafon.models.vo.req.activity.ExamTemporaryActivityReqVO;
import com.huafon.models.vo.req.evaluation.ExamEvaluationListLinkReqVO;
import com.huafon.models.vo.resp.activity.ActivityProcessConfigRespVO;
import com.huafon.models.vo.resp.activity.ActivityStudentListRespVO;
import com.huafon.models.vo.resp.activity.ActivityTestRecordListRespVO;
import com.huafon.models.vo.resp.activity.ExamActivityInfoRespVO;
import com.huafon.models.vo.resp.activity.ExamActivityListRespVO;
import com.huafon.models.vo.resp.activity.ExamActivityStatusCountRespVO;
import com.huafon.models.vo.resp.activity.ExamTemporaryActivityRespVO;
import com.huafon.models.vo.resp.activity.PaperAnsweringRespVO;
import com.huafon.models.vo.resp.evaluation.ExamEvaluationListForActivityRespVO;
import com.huafon.models.vo.resp.plan.ExamPlanHomeRespVO;
import com.huafon.models.vo.resp.subject.ExamSubjectNodeCountVO;
import com.huafon.service.ExamActivityProcessConfigService;
import com.huafon.service.ExamActivityService;
import com.huafon.service.ExamEvaluationService;
import com.huafon.support.core.pojo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description: 活动管理
 * @date 2023-01-09 8:03:00
 */
@Api(tags = "活动管理")
@RestController
@RequestMapping("/activity")
public class ActivityController {

    private final ExamActivityService examActivityService;
    private final ExamEvaluationService examEvaluationService;
    private final ExamActivityProcessConfigService examActivityProcessConfigService;

    @Autowired
    public ActivityController(ExamActivityService examActivityService,
                              @Lazy ExamEvaluationService examEvaluationService,
                              @Lazy ExamActivityProcessConfigService examActivityProcessConfigService) {
        this.examActivityService = examActivityService;
        this.examEvaluationService = examEvaluationService;
        this.examActivityProcessConfigService = examActivityProcessConfigService;
    }

    @PostMapping("/add")
    @ApiOperation(value = "添加活动")
    @OperationLog(type = OperationLogType.ADD, notes = "添加活动")
    public R addActivity(@RequestBody @Valid ExamActivityAddReqVO req) {
        examActivityService.addActivity(req);
        return R.ok();
    }

    @PostMapping("/edit")
    @ApiOperation(value = "编辑活动")
    @OperationLog(type = OperationLogType.MODITY, notes = "编辑活动")
    public R editActivity(@RequestBody @Valid ExamActivityEditReqVO req) {
        examActivityService.editActivity(req);
        return R.ok();
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除活动")
    @OperationLog(type = OperationLogType.DELETE, notes = "删除活动")
    public R delete(@PathVariable Long id) {
        examActivityService.deleteActivity(id);
        return R.ok();
    }

    @GetMapping("/info/{id}")
    @ApiOperation(value = "得到活动信息")
    public R<ExamActivityInfoRespVO> readActivityInfo(@PathVariable Long id, @RequestParam(value = "orderAsc", required = false) Boolean orderAsc) {
        return R.ok(examActivityService.readActivityInfo(id, orderAsc));
    }

    @PostMapping("/list")
    @ApiOperation(value = "返回活动列表")
    public R<CommonPage<ExamActivityListRespVO>> queryActivityList(@RequestBody ExamActivityListReqVO req) {
        return R.ok(examActivityService.queryActivityList(req));
    }

    @PostMapping("/getNodeNums/{type}")
    @ApiOperation(value = "获取对应节点的数量")
    public R<List<ExamSubjectNodeCountVO>> getNodeNums(@RequestBody List<Long> nodeIds, @PathVariable Integer type) {
        return R.ok(examActivityService.getNodeNums(nodeIds, type));
    }

    @PostMapping("/getStatusNums")
    @ApiOperation(value = "获取活动对应状态的数量")
    public R<ExamActivityStatusCountRespVO> getStatusNums(@RequestBody ExamActivityStatusCountReqVO req) {
        return R.ok(examActivityService.getStatusNums(req));
    }

    @PostMapping("/related/evaluation")
    @ApiOperation(value = "获取活动关联的评价列表")
    public R<CommonPage<ExamEvaluationListForActivityRespVO>> getRelatedEvaluation(@RequestBody ExamEvaluationListLinkReqVO req) {
        return R.ok(examEvaluationService.listForActivity(req));
    }

    @PostMapping("/test/record")
    @ApiOperation(value = "获取活动的考试记录")
    public R<CommonPage<ActivityTestRecordListRespVO>> getStudentTestRecord(@RequestBody ActivityTestRecordListReqVO req) {
        return R.ok(examActivityService.getActivityTestRecord(req));
    }

    @PostMapping("/finish/{id}")
    @ApiOperation(value = "完成培训")
    @OperationLog(type = OperationLogType.MODITY, notes = "完成培训")
    public R finishActivity(@PathVariable Long id, @Valid @RequestBody ExamActivityFinishReqVO examActivityFinishReqVO) {
        examActivityService.finishActivity(id, examActivityFinishReqVO);
        return R.ok();
    }

    @GetMapping("/belong/tenant/{id}")
    @ApiOperation(value = "返回活动属于哪个租户")
    public R<ExamPlanHomeRespVO> getActivityBelongTenant(@PathVariable Long id) {
        return R.ok(examActivityService.getActivityBelongTenant(id));
    }

    @GetMapping("/getActivitySequence")
    @ApiOperation(value = "通过序列号返回三级培训入司培训的活动名称")
    public R<ExamActivitySequenceDTO> getActivitySequence(@RequestParam("threeLevelType") Integer threeLevelType, @RequestParam("threeLevelSubject") String threeLevelSubject) {
        return R.ok(examActivityService.getActivitySequence(threeLevelType, threeLevelSubject));
    }

    @PostMapping("/getDate")
    @ApiOperation(value = "返回培训完成日期")
    public R<Date> getDate(@Valid @RequestBody ExamActivityDateReqVO reqVO) {
        return R.ok(examActivityService.getDate(reqVO));
    }

    @GetMapping("/checkBeforeFinish/{activityId}")
    @ApiOperation(value = "活动完成培训前的判断（返回未完成培训的人员列表）")
    public R<List<PersonVO>> checkBeforeFinish(@PathVariable Long activityId) {
        return R.ok(examActivityService.checkBeforeFinish(activityId));
    }

    @PostMapping("/addTemporary")
    @ApiOperation(value = "添加临时活动")
    @OperationLog(type = OperationLogType.ADD, notes = "添加临时活动")
    public void addTemporaryActivity(@RequestBody @Valid ExamTemporaryActivityReqVO reqVO) {
        examActivityService.addTemporaryActivity(reqVO);
    }

    @PostMapping("/editTemporary")
    @ApiOperation(value = "编辑临时活动")
    @OperationLog(type = OperationLogType.MODITY, notes = "编辑临时活动")
    public void editTemporaryActivity(@RequestBody @Valid ExamTemporaryActivityReqVO reqVO) {
        examActivityService.editTemporaryActivity(reqVO);
    }

    @GetMapping("/temporaryInfo/{id}")
    @ApiOperation(value = "得到临时活动信息")
    public R<ExamTemporaryActivityRespVO> readTemporaryActivityInfo(@PathVariable Long id, @RequestParam(value = "orderAsc", required = false) Boolean orderAsc) {
        return R.ok(examActivityService.readTemporaryActivityInfo(id, orderAsc));
    }

    @PostMapping("/related/student/list")
    @ApiOperation(value = "查询关联学员列表")
    public R<List<ActivityStudentListRespVO>> relatedStudentList(@Valid @RequestBody ActivityStudentListReqVO reqVO) {
        return R.ok(examActivityService.relatedStudentList(reqVO));
    }

    @PostMapping("/related/student/page")
    @ApiOperation(value = "查询关联学员分页")
    public R<CommonPage<ActivityStudentListRespVO>> relatedStudentPage(@Valid @RequestBody ActivityStudentPageReqVO reqVO) {
        return R.ok(examActivityService.relatedStudentPage(reqVO));
    }

    @PostMapping("/setProcessConfig")
    @ApiOperation(value = "设置活动配置")
    @OperationLog(type = OperationLogType.MODITY, notes = "设置活动配置")
    public void setProcessConfig(@RequestBody ActivityProcessConfigReqVO reqVO) {
        examActivityProcessConfigService.set(reqVO);
    }

    @GetMapping("/getProcessConfig")
    @ApiOperation(value = "获取活动配置")
    public R<ActivityProcessConfigRespVO> getProcessConfig() {
        return R.ok(examActivityProcessConfigService.get());
    }

    @GetMapping("/scoring/finish/{id}")
    @ApiOperation(value = "阅卷完成校验")
    public R<Boolean> scoringFinish(@PathVariable Long id) {
        return R.ok(examActivityService.scoringFinish(id));
    }

    @GetMapping("/paperAnsweringSituation/{id}")
    @ApiOperation(value = "试卷答题情况")
    public R<PaperAnsweringRespVO> paperAnsweringSituation(@PathVariable Long id) {
        return R.ok(examActivityService.paperAnsweringSituation(id));
    }

}