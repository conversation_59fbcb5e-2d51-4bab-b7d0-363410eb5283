package com.huafon.controller;

import com.huafon.api.service.dto.StatisticsV2RespDTO;
import com.huafon.models.vo.req.statistics.ActivityStatisticsReqVO;
import com.huafon.models.vo.req.statistics.ClassHourStatisticsReqVO;
import com.huafon.models.vo.req.statistics.ConsoleStatisticsReqVO;
import com.huafon.models.vo.req.statistics.ContractStatisticsReqVO;
import com.huafon.models.vo.req.statistics.DailyTrainingPassRateStatisticsReqVO;
import com.huafon.models.vo.req.statistics.DeptPassRateStatisticsReqVO;
import com.huafon.models.vo.req.statistics.PlanStatisticsReqVO;
import com.huafon.models.vo.resp.statistics.ActivityStatisticsRespVO;
import com.huafon.models.vo.resp.statistics.ClassHourStatisticsRespVO;
import com.huafon.models.vo.resp.statistics.ContractorStatisticsRespVO;
import com.huafon.models.vo.resp.statistics.DailyTrainingPassRateStatisticsRespVO;
import com.huafon.models.vo.resp.statistics.DeptPassRateStatisticsRespVO;
import com.huafon.models.vo.resp.statistics.PlanStatisticsRespVO;
import com.huafon.service.StatisticsService;
import com.huafon.support.core.pojo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 数据统计
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Api(tags = "数据统计")
@RestController
@RequestMapping("/statistics")
public class StatisticsController {

    private final StatisticsService statisticsService;

    public StatisticsController(StatisticsService statisticsService) {
        this.statisticsService = statisticsService;
    }

    @PostMapping("/activity/analysis")
    @ApiOperation(value = "培训活动分析")
    public R<List<ActivityStatisticsRespVO>> activityAnalysis(@Valid @RequestBody ActivityStatisticsReqVO reqVO) {
        return R.ok(statisticsService.activityAnalysis(reqVO));
    }

    @PostMapping("/plan/analysis")
    @ApiOperation(value = "培训计划分析")
    public R<List<PlanStatisticsRespVO>> planAnalysis(@Valid @RequestBody PlanStatisticsReqVO reqVO) throws Exception{
        return R.ok(statisticsService.planAnalysis(reqVO));
    }

    @PostMapping("/contractor/analysis")
    @ApiOperation(value = "承包商培训统计")
    public R<List<ContractorStatisticsRespVO>> contractorStatus(@Valid @RequestBody ContractStatisticsReqVO reqVO) {
        return R.ok(statisticsService.contractorAnalysis(reqVO));
    }

    @PostMapping("/classHour")
    @ApiOperation(value = "课时完成率")
    public R<ClassHourStatisticsRespVO> classHour(@Valid @RequestBody ClassHourStatisticsReqVO reqVO) {
        return R.ok(statisticsService.classHour(reqVO));
    }

    @GetMapping("/cacheParam/{type}")
    @ApiOperation(value = "获取缓存参数（type 1:课时完成率 2:部门合格率 3:训练合格率）")
    public R<String> cacheParam(@PathVariable Integer type) {
        return R.ok(statisticsService.cacheParam(type));
    }

    @PostMapping("/deptPassRate")
    @ApiOperation(value = "部门合格率统计")
    public R<List<DeptPassRateStatisticsRespVO>> deptPassRate(@Valid @RequestBody DeptPassRateStatisticsReqVO reqVO) {
        return R.ok(statisticsService.deptPassRate(reqVO));
    }

    @PostMapping("/dailyTrainingPassRate")
    @ApiOperation(value = "日常训练合格率统计")
    public R<List<DailyTrainingPassRateStatisticsRespVO>> dailyTrainingPassRate(@Valid @RequestBody DailyTrainingPassRateStatisticsReqVO reqVO) {
        return R.ok(statisticsService.dailyTrainingPassRate(reqVO));
    }

    @PostMapping("/console")
    @ApiOperation(value = "首页数据统计V2")
    public R<List<StatisticsV2RespDTO>> console(@RequestBody @Valid ConsoleStatisticsReqVO reqVO) {
        return R.ok(statisticsService.console(reqVO));
    }

}
