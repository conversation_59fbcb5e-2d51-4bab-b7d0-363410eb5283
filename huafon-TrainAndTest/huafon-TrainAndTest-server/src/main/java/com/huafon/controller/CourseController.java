package com.huafon.controller;

import com.huafon.admin.api.annotation.OperationLog;
import com.huafon.admin.api.enums.OperationLogType;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.models.vo.common.WorkflowFinishReqVO;
import com.huafon.models.vo.req.course.ExamCourseAddReqVO;
import com.huafon.models.vo.req.course.ExamCourseCancelReqVO;
import com.huafon.models.vo.req.course.ExamCourseEditReqVO;
import com.huafon.models.vo.req.course.ExamCourseEnableReqVO;
import com.huafon.models.vo.req.course.ExamCourseListForActivityReqVO;
import com.huafon.models.vo.req.course.ExamCourseListReqVO;
import com.huafon.models.vo.req.course.ExamCourseNodeNumsReqVO;
import com.huafon.models.vo.req.course.ExamCourseProcessPageReqVO;
import com.huafon.models.vo.req.course.ExamCourseProcessReqVO;
import com.huafon.models.vo.resp.course.ExamCourseInfoRespVO;
import com.huafon.models.vo.resp.course.ExamCourseListForActivityRespVO;
import com.huafon.models.vo.resp.course.ExamCourseListRespVO;
import com.huafon.models.vo.resp.course.ExamCourseProcessCountVO;
import com.huafon.models.vo.resp.course.ExamCourseProcessPageRespVO;
import com.huafon.models.vo.resp.course.ExamCourseProcessRespVO;
import com.huafon.models.vo.resp.course.ExamCourseStatusCountVO;
import com.huafon.models.vo.resp.subject.ExamSubjectNodeCountVO;
import com.huafon.service.ExamCourseService;
import com.huafon.service.ExamPeriodService;
import com.huafon.support.core.pojo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description: 课程管理
 * @date 2022-11-05 20:52:28
 */
@Api(tags = "课程管理")
@RestController
@RequestMapping("/course")
public class CourseController {

    private final ExamCourseService examCourseService;

    private final ExamPeriodService examPeriodService;

    @Autowired
    public CourseController(ExamCourseService examCourseService,
                            ExamPeriodService examPeriodService) {
        this.examCourseService = examCourseService;
        this.examPeriodService = examPeriodService;
    }

    @PostMapping("/add")
    @ApiOperation(value = "添加课程")
    @OperationLog(type = OperationLogType.ADD, notes = "添加课程")
    public R addCourse(@RequestBody @Valid ExamCourseAddReqVO examCourseAddReqVO) {
        examCourseService.addCourse(examCourseAddReqVO);
        return R.ok();
    }

    @PostMapping("/edit")
    @ApiOperation(value = "编辑课程")
    @OperationLog(type = OperationLogType.MODITY, notes = "编辑课程")
    public R edit(@RequestBody @Valid ExamCourseEditReqVO req) {
        examCourseService.editCourse(req);
        return R.ok();
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除课程")
    @OperationLog(type = OperationLogType.DELETE, notes = "删除课程")
    public R delete(@PathVariable Integer id) {
        examCourseService.deleteCourse(id);
        return R.ok();
    }

    @DeleteMapping("/period/{periodId}")
    @ApiOperation(value = "删除课程中的课时")
    @OperationLog(type = OperationLogType.DELETE, notes = "删除课程中的课时")
    public R deletePeriod(@PathVariable Long periodId) {
        examPeriodService.deleteExamPeriod(periodId);
        return R.ok();
    }

    @PostMapping("/cancel")
    @ApiOperation(value = "作废课程")
    @OperationLog(type = OperationLogType.MODITY, notes = "作废课程")
    public R cancel(@RequestBody @Valid ExamCourseCancelReqVO req) {
        examCourseService.cancelCourse(req);
        return R.ok();
    }

    @PostMapping("/enable")
    @ApiOperation(value = "启用课程")
    @OperationLog(type = OperationLogType.MODITY, notes = "启用课程")
    public R enable(@RequestBody @Valid ExamCourseEnableReqVO req) {
        examCourseService.enableCourse(req);
        return R.ok();
    }

    @GetMapping("/info/{id}")
    @ApiOperation(value = "得到课程信息")
    public R<ExamCourseInfoRespVO> getCourseInfo(@PathVariable Long id) {
        return R.ok(examCourseService.readCourseInfo(id));
    }

    @PostMapping("/list")
    @ApiOperation(value = "返回课程列表")
    public R<CommonPage<ExamCourseListRespVO>> queryCourseList(@RequestBody ExamCourseListReqVO req) {
        return R.ok(examCourseService.queryCourseList(req));
    }

    @PostMapping("/getNodeNums")
    @ApiOperation(value = "获取对应节点的数量")
    public R<List<ExamSubjectNodeCountVO>> getNodeNums(@RequestBody ExamCourseNodeNumsReqVO reqVO) {
        return R.ok(examCourseService.getNodeNums(reqVO));
    }

    @GetMapping("/getStatusNums")
    @ApiOperation(value = "获取课程对应状态的数量")
    public R<ExamCourseStatusCountVO> getStatusNums(@RequestParam(required = false) Long subjectId, @RequestParam(required = false) Integer type) {
        return R.ok(examCourseService.getStatusNums(subjectId,type));
    }

    @PostMapping("/list/by/subject")
    @ApiOperation(value = "根据培训类别获取课程列表")
    public R<CommonPage<ExamCourseListForActivityRespVO>> queryCourseListForActivity(@RequestBody ExamCourseListForActivityReqVO req) {
        return R.ok(examCourseService.queryCourseListForActivity(req));
    }

    @PostMapping("/process")
    @ApiOperation(value = "审批流程")
    @OperationLog(type = OperationLogType.MODITY, notes = "审批流程")
    public R<Long> process(@RequestBody ExamCourseProcessReqVO reqVO) {
        return R.ok(examCourseService.process(reqVO));
    }

    @GetMapping("/process/info/{id}")
    @ApiOperation(value = "审批流程信息")
    public R<ExamCourseProcessRespVO> processInfo(@PathVariable Long id) {
        return R.ok(examCourseService.processInfo(id));
    }

    @PostMapping("/process/finish")
    @ApiOperation(value = "结束流程")
    @OperationLog(type = OperationLogType.MODITY, notes = "结束流程")
    public void processFinish(@RequestBody WorkflowFinishReqVO reqVO) {
        examCourseService.processFinish(reqVO);
    }

    @PostMapping("/process/page")
    @ApiOperation(value = "流程分页")
    public R<CommonPage<ExamCourseProcessPageRespVO>> processPage(@RequestBody ExamCourseProcessPageReqVO reqVO) {
        return R.ok(examCourseService.processPage(reqVO));
    }

    @GetMapping("/process/count")
    @ApiOperation(value = "流程数量统计")
    public R<ExamCourseProcessCountVO> processCount() {
        return R.ok(examCourseService.processCount());
    }

    @DeleteMapping("/process/batchDelete")
    @ApiOperation(value = "批量删除(已取消)")
    @OperationLog(type = OperationLogType.DELETE, notes = "批量删除(已取消)")
    public void processDelete(@RequestBody List<Long> ids) {
        examCourseService.processBatchDelete(ids);
    }

    @DeleteMapping("/process/delete/{id}")
    @ApiOperation(value = "删除(流程中)")
    @OperationLog(type = OperationLogType.DELETE, notes = "删除(流程中)")
    public void processDelete(@PathVariable Long id) {
        examCourseService.processDelete(id);
    }

}