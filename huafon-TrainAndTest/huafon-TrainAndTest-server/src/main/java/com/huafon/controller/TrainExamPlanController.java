package com.huafon.controller;

import com.huafon.admin.api.annotation.OperationLog;
import com.huafon.admin.api.enums.OperationLogType;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.models.entity.ExamMaterialRecord;
import com.huafon.models.entity.ExamTrainHistory;
import com.huafon.models.vo.req.plan.ExamHisAddReqVO;
import com.huafon.models.vo.req.plan.ExamHisRepeatReqVO;
import com.huafon.models.vo.req.plan.ExamPlanAddReqVO;
import com.huafon.models.vo.req.plan.ExamPlanEditReqVO;
import com.huafon.models.vo.req.plan.ExamPlanHisListReqVO;
import com.huafon.models.vo.req.plan.ExamPlanListReqVO;
import com.huafon.models.vo.req.plan.ExamTrainResultCorrectReqVO;
import com.huafon.models.vo.req.plan.StartTrainReqVO;
import com.huafon.models.vo.req.plan.UpdateMaterialReqVO;
import com.huafon.models.vo.req.plan.ValidTrainReqVO;
import com.huafon.models.vo.resp.plan.ExamPlanHisListRespVO;
import com.huafon.models.vo.resp.plan.ExamPlanHomeRespVO;
import com.huafon.models.vo.resp.plan.ExamPlanInfoRespVO;
import com.huafon.models.vo.resp.plan.ExamPlanListRespVO;
import com.huafon.models.vo.resp.plan.ExamResultRespVO;
import com.huafon.models.vo.resp.plan.ExamTrainResultCorrectRespVO;
import com.huafon.models.vo.resp.plan.ExamTrainResultRespVO;
import com.huafon.models.vo.resp.plan.TrainExamRecordRespVO;
import com.huafon.models.vo.resp.plan.UserExamHisRespVO;
import com.huafon.service.ExamTrainHistoryService;
import com.huafon.service.ExamTrainPlanService;
import com.huafon.support.core.pojo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description: 培训考试计划
 * @date 2022-05-13 15:11:17
 */
@Api(tags = "培训考试计划")
@RestController
@RequestMapping("/plan")
public class TrainExamPlanController {

    private final ExamTrainPlanService examTrainPlanService;
    private final ExamTrainHistoryService examTrainHistoryService;

    @Autowired
    public TrainExamPlanController(ExamTrainPlanService examTrainPlanService,
                                   ExamTrainHistoryService examTrainHistoryService) {
        this.examTrainPlanService = examTrainPlanService;
        this.examTrainHistoryService = examTrainHistoryService;
    }

    @PostMapping("/add")
    @ApiOperation(value = "添加培训考试计划")
    @OperationLog(type = OperationLogType.ADD, notes = "添加培训考试计划")
    public R add(@RequestBody @Valid ExamPlanAddReqVO examPlanAddReqVO) {
        examTrainPlanService.addTrainPlan(examPlanAddReqVO);
        return R.ok();
    }

    @PutMapping("/edit")
    @ApiOperation(value = "编辑培训考试计划")
    @OperationLog(type = OperationLogType.MODITY, notes = "编辑培训考试计划")
    public R edit(@RequestBody @Valid ExamPlanEditReqVO examPlanEditReqVO) {
        examTrainPlanService.editTrainPlan(examPlanEditReqVO);
        return R.ok();
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除培训考试计划")
    @OperationLog(type = OperationLogType.DELETE, notes = "删除培训考试计划")
    public R delete(@PathVariable Long id) {
        examTrainPlanService.deleteTrainPlan(id);
        return R.ok();
    }

    @GetMapping("/info/{id}")
    @ApiOperation(value = "得到培训考试计划详情信息")
    public R<ExamPlanInfoRespVO> getExamPlanInfo(@PathVariable Long id) {
        ExamPlanInfoRespVO examPlanInfoRespVO = examTrainPlanService.readExamPlanInfo(id);
        return R.ok(examPlanInfoRespVO);
    }

    @GetMapping("/valid/{id}")
    @ApiOperation(value = "判断这个培训计划是不是有效可用了")
    public R getExamPlanValid(@PathVariable Long id) {
        examTrainPlanService.getExamPlanValid(id);
        return R.ok();
    }

    @PostMapping("/list")
    @ApiOperation(value = "培训考试计划列表")
    public R<CommonPage<ExamPlanListRespVO>> queryPlanList(@RequestBody ExamPlanListReqVO examPlanListReqVO) {
        return R.ok(examTrainPlanService.queryPlanList(examPlanListReqVO));
    }

    @GetMapping("/signature/tip/{planId}")
    @ApiOperation(value = "通过培训计划id获取培训签名提示")
    public R<String> getSignatureTip(@PathVariable Long planId) {
        return R.ok(examTrainPlanService.readSignatureTip(planId));
    }


    //----- 培训历史 -----//

    @PostMapping("/his/list")
    @ApiOperation(value = "返回培训历史")
    public R<CommonPage<ExamPlanHisListRespVO>> queryHisList(@RequestBody ExamPlanHisListReqVO examPlanHisListReqVO) {
        return R.ok(examTrainPlanService.queryHisList(ExamPlanHisListReqVO.convertToExamPlanHisListDTO(examPlanHisListReqVO)));
    }

    @PostMapping("/train/start")
    @ApiOperation(value = "开始培训")
    public R<ExamTrainHistory> startTrain(@Valid @RequestBody StartTrainReqVO startTrainReq) {
        ExamTrainHistory examTrainHistory = examTrainHistoryService.startTrain(startTrainReq);
        return R.ok(examTrainHistory);
    }

    @PostMapping("/train/valid")
    @ApiOperation(value = "返回是否有有效的培训计划记录")
    public R<ExamTrainHistory> haveValidTrainRecord(@Valid @RequestBody ValidTrainReqVO validTrainReq) {
        ExamTrainHistory examTrainHistory = examTrainHistoryService.haveValidTrainRecord(validTrainReq);
        return R.ok(examTrainHistory);
    }

    @PostMapping("/train/exam/record/{hisId}")
    @ApiOperation(value = "返回是否有有效的考试记录")
    public R<TrainExamRecordRespVO> haveValidTrainExamRecord(@PathVariable Long hisId) {
        TrainExamRecordRespVO trainExamRecordRespVO = examTrainHistoryService.haveValidTrainExamRecord(hisId);
        return R.ok(trainExamRecordRespVO);
    }

    @PostMapping("/train/material/record")
    @ApiOperation(value = "保存当前资料看到什么时候了")
    public R<ExamMaterialRecord> saveTrainMaterialRecord(@Valid @RequestBody UpdateMaterialReqVO updateMaterialReq) {
        ExamMaterialRecord examMaterialRecord = examTrainHistoryService.saveTrainMaterialRecord(updateMaterialReq);
        return R.ok(examMaterialRecord);
    }

    @PostMapping("/train/material/record/{hisId}")
    @ApiOperation(value = "通过培训历史id获取当前的完成的资料记录")
    public R<ExamMaterialRecord> getTrainMaterialRecord(@PathVariable Long hisId) {
        ExamMaterialRecord examMaterialRecord = examTrainHistoryService.getTrainMaterialRecord(hisId);
        return R.ok(examMaterialRecord);
    }

    @PostMapping("/his/repeat/submit")
    @ApiOperation(value = "重复提交考试记录")
    public R<ExamResultRespVO> repeatSubmitHis(@Valid @RequestBody ExamHisRepeatReqVO examHisRepeatReqVO) {
        ExamResultRespVO examResultRespVO = examTrainHistoryService.repeatSubmitHis(examHisRepeatReqVO);
        return R.ok(examResultRespVO);
    }

    @GetMapping("/belong/tenant/{id}")
    @ApiOperation(value = "返回计划属于哪个租户和计划")
    public R<ExamPlanHomeRespVO> getPlanBelongTenant(@PathVariable Long id) {
        return R.ok(examTrainHistoryService.readPlanBelongTenant(id));
    }

    @GetMapping("/his/{idCard}")
    @ApiOperation(value = "通过身份证获取培训历史记录(地图)")
    public R<List<UserExamHisRespVO>> getHisPlanByIdCard(@PathVariable String idCard) {
        return R.ok(examTrainHistoryService.readHisPlanByIdCard(idCard));
    }

    @GetMapping("/his2/{idCard}")
    @ApiOperation(value = "通过身份证获取培训历史记录(承包商) 此接口是数据返回是关联租户的")
    public R<List<UserExamHisRespVO>> getHis2PlanByIdCard2(@PathVariable String idCard, @RequestParam(value = "type",required = false) Integer type) {
        return R.ok(examTrainHistoryService.readHis2PlanByIdCard(idCard,type));
    }

    @GetMapping("/hisByUserId/{userId}")
    @ApiOperation(value = "通过用户id获取培训历史记录")
    public R<List<UserExamHisRespVO>> getHisPlanByUserId(@PathVariable Integer userId) {
        return R.ok(examTrainHistoryService.readHisPlanByUserId(userId));
    }

    @GetMapping("/trainResult/{id}")
    @ApiOperation(value = "通过id获取考试试卷结果")
    public R<ExamTrainResultRespVO> getTrainResult(@PathVariable Integer id) {
        return R.ok(examTrainHistoryService.getTrainResult(id));
    }

    @PostMapping("/trainResult/correct/add")
    @ApiOperation(value = "试卷结果订正新增")
    public void testResultCorrectAdd(@Valid @RequestBody ExamTrainResultCorrectReqVO reqVO) {
        examTrainHistoryService.trainResultCorrectAdd(reqVO);
    }

    @GetMapping("/trainResult/correct/info/{hisId}")
    @ApiOperation(value = "试卷结果订正详情")
    public R<ExamTrainResultCorrectRespVO> trainResultCorrectInfo(@PathVariable Long hisId) {
        return R.ok(examTrainHistoryService.trainResultCorrectInfo(hisId));
    }
}