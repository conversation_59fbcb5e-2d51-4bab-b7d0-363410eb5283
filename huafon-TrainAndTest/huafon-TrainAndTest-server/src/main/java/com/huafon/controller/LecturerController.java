package com.huafon.controller;

import com.huafon.admin.api.annotation.OperationLog;
import com.huafon.admin.api.enums.OperationLogType;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.models.vo.req.lecturer.ExamLecturerAddReqVO;
import com.huafon.models.vo.req.lecturer.ExamLecturerEditReqVO;
import com.huafon.models.vo.req.lecturer.ExamLecturerListReqVO;
import com.huafon.models.vo.req.lecturer.LecturerRelatedActivityListReqVO;
import com.huafon.models.vo.resp.lecturer.ExamLecturerInfoRespVO;
import com.huafon.models.vo.resp.lecturer.ExamLecturerListRespVO;
import com.huafon.models.vo.resp.lecturer.LecturerRelatedActivityListRespVO;
import com.huafon.service.ExamLecturerService;
import com.huafon.support.core.pojo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description: 讲师管理
 * @date 2022-11-04 17:07:30
 */
@Api(tags = "讲师管理")
@RestController
@RequestMapping("/lecturer")
public class LecturerController {

    private final ExamLecturerService examLecturerService;

    @Autowired
    public LecturerController(ExamLecturerService examLecturerService) {
        this.examLecturerService = examLecturerService;
    }

    @PostMapping("/add")
    @ApiOperation(value = "添加讲师")
    @OperationLog(type = OperationLogType.ADD, notes = "添加讲师")
    public R add(@RequestBody List<ExamLecturerAddReqVO> reqVOS) {
        examLecturerService.add(reqVOS);
        return R.ok();
    }

    @PostMapping("/edit")
    @ApiOperation(value = "编辑讲师")
    @OperationLog(type = OperationLogType.MODITY, notes = "编辑讲师")
    public R edit(@RequestBody @Valid ExamLecturerEditReqVO examLecturerEditReqVO) {
        examLecturerService.edit(examLecturerEditReqVO);
        return R.ok();
    }

    @GetMapping("/info/{id}")
    @ApiOperation(value = "返回讲师信息")
    public R<ExamLecturerInfoRespVO> getInfo(@PathVariable Long id) {
        return R.ok(examLecturerService.readInfo(id));
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除讲师")
    @OperationLog(type = OperationLogType.DELETE, notes = "删除讲师")
    public R delete(@PathVariable Long id) {
        examLecturerService.delete(id);
        return R.ok();
    }

    @PostMapping("/list")
    @ApiOperation(value = "返回讲师列表")
    public R<CommonPage<ExamLecturerListRespVO>> queryLecturerList(@RequestBody ExamLecturerListReqVO req) {
        return R.ok(examLecturerService.queryLecturerList(req));
    }

    @PostMapping("/related/activity")
    @ApiOperation(value = "获取讲师关联的活动列表")
    public R<CommonPage<LecturerRelatedActivityListRespVO>> getRelatedActivity(@RequestBody LecturerRelatedActivityListReqVO req) {
        return R.ok(examLecturerService.getRelatedActivity(req));
    }

    @PostMapping("/getByIds")
    @ApiOperation(value = "根据id批量查询")
    public R<List<ExamLecturerInfoRespVO>> getByIds(@RequestBody List<Long> ids) {
        return R.ok(examLecturerService.getByIds(ids));
    }

}