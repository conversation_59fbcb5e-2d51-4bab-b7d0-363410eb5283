package com.huafon.models.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @describe: 升级通知
 * @date 2023/10/24 9:26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("hf_exam_update_notice")
@ApiModel(value = "ExamUpdateNotice", description = "升级通知")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExamUpdateNotice implements Serializable {

    private static final long serialVersionUID = 6708331596335462700L;

    @ApiModelProperty(value = "自增主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "培训计划id")
    private Long trainPlanId;

    @ApiModelProperty(value = "身份证")
    private String idCard;

    @ApiModelProperty(value = "消息主题")
    private String subject;

    @ApiModelProperty(value = "消息类型")
    private String type;

    @ApiModelProperty(value = "消息配置id")
    private Integer configId;

    @ApiModelProperty(value = "通知配置json")
    private String timeConfig;

    @ApiModelProperty(value = "接收人json")
    private String recipients;

    @ApiModelProperty(value = "通知时间")
    private Date noticeTime;

    @ApiModelProperty(value = "通知内容")
    private String context;

    @ApiModelProperty(value = "时长（分钟）")
    private Integer min;

    @ApiModelProperty(value = "创建人id")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "删除状态 0正常 1删除")
    private Integer isDel;

    @ApiModelProperty(value = "租户id")
    private Integer tenantId;

    @ApiModelProperty(value = "业务数据json")
    private String businessData;

}
