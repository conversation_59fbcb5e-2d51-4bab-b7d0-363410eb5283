package com.huafon.models.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.huafon.models.vo.resp.statistics.ActivityStatisticsRespVO;
import com.huafon.service.support.ExpandAll;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 培训活动分析导出dto
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@Data
public class ActivityStatisticsDownloadDTO {

    @ExcelProperty(value = "年月")
    @ExpandAll(minWidth = 8)
    private String time;

    @ExcelProperty(value = "总人数")
    private Long totalPeople;

    @ExcelProperty(value = "总课时")
    private BigDecimal totalPeriod;

    @ExcelProperty(value = "总场数")
    private Long totalActivity;

    @ExcelProperty(value = "合格率")
    private BigDecimal passRate;

    public static List<ActivityStatisticsDownloadDTO> convertList(List<ActivityStatisticsRespVO> items) {
        List<ActivityStatisticsDownloadDTO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(items)) {
            items.forEach(item -> {
                ActivityStatisticsDownloadDTO activityStatisticsDownloadDTO = new ActivityStatisticsDownloadDTO();
                activityStatisticsDownloadDTO.setTime(item.getTime());
                activityStatisticsDownloadDTO.setTotalPeople(item.getTotalPeople());
                activityStatisticsDownloadDTO.setTotalPeriod(item.getTotalPeriod());
                activityStatisticsDownloadDTO.setTotalActivity(item.getTotalActivity());
                activityStatisticsDownloadDTO.setPassRate(item.getPassRate());
                list.add(activityStatisticsDownloadDTO);
            });
        }
        return list;
    }

}
