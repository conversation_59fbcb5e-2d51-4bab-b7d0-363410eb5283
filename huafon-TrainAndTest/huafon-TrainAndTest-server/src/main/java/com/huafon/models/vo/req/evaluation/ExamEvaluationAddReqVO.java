package com.huafon.models.vo.req.evaluation;

import com.alibaba.fastjson.JSONArray;
import com.google.common.base.Joiner;
import com.huafon.common.config.TenantContext;
import com.huafon.models.entity.ExamEvaluation;
import com.huafon.utils.UserInfoUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description: 评价新增请求体
 * @date 2022-12-19 10:55
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "ExamEvaluationAddReqVO", description = "评价新增请求体")
public class ExamEvaluationAddReqVO {

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "活动id")
    private Long activityId;

    @ApiModelProperty(value = "课程id")
    private Long courseId;

    @ApiModelProperty(value = "讲师id")
    private List<Long> lecturerId;

    @ApiModelProperty(value = "评价等级 1差 2一般 3好 4很好 5非常")
    private Integer level;

    @ApiModelProperty(value = "评价分")
    private Integer score;

    @ApiModelProperty(value = "1为外部培训类型 2为内部培训类型 3为三级培训类型")
    private Integer type;

    @ApiModelProperty(value = "评价详情")
    private List<ExamEvaluationItemReqVO> items;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "改进措施")
    private String improvementMeasures;


    public static ExamEvaluation convertToExamEvaluation(ExamEvaluationAddReqVO item) {
        if (item == null) {
            return null;
        }
        ExamEvaluation examEvaluation = new ExamEvaluation();
        examEvaluation.setUserId(item.getUserId());
        examEvaluation.setActivityId(item.getActivityId());
        examEvaluation.setCourseId(item.getCourseId());
        if (!CollectionUtils.isEmpty(item.getLecturerId())) {
            examEvaluation.setLecturerId(Joiner.on(",").join(item.getLecturerId()));
        }
        examEvaluation.setLevel(item.getLevel());
        examEvaluation.setScore(item.getScore());
        examEvaluation.setType(item.getType());
        examEvaluation.setItems(JSONArray.toJSONString(item.getItems()));
        examEvaluation.setTenantId(TenantContext.getOne());
        examEvaluation.setCreateTime(new Date());
        examEvaluation.setModifyTime(new Date());
        examEvaluation.setCreateDepartment(UserInfoUtils.getDepartmentIdFromLoginInfo().longValue());
        examEvaluation.setRemark(item.getRemark());
        examEvaluation.setImprovementMeasures(item.getImprovementMeasures());
        return examEvaluation;
    }
}