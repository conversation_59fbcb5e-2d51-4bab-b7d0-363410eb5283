package com.huafon.models.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @describe:
 * @date 2022/5/17 13:39
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("hf_exam_paper_random_build")
@ApiModel(value = "ExamPaperRandomBuild对象", description = "")
@Accessors(chain = true)
public class ExamPaperRandomBuild implements Serializable {

    private static final long serialVersionUID = -5459506977587791897L;

    @ApiModelProperty(value = "自增主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "试卷名称")
    private Long examPaperId;

    @ApiModelProperty(value = "SINGLE_CHOICE:单选题 MULTIPLE_CHOICE.多选题 TRUE_FALSE.判断题 GAP_FILLING.填空题 SHORT_ANSWER.简答题 ")
    private String questionCode;

    @ApiModelProperty(value = "题目数量")
    private Integer questionNum;

    @ApiModelProperty(value = "题型总分")
    private BigDecimal questionTypeScore;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "删除状态 0正常，1删除")
    private Integer isDel;

    @ApiModelProperty(value = "题目来源类型id")
    private Long questionSubjectId;
}
