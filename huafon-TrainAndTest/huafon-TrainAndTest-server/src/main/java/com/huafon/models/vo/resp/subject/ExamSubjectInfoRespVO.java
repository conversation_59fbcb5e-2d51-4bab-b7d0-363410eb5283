package com.huafon.models.vo.resp.subject;

import com.huafon.models.entity.ExamSubject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2022-11-04 11:22:52
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "ExamSubjectInfoRespVO", description = "返回培训类别详情信息")
public class ExamSubjectInfoRespVO {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "培训类别类型名称")
    private String subjectName;

    @ApiModelProperty(value = "父亲节点id")
    private Long parentId;

    @ApiModelProperty(value = "上级分类的名称")
    private String parentName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "图标url")
    private String icon;

    @ApiModelProperty(value = "图标宽度")
    private BigDecimal iconWidth;

    @ApiModelProperty(value = "图标高度")
    private BigDecimal iconHeight;

    @ApiModelProperty(value = "节点类型 TOP顶级 NORMAL普通 NOCLASSIFY待分类")
    private String nodeType;

    public static ExamSubjectInfoRespVO convertToExamSubject(ExamSubject item) {
        if (item == null) {
            return null;
        }
        ExamSubjectInfoRespVO examSubjectInfoRespVO = new ExamSubjectInfoRespVO();
        examSubjectInfoRespVO.setId(item.getId());
        examSubjectInfoRespVO.setSubjectName(item.getSubjectName());
        examSubjectInfoRespVO.setParentId(item.getParentId());
        examSubjectInfoRespVO.setRemark(item.getRemark());
        examSubjectInfoRespVO.setIcon(item.getIcon());
        examSubjectInfoRespVO.setIconWidth(item.getIconWidth());
        examSubjectInfoRespVO.setIconHeight(item.getIconHeight());
        examSubjectInfoRespVO.setNodeType(item.getNodeType());
        return examSubjectInfoRespVO;
    }
}
