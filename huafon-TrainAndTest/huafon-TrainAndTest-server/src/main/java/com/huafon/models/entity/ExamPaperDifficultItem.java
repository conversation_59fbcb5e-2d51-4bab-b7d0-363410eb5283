package com.huafon.models.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("hf_exam_paper_difficult_item")
@ApiModel(value = "ExamPaperDifficultItem对象", description = "")
@Accessors(chain = true)
public class ExamPaperDifficultItem implements Serializable {

    private static final long serialVersionUID = -2450180578822990217L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 随机试卷构建id
     */
    private Long examPaperRandomBuildId;

    /**
     * 难度
     */
    private String difficult;

    /**
     * 题目数量
     */
    private Integer questionNum;

    /**
     * 题型总分
     */
    private BigDecimal questionTypeScore;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 删除状态 0正常，1删除
     */
    private Integer isDel;

}
