package com.huafon.models.vo.resp.student;

import com.huafon.models.entity.ExamPeriod;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2022-11-18 17:41:40
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "CoursePeriodListRespVO", description = "返回课程课时列表")
public class CoursePeriodListRespVO {

    @ApiModelProperty(value = "课时名称")
    private String periodName;

    @ApiModelProperty(value = "学习时长")
    private Integer learnTime;

    public static CoursePeriodListRespVO convertCoursePeriodListRespVO(ExamPeriod examPeriod) {
        if (examPeriod == null) {
            return null;
        }
        CoursePeriodListRespVO coursePeriodListRespVO = new CoursePeriodListRespVO();
        coursePeriodListRespVO.setPeriodName(examPeriod.getPeriodName());
        coursePeriodListRespVO.setLearnTime(examPeriod.getLearnTime());
        return coursePeriodListRespVO;
    }
}