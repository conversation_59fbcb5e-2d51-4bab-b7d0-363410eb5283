package com.huafon.models.vo.resp.plan;

import com.huafon.models.dto.ExamPlanListDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR> zhang
 * @program: huafon-base
 * @description:
 * @date 2022-05-25 13:40:19
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ExamPlanListRespVO", description = "返回培训考试计划列表")
public class ExamPlanListRespVO {

    @ApiModelProperty(value = "自增主键")
    private Long id;

    @ApiModelProperty(value = "培训计划名称")
    private String trainName;

    @ApiModelProperty(value = "培训有效期 多少个月")
    private Integer validTime;

    @ApiModelProperty(value = "培训模式：1.仅培训 2.仅考试 3.培训加考试")
    private String trainMode;

    @ApiModelProperty(value = "及格分数")
    private Integer passingScore;

    @ApiModelProperty(value = "培训类型 0:未知 1:承包商 2:访客")
    private Integer type;

    @ApiModelProperty(value = "有效期类型 1:按固定期限 2:按自然年")
    private Integer effectiveType;

    @ApiModelProperty(value = "创建人ID")
    private Long createById;

    @ApiModelProperty(value = "创建人")
    private String createByName;

    @ApiModelProperty(value = "创建部门ID")
    private Long createDepartment;

    @ApiModelProperty(value = "创建部门")
    private String createDepartmentName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    public static ExamPlanListRespVO convertToExamPlanListRespVO(ExamPlanListDTO item) {
        if (item == null) {
            return null;
        }
        ExamPlanListRespVO result = new ExamPlanListRespVO();
        result.setId(item.getId());
        result.setTrainName(item.getTrainName());
        result.setValidTime(item.getValidTime());
        if (item.getIsNeed() == 1 && item.getIsTest() == 1) {
            result.setTrainMode("培训、考试");
        } else if (item.getIsNeed() == 1 && item.getIsTest() == 0) {
            result.setTrainMode("仅培训");
        } else if (item.getIsNeed() == 0 && item.getIsTest() == 1) {
            result.setTrainMode("仅考试");
        }
        result.setPassingScore(item.getPassingScore());
        result.setType(item.getType());
        result.setEffectiveType(item.getEffectiveType());
        result.setCreateById(item.getCreateById());
        result.setCreateByName(item.getCreateByName());
        result.setCreateDepartment(item.getCreateDepartment());
        result.setCreateDepartmentName(item.getCreateDepartmentName());
        result.setCreateTime(item.getCreateTime());
        return result;
    }

}