package com.huafon.models.vo.req.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> zhang
 * @program: huafon-base
 * @description:
 * @date 2022-05-26 16:23:59
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ExamActivityFinishReqVO", description = "完成培训请求VO")
public class ExamActivityFinishReqVO {

    @ApiModelProperty(value = "培训效果")
    private String trainEffect;

    @ApiModelProperty(value = "培训附件")
    private String attachment;

    @ApiModelProperty(value = "证书背景图片")
    private String templateBackground;

    @ApiModelProperty(value = "考评情况")
    private List<@Valid AssessmentSituationReqVO> assessmentSituationList;

    @ApiModelProperty(value = "实际开始时间")
    private Date actualStartTime;

    @ApiModelProperty(value = "实际结束时间")
    private Date actualEndTime;

}