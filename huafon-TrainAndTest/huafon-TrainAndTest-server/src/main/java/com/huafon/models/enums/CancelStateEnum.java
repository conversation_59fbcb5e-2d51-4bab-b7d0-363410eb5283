package com.huafon.models.enums;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2022-11-16 09:38:03
 */
public enum CancelStateEnum {

    // 是否作废 1为作废 0为没有作废

    no_cancel(0, "没有作废"),

    is_cancel(1, "作废");

    Integer state;

    String desc;

    CancelStateEnum(Integer state, String desc) {
        this.state = state;
        this.desc = desc;
    }


    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}