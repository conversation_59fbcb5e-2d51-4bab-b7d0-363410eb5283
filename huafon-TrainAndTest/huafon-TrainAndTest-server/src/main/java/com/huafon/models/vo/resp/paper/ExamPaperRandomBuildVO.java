package com.huafon.models.vo.resp.paper;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.huafon.models.entity.ExamPaper;
import com.huafon.models.entity.ExamPaperRandomBuild;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR> zhang
 * @program: huafon-base
 * @description: 随机试卷构建信息
 * @date 2022-05-26 09:45:20
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ExamPaperRandomBuildVO", description = "随机试卷构建信息")
public class ExamPaperRandomBuildVO {

    @ApiModelProperty(value = "自增主键")
    private Long id;

    @ApiModelProperty(value = "SINGLE_CHOICE:单选题 MULTIPLE_CHOICE.多选题 TRUE_FALSE.判断题 GAP_FILLING.填空题 SHORT_ANSWER.简答题 ")
    private String questionCode;

    @ApiModelProperty(value = "题目数量")
    private Integer questionNum;

    @ApiModelProperty(value = "题型总分")
    private BigDecimal questionTypeScore;

    @ApiModelProperty(value = "题目来源类型id")
    private Long questionSubjectId;

    @ApiModelProperty(value = "题目来源类型")
    private String questionSubjectName;

    public static ExamPaperRandomBuildVO convertToExamPaperRandomBuildVO(ExamPaperRandomBuild item) {
        if (item == null) {
            return null;
        }
        ExamPaperRandomBuildVO result = new ExamPaperRandomBuildVO();
        result.setId(item.getId());
        result.setQuestionCode(item.getQuestionCode());
        result.setQuestionNum(item.getQuestionNum());
        result.setQuestionTypeScore(item.getQuestionTypeScore());
        result.setQuestionSubjectId(item.getQuestionSubjectId());
        return result;
    }

    public static ExamPaperDifficultBuildVO convertToExamPaperDifficultBuildVO(ExamPaperRandomBuild item) {
        if (item == null) {
            return null;
        }
        ExamPaperDifficultBuildVO result = new ExamPaperDifficultBuildVO();
        result.setId(item.getId());
        result.setQuestionCode(item.getQuestionCode());
        return result;
    }
}