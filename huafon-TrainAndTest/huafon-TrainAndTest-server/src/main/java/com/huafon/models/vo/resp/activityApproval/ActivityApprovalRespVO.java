package com.huafon.models.vo.resp.activityApproval;

import com.huafon.models.entity.ExamActivityApproval;
import com.huafon.models.vo.resp.activity.ExamActivityInfoRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 培训活动审批返回VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
@Data
@ApiModel(value = "ActivityApprovalRespVO", description = "培训活动审批返回VO")
public class ActivityApprovalRespVO {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("活动id")
    private Long activityId;

    @ApiModelProperty("活动类型")
    private Integer type;

    @ApiModelProperty("培训效果")
    private String trainEffect;

    @ApiModelProperty("培训附件")
    private String attachment;

    @ApiModelProperty("流程状态")
    private Integer status;

    @ApiModelProperty("工作流id")
    private String workflowId;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty("修改人id")
    private Long modifyBy;

    @ApiModelProperty("修改人")
    private String modifyByName;

    @ApiModelProperty("修改时间")
    private Date modifyTime;

    public static ActivityApprovalRespVO convert(ExamActivityApproval item) {
        if (item == null) {
            return null;
        }
        ActivityApprovalRespVO activityApprovalRespVO = new ActivityApprovalRespVO();
        activityApprovalRespVO.setId(item.getId());
        activityApprovalRespVO.setActivityId(item.getActivityId());
        activityApprovalRespVO.setTrainEffect(item.getTrainEffect());
        activityApprovalRespVO.setAttachment(item.getAttachment());
        activityApprovalRespVO.setStatus(item.getStatus());
        activityApprovalRespVO.setWorkflowId(item.getWorkflowId());
        activityApprovalRespVO.setCode(item.getCode());
        activityApprovalRespVO.setModifyBy(item.getModifyBy());
        activityApprovalRespVO.setModifyByName(item.getModifyByName());
        activityApprovalRespVO.setModifyTime(item.getModifyTime());
        return activityApprovalRespVO;
    }

}
