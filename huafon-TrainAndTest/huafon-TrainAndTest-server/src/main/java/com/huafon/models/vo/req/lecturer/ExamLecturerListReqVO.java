package com.huafon.models.vo.req.lecturer;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.huafon.framework.mybatis.pojo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2022-11-05 15:50:02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "ExamLecturerListReqVO", description = "讲师列表请求体")
public class ExamLecturerListReqVO extends PageRequest {

    @ApiModelProperty(value = "讲师姓名")
    private String name;

    @ApiModelProperty(value = "讲师性别 1男 2女")
    private Integer sex;

    @ApiModelProperty(value = "讲师类型 内部INTERNAL 外部EXTERNAL")
    public String lecturerType;

    @ApiModelProperty(value = "租户id")
    @JsonIgnore
    private List<Integer> tenantIds;
}