package com.huafon.models.vo.resp.process;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <p>
 * 流程数量统计返回VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Data
@ApiModel(value = "", description = "流程数量统计返回VO")
public class ProcessStatusNumRespVO {

    private Integer total;

    private Integer process;

    private Integer finish;

    private Integer cancel;

    public ProcessStatusNumRespVO() {
        this.total = 0;
        this.process = 0;
        this.finish = 0;
        this.cancel = 0;
    }

}
