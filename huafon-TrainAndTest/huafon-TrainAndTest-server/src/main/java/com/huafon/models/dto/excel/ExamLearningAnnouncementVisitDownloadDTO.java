package com.huafon.models.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.huafon.models.vo.resp.learningAnnouncement.ExamLearningAnnouncementVisitPageRespVO;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.StringJoiner;

/**
 * <p>
 * 学习公告查阅记录导出dto
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-09
 */
@Data
public class ExamLearningAnnouncementVisitDownloadDTO {

    @ExcelProperty(value = "姓名")
    private String name;

    @ExcelProperty(value = "工号")
    private String workNum;

    @ExcelProperty(value = "联系电话")
    private String mobile;

    @ExcelProperty(value = "部门岗位")
    private String deptPostList;

    @ExcelProperty(value = "查阅时间")
    private Date createTime;

    public static List<ExamLearningAnnouncementVisitDownloadDTO> convertList(List<ExamLearningAnnouncementVisitPageRespVO> itemList) {
        List<ExamLearningAnnouncementVisitDownloadDTO> dataList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(itemList)) {
            for (ExamLearningAnnouncementVisitPageRespVO item: itemList) {
                ExamLearningAnnouncementVisitDownloadDTO data = new ExamLearningAnnouncementVisitDownloadDTO();
                data.setName(item.getName());
                data.setWorkNum(item.getWorkNum());
                data.setMobile(item.getMobile());
                if (!CollectionUtils.isEmpty(item.getDeptPostList())) {
                    StringJoiner deptPostList = new StringJoiner("|");
                    for (String deptPost : item.getDeptPostList()) {
                        deptPostList.add(deptPost);
                    }
                    data.setDeptPostList(deptPostList.toString());
                }
                data.setCreateTime(item.getCreateTime());
                dataList.add(data);
            }
        }
        return dataList;
    }

}
