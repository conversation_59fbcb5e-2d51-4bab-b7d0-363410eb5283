package com.huafon.models.vo.req.annual;

import com.huafon.common.config.TenantContext;
import com.huafon.models.entity.annual.ExamAnnualTrainTaskChange;
import com.huafon.models.vo.req.process.ProcessCompleteReqVO;
import com.huafon.support.config.UserContext;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

/**
 * <p>
 * 年度培训任务变更请求VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@Data
@ApiModel(value = "ExamAnnualTrainTaskChangeReqVO", description = "年度培训任务变更请求VO")
public class ExamAnnualTrainTaskChangeReqVO extends ProcessCompleteReqVO {

    @ApiModelProperty(value = "年度培训计划id")
    private Long annualTrainPlanId;

    @ApiModelProperty(value = "年度培训计划名称")
    private String annualTrainPlanName;

    @ApiModelProperty(value = "年度培训任务id")
    private Integer annualTrainTaskId;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("单次学时")
    private Double singleClassHour;

    @ApiModelProperty("培训对象")
    private String trainStudent;

    @ApiModelProperty("培训人数")
    private Integer trainStudentNum;

    @ApiModelProperty("周期安排")
    private String trainPeriod;

    @ApiModelProperty("用户id")
    private Integer userId;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("责任部门id")
    private Integer userDeptId;

    @ApiModelProperty("责任部门")
    private String userDeptName;

    @ApiModelProperty("变更原因")
    private String changeReason;

    @ApiModelProperty("跳过第一个节点 true|提交 false|暂存")
    private Boolean isCompleteFirst;

    public static ExamAnnualTrainTaskChange convert(ExamAnnualTrainTaskChangeReqVO item) {
        if (item == null) {
            return null;
        }
        ExamAnnualTrainTaskChange examAnnualTrainTaskChange = new ExamAnnualTrainTaskChange();
        examAnnualTrainTaskChange.setId(item.getId());
        examAnnualTrainTaskChange.setAnnualTrainPlanId(item.getAnnualTrainPlanId());
        examAnnualTrainTaskChange.setAnnualTrainPlanName(item.getAnnualTrainPlanName());
        examAnnualTrainTaskChange.setAnnualTrainTaskId(item.getAnnualTrainTaskId());
        examAnnualTrainTaskChange.setCode(item.getCode());
        examAnnualTrainTaskChange.setName(item.getName());
        examAnnualTrainTaskChange.setSingleClassHour(item.getSingleClassHour());
        examAnnualTrainTaskChange.setTrainStudent(item.getTrainStudent());
        examAnnualTrainTaskChange.setTrainStudentNum(item.getTrainStudentNum());
        examAnnualTrainTaskChange.setTrainPeriod(item.getTrainPeriod());
        examAnnualTrainTaskChange.setUserId(item.getUserId());
        examAnnualTrainTaskChange.setUserName(item.getUserName());
        examAnnualTrainTaskChange.setUserDeptId(item.getUserDeptId());
        examAnnualTrainTaskChange.setUserDeptName(item.getUserDeptName());
        examAnnualTrainTaskChange.setChangeReason(item.getChangeReason());
        if (Objects.isNull(item.getId())) {
            examAnnualTrainTaskChange.setCreate(UserContext.getId());
            examAnnualTrainTaskChange.setCreateByName(UserContext.get().getName());
            examAnnualTrainTaskChange.setTenantId(TenantContext.getOne());
        }
        examAnnualTrainTaskChange.setModify(UserContext.getId());
        examAnnualTrainTaskChange.setModifyByName(UserContext.get().getName());
        return examAnnualTrainTaskChange;
    }

}
