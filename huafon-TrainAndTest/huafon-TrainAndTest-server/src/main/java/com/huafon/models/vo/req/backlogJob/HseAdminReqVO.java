package com.huafon.models.vo.req.backlogJob;

import com.alibaba.fastjson.JSONObject;
import com.huafon.common.config.TenantContext;
import com.huafon.framework.mybatis.enums.DelFlag;
import com.huafon.models.entity.ExamHseAdmin;
import com.huafon.support.config.UserContext;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description: HSE部要素管理员请求体
 * @date 2023-01-07 16:52:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "HseAdminReqVO", description = "HSE部要素管理员请求体")
public class HseAdminReqVO {

    @ApiModelProperty("入司培训公司级")
    private HseAdminConfigReqVO companyConfig;

    @ApiModelProperty("入司培训部门级")
    private HseAdminConfigReqVO deptConfig;

    @ApiModelProperty("转岗培训部门级")
    private HseAdminConfigReqVO transferDeptConfig;

    public static ExamHseAdmin convertToExamHseAdmin(HseAdminReqVO item) {
        if (item == null) {
            return null;
        }
        ExamHseAdmin examHseAdmin = new ExamHseAdmin();
        examHseAdmin.setCompanyConfig(JSONObject.toJSONString(item.getCompanyConfig()));
        examHseAdmin.setDeptConfig(JSONObject.toJSONString(item.getDeptConfig()));
        examHseAdmin.setTransferDeptConfig(JSONObject.toJSONString(item.getTransferDeptConfig()));
        examHseAdmin.setCreateBy(UserContext.get().getUserId());
        examHseAdmin.setCreateTime(new Date());
        examHseAdmin.setModifyBy(UserContext.get().getUserId());
        examHseAdmin.setModifyByName(UserContext.get().getName());
        examHseAdmin.setModifyTime(new Date());
        examHseAdmin.setTenantId(TenantContext.getOne());
        examHseAdmin.setIsDel(DelFlag.SAVE.getValue());
        return examHseAdmin;
    }

    public static ExamHseAdmin convertToExamHseAdmin(HseAdminReqVO item, Long id) {
        if (item == null) {
            return null;
        }
        ExamHseAdmin examHseAdmin = new ExamHseAdmin();
        examHseAdmin.setId(id);
        examHseAdmin.setCompanyConfig(JSONObject.toJSONString(item.getCompanyConfig()));
        examHseAdmin.setDeptConfig(JSONObject.toJSONString(item.getDeptConfig()));
        examHseAdmin.setTransferDeptConfig(JSONObject.toJSONString(item.getTransferDeptConfig()));
        examHseAdmin.setModifyBy(UserContext.get().getUserId());
        examHseAdmin.setModifyByName(UserContext.get().getName());
        examHseAdmin.setModifyTime(new Date());
        return examHseAdmin;
    }

}