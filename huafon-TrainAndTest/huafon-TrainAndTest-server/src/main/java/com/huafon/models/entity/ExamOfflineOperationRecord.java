package com.huafon.models.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 线下实操记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("hf_exam_offline_operation_record")
@ApiModel(value = "ExamOfflineOperationRecord对象", description = "线下实操记录")
@Accessors(chain = true)
public class ExamOfflineOperationRecord extends BaseEntity {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 记录编码
     */
    private String code;

    /**
     * 培训名称
     */
    private String activityName;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 工号
     */
    private String workNum;

    /**
     * 是否颁布证书 1:是 0:否
     */
    private Integer isAward;

    /**
     * 是否通过 1:是 0:否
     */
    private Integer isPass;

    /**
     * 考试成绩
     */
    private String examScore;

    /**
     * 修改人
     */
    private String modifyByName;

    /**
     * 租户id
     */
    private Integer tenantId;

    /**
     * 讲师json
     */
    private String lecturer;

    /**
     * 证书类型id
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer licenseTypeId;

    /**
     * 证书名称
     */
    private String licenseName;

    /**
     * 证书编号
     */
    private String licenseNumber;

    /**
     * 证书url
     */
    private String license;

    /**
     * 附件
     */
    private String attachment;

    /**
     * 联系电话
     */
    private String mobile;

    /**
     * 培训类别id
     */
    private Long subjectId;

    /**
     * 基本信息附件
     */
    private String baseAttachment;

}
