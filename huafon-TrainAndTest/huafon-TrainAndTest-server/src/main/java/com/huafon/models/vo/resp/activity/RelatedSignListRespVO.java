package com.huafon.models.vo.resp.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2023-01-10 14:13:37
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "RelatedSignListRespVO", description = "返回活动签到列表")
public class RelatedSignListRespVO {

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "姓名")
    private String userName;

    @ApiModelProperty(value = "签到 true:已签到 false:未签到")
    private Boolean isSign;

    @ApiModelProperty(value = "下课签到 true:已签到 false:未签到")
    private Boolean isOverSign;

}
