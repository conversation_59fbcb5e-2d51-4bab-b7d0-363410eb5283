package com.huafon.models.dto.excel;

import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 岗位能力矩阵导出
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class MatrixDownloadDTO {

	/**
	 * 导出数据
	 */
	private List<Map<String, Object>> dataList;

	/**
	 * 表头数据
	 */
	private List<ExcelExportEntity> headList;

	/**
	 * 部门名称
	 */
	private String deptName;

}
