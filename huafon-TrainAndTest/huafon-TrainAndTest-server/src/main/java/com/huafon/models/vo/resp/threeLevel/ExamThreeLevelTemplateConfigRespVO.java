package com.huafon.models.vo.resp.threeLevel;

import com.huafon.models.entity.ExamThreeLevelTemplateConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 三级培训培训模板配置返回VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-10
 */
@Data
@ApiModel(value = "ExamThreeLevelTemplateConfigRespVO", description = "三级培训培训模板配置返回VO")
public class ExamThreeLevelTemplateConfigRespVO {

    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("培训日期 1:计划培训时间 2:实际培训时间")
    private Integer timeType;

    @ApiModelProperty("培训模板列表")
    private List<ExamThreeLevelTemplateRespVO> templateList;

    @ApiModelProperty("修改人id")
    private Long modifyBy;

    @ApiModelProperty("修改人")
    private String modifyByName;

    @ApiModelProperty("修改时间")
    private Date modifyTime;

    public static ExamThreeLevelTemplateConfigRespVO convert(ExamThreeLevelTemplateConfig item) {
        if (item == null) {
            return null;
        }
        ExamThreeLevelTemplateConfigRespVO examThreeLevelTemplateConfigRespVO = new ExamThreeLevelTemplateConfigRespVO();
        examThreeLevelTemplateConfigRespVO.setId(item.getId());
        examThreeLevelTemplateConfigRespVO.setTimeType(item.getTimeType());
        examThreeLevelTemplateConfigRespVO.setModifyBy(item.getModifyBy());
        examThreeLevelTemplateConfigRespVO.setModifyByName(item.getModifyByName());
        examThreeLevelTemplateConfigRespVO.setModifyTime(item.getModifyTime());
        return examThreeLevelTemplateConfigRespVO;
    }

}
