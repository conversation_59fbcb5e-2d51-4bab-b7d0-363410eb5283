package com.huafon.models.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *  ExamActivity对象
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("hf_exam_activity")
@ApiModel(value="ExamActivity对象", description="ExamActivity对象")
public class ExamActivity implements Serializable {

    private static final long serialVersionUID = -8180781182265893975L;

    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "活动")
    private String activityName;

    @ApiModelProperty(value = "学科id 关联hf_exam_subject.id")
    private Long subjectId;

    @ApiModelProperty(value = "课程id 关联hf_exam_course.id")
    private Long courseId;

    @ApiModelProperty(value = "讲师id列表（,分隔）")
    private String lecturerId;

    @ApiModelProperty(value = "计划开始时间")
    private Date startTime;

    @ApiModelProperty(value = "计划结束时间")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date endTime;

    @ApiModelProperty(value = "活动结束时间标识（0为结束时间有期限，1为结束时间没有期限，永远不结束）")
    private Integer endMark;

    @ApiModelProperty(value = "培训地点")
    private String location;

    @ApiModelProperty(value = "组织部门id 关联hf_org_department.id")
    private Integer trainDepartmentId;

    @ApiModelProperty(value = "是否授予证书 1为授予 0为不授予")
    private Integer isAward;

    @ApiModelProperty(value = "证书类型id 关联hf_staff_license.license_type_id关联")
    private Integer licenseTypeId;

    @ApiModelProperty(value = "培训状态 0为培训 1已培训")
    private Integer status;

    @ApiModelProperty(value = "租户id")
    private Integer tenantId;

    @ApiModelProperty(value = "创建者用户id")
    private Long createBy;

    @ApiModelProperty(value = "修改者用户id")
    private Long modifyBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "删除状态 0正常，1删除")
    private Integer isDel;

    @ApiModelProperty(value = "1为外部培训类型 2为内部培训类型 3为三级培训类型")
    private Integer type;

    @ApiModelProperty(value = "三级培训级别 COMPANY:公司级 DEPARTMENT:部门级 TEAM:班组级")
    private String threeLevelSubject;

    @ApiModelProperty(value = "三级培训类型 1:入司培训 2:转岗培训")
    private Integer threeLevelType;

    @ApiModelProperty(value = "培训效果")
    private String trainEffect;

    @ApiModelProperty(value = "序列号")
    private Integer sequence;

    @ApiModelProperty(value = "培训完成日期")
    private Date finishTime;

    @ApiModelProperty(value = "培训附件")
    private String attachment;

    @ApiModelProperty(value = "待办任务id")
    private Long jobId;

    @ApiModelProperty(value = "扫码加入 true|是 false|否")
    private Boolean supportJoin;

    @ApiModelProperty(value = "创建者部门id")
    private Long createDepartment;

    @ApiModelProperty(value = "年度培训计划id")
    private Long annualTrainPlanId;

    @ApiModelProperty(value = "岗位能力矩阵标识（部门id-课程id-岗位id）")
    private String matrixKey;

    @ApiModelProperty(value = "年度培训计划待办任务id")
    private Integer annualTrainTaskId;

    @ApiModelProperty(value = "评价模板ID")
    private Integer evaluationTemplateId;

    @ApiModelProperty(value = "评价模板名称")
    private String evaluationTemplateName;

    @ApiModelProperty(value = "实际开始时间")
    private Date actualStartTime;

    @ApiModelProperty(value = "实际结束时间")
    private Date actualEndTime;

    @ApiModelProperty(value = "活动主题")
    private String subject;

    @ApiModelProperty(value = "再培训")
    private Boolean repeat;

    @ApiModelProperty(value = "培训级别")
    private String level;

}
