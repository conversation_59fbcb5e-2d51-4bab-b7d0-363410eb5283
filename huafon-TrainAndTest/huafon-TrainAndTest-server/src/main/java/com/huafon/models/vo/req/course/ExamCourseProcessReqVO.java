package com.huafon.models.vo.req.course;

import com.huafon.common.config.TenantContext;
import com.huafon.models.entity.ExamCourse;
import com.huafon.models.enums.TrainTypeEnum;
import com.huafon.support.config.UserContext;
import com.huafon.utils.UserInfoUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 课程流程请求VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "ExamCourseProcessReqVO", description = "课程流程请求VO")
public class ExamCourseProcessReqVO {

    @ApiModelProperty(value = "主键")
    private Long courseId;

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "学科id 关联hf_exam_subject.id")
    private Long subjectId;

    @ApiModelProperty(value = "封面地址")
    private String coverUrl;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "是否需要培训（0不需要 1需要 默认值为0；如果需要培训，需要添加课时）")
    private Integer isTrain;

    @ApiModelProperty(value = "课时数量（培训课时总数 默认值为0）")
    private Integer periodNum;

    @ApiModelProperty(value = "学习时长")
    private Integer learnTime;

    @ApiModelProperty(value = "预计学习人数")
    private Integer learnNum;

    @ApiModelProperty(value = "课程总学分")
    private Integer courseCredit;

    @ApiModelProperty(value = "课时信息")
    private List<ExamPeriodReqVO> periodInfos;

    @ApiModelProperty(value = "int2 是否参加考试 0 不参与 1 参与")
    private Integer isTest;

    @ApiModelProperty(value = "试卷id")
    private Long examPaperId;

    @ApiModelProperty(value = "合格分数")
    private Integer passingScore;

    @ApiModelProperty(value = "模拟试卷id")
    private Long simulatedExamPaperId;

    @ApiModelProperty(value = "模拟合格分数")
    private Integer simulatedPassingScore;

    @ApiModelProperty(value = "考试重复次数 -1 为不限制")
    private Integer repeat;

    @ApiModelProperty(value = "签字提示")
    private String signatureTip;

    @ApiModelProperty(value = "课时")
    private Double period;

    @ApiModelProperty(value = "阅卷方式 0|手动阅卷 1|自动阅卷")
    private Integer scoringMethod;

    @ApiModelProperty(value = "是否必须评价 1是 0否")
    private Integer isEvaluate;

    @ApiModelProperty(value = "是否生成课程任务 1是 0否")
    private Integer isStudy;

    @ApiModelProperty(value = "考试结果查看 2培训完成查看 1立即查看 0全部学员考完查看")
    private Integer paperResultView;

    @ApiModelProperty(value = "考试结束是否需要订正 1是 0否")
    private Integer needCorrect;

    @ApiModelProperty(value = "防挂机限制 1是 0否")
    private Integer onHookLimit;

    @ApiModelProperty(value = "防挂机时长(分钟)")
    private Integer onHookTime;

    @ApiModelProperty(value = "防切屏限制 1是 0否")
    private Integer toggleScreenLimit;

    @ApiModelProperty(value = "防切屏次数")
    private Integer toggleScreenTime;

    @ApiModelProperty(value = "类型 2:内部培训类型 4:岗位能力矩阵 默认4")
    private Integer type = TrainTypeEnum.postMatrix.getType();

    @ApiModelProperty(value = "课程等级 1:低 2:中 3:高")
    private Integer level;

    @ApiModelProperty("发起流程时 true|暂存 false|提交")
    private Boolean isSubmit;

    @ApiModelProperty(value = "工作流自定义条件")
    private Map<String, Object> workflowMap;

    @ApiModelProperty(value = "工作流id")
    private String workflowId;

    @ApiModelProperty(value = "工作流节点id")
    private String workflowNodeId;

    @ApiModelProperty(value = "退回的流程节点id")
    private String retreatWorkflowNodeId;

    @ApiModelProperty(value = "审批 通过:true 退回:false")
    private Boolean check;

    @ApiModelProperty(value = "审批文本")
    private String checkContent;

    @ApiModelProperty(value = "审批签字")
    private String sign;

    public static ExamCourse convert(ExamCourseProcessReqVO item) {
        if (item == null) {
            return null;
        }
        ExamCourse examCourse = new ExamCourse();
        examCourse.setId(item.getCourseId());
        examCourse.setCourseName(item.getCourseName());
        examCourse.setSubjectId(item.getSubjectId());
        examCourse.setCoverUrl(item.getCoverUrl());
        examCourse.setRemark(item.getRemark());
        examCourse.setIsTrain(item.getIsTrain());
        examCourse.setPeriodNum(item.getPeriodNum());
        examCourse.setLearnTime(item.getLearnTime());
        examCourse.setLearnNum(item.getLearnNum());
        examCourse.setCourseCredit(item.getCourseCredit());
        examCourse.setIsTest(item.getIsTest());
        examCourse.setExamPaperId(item.getExamPaperId());
        examCourse.setPassingScore(item.getPassingScore());
        examCourse.setSimulatedExamPaperId(item.getSimulatedExamPaperId());
        examCourse.setSimulatedPassingScore(item.getSimulatedPassingScore());
        examCourse.setRepeat(item.getRepeat());
        examCourse.setSignatureTip(item.getSignatureTip());
        examCourse.setPeriod(item.getPeriod());
        examCourse.setScoringMethod(item.getScoringMethod());
        examCourse.setIsEvaluate(item.getIsEvaluate());
        examCourse.setIsStudy(item.getIsStudy());
        examCourse.setPaperResultView(item.getPaperResultView());
        examCourse.setNeedCorrect(item.getNeedCorrect());
        examCourse.setOnHookLimit(item.getOnHookLimit());
        examCourse.setOnHookTime(item.getOnHookTime());
        examCourse.setToggleScreenLimit(item.getToggleScreenLimit());
        examCourse.setToggleScreenTime(item.getToggleScreenTime());
        examCourse.setType(item.getType());
        examCourse.setLevel(item.getLevel());
        if (Objects.isNull(item.getCourseId())) {
            examCourse.setCreateBy(UserContext.get().getUserId());
            examCourse.setCreateByName(UserContext.get().getName());
            examCourse.setCreateUserMobile(UserContext.get().getMobile());
            examCourse.setCreateDepartment(UserInfoUtils.getDepartmentIdFromLoginInfo().longValue());
            examCourse.setCreateTime(new Date());
            examCourse.setTenantId(TenantContext.getOne());
        }
        examCourse.setModifyBy(UserContext.get().getUserId());
        examCourse.setModifyByName(UserContext.get().getName());
        examCourse.setModifyUserMobile(UserContext.get().getMobile());
        examCourse.setModifyTime(new Date());
        return examCourse;
    }


}