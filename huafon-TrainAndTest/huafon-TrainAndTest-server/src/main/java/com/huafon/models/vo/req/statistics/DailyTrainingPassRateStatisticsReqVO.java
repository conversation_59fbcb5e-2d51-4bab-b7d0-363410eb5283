package com.huafon.models.vo.req.statistics;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 日常训练合格率请求VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Data
@ApiModel(value = "DailyTrainingPassRateStatisticsReqVO", description = "日常训练合格率请求VO")
public class DailyTrainingPassRateStatisticsReqVO {

    @ApiModelProperty(value = "开始时间", required = true)
    @NotNull(message = "开始时间不能为空")
    private Date beginTime;

    @ApiModelProperty(value = "结束时间", required = true)
    @NotNull(message = "结束时间不能为空")
    private Date endTime;

    @ApiModelProperty(value = "部门")
    private List<@Valid Dept> depts;

    @ApiModelProperty(value = "租户ID", hidden = true)
    private Integer tenantId;

    @ApiModelProperty(value = "用户ID列表", hidden = true)
    private Collection<Integer> userIds;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Dept {

        @ApiModelProperty(value = "部门ID", required = true)
        @NotNull(message = "部门ID不能为空")
        private Integer deptId;

        @ApiModelProperty(value = "部门名称", required = true)
        @NotBlank(message = "部门名称不能为空")
        private String deptName;

    }

}
