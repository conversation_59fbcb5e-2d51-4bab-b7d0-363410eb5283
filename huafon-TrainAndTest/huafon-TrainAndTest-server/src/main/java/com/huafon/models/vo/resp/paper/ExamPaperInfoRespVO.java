package com.huafon.models.vo.resp.paper;

import com.alibaba.fastjson.JSONArray;
import com.huafon.models.entity.ExamPaper;
import com.huafon.models.entity.ExamPaperFixedBuild;
import com.huafon.models.entity.ExamPaperRandomBuild;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> zhang
 * @program: huafon-base
 * @description:
 * @date 2022-05-19 16:39:51
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ExamPaperInfoRespVO", description = "前端请求试卷的详情")
public class ExamPaperInfoRespVO extends ExamPaperVO {

    @ApiModelProperty(value = "试卷题型")
    private List<ExamQuestionTypeRespVO> examQuestionTypes;

    public static ExamPaperInfoRespVO convertToExamPaperInfoRespVO(ExamPaper item) {
        if (item == null) {
            return null;
        }
        ExamPaperInfoRespVO result = new ExamPaperInfoRespVO();
        result.setId(item.getId());
        result.setPaperName(item.getPaperName());
        result.setSubjectCode(item.getSubjectCode());
        result.setSubjectName(item.getSubjectName());
        result.setDuration(item.getDuration());
        result.setPaperType(item.getPaperType());
        result.setTotalPoints(item.getTotalPoints());
        result.setType(item.getType());
        result.setSubjectId(item.getSubjectId());
        result.setClassification(item.getClassification());
        result.setOpen(item.getOpen());
        result.setDraft(item.getDraft());
        if (!StringUtils.isEmpty(item.getQuestionSubjectId())) {
            result.setQuestionSubjectId(JSONArray.parseArray(item.getQuestionSubjectId(),Long.class));
        } else {
            result.setQuestionSubjectId(Collections.emptyList());
        }
        result.setCreateById(item.getCreateById());
        result.setCreateByName(item.getCreateByName());
        result.setCreateTime(item.getCreateTime());
        result.setCreateDepartment(item.getCreateDepartment());
        result.setCreateDepartmentName(item.getCreateDepartmentName());
        result.setModifyById(item.getModifyById());
        result.setModifyByName(item.getModifyByName());
        result.setModifyTime(item.getModifyTime());
        return result;
    }

    public void setExamQuestionTypeRespVOS(List<ExamPaperFixedBuild> examPaperFixedBuilds) {
        List<ExamQuestionTypeRespVO> examQuestionTypeRespVOS = new ArrayList<>();
        examPaperFixedBuilds.forEach(examPaperFixedBuild -> {
            examQuestionTypeRespVOS.add(ExamQuestionTypeRespVO.convertToExamQuestionTypeRespVO(examPaperFixedBuild));
        });
        this.examQuestionTypes = examQuestionTypeRespVOS;
    }

    public void setExamQuestionTypeRespVOS2(List<ExamPaperRandomBuild> examPaperRandomBuilds) {
        List<ExamQuestionTypeRespVO> examQuestionTypeRespVOS = new ArrayList<>();
        examPaperRandomBuilds.forEach(examPaperRandomBuild -> {
            examQuestionTypeRespVOS.add(ExamQuestionTypeRespVO.convertToExamQuestionTypeRespVO(examPaperRandomBuild));
        });
        this.examQuestionTypes = examQuestionTypeRespVOS;
    }
}