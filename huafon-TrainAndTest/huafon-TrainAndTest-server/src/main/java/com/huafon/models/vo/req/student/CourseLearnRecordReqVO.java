package com.huafon.models.vo.req.student;

import com.huafon.common.config.TenantContext;
import com.huafon.models.entity.ExamCourseLearnRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2022-11-15 15:40:16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "CourseLearnRecordReqVO", description = "培训课程学习记录")
public class CourseLearnRecordReqVO {

    @ApiModelProperty(value = "用户id 系统全局的用户ID")
    private Long userId;

    @ApiModelProperty(value = "活动id")
    private Long activityId;

    @ApiModelProperty(value = "课程id")
    private Long courseId;

    @ApiModelProperty(value = "课时id")
    private Long periodId;

    @ApiModelProperty(value = "课时学分")
    private Integer periodCredit;

    @ApiModelProperty(value = "课时的学习时长")
    private Integer learnTime;

    @ApiModelProperty(value = "完成的学习时长")
    private Integer completeLearnTime;

    @ApiModelProperty(value = "培训资料是否已经完成，1完成 0未完成。")
    private Integer isComplete;

    public static ExamCourseLearnRecord convertToExamCourseLearnRecordForAdd(CourseLearnRecordReqVO courseLearnRecordReqVO) {
        if (courseLearnRecordReqVO == null) {
            return null;
        }
        ExamCourseLearnRecord examCourseLearnRecord = new ExamCourseLearnRecord();
        examCourseLearnRecord.setUserId(courseLearnRecordReqVO.getUserId());
        examCourseLearnRecord.setActivityId(courseLearnRecordReqVO.getActivityId());
        examCourseLearnRecord.setCourseId(courseLearnRecordReqVO.getCourseId());
        examCourseLearnRecord.setPeriodId(courseLearnRecordReqVO.getPeriodId());
        examCourseLearnRecord.setPeriodCredit(courseLearnRecordReqVO.getPeriodCredit());
        examCourseLearnRecord.setLearnTime(courseLearnRecordReqVO.getLearnTime());
        examCourseLearnRecord.setCompleteLearnTime(courseLearnRecordReqVO.getCompleteLearnTime());
        examCourseLearnRecord.setIsComplete(courseLearnRecordReqVO.getIsComplete());

        examCourseLearnRecord.setCreateTime(new Date());
        examCourseLearnRecord.setModifyTime(new Date());
        examCourseLearnRecord.setTenantId(TenantContext.getOne());
        return examCourseLearnRecord;
    }

    public static ExamCourseLearnRecord convertToExamCourseLearnRecordForUpdate(CourseLearnRecordReqVO courseLearnRecordReqVO) {
        if (courseLearnRecordReqVO == null) {
            return null;
        }
        ExamCourseLearnRecord examCourseLearnRecord = new ExamCourseLearnRecord();
        examCourseLearnRecord.setUserId(courseLearnRecordReqVO.getUserId());
        examCourseLearnRecord.setCourseId(courseLearnRecordReqVO.getCourseId());
        examCourseLearnRecord.setPeriodId(courseLearnRecordReqVO.getPeriodId());
        examCourseLearnRecord.setPeriodCredit(courseLearnRecordReqVO.getPeriodCredit());
        examCourseLearnRecord.setLearnTime(courseLearnRecordReqVO.getLearnTime());
        examCourseLearnRecord.setCompleteLearnTime(courseLearnRecordReqVO.getCompleteLearnTime());
        examCourseLearnRecord.setIsComplete(courseLearnRecordReqVO.getIsComplete());

        examCourseLearnRecord.setModifyTime(new Date());
        return examCourseLearnRecord;
    }
}