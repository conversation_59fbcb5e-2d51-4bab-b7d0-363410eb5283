package com.huafon.models.enums;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2022-11-23 09:58:45
 */
public enum passStateEnum {

    // 0:没有通过考试 1:通过考试

    no_pass(0, "没有通过考试"),

    is_pass(1, "通过考试");

    Integer state;

    String desc;

    passStateEnum(Integer state, String desc) {
        this.state = state;
        this.desc = desc;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}