package com.huafon.models.vo.req.activityAppeal;

import com.huafon.framework.mybatis.pojo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 活动申诉分页请求VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
@Data
@ApiModel(value = "ActivityAppealPageReqVO", description = "活动申诉分页请求VO")
public class ActivityAppealPageReqVO extends PageRequest {

    @ApiModelProperty("模糊搜索")
    private String searchVal;

    @ApiModelProperty("流程状态")
    private Integer status;

    @ApiModelProperty(value = "讲师id列表（,分隔）",hidden = true)
    private String lecturerId;

    @ApiModelProperty(value = "租户id",hidden = true)
    private Integer tenantId;

}
