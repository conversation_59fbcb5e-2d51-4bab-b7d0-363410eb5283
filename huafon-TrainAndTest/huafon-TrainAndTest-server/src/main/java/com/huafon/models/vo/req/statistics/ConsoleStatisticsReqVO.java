package com.huafon.models.vo.req.statistics;

import com.huafon.models.enums.ThreeLevelSubjectEnum;
import com.huafon.models.enums.ThreeLevelTypeEnum;
import com.huafon.models.enums.TrainTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 首页数据统计请求VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Data
@ApiModel(value = "ConsoleStatisticsReqVO", description = "首页数据统计请求VO")
public class ConsoleStatisticsReqVO {

    @ApiModelProperty(value = "开始时间")
    @NotNull(message = "开始时间不能为空")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @NotNull(message = "结束时间不能为空")
    private Date endTime;

    @ApiModelProperty(value = "部门id")
    private Integer deptId;

    public static ActivityStatisticsReqVO convertJoinTrain(ConsoleStatisticsReqVO item) {
        if (item == null) {
            return null;
        }
        ActivityStatisticsReqVO activityStatisticsReqVO = new ActivityStatisticsReqVO();
        activityStatisticsReqVO.setStartDate(item.getStartTime());
        activityStatisticsReqVO.setEndDate(item.getEndTime());
        activityStatisticsReqVO.setTrainDepartmentId(item.getDeptId());
        activityStatisticsReqVO.setThreeLevelType(ThreeLevelTypeEnum.joinCompany.getType());
        activityStatisticsReqVO.setTypes(Arrays.asList(TrainTypeEnum.threeLevel.getType()));
        return activityStatisticsReqVO;
    }

    public static ActivityStatisticsReqVO convertTransferTrain(ConsoleStatisticsReqVO item) {
        if (item == null) {
            return null;
        }
        ActivityStatisticsReqVO activityStatisticsReqVO = new ActivityStatisticsReqVO();
        activityStatisticsReqVO.setStartDate(item.getStartTime());
        activityStatisticsReqVO.setEndDate(item.getEndTime());
        activityStatisticsReqVO.setTrainDepartmentId(item.getDeptId());
        activityStatisticsReqVO.setThreeLevelType(ThreeLevelTypeEnum.transferPost.getType());
        activityStatisticsReqVO.setTypes(Arrays.asList(TrainTypeEnum.threeLevel.getType()));
        return activityStatisticsReqVO;
    }

    public static ActivityStatisticsReqVO convertDailyTrain(ConsoleStatisticsReqVO item) {
        ActivityStatisticsReqVO activityStatisticsReqVO = new ActivityStatisticsReqVO();
        activityStatisticsReqVO.setStartDate(item.getStartTime());
        activityStatisticsReqVO.setEndDate(item.getEndTime());
        activityStatisticsReqVO.setTrainDepartmentId(item.getDeptId());
        activityStatisticsReqVO.setTypes(Arrays.asList(TrainTypeEnum.internal.getType(),TrainTypeEnum.temporary.getType()));
        return activityStatisticsReqVO;
    }

}
