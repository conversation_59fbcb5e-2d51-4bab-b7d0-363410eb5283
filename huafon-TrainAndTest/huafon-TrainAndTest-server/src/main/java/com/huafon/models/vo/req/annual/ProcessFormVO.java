package com.huafon.models.vo.req.annual;

import com.huafon.service.support.validateGroup.DefaultGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

@Data
@ApiModel(value="ProcessFormVO", description="审核VO")
public class ProcessFormVO {
    @ApiModelProperty("年度计划id")
    @NotNull(message = "年度计划id为空",groups = {DefaultGroup.class})
    private Long id;

    @ApiModelProperty("处理意见（0：暂存，1：通过，2：退回 ，3：拒绝）")
    @NotBlank(message = "处理状态为空",groups = {DefaultGroup.class})
    private String action;

    @ApiModelProperty("反馈意见")
    private String remark;

    @ApiModelProperty("流程节点id")
    private String checkId;

    @ApiModelProperty("参数map")
    private Map<String,Object> workflowMap;

    @ApiModelProperty("签字")
    private String sign;

    @Valid
    @NotNull(message = "年度计划数据不能为空",groups = {DefaultGroup.class})
    private ExamAnnualTrainPlanUpdateReqVO data;

    @ApiModelProperty("退回流程节点id")
    private String retreatWorkflowNodeId;

}
