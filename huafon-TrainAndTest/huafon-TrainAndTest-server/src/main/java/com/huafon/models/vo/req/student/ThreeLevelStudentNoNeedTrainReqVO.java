package com.huafon.models.vo.req.student;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/5 8:04:00
 * @description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "ThreeLevelStudentNoNeedTrainReqVO", description = "三级培训学员不需要培训请求VO")
public class ThreeLevelStudentNoNeedTrainReqVO {

    @ApiModelProperty(value = "三级培训类型 1:入司培训 2:转岗培训")
    @NotNull(message = "三级培训类型不能为空")
    private Integer threeLevelType;

    @ApiModelProperty(value = "三级培训级别 COMPANY:公司级 DEPARTMENT:部门级 TEAM:班组级")
    @NotNull(message = "三级培训级别不能为空")
    private String threeLevelSubject;

    @ApiModelProperty(value = "用户ids")
    @NotNull(message = "用户ids不能为空")
    private List<Long> userIds;

}
