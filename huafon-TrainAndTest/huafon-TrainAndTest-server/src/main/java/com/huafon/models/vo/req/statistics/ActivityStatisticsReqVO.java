package com.huafon.models.vo.req.statistics;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 数据统计培训活动请求VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel(value = "ActivityStatisticsReqVO", description = "数据统计培训活动请求VO")
public class ActivityStatisticsReqVO {

    @ApiModelProperty(value = "开始日期")
    @NotNull(message = "开始日期不能为空")
    private Date startDate;

    @ApiModelProperty(value = "结束日期")
    @NotNull(message = "结束日期不能为空")
    private Date endDate;

    @ApiModelProperty(value = "培训部门id")
    private Integer trainDepartmentId;

    @ApiModelProperty(value = "三级培训类型 1:入司培训 2:转岗培训")
    private Integer threeLevelType;

    @ApiModelProperty(value = "三级培训级别 COMPANY:公司级 DEPARTMENT:部门级 TEAM:班组级")
    private String threeLevelSubject;

    @ApiModelProperty(value = "培训类型 2日常培训活动 3三级培训活动 5临时培训活动")
    private List<Integer> types;

    @ApiModelProperty(value = "培训类别")
    private Integer subjectId;

    /**
     * 租户id
     */
    @JsonIgnore
    private Integer tenantId;

    /**
     * 培训部门ids
     */
    private List<Integer> trainDepartmentIds;

}
