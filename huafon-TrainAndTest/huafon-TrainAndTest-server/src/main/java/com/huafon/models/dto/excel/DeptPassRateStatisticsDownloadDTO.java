package com.huafon.models.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.huafon.models.vo.resp.statistics.DeptPassRateStatisticsRespVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 部门合格率导出dto
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-23
 */
@Data
public class DeptPassRateStatisticsDownloadDTO {

    @ExcelProperty(value = "部门")
    private String deptName;

    @ExcelProperty(value = "培训人数")
    private Integer totalNum;

    @ExcelProperty(value = "完成人数")
    private Integer finishNum;

    @ExcelProperty(value = "未完成人数")
    private Integer unFinishNum;

    @ExcelProperty(value = "完成率(%)")
    private BigDecimal finishRate;

    @ExcelProperty(value = "合格人数")
    private Integer passNum;

    @ExcelProperty(value = "不合格人数")
    private Integer unPassNum;

    @ExcelProperty(value = "合格率(%)")
    private BigDecimal passRate;

    public static List<DeptPassRateStatisticsDownloadDTO> convertList(List<DeptPassRateStatisticsRespVO> itemList, Integer status) {
        List<DeptPassRateStatisticsDownloadDTO> dataList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(itemList)) {
            for (DeptPassRateStatisticsRespVO item: itemList) {
                DeptPassRateStatisticsDownloadDTO deptPassRateStatisticsDownloadDTO = new DeptPassRateStatisticsDownloadDTO();
                deptPassRateStatisticsDownloadDTO.setDeptName(item.getDeptName());
                deptPassRateStatisticsDownloadDTO.setTotalNum(item.getTotalNum());
                deptPassRateStatisticsDownloadDTO.setFinishNum(item.getFinishNum());
                deptPassRateStatisticsDownloadDTO.setUnFinishNum(item.getUnFinishNum());
                deptPassRateStatisticsDownloadDTO.setFinishRate(item.getFinishRate());
                if (!Objects.equals(status,0)) {
                    deptPassRateStatisticsDownloadDTO.setPassNum(item.getPassNum());
                    deptPassRateStatisticsDownloadDTO.setUnPassNum(item.getUnPassNum());
                    deptPassRateStatisticsDownloadDTO.setPassRate(item.getPassRate());
                }
                dataList.add(deptPassRateStatisticsDownloadDTO);
            }
        }
        return dataList;
    }

}
