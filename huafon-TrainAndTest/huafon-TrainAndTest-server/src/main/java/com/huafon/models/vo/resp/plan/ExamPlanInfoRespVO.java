package com.huafon.models.vo.resp.plan;

import com.huafon.models.entity.ExamTrainMaterial;
import com.huafon.models.entity.ExamTrainPlan;
import com.huafon.models.vo.resp.material.ExamMaterialInfoRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> zhang
 * @program: huafon-base
 * @description:
 * @date 2022-05-24 10:33:42
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ExamPlanInfoRespVO", description = "前端请求培训考试计划详情信息")
public class ExamPlanInfoRespVO {

    @ApiModelProperty(value = "计划id")
    private Long id;

    @ApiModelProperty(value = "试卷id")
    private Long paperId;

    @ApiModelProperty(value = "培训计划名称")
    private String trainName;

    @ApiModelProperty(value = "培训有效期 多少个月")
    private Integer validTime;

    @ApiModelProperty(value = "是否参加考试 0 不参与 1 参与")
    private Integer isTest;

    @ApiModelProperty(value = "试卷名称")
    private String paperName;

    @ApiModelProperty(value = "合格分数")
    private Integer passingScore;

    @ApiModelProperty(value = "是否需要资料")
    private Integer isNeed;

    @ApiModelProperty(value = "培训考试重复次数")
    private Integer repeat;

    @ApiModelProperty(value = "签字确认提示")
    private String signatureTip;

    @ApiModelProperty(value = "考试资料信息")
    private List<ExamMaterialInfoRespVO> examMaterialInfos;

    @ApiModelProperty(value = "计划开始时间")
    private Date startTime;

    @ApiModelProperty(value = "计划结束时间")
    private Date endTime;

    @ApiModelProperty(value = "活动结束时间标识（0为结束时间有期限，1为结束时间没有期限，永远不结束）")
    private Integer endMark;

    @ApiModelProperty(value = "课时")
    private Double period;

    @ApiModelProperty(value = "考试结果是否可见 1可见 0不可见")
    private Integer paperResultView;

    @ApiModelProperty(value = "考试结束是否需要订正 1是 0否")
    private Integer needCorrect;

    @ApiModelProperty(value = "培训类型 0:未知 1:承包商 2:访客")
    private Integer type;

    @ApiModelProperty(value = "有效期类型 1:按固定期限 2:按自然年")
    private Integer effectiveType;

    @ApiModelProperty(value = "创建人ID")
    private Long createById;

    @ApiModelProperty(value = "创建人")
    private String createByName;

    @ApiModelProperty(value = "创建部门ID")
    private Long createDepartment;

    @ApiModelProperty(value = "创建部门")
    private String createDepartmentName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改人ID")
    private Long modifyById;

    @ApiModelProperty(value = "修改人")
    private String modifyByName;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    public static ExamPlanInfoRespVO convertToExamPlanInfoRespVO(ExamTrainPlan item) {
        if (item == null) {
            return null;
        }
        ExamPlanInfoRespVO result = new ExamPlanInfoRespVO();
        result.setId(item.getId());
        result.setTrainName(item.getTrainName());
        result.setValidTime(item.getValidTime());
        result.setIsTest(item.getIsTest());
        result.setPassingScore(item.getPassingScore());
        result.setIsNeed(item.getIsNeed());
        result.setRepeat(item.getRepeat());
        result.setSignatureTip(item.getSignatureTip());
        result.setStartTime(item.getStartTime());
        result.setEndTime(item.getEndTime());
        result.setEndMark(item.getEndMark());
        result.setPeriod(item.getPeriod());
        result.setPaperResultView(item.getPaperResultView());
        result.setNeedCorrect(item.getNeedCorrect());
        result.setType(item.getType());
        result.setEffectiveType(item.getEffectiveType());
        result.setCreateById(item.getCreateById());
        result.setCreateByName(item.getCreateByName());
        result.setCreateTime(item.getCreateTime());
        result.setCreateDepartment(item.getCreateDepartment());
        result.setCreateDepartmentName(item.getCreateDepartmentName());
        result.setModifyById(item.getModifyById());
        result.setModifyByName(item.getModifyByName());
        result.setModifyTime(item.getModifyTime());
        return result;
    }

    public void setExamMaterialInfoRespVOS(List<ExamTrainMaterial> examTrainMaterials) {
        List<ExamMaterialInfoRespVO> examMaterialInfoRespVOS = new ArrayList<>();
        examTrainMaterials.forEach(examTrainMaterial -> {
            examMaterialInfoRespVOS.add(ExamMaterialInfoRespVO.convertToExamMaterialInfoRespVO(examTrainMaterial));
        });
        this.examMaterialInfos = examMaterialInfoRespVOS;
    }

}