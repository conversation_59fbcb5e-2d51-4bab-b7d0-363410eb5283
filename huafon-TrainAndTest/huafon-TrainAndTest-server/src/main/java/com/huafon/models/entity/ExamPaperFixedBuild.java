package com.huafon.models.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @describe:
 * @date 2022/5/17 13:39
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("hf_exam_paper_fixed_build")
@ApiModel(value = "ExamPaperFixedBuild对象", description = "")
@Accessors(chain = true)
public class ExamPaperFixedBuild implements Serializable {

    private static final long serialVersionUID = 8140583633131020758L;

    @ApiModelProperty(value = "自增主键 ")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "试卷id 关联hf_exam_paper.id ")
    private Long examPaperId;

    @ApiModelProperty(value = "题型总分 ")
    private BigDecimal questionTypeScore;

    @ApiModelProperty(value = "SINGLE_CHOICE:单选题 MULTIPLE_CHOICE.多选题 TRUE_FALSE.判断题 GAP_FILLING.填空题 SHORT_ANSWER.简答题 ")
    private String questionCode;

    @ApiModelProperty(value = "创建时间 ")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "删除状态 0正常，1删除")
    private Integer isDel;
}
