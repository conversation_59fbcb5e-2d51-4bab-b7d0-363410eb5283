package com.huafon.models.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/4/4 11:33:01
 * @description
 */
@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("hf_exam_hse_admin")
@ApiModel(value="ExamHseAdmin对象", description="ExamHseAdmin对象")
public class ExamHseAdmin implements Serializable {

    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "入司培训公司级json")
    private String companyConfig;

    @ApiModelProperty(value = "入司培训部门级json")
    private String deptConfig;

    @ApiModelProperty(value = "转岗培训部门级json")
    private String transferDeptConfig;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建者用户id")
    private Long createBy;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "修改人id")
    private Long modifyBy;

    @ApiModelProperty(value = "修改人")
    private String modifyByName;

    @ApiModelProperty(value = "租户id")
    private Integer tenantId;

    @ApiModelProperty(value = "删除状态 0:正常，1:删除")
    private Integer isDel;

}
