package com.huafon.models.vo.resp.activity;

import com.huafon.models.vo.resp.question.ExamQuestionItemInfoRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 试卷答题返回VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Data
@ApiModel(value = "PaperAnsweringSituationRespVO", description = "试卷答题情况返回VO")
public class PaperAnsweringQuestionRespVO {

    @ApiModelProperty("题目id")
    private Long id;

    @ApiModelProperty("题干")
    private String shortTitle;

    @ApiModelProperty(value = "选项")
    private List<ExamQuestionItemInfoRespVO> examQuestionItem;

    @ApiModelProperty(value = "正确答案")
    private List<String> correct;

    @ApiModelProperty(value = "答题解析")
    private String analysis;

    @ApiModelProperty(value = "答题人次")
    private Integer total;

    @ApiModelProperty(value = "正确人体")
    private Integer right;

    @ApiModelProperty(value = "正确率")
    private BigDecimal rightRate;

    public static PaperAnsweringQuestionRespVO init() {
        PaperAnsweringQuestionRespVO paperAnsweringQuestionRespVO = new PaperAnsweringQuestionRespVO();
        paperAnsweringQuestionRespVO.setTotal(0);
        paperAnsweringQuestionRespVO.setRight(0);
        paperAnsweringQuestionRespVO.setRightRate(BigDecimal.ZERO);
        return paperAnsweringQuestionRespVO;
    }

    public void calculateRightRate() {
        if (right >= 0 && total > 0) {
            rightRate = new BigDecimal(right*100).divide(new BigDecimal(total), 2, BigDecimal.ROUND_HALF_UP);
        }
    }

}
