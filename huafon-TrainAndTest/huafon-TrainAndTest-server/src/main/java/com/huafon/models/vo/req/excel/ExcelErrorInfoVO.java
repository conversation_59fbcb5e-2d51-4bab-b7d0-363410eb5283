package com.huafon.models.vo.req.excel;

import com.huafon.service.support.ExcelValidationError;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> zhang
 * @program: huafon-base
 * @description:
 * @date 2022-10-25 18:56:44
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "Excel错误信息下载")
public class ExcelErrorInfoVO<T> {
    @ApiModelProperty(value = "错误信息")
    @NotNull(message = "错误信息不能为NULL")
    private List<ExcelValidationError<T>> errorInfos;
}