package com.huafon.models.vo.req.my;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 我的课程取消请求VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-05
 */
@Data
@ApiModel(value = "MyCourseCancelReqVO", description = "我的课程取消请求VO")
public class MyCourseCancelReqVO {

    @ApiModelProperty(name = "活动id", required = true)
    @NotNull(message = "活动id不能为空")
    private Long activityId;

    @ApiModelProperty(name = "取消原因", required = true)
    @NotBlank(message = "取消原因不能为空")
    private String cancelReason;

}
