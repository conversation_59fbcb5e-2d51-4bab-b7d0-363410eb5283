package com.huafon.models.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <p>
 * 证件类型枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-24
 */
@Getter
public enum IdTypeEnum {

    idCard("idCard","身份证"),
    passport("passport","护照"),
    other("other","其他");

    IdTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;

    private String name;

    public static String getCodeByName(String name) {
        for (IdTypeEnum value : IdTypeEnum.values()) {
            if (Objects.equals(value.getName(),name)) {
                return value.getCode();
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        for (IdTypeEnum value : IdTypeEnum.values()) {
            if (Objects.equals(value.getCode(),code)) {
                return value.getName();
            }
        }
        return null;
    }

}
