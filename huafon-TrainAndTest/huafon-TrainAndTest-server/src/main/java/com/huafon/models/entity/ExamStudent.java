package com.huafon.models.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("hf_exam_student")
@ApiModel(value = "ExamStudent对象", description = "")
public class ExamStudent implements Serializable {

    private static final long serialVersionUID = 1858408867563535479L;

    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "身份证号码")
    private String idNumber;

    @ApiModelProperty(value = "手机号码")
    private String mobile;

    @ApiModelProperty(value = "职员id")
    private Long staffId;

    @ApiModelProperty(value = "职员名称")
    private String staffName;

    @ApiModelProperty(value = "系统全局的用户id")
    private Long userId;

    @ApiModelProperty(value = "职员工号")
    private String workNum;

    @ApiModelProperty(value = "性别 1 男 2 女")
    private Integer gender;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "头像照片")
    private String photo;

    @ApiModelProperty(value = "1为外部培训类型 2为内部培训类型")
    private Integer type;

    @ApiModelProperty(value = "租户id")
    private Integer tenantId;

    @ApiModelProperty(value = "创建者用户id")
    private Long createBy;

    @ApiModelProperty(value = "创建者名称")
    private String createByName;

    @ApiModelProperty(value = "创建者部门id")
    private Long createDepartment;

    @ApiModelProperty(value = "创建者部门名称")
    private String createDepartmentName;

    @ApiModelProperty(value = "创建者用户号码")
    private String createUserMobile;

    @ApiModelProperty(value = "修改者id")
    private Long modifyBy;

    @ApiModelProperty(value = "修改者名称")
    private String modifyByName;

    @ApiModelProperty(value = "修改者部门id")
    private Long modifyDepartment;

    @ApiModelProperty(value = "修改者部门名称")
    private String modifyDepartmentName;

    @ApiModelProperty(value = "修改者用户手机号码")
    private String modifyUserMobile;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "删除状态 0正常，1删除")
    private Integer isDel;

    @ApiModelProperty(value = "入司培训 0不参加 1参加")
    private Integer isJoinTrain;

    @ApiModelProperty(value = "入司培训类型 COMPANY:公司级 DEPARTMENT:部门级 TEAM:班组级")
    private String joinTrainSubject;

    @ApiModelProperty(value = "转岗培训 0不参加 1参加")
    private Integer isTransferTrain;

    @ApiModelProperty(value = "转岗培训类型 DEPARTMENT:部门级 TEAM:班组级")
    private String transferTrainSubject;

    @ApiModelProperty(value = "正式学员 0否 1是")
    private Integer isFormal;

    @ApiModelProperty(value = "入司培训班组长待分配状态 0否 1是")
    private Integer joinTrainAllocated;

    @ApiModelProperty(value = "转岗培训班组长待分配状态 0否 1是")
    private Integer transferTrainAllocated;

    @ApiModelProperty(value = "本地三级培训卡附件")
    private String localAttachment;

    @ApiModelProperty(value = "入司培训时间")
    private Date joinTrainTime;

    @ApiModelProperty(value = "转岗培训时间")
    private Date transferTrainTime;

    @ApiModelProperty(value = "转岗前部门岗位")
    private String transferDeptPostBefore;

    @ApiModelProperty(value = "转岗后部门岗位")
    private String transferDeptPostAfter;

    @TableField(exist = false)
    private BigDecimal period;

    @TableField(exist = false)
    private BigDecimal credit;

}
