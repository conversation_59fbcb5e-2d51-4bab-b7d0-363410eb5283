package com.huafon.models.vo.req.paper.fixed;

import com.alibaba.fastjson.JSONArray;
import com.huafon.common.config.TenantContext;
import com.huafon.models.entity.ExamPaper;
import com.huafon.models.enums.PaperTypeEnum;
import com.huafon.models.enums.TrainTypeEnum;
import com.huafon.models.vo.req.paper.ExamPaperAddReqVO;
import com.huafon.support.config.UserContext;
import com.huafon.utils.UserInfoUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> zhang
 * @program: huafon-base
 * @description: 前端请求添加固定试卷
 * @date 2022-05-18 19:06:12
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ExamFixedPaperAddReqVO", description = "前端请求添加固定试卷")
public class ExamFixedPaperAddReqVO extends ExamPaperAddReqVO {

    @ApiModelProperty(value = "固定题目")
    @NotNull(message = "固定题目不能为空")
    private List<ExamFixedPaperBuildAddReqVO> examFixedPaperBuilds;

    public static ExamPaper convertToExamPaper(ExamFixedPaperAddReqVO item) {
        if (item == null) {
            return null;
        }
        ExamPaper result = new ExamPaper();
        result.setPaperName(item.getPaperName());
        result.setSubjectCode(item.getSubjectCode());
        result.setSubjectName(item.getSubjectName());
        result.setDuration(item.getDuration());
        result.setPaperType(PaperTypeEnum.Fixed.getCode());
        result.setTotalPoints(item.getTotalPoints());
        result.setSubjectId(item.getSubjectId());
        result.setType(Objects.isNull(item.getType()) ? TrainTypeEnum.external.getType() : item.getType());
        result.setClassification(item.getClassification());
        result.setOpen(item.getOpen());
        result.setDraft(item.getDraft());
        if (!CollectionUtils.isEmpty(item.getQuestionSubjectId())) {
            result.setQuestionSubjectId(JSONArray.toJSONString(item.getQuestionSubjectId()));
        } else {
            result.setQuestionSubjectId(Strings.EMPTY);
        }
        // 基础信息
        if (Objects.nonNull(item.getCreateById())) {
            result.setCreateById(item.getCreateById());
            result.setCreateByName(item.getCreateByName());
        } else {
            result.setCreateById(UserContext.getId());
            result.setCreateByName(UserContext.get().getName());
        }
        if (Objects.nonNull(item.getCreateDepartment())) {
            result.setCreateDepartment(item.getCreateDepartment());
            result.setCreateDepartmentName(item.getCreateDepartmentName());
        } else {
            result.setCreateDepartment(UserInfoUtils.getDepartmentIdFromLoginInfo().longValue());
            result.setCreateDepartmentName(UserInfoUtils.getDepartmentNameFromLoginInfo());
        }
        result.setCreateTime(new Date());
        result.setModifyById(UserContext.getId());
        result.setModifyByName(UserContext.get().getName());
        result.setModifyTime(new Date());
        result.setTenantId(TenantContext.getOne());
        return result;
    }

}