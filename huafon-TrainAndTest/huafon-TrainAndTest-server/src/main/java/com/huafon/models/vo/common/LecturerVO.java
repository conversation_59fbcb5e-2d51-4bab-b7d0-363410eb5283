package com.huafon.models.vo.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 讲师信息VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "LecturerVO", description = "讲师信息VO")
public class LecturerVO {

    @ApiModelProperty("讲师id")
    private Long lecturerId;

    @ApiModelProperty("讲师名称")
    private String lecturerName;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("讲师类型")
    private String lecturerType;

}
