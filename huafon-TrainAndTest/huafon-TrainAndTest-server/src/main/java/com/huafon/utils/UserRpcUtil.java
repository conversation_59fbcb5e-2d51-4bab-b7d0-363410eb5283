package com.huafon.utils;

import cn.hutool.core.util.StrUtil;
import com.huafon.portal.api.dto.UserDto;
import com.huafon.portal.api.dto.query.UserQuery;
import com.huafon.portal.api.enums.UserSearchKeyType;
import com.huafon.portal.api.service.UserRpcV2Service;
import com.huafon.support.exceptions.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/13 14:22:59
 * @description
 */
@Component
@Slf4j
public class UserRpcUtil {
    @DubboReference
    private UserRpcV2Service userRpcV2Service;

    public UserDto getById(Integer userId, Integer tenantId) {
        UserDto userDto = null;
        try {
            userDto = userRpcV2Service.getById(userId, tenantId);
        }catch (Exception e) {
            throw new ServiceException("portal服务RPC调用异常,userRpcV2Service.getById");
        }
        return userDto;
    }

    public Map<Integer, UserDto> getMapByIds(List<Integer> userIds, Integer tenantId) {
        Map<Integer, UserDto> map = new HashMap<>();
        try {
            map = userRpcV2Service.queryMappingByIds(userIds, tenantId);
        }catch (Exception e) {
            throw new ServiceException("portal服务RPC调用异常,userRpcV2Service.queryMappingByIds");
        }
        return map;
    }

    public List<Integer> getUserIdsByDepartmentId(Integer departmentId, Integer tenantId) {
        List<Integer> userIds = new ArrayList<>();
        try {
            List<UserDto> users = userRpcV2Service.getByDepartmentId(departmentId, true, tenantId);
            if (!CollectionUtils.isEmpty(users)) {
                userIds = users.stream().map(UserDto::getUserId).collect(Collectors.toList());
            }
        }catch (Exception e) {
            throw new ServiceException("portal服务RPC调用异常,userRpcV2Service.getByDepartmentId");
        }
        return userIds;
    }

    public List<String> getDeptPostById(Integer userId, Integer tenantId) {
        List<String> deptPost = new ArrayList<>();
        try {
            UserDto userDto = userRpcV2Service.getById(userId, tenantId);
            if (Objects.nonNull(userDto)) {
                deptPost = userDto.getDepartPostInfo();
            }
        }catch (Exception e) {
            throw new ServiceException("portal服务RPC调用异常,userRpcV2Service.getById");
        }
        return deptPost;
    }

    public List<UserDto> getByDepartmentId(Integer departmentId, boolean includeSub, Integer tenantId) {
        List<UserDto> userDtos = new ArrayList<>();
        try {
            userDtos = userRpcV2Service.getByDepartmentId(departmentId, includeSub, tenantId);
        }catch (Exception e) {
            throw new ServiceException("portal服务RPC调用异常,userRpcV2Service.getByDepartmentId");
        }
        return userDtos;
    }

    public List<UserDto> getByPostIds(Collection<Integer> postIds, Integer tenantId) {
        List<UserDto> userDtos = new ArrayList<>();
        try {
            userDtos = userRpcV2Service.getByPostIds(postIds, tenantId);
        }catch (Exception e) {
            throw new ServiceException("portal服务RPC调用异常,userRpcV2Service.getByDepartmentId");
        }
        return userDtos;
    }

    public List<UserDto> getByUsernames(List<String> usernames, Integer tenantId) {
        List<UserDto> userDtos = new ArrayList<>();
        try {
            userDtos = userRpcV2Service.getByUsernames(usernames, tenantId);
        }catch (Exception e) {
            throw new ServiceException("portal服务RPC调用异常,userRpcV2Service.getByUsernames");
        }
        return userDtos;
    }


    public UserDto getByName(String name, Integer tenantId) {
        UserDto userDtos = null;
        try {
            UserQuery userQuery = new UserQuery();
            userQuery.setNameLike(name);
            userQuery.setTenantId(tenantId);
            List<Integer> userIds = userRpcV2Service.queryByDeptAndPostAndNameLike(userQuery);
            List<UserDto> userList = userRpcV2Service.getByUserIds(userIds);
            for (UserDto userDto : userList) {
                if (StrUtil.equals(name,userDto.getName())){
                    return userDto;
                }
            }
        }catch (Exception e) {
            throw new ServiceException("portal服务RPC调用异常,userRpcV2Service.getByUsernames");
        }
        return userDtos;
    }

    public List<Long> getByLevels(Long userId, Integer tenantId, Collection<String> levels) {
        List<Long> userIds = new ArrayList<>();
        try {
            Set<UserDto> userDtoSet = userRpcV2Service.getByLevels(userId.intValue(), tenantId, levels);
            userIds = userDtoSet.stream().map(UserDto::getUserId).map(Integer::longValue).collect(Collectors.toList());
        }catch (Exception e) {
            throw new ServiceException("portal服务RPC调用异常,userRpcV2Service.getByLevels");
        }
        return userIds;
    }

    public UserDto queryByIdNumber(String idNumber) {
        UserDto userDto = null;
        try {
            userDto = userRpcV2Service.queryByIdNumber(idNumber);
        }catch (Exception e) {
            throw new ServiceException("portal服务RPC调用异常,userRpcV2Service.queryByIdNumber");
        }
        return userDto;
    }

    public List<UserDto> getByIds(List<Integer> userIds, Integer tenantId) {
        List<UserDto> userDtos = new ArrayList<>();
        try {
            userDtos = userRpcV2Service.getByIds(userIds, tenantId);
        }catch (Exception e) {
            throw new ServiceException("portal服务RPC调用异常,userRpcV2Service.getByIds");
        }
        return userDtos;
    }

    public List<UserDto> getByWorkNum(String workNum, Integer tenantId) {
        List<UserDto> userDtos = new ArrayList<>();
        try {
            userDtos = userRpcV2Service.getByWorkNum(workNum,tenantId);
        }catch (Exception e) {
            throw new ServiceException("portal服务RPC调用异常,userRpcV2Service.getByWorkNum");
        }
        return userDtos;
    }

    public List<Integer> getUserIdsByPostId(Integer postId, Integer tenantId) {
        List<Integer> userIds = new ArrayList<>();
        try {
            List<UserDto> userDtos = userRpcV2Service.getByPostId(postId,tenantId);
            if (!CollectionUtils.isEmpty(userDtos)) {
                userIds = userDtos.stream().map(UserDto::getUserId).collect(Collectors.toList());
            }
        }catch (Exception e) {
            throw new ServiceException("portal服务RPC调用异常,userRpcV2Service.getByPostId");
        }
        return userIds;
    }

    public List<Integer> fuzzyQueryByNameAndWorkNum(String searchKey, Integer tenantId) {
        List<Integer> userIds = new ArrayList<>();
        try {
            userIds = userRpcV2Service.fuzzyQueryIdsWithTenantId(searchKey, Arrays.asList(UserSearchKeyType.NAME,UserSearchKeyType.WORK_NUM), tenantId);
        }catch (Exception e) {
            throw new ServiceException("portal服务RPC调用异常,userRpcV2Service.fuzzyQueryIdsWithTenantId");
        }
        return userIds;
    }

}
