package com.huafon.utils.excel;

import java.awt.*;

/**
 * <AUTHOR>
 * @ClassName com.huafon.utils.excel.ExcelStyleConstant
 * @Description  Excel导入样式定义
 * @createTime 2023年11月08日 10:08:00
 */
public class ExcelStyleConstant {

    /**
     * 标题字体
     */
    public static final String TITLE_FONT = "宋体";
    /**
     * 标题字号
     */
    public static final short TITLE_FONT_HEIGHT_IN_POINTS = 14;
    /**
     * 标题加粗
     */
    public static final boolean TITLE_BOLD = true;

    /**
     * 标题行高（磅值）
     */
    public static final int TITLE_HEIGHT_IN_POINTS = 20;

    /**
     * 标题底纹色值
     */
    public static final String HEX_COLOR = "BDD7EE";

    /******************************************************************/

    /**
     * 内容字体
     */
    public static final String CONTENT_FONT = "宋体";

    /**
     * 内容字号
     */
    public static final short CONTENT_FONT_HEIGHT_IN_POINTS = 12;

    /**
     * 内容行高（磅值）
     */
    public static final int CONTENT_HEIGHT_IN_POINTS = 18;

    /******************************************************************/

    /**
     * 默认单行展示宽度
     */
    public static final int DEFAULT_WIDTH_SINGLE_LINE = 32;

    /**
     * 默认多行展示宽度
     */
    public static final int DEFAULT_WIDTH_MULTI_LINE = 60;




    /**
     * 根据6为16进制RGB色值转换10进制色值：如”BDD7EE“ -> r:89, g:215, b:238
     * @param hexColor
     * @return
     */
    public static Color getColor(String hexColor){
        // 转换16进制为10进制rbg色号
        int r = Integer.parseInt(hexColor.substring(0, 2), 16);
        int g = Integer.parseInt(hexColor.substring(2, 4), 16);
        int b = Integer.parseInt(hexColor.substring(4, 6), 16);
        return new java.awt.Color(r, g, b);
    }
}
