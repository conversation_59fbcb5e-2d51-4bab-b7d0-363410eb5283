package com.huafon.utils;

import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
public class BigDecimalUtils {

    public static BigDecimal multiply(Number ...numbers) {
        BigDecimal bigDecimal = BigDecimal.ONE;
        for (Number number: numbers) {
            bigDecimal = bigDecimal.multiply(new BigDecimal(number.toString()));
        }
        return bigDecimal;
    }

}
