<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huafon.dao.mapper.ExamAnnualPlanDetailMapper">

    <select id="queryByPlanIds" resultType="com.huafon.models.vo.resp.annual.ExamAnnualPlanDetailRespVO">
        SELECT
            *
        FROM hf_exam_annual_plan_detail
        WHERE is_del = 0
          AND annual_plan_id IN
        <foreach collection="planIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY sort ASC
    </select>


</mapper>