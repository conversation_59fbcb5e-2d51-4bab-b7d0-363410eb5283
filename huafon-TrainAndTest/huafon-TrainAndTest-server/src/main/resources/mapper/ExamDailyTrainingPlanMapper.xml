<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huafon.dao.mapper.ExamDailyTrainingPlanMapper">

    <select id="page" resultType="com.huafon.models.vo.resp.dailyTraining.ExamDailyTrainingPlanPageRespVO">
        SELECT
            p1.id,
            p1.code,
            p1.name,
            p2.paper_name,
            p1.create_by,
            p1.create_by_name,
            p1.create_department_name,
            p1.create_time,
            p1.status,
            p1.executor
        FROM hf_exam_daily_training_plan p1
        LEFT JOIN hf_exam_paper p2 ON p1.paper_id = p2.id
        WHERE p1.is_del = 0
        <if test="reqVO.searchKey != null and reqVO.searchKey != ''">
            AND (p1.code LIKE CONCAT('%',#{reqVO.searchKey},'%')
                OR p1.name LIKE CONCAT('%',#{reqVO.searchKey},'%')
                OR p2.paper_name LIKE CONCAT('%',#{reqVO.searchKey},'%')
                OR p1.create_by_name LIKE CONCAT('%',#{reqVO.searchKey},'%'))
        </if>
        <if test="reqVO.createDepartmentId != null">
            AND p1.create_department_id = #{reqVO.createDepartmentId}
        </if>
        <if test="reqVO.status != null">
            AND p1.status = #{reqVO.status}
        </if>
        <if test="reqVO.tenantId != null">
            AND p1.tenant_id = #{reqVO.tenantId}
        </if>
        <choose>
            <when test="reqVO.orders != null and reqVO.orders.size() > 0">
                ORDER BY
                <foreach collection="reqVO.orders" separator="," item="item">
                    p1.${item.column} <choose><when test="item.asc">ASC</when><otherwise>DESC</otherwise></choose>
                </foreach>
            </when>
            <otherwise>
                ORDER BY p1.create_time DESC
            </otherwise>
        </choose>
    </select>

    <select id="count" resultType="com.huafon.models.vo.resp.dailyTraining.ExamDailyTrainingPlanCountRespVO">
        SELECT
            COUNT(0) AS total,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) AS enable,
            SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) AS disable
        FROM hf_exam_daily_training_plan
        WHERE is_del = 0
          AND tenant_id = #{tenantId}
    </select>

</mapper>
