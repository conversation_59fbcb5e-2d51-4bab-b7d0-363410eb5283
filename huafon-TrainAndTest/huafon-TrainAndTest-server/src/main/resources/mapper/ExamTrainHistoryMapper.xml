<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huafon.dao.mapper.ExamTrainHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huafon.models.entity.ExamTrainHistory">
        <id column="id" property="id"/>
        <result column="train_name" property="trainName"/>
        <result column="exam_train_plan_id" property="examTrainPlanId"/>
        <result column="name" property="name"/>
        <result column="organization" property="organization"/>
        <result column="phone" property="phone"/>
        <result column="id_card" property="idCard"/>
        <result column="is_test" property="isTest"/>
        <result column="passing_score" property="passingScore"/>
        <result column="exam_score" property="examScore"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="is_del" property="isDel"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="take_effect_time" property="takeEffectTime"/>
        <result column="dead_time" property="deadTime"/>
        <result column="type" property="type"/>
        <result column="is_pass" property="isPass"/>
        <result column="is_valid" property="isValid"/>
    </resultMap>

    <select id="pastDueList" resultMap="BaseResultMap">
        SELECT
            *
        FROM
            hf_exam_train_history
        WHERE dead_time <![CDATA[<]]> now()
          AND is_valid = 1
    </select>

    <update id="pastDue" parameterType="java.util.List">
        UPDATE hf_exam_train_history
        SET is_valid = 0
        WHERE id IN
          <foreach collection="ids" item="item" open="(" separator="," close=")">
              #{item}
          </foreach>
          AND is_valid = 1
    </update>

    <select id="queryHisListPage" resultMap="queryHisListPage">
        SELECT
        id,
        train_name,
        name,
        organization,
        phone,
        UPPER ( id_card ) AS id_card,
        is_test,
        exam_score,
        take_effect_time,
        dead_time,
        type,
        is_pass,
        is_valid,
        signature,
        modify_time,
        tenant_id,
        period,
        ( SELECT COUNT ( * ) FROM hf_exam_train_record WHERE his_id = hf_exam_train_history.id ) AS test_num,
        exam_train_plan_id,
        commitment,
        id_type_code,
        id_type_name
        FROM
        hf_exam_train_history
        WHERE
        is_del = 0
        AND ( signature IS NOT NULL OR signature != '' )
        <if test="examPlanHisListReqDTO.trainName != null and examPlanHisListReqDTO.trainName != ''">
            AND train_name LIKE concat('%',#{examPlanHisListReqDTO.trainName},'%')
        </if>
        <if test="examPlanHisListReqDTO.isTest != null ">
            AND is_test = #{examPlanHisListReqDTO.isTest}
        </if>
        <if test="examPlanHisListReqDTO.isPass != null ">
            AND is_pass = #{examPlanHisListReqDTO.isPass}
        </if>
        <if test="examPlanHisListReqDTO.condition != null and examPlanHisListReqDTO.condition != ''">
            AND ("name" LIKE concat('%',#{examPlanHisListReqDTO.condition},'%')
            OR phone LIKE concat('%',#{examPlanHisListReqDTO.condition},'%')
            OR id_card LIKE concat('%',#{examPlanHisListReqDTO.condition},'%'))
        </if>
        <if test="examPlanHisListReqDTO.tenantIds != null and examPlanHisListReqDTO.tenantIds.size() > 0">
            AND tenant_id in
            <foreach collection="examPlanHisListReqDTO.tenantIds" item="item" index="index" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="examPlanHisListReqDTO.takeEffectTimeBegin != null">
            AND take_effect_time &gt;= #{examPlanHisListReqDTO.takeEffectTimeBegin}
        </if>
        <if test="examPlanHisListReqDTO.takeEffectTimeEnd != null">
            AND take_effect_time &lt;= #{examPlanHisListReqDTO.takeEffectTimeEnd}
        </if>
        <if test="examPlanHisListReqDTO.type != null">
            AND type = #{examPlanHisListReqDTO.type}
        </if>
        <choose>
            <when test="examPlanHisListReqDTO.orders != null and examPlanHisListReqDTO.orders.size>0">
                ORDER BY
                <foreach collection="examPlanHisListReqDTO.orders" item="item" open=" " close=" " separator=",">
                    <choose>
                        <when test="item.column != null and item.column !='' and item.asc == true">
                            ${item.column} ASC
                        </when>
                        <when test="item.column != null and item.column !='' and item.asc == false">
                            ${item.column} DESC
                        </when>
                        <otherwise>
                            create_time DESC
                        </otherwise>
                    </choose>
                </foreach>
            </when>
            <otherwise>ORDER BY create_time DESC</otherwise>
        </choose>
    </select>

    <select id="getExamTrainPlanIds" resultType="java.lang.Long">
        SELECT DISTINCT exam_train_plan_id
        FROM (SELECT exam_train_plan_id, create_time
              FROM hf_exam_train_history
              WHERE is_del = 0
              ORDER BY create_time DESC) AS A
    </select>

    <resultMap id="queryHisListPage" type="com.huafon.models.vo.resp.plan.ExamPlanHisListRespVO">
        <id column="id" property="id"/>
        <result column="train_name" property="trainName"/>
        <result column="name" property="name"/>
        <result column="organization" property="organization"/>
        <result column="phone" property="phone"/>
        <result column="id_card" property="idCard"/>
        <result column="is_test" property="isTest"/>
        <result column="exam_score" property="examScore"/>
        <result column="take_effect_time" property="takeEffectTime"/>
        <result column="dead_time" property="deadTime"/>
        <result column="type" property="type"/>
        <result column="is_pass" property="isPass"/>
        <result column="is_valid" property="isValid"/>
        <result column="signature" property="signature"/>
        <result column="test_num" property="testNum"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="period" property="period"/>
        <result column="exam_train_plan_id" property="examTrainPlanId"/>
        <result column="commitment" property="commitment"/>
        <result column="id_type_code" property="idTypeCode"/>
        <result column="id_type_name" property="idTypeName"/>
    </resultMap>

    <select id="readTrainState" resultMap="BaseResultMap">
        SELECT
            *
        FROM
            (
                SELECT
                    *,
                    ROW_NUMBER() OVER (PARTITION BY id_card, exam_train_plan_id ORDER BY create_time DESC)
                FROM
                    hf_exam_train_history
                WHERE is_del = 0
                  AND signature IS NOT NULL
                  AND tenant_id IN
                <foreach collection="tenantIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                <if test="types != null and types.size() > 0">
                    AND type IN
                    <foreach collection="types" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            ) T
        WHERE ROW_NUMBER = 1
          AND is_pass = 1
          AND is_valid = 1
          AND id_card IN
        <foreach collection="idCards" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectNearOverdue" resultMap="BaseResultMap">
        SELECT
            *
        FROM hf_exam_train_history
        WHERE is_del = 0
          AND type = 1
          AND is_valid = 1
          AND to_char(dead_time,'yyyy-MM-dd') = to_char((now() + INTERVAL '1 day' * #{day}),'yyyy-MM-dd')
    </select>

    <select id="selectEffectiveIdCards" resultType="java.lang.String">
        SELECT
            DISTINCT id_card
        FROM hf_exam_train_history
        WHERE is_del = 0
          AND type = 1
          AND is_pass = 1
          AND is_valid = 1
          AND train_plan_id = #{trainPlanId}
          AND id_card IN
        <foreach collection="idCards" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
          AND to_char(dead_time,'yyyy-MM-dd') &gt; to_char(#{expireDate},'yyyy-MM-dd')
          AND tenant_id = #{tenantId}
    </select>

</mapper>
