<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huafon.dao.mapper.ExamLearningAnnouncementMapper">

    <select id="page" resultType="com.huafon.models.vo.resp.learningAnnouncement.ExamLearningAnnouncementPageRespVO">
        SELECT
            *
        FROM hf_exam_learning_announcement
        WHERE is_del = 0
        <if test="reqVO.searchKey != null and reqVO.searchKey != ''">
            AND (code LIKE CONCAT('%',#{reqVO.searchKey},'%')
                OR title LIKE CONCAT('%',#{reqVO.searchKey},'%'))
        </if>
        <if test="reqVO.createTimeStart != null">
            AND create_time &gt;= #{reqVO.createTimeStart}
        </if>
        <if test="reqVO.createTimeEnd != null">
            AND create_time &lt;= #{reqVO.createTimeEnd}
        </if>
        <if test="reqVO.isDraft != null">
            AND is_draft = #{reqVO.isDraft}
        </if>
        <if test="reqVO.status != null and reqVO.status == 0">
            AND limit_time &lt; now()
        </if>
        <if test="reqVO.status != null and reqVO.status == 1">
            AND (limit_time IS NULL OR limit_time &gt; now())
        </if>
        <if test="reqVO.createBy != null">
            AND create_by = #{reqVO.createBy}
        </if>
        <if test="reqVO.tenantId != null">
            AND tenant_id = #{reqVO.tenantId}
        </if>
        <choose>
            <when test="reqVO.orders != null and reqVO.orders.size() > 0">
                ORDER BY
                <foreach collection="reqVO.orders" separator="," item="item">
                    ${item.column} <choose><when test="item.asc">ASC</when><otherwise>DESC</otherwise></choose>
                </foreach>
            </when>
            <otherwise>
                ORDER BY create_time DESC
            </otherwise>
        </choose>
    </select>
    
    <update id="batchDelete">
        UPDATE 
            hf_exam_learning_announcement 
        SET modify_by = #{modifyBy}, 
            modify_by_name = #{modifyByName}, 
            modify_time = #{modifyTime}, 
            is_del = 1 
        WHERE is_del = 0
          AND id IN
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="countNum" resultType="com.huafon.models.vo.resp.learningAnnouncement.ExamLearningAnnouncementCountRespVO">
        SELECT
            SUM(CASE WHEN (limit_time IS NULL OR limit_time &gt; now()) AND is_draft = false THEN 1 END) as current,
            SUM(CASE WHEN limit_time &lt; now() AND is_draft = false THEN 1 END) as history,
            SUM(CASE WHEN create_by = #{createBy} AND is_draft = true THEN 1 END) as draft
        FROM hf_exam_learning_announcement
        WHERE is_del = 0
          AND tenant_id = #{tenantId}
    </select>

</mapper>
