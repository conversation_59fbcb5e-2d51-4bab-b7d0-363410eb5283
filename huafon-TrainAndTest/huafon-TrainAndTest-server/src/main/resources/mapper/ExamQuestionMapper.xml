<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huafon.dao.mapper.ExamQuestionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huafon.models.entity.ExamQuestion">
        <id column="id" property="id"/>
        <result column="question_code" property="questionCode"/>
        <result column="subject_code" property="subjectCode"/>
        <result column="short_title" property="shortTitle"/>
        <result column="difficult" property="difficult"/>
        <result column="correct" property="correct"/>
        <result column="subject_id" property="subjectId"/>
        <result column="type" property="type"/>
        <result column="open" property="open"/>
        <result column="analysis" property="analysis"/>
        <result column="code" property="code"/>
        <result column="create_by_id" property="createById"/>
        <result column="create_by_name" property="createByName"/>
        <result column="create_department" property="createDepartment"/>
        <result column="create_department_name" property="createDepartmentName"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_by_id" property="modifyById"/>
        <result column="modify_by_name" property="modifyByName"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="is_del" property="isDel"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, question_code, subject_code, short_title, difficult, correct, subject_id, type, open, analysis, code, create_by_id, create_by_name, create_department, create_department_name, create_time, modify_by_id, modify_by_name, modify_time, tenant_id, is_del
    </sql>

    <select id="selectRandomExamQuestion" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hf_exam_question
        WHERE question_code = #{questionCode}
          AND subject_code = #{subjectCode}
          AND is_del = 0 and tenant_id=#{tenantId}
        ORDER BY random() LIMIT #{questionNum};
    </select>

    <select id="selectRandomExamQuestionDifficult" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hf_exam_question
        WHERE difficult = #{difficult}
          AND question_code = #{questionCode}
          AND subject_code = #{subjectCode}
          AND tenant_id=#{tenantId}
          AND is_del = 0
        ORDER BY random() LIMIT #{questionNum};
    </select>

    <select id="getQuestionListPage" resultType="com.huafon.models.vo.resp.question.ExamQuestionListRespVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hf_exam_question
        WHERE is_del = 0
            AND type = #{examQuestionListReqVO.type}
        <if test="examQuestionListReqVO.searchKey != null and examQuestionListReqVO.searchKey != ''">
            AND (short_title LIKE concat('%',#{examQuestionListReqVO.searchKey},'%')
                OR code LIKE concat('%',#{examQuestionListReqVO.searchKey},'%'))
        </if>
        <if test="examQuestionListReqVO.subjectCode != null and examQuestionListReqVO.subjectCode != ''">
            AND subject_code = #{examQuestionListReqVO.subjectCode}
        </if>
        <if test="examQuestionListReqVO.questionCode != null and examQuestionListReqVO.questionCode != ''">
            AND question_code = #{examQuestionListReqVO.questionCode}
        </if>
        <if test="examQuestionListReqVO.difficult != null and examQuestionListReqVO.difficult != ''">
            AND difficult = #{examQuestionListReqVO.difficult}
        </if>
        <if test="examQuestionListReqVO.subjectIds != null and examQuestionListReqVO.subjectIds.size() > 0">
            AND subject_id IN
            <foreach collection="examQuestionListReqVO.subjectIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="examQuestionListReqVO.createById != null">
            AND ((open = 1) OR (open = 0 AND create_by_id = #{examQuestionListReqVO.createById}))
        </if>
        <if test="examQuestionListReqVO.createDepartments != null and examQuestionListReqVO.createDepartments.size() > 0">
            AND create_department IN
            <foreach collection="examQuestionListReqVO.createDepartments" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="examQuestionListReqVO.questionSubjectIds != null and examQuestionListReqVO.questionSubjectIds.size() > 0">
            AND subject_id IN
            <foreach collection="examQuestionListReqVO.questionSubjectIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="examQuestionListReqVO.tenantId != null">
            AND tenant_id = #{examQuestionListReqVO.tenantId}
        </if>
        <if test="examQuestionListReqVO.open != null">
            AND open = #{examQuestionListReqVO.open}
        </if>
        <choose>
            <when test="examQuestionListReqVO.orders != null and examQuestionListReqVO.orders.size>0">
                ORDER BY
                <foreach collection="examQuestionListReqVO.orders" item="item" open=" " close=" " separator=",">
                    <choose>
                        <when test="item.column != null and item.column !='' and item.asc == true">
                            ${item.column} ASC
                        </when>
                        <when test="item.column != null and item.column !='' and item.asc == false">
                            ${item.column} DESC
                        </when>
                        <otherwise>
                            ID DESC
                        </otherwise>
                    </choose>
                </foreach>
            </when>
            <otherwise>
                ORDER BY ID DESC
            </otherwise>
        </choose>
    </select>

    <select id="selectRandomExamQuestionBySubjectId" resultType="com.huafon.models.entity.ExamQuestion">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hf_exam_question
        WHERE question_code = #{questionCode}
        <if test="subjectIds != null and subjectIds.size() > 0">
            AND subject_id in
            <foreach collection="subjectIds" item="item" index="index" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        AND is_del = 0 and tenant_id=#{tenantId}
        ORDER BY random() LIMIT #{questionNum};
    </select>

    <select id="selectRandomExamQuestionByDifficult" resultType="com.huafon.models.entity.ExamQuestion">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hf_exam_question
        WHERE difficult = #{difficult}
        AND question_code = #{questionCode}
        <if test="subjectIds != null and subjectIds.size() > 0">
            AND subject_id in
            <foreach collection="subjectIds" item="item" index="index" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        AND is_del = 0 and tenant_id=#{tenantId}
        ORDER BY random() LIMIT #{questionNum};
    </select>

</mapper>
