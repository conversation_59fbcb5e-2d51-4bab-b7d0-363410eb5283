<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="he">
<head>
    <title>Document</title>
    <style type="text/css">
        @page  {
            margin: 0px;
        }
        *{
            padding: 0;
            margin: 0;
        }
        .certificate{
            width: 900px;
            height: 540px;
            position: relative;
            background-image: url(${data.templateBackground});
            background-size: 100%;
            background-repeat: no-repeat;
        }
        .certificate-body{
            width: 820px;
            margin: 0 40px;
            padding-top: 56px;
        }
        .certificate-header{
            text-align: center;
            margin-bottom: 46px;
        }
        .certificate-title{
            font-size: 42px;
            font-weight: 500;
            color: #262523;
            line-height: 46px;
            display: block;
            text-align: center;
            margin-top: 46px;
        }
        .certificate-name{
            margin-left: 50px;
            margin-bottom: 6px;
        }
        .certificate-name-title{
            font-size: 28px;
            font-weight: 400;
            text-align: left;
            color: #262523;
            line-height: 40px;
            display: inline-block;
        }
        .certificate-name-subtitle{
            margin-left: 12px;
            font-size: 28px;
            font-weight: 500;
            text-align: left;
            color: #8B8883;
            line-height: 40px;
            display: inline-block;
        }
        .certificate-content{
            font-size: 32px;
            font-weight: 500;
            text-align: left;
            color: #262523;
            line-height: 46px;
            display: inline-block;
            margin: 0 50px 6px 50px;
        }
        .certificate-footer{
            position: absolute;
            bottom: 87px;
            right: 92px;
            text-align: right;
        }
        .certificate-company{
            text-align: right;
            margin-bottom: 10px;
            font-size: 16px;
            font-weight: 400;
            color: #262523;
            line-height: 20px;
        }
        .certificate-time{
            text-align: right;
            font-size: 16px;
            font-weight: 400;
            color: #262523;
            line-height: 20px;
        }
    </style>
</head>
<body>
<div class="certificate">
    <div class="certificate-body">
        <div class="certificate-header">
            <div class="certificate-title">
                ${data.templateLicenseName!}
            </div>
        </div>
        <div class="certificate-name">
            <span class="certificate-name-title">兹授予</span>
            <span class="certificate-name-subtitle">${data.templateStaffName!}</span>
        </div>
        <div class="certificate-content">
            ${data.templateLicenseContent!}
        </div>
    </div>
    <div class="certificate-footer">
        <div class="certificate-company">${data.templateCompanyName!}</div>
        <div class="certificate-time">
            ${data.templateTime!}
        </div>
    </div>
</div>
</body>
</html>