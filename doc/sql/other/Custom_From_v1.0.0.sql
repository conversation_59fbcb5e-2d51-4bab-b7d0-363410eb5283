--- 我的应用 ---------------------------------------------------------------------------------------------------------------------------------------------------------
create table hf_application
(
    id          bigserial
        constraint hf_application_pk primary key,
    name        varchar(63)                            not null,
    icon        varchar(255)                           not null,
    tenant_id   bigint                                 not null,
    create_by   bigint                                 not null,
    create_time timestamp(0) default CURRENT_TIMESTAMP not null,
    modify_by   bigint                                 not null,
    modify_time timestamp(0) default CURRENT_TIMESTAMP not null,
    is_del      smallint     default 0                 not null
);
comment
on table hf_accident_library is '我的应用';

comment
on column hf_application.name is '名称';
comment
on column hf_application.icon is 'icon';

alter table hf_application
    owner to postgres;

--- 应用布局 预留 ---------------------------------------------------------------------------------------------------------------------------------------------------------
create table hf_application_layout
(
    id          bigserial
        constraint hf_application_layout_pk primary key,
    tenant_id   bigint                                 not null,

    create_by   bigint                                 not null,
    create_time timestamp(0) default CURRENT_TIMESTAMP not null,
    modify_by   bigint                                 not null,
    modify_time timestamp(0) default CURRENT_TIMESTAMP not null,
    is_del      smallint     default 0                 not null
);

comment
on table hf_accident_library is '应用布局';

alter table hf_accident_library
    owner to postgres;

--- 我的应用-功能关联表 ---------------------------------------------------------------------------------------------------------------------------------------------------------
create table hf_application_module
(
    id             bigserial
        constraint hf_application_module_pk primary key,
    application_id bigint                                 not null,
    module_type    varchar(63)                            not null,
    module_id      bigint                                 not null,
    create_by      bigint                                 not null,
    create_time    timestamp(0) default CURRENT_TIMESTAMP not null,
    modify_by      bigint                                 not null,
    modify_time    timestamp(0) default CURRENT_TIMESTAMP not null,
    is_del         smallint     default 0                 not null
);
comment
on table hf_application_module is '我的应用-功能关联表';

comment
on column hf_application_module.application_id is '应用名称';
comment
on column hf_application_module.module_type is '模块类型';
comment
on column hf_application_module.module_id is '模块id';

alter table hf_application_module
    owner to postgres;

--- 自定义表单 ---------------------------------------------------------------------------------------------------------------------------------------------------------
create table hf_custom_form
(
    id          bigserial
        constraint hf_custom_form_pk primary key,
    name        varchar(127)                           not null,
    tenant_id   bigint                                 not null,
    create_by   bigint                                 not null,
    create_time timestamp(0) default CURRENT_TIMESTAMP not null,
    modify_by   bigint                                 not null,
    modify_time timestamp(0) default CURRENT_TIMESTAMP not null,
    is_del      smallint     default 0                 not null
);

comment
on table hf_custom_form is '自定义表单';
comment
on column hf_custom_form.name is '名称';

alter table hf_custom_form
    owner to postgres;
--- 自定义表单查询列记录 ---------------------------------------------------------------------------------------------------------------------------------------------------------
create table hf_custom_form_column_history
(
    id          bigserial
        constraint hf_custom_form_column_history_pk primary key,
    form_id     bigint                                 not null,
    field_id    bigint                                 not null,
    field_name  varchar(127)                           not null,
    user_id     bigint                                 not null,
    create_by   bigint                                 not null,
    create_time timestamp(0) default CURRENT_TIMESTAMP not null,
    modify_by   bigint                                 not null,
    modify_time timestamp(0) default CURRENT_TIMESTAMP not null,
    is_del      smallint     default 0                 not null
);

comment
on table hf_custom_form_column_history is '自定义表单查询列记录';
comment
on column hf_custom_form_column_history.form_id is '表单id';
comment
on column hf_custom_form_column_history.field_id is '字段id';
comment
on column hf_custom_form_column_history.field_name is '字段名';
comment
on column hf_custom_form_column_history.user_id is '用户id';

alter table hf_custom_form_column_history
    owner to postgres;

--- 自定义表单原始数据 ---------------------------------------------------------------------------------------------------------------------------------------------------------
create table hf_custom_form_original
(
    id            bigserial
        constraint hf_custom_form_original_pk primary key,
    form_id       bigint                                 not null,
    original_json text                                   not null,
    create_by     bigint                                 not null,
    create_time   timestamp(0) default CURRENT_TIMESTAMP not null,
    modify_by     bigint                                 not null,
    modify_time   timestamp(0) default CURRENT_TIMESTAMP not null,
    is_del        smallint     default 0                 not null
);

comment
on table hf_custom_form_original is '自定义表单原始数据';

comment
on column hf_custom_form_original.form_id is '表单id';
comment
on column hf_custom_form_original.original_json is '表单原值';

alter table hf_custom_form_original
    owner to postgres;

--- 自定义表数据 ---------------------------------------------------------------------------------------------------------------------------------------------------------
create table hf_custom_table
(
    id          bigserial
        constraint hf_custom_table_pk primary key,
    table_name  varchar(127)                           not null,
    create_by   bigint                                 not null,
    create_time timestamp(0) default CURRENT_TIMESTAMP not null,
    modify_by   bigint                                 not null,
    modify_time timestamp(0) default CURRENT_TIMESTAMP not null,
    is_del      smallint     default 0                 not null
);

comment
on table hf_custom_table is '自定义表数据';
comment
on column hf_custom_table.table_name is '数据库表 名称';

alter table hf_custom_table
    owner to postgres;

--- 自定义表单字段 ---------------------------------------------------------------------------------------------------------------------------------------------------------
create table hf_custom_form_field
(
    id           bigserial
        constraint hf_custom_form_field_pk primary key,
    field_name   varchar(127)                           not null,
    alias_name   varchar(127)                           not null,
    component_id bigint                                 not null,
    form_id      bigint                                 not null,
    table_id     bigint                                 not null,
    field_type   varchar(31)                            not null,
    table_show   varchar(15)  default 'HIDDEN'          not null,
    table_search varchar(15)  default 'HIDDEN'          not null,
    create_by    bigint                                 not null,
    create_time  timestamp(0) default CURRENT_TIMESTAMP not null,
    modify_by    bigint                                 not null,
    modify_time  timestamp(0) default CURRENT_TIMESTAMP not null,
    is_del       smallint     default 0                 not null
);

comment
on table hf_custom_form_field is '自定义表单字段';

comment
on column hf_custom_form_field.field_name is '字段名';
comment
on column hf_custom_form_field.alias_name is '别名';
comment
on column hf_custom_form_field.form_id is '表单id';
comment
on column hf_custom_form_field.table_id is '数据库表id';
comment
on column hf_custom_form_field.component_id is '组件id';
comment
on column hf_custom_form_field.field_type is '字段类型';
comment
on column hf_custom_form_field.table_show is '表单展示状态';
comment
on column hf_custom_form_field.table_search is '表单展示搜索';

alter table hf_custom_form_field
    owner to postgres;

--- 自定义表单字段校验 ---------------------------------------------------------------------------------------------------------------------------------------------------------
create table hf_custom_form_field_valid
(
    id                 bigserial
        constraint hf_custom_form_field_valid_pk primary key,
    field_id           bigint                                 not null,
    not_null           varchar(15)  default 'FALSE'           not null,
    check_size_max     boolean      default 'FALSE'           not null,
    size_max           int null,
    check_size_min     boolean      default 'FALSE'           not null,
    size_min           int null,
    check_date_pattern boolean      default 'FALSE'           not null,
    date_pattern       varchar(63) null,
    check_regular      boolean      default 'FALSE'           not null,
    regular            varchar(63) null,
    create_by          bigint                                 not null,
    create_time        timestamp(0) default CURRENT_TIMESTAMP not null,
    modify_by          bigint                                 not null,
    modify_time        timestamp(0) default CURRENT_TIMESTAMP not null,
    is_del             smallint     default 0                 not null
);

comment
on table hf_custom_form_field_valid is '自定义表单字段';

comment
on column hf_custom_form_field_valid.field_id is '字段id';
comment
on column hf_custom_form_field_valid.not_null is '是否不可为空';
comment
on column hf_custom_form_field_valid.check_size_max is '是否限制最大值';
comment
on column hf_custom_form_field_valid.size_max is '限制最大值';
comment
on column hf_custom_form_field_valid.check_size_min is '是否限制最小值';
comment
on column hf_custom_form_field_valid.size_min is '限制最小值';
comment
on column hf_custom_form_field_valid.check_date_pattern is '是否校验时间格式';
comment
on column hf_custom_form_field_valid.date_pattern is '时间格式';
comment
on column hf_custom_form_field_valid.check_regular is '是否校验正则';
comment
on column hf_custom_form_field_valid.regular is '正则表达式';

alter table hf_custom_form_field_valid
    owner to postgres;

--- 自定义表单组件库 ---------------------------------------------------------------------------------------------------------------------------------------------------------
create table hf_custom_component_repository
(
    id          bigserial
        constraint hf_custom_component_repository_pk primary key,
    component   varchar(63)                            not null,
    type        varchar(31)                            not null,
    db_type     varchar(15) null,
    create_by   bigint                                 not null,
    create_time timestamp(0) default CURRENT_TIMESTAMP not null,
    modify_by   bigint                                 not null,
    modify_time timestamp(0) default CURRENT_TIMESTAMP not null,
    is_del      smallint     default 0                 not null
);

comment
on table hf_custom_component_repository is '自定义表单组件库';

comment
on column hf_custom_component_repository.component is '组件code';
comment
on column hf_custom_component_repository.type is '组件类型';

comment
on column hf_custom_component_repository.db_type is '数据库类型';

alter table hf_custom_component_repository
    owner to postgres;


--=== 相关字典 =======================================================================================================================================================
--- APPLICATION_TYPE ---------------------------------------------------------------------------------------------------------------------------------------------------------
INSERT INTO sys_dict (name, code, parent_id, create_by, modify_by, tenant_id)
VALUES ('应用类型', 'APPLICATION_TYPE', 0, 0, 0, 1);
INSERT INTO sys_dict (name, code, parent_id, create_by, modify_by, tenant_id)
VALUES ('表单', 'FORM', (select id from sys_dict where code = 'APPLICATION_TYPE'), 0, 0, 1),
       ('流程表单', 'FLOW_FORM', (select id from sys_dict where code = 'APPLICATION_TYPE'), 0, 0, 1),
       ('计划任务', 'PLAN', (select id from sys_dict where code = 'APPLICATION_TYPE'), 0, 0, 1);

--- CUSTOM_FORM_FIELD_TYPE ---------------------------------------------------------------------------------------------------------------------------------------------------------
INSERT INTO sys_dict (name, code, parent_id, create_by, modify_by, tenant_id)
VALUES ('自定义表单字段类型', 'CUSTOM_FORM_FIELD_TYPE', 0, 0, 0, 1);
INSERT INTO sys_dict (name, code, parent_id, create_by, modify_by, tenant_id)
VALUES ('文本', 'VALUE', (select id from sys_dict where code = 'CUSTOM_FORM_FIELD_TYPE' and tenant_id = 1), 0, 0, 1),
       ('字典', 'DICT', (select id from sys_dict where code = 'CUSTOM_FORM_FIELD_TYPE' and tenant_id = 1), 0, 0, 1),
       ('数值', 'NUMBER', (select id from sys_dict where code = 'CUSTOM_FORM_FIELD_TYPE' and tenant_id = 1), 0, 0, 1),
       ('链接', 'LINK', (select id from sys_dict where code = 'CUSTOM_FORM_FIELD_TYPE' and tenant_id = 1), 0, 0, 1),
       ('日期', 'DATE', (select id from sys_dict where code = 'CUSTOM_FORM_FIELD_TYPE' and tenant_id = 1), 0, 0, 1),
       ('时间', 'TIME', (select id from sys_dict where code = 'CUSTOM_FORM_FIELD_TYPE' and tenant_id = 1), 0, 0, 1);

--=== 自定义表单组件库 init =======================================================================================================================================================
INSERT INTO hf_custom_component_repository (component, type, db_type, default_size, create_by, modify_by)
values ('input', 'BUSINESS', 'varchar', 127, 0, 0),
       ('textarea', 'BUSINESS', 'varchar', 511, 0, 0),
       ('number', 'BUSINESS', 'int', 0, 0, 0),
       ('select', 'BUSINESS', 'varchar', 127, 0, 0),
       ('checkbox', 'BUSINESS', 'varchar', 127, 0, 0),
       ('radio', 'BUSINESS', 'varchar', 127, 0, 0),
       ('date', 'BUSINESS', 'date', 0, 0, 0),
       ('time', 'BUSINESS', 'time', 0, 0, 0),
       ('rate', 'BUSINESS', 'int', 0, 0, 0),
       ('slider', 'BUSINESS', 'int', 0, 0, 0),
       ('uploadFile', 'BUSINESS', 'varchar', 255, 0, 0),
       ('uploadImg', 'BUSINESS', 'varchar', 255, 0, 0),
       ('treeSelect', 'BUSINESS', 'varchar', 127, 0, 0),
       ('cascader', 'BUSINESS', 'varchar', 127, 0, 0),
       ('editor', 'BUSINESS', 'text', 0, 0, 0),
       ('switch', 'BUSINESS', 'boolean', 0, 0, 0),
       ('selectInputList', 'LAYOUT', 127, 0, 0),
       ('divider', 'LAYOUT', null, 511, 0, 0),
       ('card', 'LAYOUT', null, 0, 0, 0),
       ('grid', 'LAYOUT', null, 127, 0, 0),
       ('tabs', 'LAYOUT', null, 127, 0, 0),
       ('table', 'LAYOUT', null, 127, 0, 0),
       ('batch', 'LAYOUT', null, 0, 0, 0);
