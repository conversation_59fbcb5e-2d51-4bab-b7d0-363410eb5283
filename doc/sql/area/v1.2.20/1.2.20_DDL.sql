alter table hf_area_white_black_log
    add operator int;
alter table hf_area_white_black_log
    add operator_name varchar(63);
alter table hf_area_white_black_log
    add type varchar(63);

alter table security_environment.hf_area_white_black_log
    alter column description drop not null;

comment
on column hf_area_white_black_log.type is '区域黑白名单 日志类型 INTRUDE EDIT';

create table hf_area_white_black_log_person
(
    id          bigserial,
    area_id     bigint                                 not null,
    log_id      bigint                                 not null,
    user_id     bigint,
    username    varchar(127),
    type        varchar(63)                            not null,
    create_by   bigint                                 not null,
    create_time timestamp(0) default CURRENT_TIMESTAMP not null,
    modify_by   bigint                                 not null,
    modify_time timestamp(0) default CURRENT_TIMESTAMP not null,
    is_del      smallint     default 0                 not null
);

comment
on column hf_area_white_black_log_person.type is '区域黑白名单 日志类型 INTRUDE ADD_WHITE ADD_BLACK DEL_WHITE DEL_BLACK';



alter table security_environment.hf_car
alter
column type type varchar(63);

