CREATE TABLE hf_bad_habits (
    id BIGSERIAL PRIMARY KEY, -- 主键，自增
    bad_habit_code VARCHAR(255) NOT NULL, -- 陋习编码
    category_id BIGINT, -- 关联陋习分类id
    category_name VARCHAR(255), -- 陋习分类名称
    description TEXT, -- 问题描述
    score INTEGER DEFAULT 0, -- 奖励积分，默认值为0
    create_by BIGINT, -- 创建者
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    modify_by BIGINT, -- 修改者
    modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 修改时间
    is_del INTEGER DEFAULT 0, -- 是否删除，默认值为0
    create_name varchar(255) NULL, -- 创建人姓名
    modify_name varchar(255) NULL, -- 编辑人姓名
    tenant_id INTEGER -- 租户id（父类字段）
);

COMMENT ON COLUMN hf_bad_habits.bad_habit_code IS '陋习编码';
COMMENT ON COLUMN hf_bad_habits.category_id IS '关联陋习分类id';
COMMENT ON COLUMN hf_bad_habits.category_name IS '陋习分类名称';
COMMENT ON COLUMN hf_bad_habits.description IS '问题描述';
COMMENT ON COLUMN hf_bad_habits.score IS '奖励积分';
COMMENT ON COLUMN hf_bad_habits.tenant_id IS '租户id';
COMMENT ON COLUMN hf_bad_habits.create_by IS '创建者';
COMMENT ON COLUMN hf_bad_habits.create_time IS '创建时间';
COMMENT ON COLUMN hf_bad_habits.modify_by IS '修改者';
COMMENT ON COLUMN hf_bad_habits.modify_time IS '修改时间';
COMMENT ON COLUMN hf_bad_habits.is_del IS '是否删除';
COMMENT ON COLUMN hf_bad_habits.create_name IS '创建人姓名';
COMMENT ON COLUMN hf_bad_habits.modify_name IS '编辑人姓名';
COMMENT ON COLUMN hf_bad_habits.tenant_id IS '租户id（父类字段）';


CREATE TABLE hf_bad_habits_category (
    id BIGSERIAL PRIMARY KEY, -- 主键，自增
    name VARCHAR(255) NOT NULL, -- 目录名称
    icon VARCHAR(255), -- 图标
    remark TEXT, -- 备注
    parent_id BIGINT, -- 父节点
    sort_order INTEGER DEFAULT 0, -- 排序值，默认值为0
    node_type VARCHAR(50), -- 类型：TOP-顶级节点，ORDINARY-普通节点，UNDETERMINED-待分类节点
    tenant_id INTEGER, -- 租户id
    create_by BIGINT, -- 创建者
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    modify_by BIGINT, -- 修改者
    modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 修改时间
    is_del INTEGER DEFAULT 0 -- 是否删除，默认值为0
);

COMMENT ON COLUMN hf_bad_habits_category.name IS '目录名称';
COMMENT ON COLUMN hf_bad_habits_category.icon IS '图标';
COMMENT ON COLUMN hf_bad_habits_category.remark IS '备注';
COMMENT ON COLUMN hf_bad_habits_category.parent_id IS '父节点';
COMMENT ON COLUMN hf_bad_habits_category.sort_order IS '排序值';
COMMENT ON COLUMN hf_bad_habits_category.node_type IS '类型：TOP-顶级节点，ORDINARY-普通节点，UNDETERMINED-待分类节点';
COMMENT ON COLUMN hf_bad_habits_category.tenant_id IS '租户id';
COMMENT ON COLUMN hf_bad_habits_category.create_by IS '创建者';
COMMENT ON COLUMN hf_bad_habits_category.create_time IS '创建时间';
COMMENT ON COLUMN hf_bad_habits_category.modify_by IS '修改者';
COMMENT ON COLUMN hf_bad_habits_category.modify_time IS '修改时间';
COMMENT ON COLUMN hf_bad_habits_category.is_del IS '是否删除';


CREATE TABLE hf_bad_habits_report (
    id SERIAL PRIMARY KEY, -- 主键，自增
    bad_habit_report_code VARCHAR(255) NOT NULL, -- 陋习上报编码
    report_person_id BIGINT, -- 上报人id
    report_person_name VARCHAR(255), -- 上报人姓名
    discoverer_id BIGINT, -- 发现人id
    discoverer_name VARCHAR(255), -- 发现人姓名
    discover_time TIMESTAMP, -- 发现时间
    discover_place VARCHAR(255), -- 发现地点
    category_name VARCHAR(255), -- 陋习分类
    description TEXT, -- 问题描述
    pictures_json_arr TEXT, -- 问题照片，json arr格式
    involved_person_json_arr TEXT, -- 涉及人员，json arr格式
    score INTEGER DEFAULT 0, -- 奖励积分，默认值为0
    tenant_id INTEGER, -- 租户id
    create_by BIGINT, -- 创建者
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    modify_by BIGINT, -- 修改者
    modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 修改时间
    is_del INTEGER DEFAULT 0 -- 是否删除，默认值为0
);

COMMENT ON COLUMN hf_bad_habits_report.bad_habit_report_code IS '陋习上报编码';
COMMENT ON COLUMN hf_bad_habits_report.report_person_id IS '上报人id';
COMMENT ON COLUMN hf_bad_habits_report.report_person_name IS '上报人姓名';
COMMENT ON COLUMN hf_bad_habits_report.discoverer_id IS '发现人id';
COMMENT ON COLUMN hf_bad_habits_report.discoverer_name IS '发现人姓名';
COMMENT ON COLUMN hf_bad_habits_report.discover_time IS '发现时间';
COMMENT ON COLUMN hf_bad_habits_report.discover_place IS '发现地点';
COMMENT ON COLUMN hf_bad_habits_report.category_name IS '陋习分类';
COMMENT ON COLUMN hf_bad_habits_report.description IS '问题描述';
COMMENT ON COLUMN hf_bad_habits_report.pictures_json_arr IS '问题照片，json arr格式';
COMMENT ON COLUMN hf_bad_habits_report.involved_person_json_arr IS '涉及人员，json arr格式';
COMMENT ON COLUMN hf_bad_habits_report.score IS '奖励积分';
COMMENT ON COLUMN hf_bad_habits_report.tenant_id IS '租户id';
COMMENT ON COLUMN hf_bad_habits_report.create_by IS '创建者';
COMMENT ON COLUMN hf_bad_habits_report.create_time IS '创建时间';
COMMENT ON COLUMN hf_bad_habits_report.modify_by IS '修改者';
COMMENT ON COLUMN hf_bad_habits_report.modify_time IS '修改时间';
COMMENT ON COLUMN hf_bad_habits_report.is_del IS '是否删除';


INSERT INTO security_environment.hf_bad_habits_category ("name",icon,remark,parent_id,sort_order,node_type,tenant_id,create_by,create_time,modify_by,modify_time,is_del) VALUES
	 ('"5S类"','folder-open','',0,2,'ORDINARY',1,0,'2024-10-29 19:57:06.751',0,'2024-10-29 19:57:06.751',0),
	 ('厂内外交通类','folder-open','',0,2,'ORDINARY',1,0,'2024-10-29 19:57:06.751',0,'2024-10-29 19:57:06.751',0),
	 ('劳保用品佩戴类','folder-open','',0,2,'ORDINARY',1,0,'2024-10-29 19:57:06.751',0,'2024-10-29 19:57:06.751',0),
	 ('不文明行为类','folder-open','',0,2,'ORDINARY',1,0,'2024-10-29 19:57:06.751',0,'2024-10-29 19:57:06.751',0),
	 ('违反劳动纪律类','folder-open','',0,2,'ORDINARY',1,0,'2024-10-29 19:57:06.751',0,'2024-10-29 19:57:06.751',0),
	 ('设备隐患类','folder-open','',0,2,'ORDINARY',1,0,'2024-10-29 19:57:06.751',0,'2024-10-29 19:57:06.751',0),
	 ('跑冒滴漏类','folder-open','',0,2,'ORDINARY',1,0,'2024-10-29 19:57:06.751',0,'2024-10-29 19:57:06.751',0),
	 ('现场环境类','folder-open','',0,2,'ORDINARY',1,0,'2024-10-29 19:57:06.751',0,'2024-10-29 19:57:06.751',0),
	 ('其他','folder-open','',0,2,'ORDINARY',1,0,'2024-10-29 19:57:06.751',0,'2024-10-29 19:57:06.751',0);
