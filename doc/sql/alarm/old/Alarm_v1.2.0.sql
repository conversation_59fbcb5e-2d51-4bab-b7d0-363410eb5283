alter table security_environment.hf_alarm_template
    alter column template_id drop not null;

alter table security_environment.hf_alarm_template
    add notice_level int;

alter table security_environment.hf_alarm_template
    add user_id bigint;

alter table security_environment.hf_alarm_template
    add category varchar(63);
alter table security_environment.hf_alarm_template
    add time_config varchar(1023);
alter table security_environment.hf_alarm_template
    add type_desc varchar(1023);

alter table security_environment.hf_alarm_template
    add type varchar(63);

alter table security_environment.hf_alarm_template
    add level varchar(63);

alter table security_environment.hf_alarm_template
    add create_by bigint default 1 not null;
alter table security_environment.hf_alarm_template
    add create_time timestamp(0) default CURRENT_TIMESTAMP not null;
alter table security_environment.hf_alarm_template
    add modify_by bigint default 1 not null;
alter table security_environment.hf_alarm_template
    add modify_time timestamp(0) default CURRENT_TIMESTAMP not null;
alter table security_environment.hf_alarm_template
    add is_del smallint default 0 not null;

alter table security_environment.hf_alarm_notice_recipient
    alter column template_id drop not null;

alter table security_environment.hf_alarm
    add channel boolean;
alter table security_environment.hf_alarm
    add restore boolean;
alter table security_environment.hf_alarm
    add trigger boolean;

alter table security_environment.hf_alarm_notice
    add next_levelup_time timestamp(0);

alter table security_environment.hf_alarm_notice
    add notice_level int;

alter table security_environment.hf_alarm_notice_recipient
    add notice_level int;

alter table security_environment.hf_alarm_notice
    add channel varchar(63);
alter table security_environment.hf_alarm_notice
    add restore boolean;
alter table security_environment.hf_alarm_notice
    add trigger boolean;
