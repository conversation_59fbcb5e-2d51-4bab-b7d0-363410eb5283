package com.huafon.office.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huafon.office.dao.mapper.EquipmentMapper;
import com.huafon.office.dao.mapper.MeetingRoomMapper;
import com.huafon.office.dao.mapper.MeetingRoomOpeningMapper;
import com.huafon.office.dao.mapper.MeetingRoomReserveMapper;
import com.huafon.office.model.entity.Equipment;
import com.huafon.office.model.entity.MeetingRoom;
import com.huafon.office.model.entity.MeetingRoomOpeningTime;
import com.huafon.office.model.entity.MeetingRoomReserve;
import com.huafon.office.model.enums.MeetingRoomStatus;
import com.huafon.office.model.query.MeetingRoomNoAvailableTimeQuery;
import com.huafon.office.model.query.MeetingRoomQuery;
import com.huafon.office.model.vo.MeetingRoomNoAvailableTimeVo;
import com.huafon.office.service.MeetingRoomService;
import com.huafon.office.service.support.meetingRoom.MeetingRoomStatusProcess;
import com.huafon.support.config.UserContext;
import com.huafon.support.exceptions.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.huafon.office.model.constants.RedisConstants.HF_MEETING_ROOM_LOCK_FORMAT;
import static com.huafon.office.model.constants.RedisConstants.HF_MEETING_ROOM_LOCK_WAIT;
import static com.huafon.office.model.enums.MeetingRoomReserveStatus.PROCESSING;
import static com.huafon.office.model.enums.MeetingRoomReserveStatus.READY;

/**
 * 会议预约
 *
 * <AUTHOR>
 * @since 2022-06-08 10:01
 **/
@Service
@Slf4j
public class MeetingRoomServiceImpl implements MeetingRoomService {

    private final MeetingRoomMapper meetingRoomMapper;
    private final MeetingRoomOpeningMapper meetingRoomOpeningMapper;
    private final EquipmentMapper equipmentMapper;
    private final MeetingRoomReserveMapper meetingRoomReserveMapper;
    private final RedissonClient redissonClient;

    @Autowired
    public MeetingRoomServiceImpl(MeetingRoomMapper meetingRoomMapper, MeetingRoomOpeningMapper meetingRoomOpeningMapper, EquipmentMapper equipmentMapper, MeetingRoomReserveMapper meetingRoomReserveMapper, RedissonClient redissonClient) {
        this.meetingRoomMapper = meetingRoomMapper;
        this.meetingRoomOpeningMapper = meetingRoomOpeningMapper;
        this.equipmentMapper = equipmentMapper;
        this.meetingRoomReserveMapper = meetingRoomReserveMapper;
        this.redissonClient = redissonClient;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createMeetingRoom(MeetingRoom meetingRoom) {
        log.info("【创建会议室信息】{}", meetingRoom);
        meetingRoom.setCreate(UserContext.getId());
        meetingRoomMapper.insert(meetingRoom);
        if (meetingRoom.getMeetingRoomId() != null) {
            Integer roomId = meetingRoom.getMeetingRoomId();
            List<MeetingRoomOpeningTime> openingTimes = meetingRoom.getOpeningTimes();
            openingTimes = convertMeetingRoomOpeningTime(openingTimes, roomId, false);

            meetingRoomOpeningMapper.insertBatch(openingTimes);
            if (!CollectionUtils.isEmpty(meetingRoom.getEquipments())) {
                equipmentMapper.createMeetingRoomEquipments(roomId, meetingRoom.getEquipments());
            }
        }
    }


    @Override
    public void updateMeetingRoom(MeetingRoom meetingRoom) {
        log.info("【更新会议室信息】{}", meetingRoom);
        Integer roomId = meetingRoom.getMeetingRoomId();
        RLock lock = redissonClient.getLock(String.format(HF_MEETING_ROOM_LOCK_FORMAT, roomId));
        try {
            if (lock.tryLock(HF_MEETING_ROOM_LOCK_WAIT, TimeUnit.SECONDS)) {
                MeetingRoom findMeetingRoom = this.findMeetingRoomById(roomId);
                if (findMeetingRoom == null) {
                    throw new ServiceException("更新失败, 会议室信息不存在。");
                }
                meetingRoom.setModify(UserContext.getId());
                meetingRoomMapper.updateById(meetingRoom);
                List<MeetingRoomOpeningTime> newOpeningTimes = meetingRoom.getOpeningTimes();
                newOpeningTimes = convertMeetingRoomOpeningTime(newOpeningTimes, roomId, true);

                //处理开放时间
                List<MeetingRoomOpeningTime> oldOpeningTimes = meetingRoomOpeningMapper
                        .selectList(new QueryWrapper<MeetingRoomOpeningTime>().lambda().eq(MeetingRoomOpeningTime::getMeetingRoomId, roomId));
                Collection<MeetingRoomOpeningTime> willAdd = CollectionUtils.subtract(newOpeningTimes, oldOpeningTimes);
                Collection<MeetingRoomOpeningTime> willDelete = CollectionUtils.subtract(oldOpeningTimes, newOpeningTimes);
                if (!CollectionUtils.isEmpty(willDelete)) {
                    meetingRoomOpeningMapper.deleteBatch(willDelete.stream().map(MeetingRoomOpeningTime::getId).collect(Collectors.toList()), UserContext.getId());
                }
                if (!CollectionUtils.isEmpty(willAdd)) {
                    meetingRoomOpeningMapper.insertBatch(new ArrayList<>(willAdd));
                }

                //处理办公设备
                List<Equipment> oldEquipment = equipmentMapper.searchEquipmentByMeetingRoom(roomId);
                List<Equipment> newEquipment = meetingRoom.getEquipments();
                Collection<Equipment> plusEquipment = CollectionUtils.subtract(newEquipment, oldEquipment);
                Collection<Equipment> minusEquipment = CollectionUtils.subtract(oldEquipment, newEquipment);
                if (!CollectionUtils.isEmpty(minusEquipment))
                    equipmentMapper.deleteMeetingRoomEquipments(roomId, minusEquipment);
                if (!CollectionUtils.isEmpty(plusEquipment))
                    equipmentMapper.createMeetingRoomEquipments(roomId, plusEquipment);
            } else {
                log.warn("【获取会议室Redis Lock失败】{}", roomId);
                throw new ServiceException("更新会议室信息失败，请稍后重试。");
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
            throw new ServiceException("更新会议室信息失败，请稍后重试。");
        } finally {
            lock.unlock();
        }
    }

    /**
     * 校验会议室开放时间
     *
     * @param openingTimes  开放时间列表
     * @param meetingRoomId 会议室ID
     * @param update        是否是更新
     */
    private List<MeetingRoomOpeningTime> convertMeetingRoomOpeningTime(List<MeetingRoomOpeningTime> openingTimes, Integer meetingRoomId, boolean update) {
        if (CollectionUtils.isEmpty(openingTimes)) {
            return openingTimes;
        }
        for (MeetingRoomOpeningTime currentTime : openingTimes) {
            LocalTime L = currentTime.getOpeningBeginTime(), R = currentTime.getOpeningEndTime();
            if (Objects.isNull(L)) L = LocalTime.MIN;
            if (Objects.isNull(R)) R = LocalTime.MAX;
            if (L.isAfter(R) || L.equals(R)) {
                throw new ServiceException("会议室开放时间不正确。");
            }
            currentTime.setMeetingRoomId(meetingRoomId);
            if (update) currentTime.setModify(UserContext.getId());
            else currentTime.setCreate(UserContext.getId());
        }

        Map<DayOfWeek, List<MeetingRoomOpeningTime>> weekMapping = openingTimes
                .stream().collect(Collectors.groupingBy(MeetingRoomOpeningTime::getWeek));
        List<MeetingRoomOpeningTime> result = new ArrayList<>();
        for (Map.Entry<DayOfWeek, List<MeetingRoomOpeningTime>> entry : weekMapping.entrySet()) {
            List<MeetingRoomOpeningTime> value = entry.getValue();
            if (value.size() == 1) {
                result.add(value.get(0));
                continue;
            }
            value.sort(Comparator.comparing(MeetingRoomOpeningTime::getOpeningBeginTime));
            List<MeetingRoomOpeningTime> weekOfOpeningTime = new ArrayList<>();
            for (int i = 0; i < value.size(); i++) {
                MeetingRoomOpeningTime V = value.get(i);
                LocalTime L = V.getOpeningBeginTime(), R = V.getOpeningEndTime();
                if (i == 0 || weekOfOpeningTime.get(weekOfOpeningTime.size() - 1).getOpeningEndTime().isBefore(L)) {
                    weekOfOpeningTime.add(V);
                } else {
                    MeetingRoomOpeningTime preOpeningTime = weekOfOpeningTime.get(weekOfOpeningTime.size() - 1);
                    if (preOpeningTime.getOpeningEndTime().isBefore(R)) {
                        preOpeningTime.setOpeningEndTime(R);
                    }
                }
            }
            result.addAll(weekOfOpeningTime);
        }

        return result;
    }

    @Override
    public List<MeetingRoom> searchMeetingRooms(MeetingRoomQuery query) {
        log.info("【查询会议室列表】{}", query);
        List<MeetingRoom> meetingRooms = meetingRoomMapper.selectList(query.lambdaQueryWrapper());
        for (MeetingRoom meetingRoom : meetingRooms) {
            List<MeetingRoomOpeningTime> openingTimes = meetingRoomOpeningMapper.selectList(new QueryWrapper<MeetingRoomOpeningTime>().lambda().eq(MeetingRoomOpeningTime::getMeetingRoomId, meetingRoom.getMeetingRoomId()));
            meetingRoom.setOpeningTimes(openingTimes);
            List<Equipment> equipment = equipmentMapper.searchEquipmentByMeetingRoom(meetingRoom.getMeetingRoomId());
            meetingRoom.setEquipments(equipment);
        }
        return meetingRooms;
    }


    @Override
    public MeetingRoom findMeetingRoomById(Integer meetingRoomId) {
        log.info("【查询会议室信息】{}", meetingRoomId);
        MeetingRoom meetingRoom = meetingRoomMapper.selectById(meetingRoomId);
        if (Objects.nonNull(meetingRoom)) {
            meetingRoom.setOpeningTimes(meetingRoomOpeningMapper.selectList(new QueryWrapper<MeetingRoomOpeningTime>().lambda().eq(MeetingRoomOpeningTime::getMeetingRoomId, meetingRoomId)));
            meetingRoom.setEquipments(equipmentMapper.searchEquipmentByMeetingRoom(meetingRoomId));
        }
        return meetingRoom;
    }


    @Override
    public boolean updateMeetingRoomStatus(Integer meetingRoomId, MeetingRoomStatus targetStatus) {
        log.info("【更新会议室状态】{} {}", meetingRoomId, targetStatus);
        RLock lock = redissonClient.getLock(String.format(HF_MEETING_ROOM_LOCK_FORMAT, meetingRoomId));
        try {
            if (lock.tryLock(HF_MEETING_ROOM_LOCK_WAIT, TimeUnit.SECONDS)) {
                MeetingRoom meetingRoom = meetingRoomMapper.selectById(meetingRoomId);
                if (meetingRoom == null) {
                    log.warn("【更新会议室状态，会议室信息为null】");
                    return false;
                }
                if (Objects.isNull(meetingRoom.getMeetingRoomId())) {
                    log.warn("【更新会议室状态，会议室ID为null】");
                    return false;
                }
                MeetingRoomStatusProcess.preProcessing(meetingRoom.getStatus(), targetStatus);
                meetingRoomMapper.updateMeetingRoomStatus(targetStatus.getValue(), UserContext.getId(), meetingRoom.getMeetingRoomId());
                return true;
            } else {
                log.warn("【获取会议室Redis Lock失败】{}", meetingRoomId);
                throw new ServiceException("更新会议室状态失败，请稍后重试。");
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
            throw new ServiceException("更新会议室状态失败失败，请稍后重试。");
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void deleteMeetingRoom(Integer meetingRoomId) {
        log.info("【删除会议室信息】{}", meetingRoomId);
        meetingRoomMapper.deleteById(meetingRoomId);
    }


    @Override
    public List<MeetingRoomNoAvailableTimeVo> queryMeetingRoomNoAvailableTime(MeetingRoomNoAvailableTimeQuery query) {
        log.info("【查询会议室不可用时间】{}", query);
        Integer roomId = query.getMeetingRoomId();
        if (Objects.isNull(roomId)) {
            log.warn("【查询会议室不可用时间】会议室ID为空");
            return Collections.emptyList();
        }

        MeetingRoom meetingRoom = this.findMeetingRoomById(roomId);
        if (Objects.isNull(meetingRoom)) {
            throw new ServiceException("查询失败，会议室信息不存在。");
        }
        List<MeetingRoomOpeningTime> openingTimes = meetingRoom.getOpeningTimes();//开放的时间
        LocalTime[][] openingTimeArray = openingTimes.stream()
                .filter(item -> Objects.isNull(query.getQueryDate()) || query.getQueryDate().getDayOfWeek().equals(item.getWeek()))
                .map(item -> new LocalTime[]{item.getOpeningBeginTime(), item.getOpeningEndTime()})
                .sorted(Comparator.comparing(a -> a[0]))
                .toArray(LocalTime[][]::new);
        int len = openingTimeArray.length;
        if (len == 0) {
            return Collections.singletonList(buildThroughoutAllDayNotAvailable(query.getQueryDate()));
        }

		/*
		   今天之前：全天不可用
		   等于今天：当前时间之前均不可用
		   今天之后：按开放时间和预约时间进行计算
		 */
        LocalDate queryDate = query.getQueryDate();
        LocalDate today = LocalDate.now();
        if (queryDate.isBefore(today)) {
            return Collections.singletonList(buildThroughoutAllDayNotAvailable(queryDate));
        }
        List<LocalDateTime[]> noAvailableTime = new ArrayList<>();
        if (today.isEqual(queryDate))
            noAvailableTime.add(new LocalDateTime[]{LocalDateTime.of(today, LocalTime.MIN), LocalDateTime.now()});
        for (int i = 0; i < len; i++) {
            LocalTime L = openingTimeArray[i][0];
            if (i == 0) {
                if (LocalTime.MIN.isBefore(L)) {
                    noAvailableTime.add(new LocalDateTime[]{LocalDateTime.of(queryDate, LocalTime.MIN), LocalDateTime.of(queryDate, L)});
                }
            } else {
                LocalTime preTail = openingTimeArray[i - 1][1], curHead = openingTimeArray[i][0];
                if (preTail.isBefore(curHead)) {
                    noAvailableTime.add(new LocalDateTime[]{LocalDateTime.of(queryDate, preTail), LocalDateTime.of(queryDate, curHead)});
                }
            }
        }
        if (openingTimeArray[len - 1][1].isBefore(LocalTime.MAX)) {
            LocalTime head = openingTimeArray[len - 1][1];
            noAvailableTime.add(new LocalDateTime[]{LocalDateTime.of(queryDate, head), LocalDateTime.of(queryDate, LocalTime.MAX)});
        }

        LambdaQueryWrapper<MeetingRoomReserve> queryWrapper = query.lambdaQueryWrapper()
                .in(MeetingRoomReserve::getStatus, READY, PROCESSING)
                .orderByAsc(MeetingRoomReserve::getReserveBeginTime);
        List<MeetingRoomReserve> meetingRoomReserves = meetingRoomReserveMapper.selectList(queryWrapper);
        for (MeetingRoomReserve reserve : meetingRoomReserves) {
            LocalDateTime L = reserve.getReserveBeginTime(), R = reserve.getReserveEndTime();
            noAvailableTime.add(new LocalDateTime[]{L, R});
        }

        noAvailableTime.sort(Comparator.comparing(a -> a[0]));
        List<MeetingRoomNoAvailableTimeVo> result = new ArrayList<>();
        for (int i = 0; i < noAvailableTime.size(); i++) {
            LocalDateTime L = noAvailableTime.get(i)[0], R = noAvailableTime.get(i)[1];
            if (i == 0 || result.get(result.size() - 1).getEndTime().isBefore(L)) {
                result.add(new MeetingRoomNoAvailableTimeVo(L, R));
            } else {
                MeetingRoomNoAvailableTimeVo preNoAvailableTime = result.get(result.size() - 1);
                if (preNoAvailableTime.getEndTime().isBefore(R)) {
                    preNoAvailableTime.setEndTime(R);
                }
            }
        }

        return result;
    }

    /**
     * 全天不可用
     */
    private MeetingRoomNoAvailableTimeVo buildThroughoutAllDayNotAvailable(LocalDate localDate) {
        MeetingRoomNoAvailableTimeVo noAvailableTime = new MeetingRoomNoAvailableTimeVo();
        noAvailableTime.setBeginTime(LocalDateTime.of(localDate, LocalTime.MIN));
        noAvailableTime.setEndTime(LocalDateTime.of(localDate, LocalTime.MAX));
        return noAvailableTime;
    }
}
