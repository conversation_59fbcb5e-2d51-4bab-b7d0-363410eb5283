package com.huafon.office.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import com.huafon.office.model.enums.MeetingRoomStatus;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * 会议室
 *
 * <AUTHOR>
 * @since 2022-06-07 18:48
 **/
@Data
@TableName("hf_office_meeting_room")
public class MeetingRoom extends BaseEntity {
	private static final long serialVersionUID = 8226241059688516100L;

	@TableId(value = "id", type = IdType.AUTO)
	private Integer meetingRoomId;

	@TableField(value = "name")
	private String name;

	@TableField(value = "description")
	private String description;//描述

	@TableField(value = "permit_number")
	private Integer permitNumber;//可容纳人数

	@TableField(value = "status")
	private MeetingRoomStatus status;

	@TableField(exist = false)
	private List<MeetingRoomOpeningTime> openingTimes;

	@TableField(exist = false)
	private List<Equipment> equipments;


	public void setOpeningTimes(List<MeetingRoomOpeningTime> openingTimes) {
		if(openingTimes == null) openingTimes = Collections.emptyList();
		this.openingTimes = openingTimes;
	}


	public void setEquipments(List<Equipment> equipments) {
		if(equipments == null) equipments = Collections.emptyList();
		this.equipments = equipments;
	}
}
