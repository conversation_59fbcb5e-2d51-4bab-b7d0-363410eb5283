package com.huafon.office.service.impl;

import com.huafon.office.dao.mapper.EquipmentMapper;
import com.huafon.office.model.entity.Equipment;
import com.huafon.office.service.EquipmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-06-14 13:28
 **/
@Service
public class EquipmentServiceImpl implements EquipmentService {

	private final EquipmentMapper equipmentMapper;

	@Autowired
	public EquipmentServiceImpl(EquipmentMapper equipmentMapper) {
		this.equipmentMapper = equipmentMapper;
	}

	@Override
	public List<Equipment> searchEquipments() {
		return equipmentMapper.searchEquipmentList();
	}
}
