package com.huafon.office.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.huafon.office.model.entity.Equipment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-06-14 10:45
 **/
@Mapper
public interface EquipmentMapper extends BaseMapper<Equipment> {

	/**
	 * 查询装备列表
	 */
	List<Equipment> searchEquipmentList();

	/**
	 * 查询会议室的设备列表
	 * @param meetingRoomId 会议室ID
	 */
	List<Equipment> searchEquipmentByMeetingRoom(@Param("meetingRoomId") Integer meetingRoomId);

	/**
	 * 创建会议室装备
	 * @param meetingRoomId 会议室ID
	 * @param equipments 设备列表
	 */
	void createMeetingRoomEquipments(@Param("meetingRoomId") Integer meetingRoomId, @Param("equipments") Collection<Equipment> equipments);

	/**
	 * 删除会议室办公设备
	 * @param meetingRoomId 会议室ID
	 * @param equipments 设备列表
	 */
	void deleteMeetingRoomEquipments(@Param("meetingRoomId") Integer meetingRoomId, @Param("equipments") Collection<Equipment> equipments);
}
