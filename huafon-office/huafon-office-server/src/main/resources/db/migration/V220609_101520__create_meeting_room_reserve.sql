drop table if exists hf_office_meeting_room_reserve;
create table hf_office_meeting_room_reserve
(
    id                  serial
        constraint hf_office_meeting_room_reserve_pk
            primary key,
    meeting_room_id     integer     not null,
    meeting_name        varchar(32) not null,
    meeting_description varchar(128) default ''::character varying,
    remark              varchar(128) default ''::character varying,
    reserve_date        date        not null,
    reserve_begin_time  timestamp   not null,
    reserve_end_time    timestamp   not null,
    status              integer      default 0,
    cancel_time         timestamp,
    create_by           integer     not null,
    create_time         timestamp,
    modify_by           integer     not null,
    modify_time         timestamp,
    is_del              integer      default 0
);

comment on table hf_office_meeting_room_reserve is '会议室预约';

comment on column hf_office_meeting_room_reserve.meeting_room_id is '关联meeting_room.id';

comment on column hf_office_meeting_room_reserve.meeting_name is '会议名称';

comment on column hf_office_meeting_room_reserve.meeting_description is '会议描述';

comment on column hf_office_meeting_room_reserve.remark is '备注';

comment on column hf_office_meeting_room_reserve.reserve_end_time is '预约结束时间';

comment on column hf_office_meeting_room_reserve.reserve_begin_time is '预约会议开始时间';

comment on column hf_office_meeting_room_reserve.status is '预约记录状态：0待开始,1进行中,2结束,-1取消';

comment on column hf_office_meeting_room_reserve.reserve_date is '会议日期';

comment on column hf_office_meeting_room_reserve.cancel_time is '取消时间：如果预约被取消则存在';


create index hf_office_meeting_room_reserve__room_id_idx
    on hf_office_meeting_room_reserve (meeting_room_id);
