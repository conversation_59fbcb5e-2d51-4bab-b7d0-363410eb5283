drop table if exists hf_office_meeting_room_opening_time;
create table hf_office_meeting_room_opening_time
(
    id                 serial
        constraint hf_office_meeting_room_opening_time_pk
            primary key,
    meeting_room_id    int  not null,
    week               varchar(32),
    opening_begin_time time not null,
    opening_end_time   time not null,
    create_by     int  not null,
    create_time     timestamp,
    modify_by       int not null,
    modify_time     timestamp,
    is_del          integer     default 0
);

comment on table hf_office_meeting_room_opening_time is '会议室开放时间';

comment on column hf_office_meeting_room_opening_time.meeting_room_id is '关联meeting_room.id';

comment on column hf_office_meeting_room_opening_time.week is '星期几';

comment on column hf_office_meeting_room_opening_time.opening_begin_time is '开始时间';

comment on column hf_office_meeting_room_opening_time.opening_end_time is '结束时间';

create index hf_office_meeting_room_opening_time__room_idx
    on hf_office_meeting_room_opening_time (meeting_room_id);
