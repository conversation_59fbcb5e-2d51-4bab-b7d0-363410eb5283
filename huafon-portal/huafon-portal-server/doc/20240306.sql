BEGIN;

-- 个人素养积分增加初始积分字段
ALTER TABLE security_environment.hf_personal_score_rank ADD initialize_score int4 NULL DEFAULT 0;
COMMENT ON COLUMN security_environment.hf_personal_score_rank.initialize_score IS '初始积分';

-- 个人素养积分增加可用积分字段
ALTER TABLE security_environment.hf_personal_score_rank ADD available_score int4 NULL DEFAULT 0;
COMMENT ON COLUMN security_environment.hf_personal_score_rank.available_score IS '可用积分';


-- 同步初始化分数
UPDATE hf_personal_score_rank AS r
SET initialize_score = c.initialize_score
FROM hf_personal_score_initialize_config AS c
WHERE r.tenant_id = c.tenant_id;

-- 同步可用分数
UPDATE hf_personal_score_rank
SET available_score = GREATEST(total_score - initialize_score, 0);

COMMIT;