BEGIN;
--人员入职提醒
INSERT INTO hf_message_config(tenant_id,name,type,context,time_config,context_template,enable,create_by,create_time,modify_by,modify_time,is_del,attribution)
SELECT tenant_id,
       '人员入职提醒',
       'STAFF_ENTRY',
       '【{{所属企业}}】{{内部员工姓名}}（{{成员工号}}）入职{{部门岗位}}，请在人员流动页面确认入职信息。',
       '{"content":"当有新的人员入职时"}',
       '【%s】%s（%s）入职%s，请在人员流动页面确认入职信息。',
       false,
       0,
       now(),
       0,
       now(),
       0,
       'STAFF_TURNOVER'
FROM hf_tenant WHERE is_del = 0;
--人员转岗提醒
INSERT INTO hf_message_config(tenant_id,name,type,context,time_config,context_template,enable,create_by,create_time,modify_by,modify_time,is_del,attribution)
SELECT tenant_id,
       '人员转岗提醒',
       'STAFF_JOB_TRANSFER',
       '【{{所属企业}}】{{内部员工姓名}}（{{成员工号}}）从{{原部门岗位}}转入{{新部门岗位}}，请在人员流动页面确认转岗信息。',
       '{"content":"当有新的人员转岗时"}',
       '【%s】%s（%s）从%s转入%s，请在人员流动页面确认转岗信息。',
       false,
       0,
       now(),
       0,
       now(),
       0,
       'STAFF_TURNOVER'
FROM hf_tenant WHERE is_del = 0;
--人员离职提醒
INSERT INTO hf_message_config(tenant_id,name,type,context,time_config,context_template,enable,create_by,create_time,modify_by,modify_time,is_del,attribution)
SELECT tenant_id,
       '人员离职提醒',
       'STAFF_DEPART',
       '【{{所属企业}}】{{内部员工姓名}}（{{成员工号}}）从{{部门岗位}}离职，请在人员流动页面确认离职信息。',
       '{"content":"当有新的人员离职时"}',
       '【%s】%s（%s）从%s离职，请在人员流动页面确认离职信息。',
       false,
       0,
       now(),
       0,
       now(),
       0,
       'STAFF_TURNOVER'
FROM hf_tenant WHERE is_del = 0;

--初始化消息接收渠道
INSERT INTO hf_message_config_channel(config_id,channel,enable,create_by,create_time,modify_by,modify_time,is_del)
SELECT id,
       'SYS',
       true,
       0,
       now(),
       0,
       now(),
       0
FROM hf_message_config where type IN ('STAFF_ENTRY','STAFF_JOB_TRANSFER','STAFF_DEPART');
INSERT INTO hf_message_config_channel(config_id,channel,enable,create_by,create_time,modify_by,modify_time,is_del)
SELECT id,
       'SMS',
       false,
       0,
       now(),
       0,
       now(),
       0
FROM hf_message_config where type IN ('STAFF_ENTRY','STAFF_JOB_TRANSFER','STAFF_DEPART');
INSERT INTO hf_message_config_channel(config_id,channel,enable,create_by,create_time,modify_by,modify_time,is_del)
SELECT id,
       'FEISHU',
       false,
       0,
       now(),
       0,
       now(),
       0
FROM hf_message_config where type IN ('STAFF_ENTRY','STAFF_JOB_TRANSFER','STAFF_DEPART');
INSERT INTO hf_message_config_channel(config_id,channel,enable,create_by,create_time,modify_by,modify_time,is_del)
SELECT id,
       'WECHAT',
       false,
       0,
       now(),
       0,
       now(),
       0
FROM hf_message_config where type IN ('STAFF_ENTRY','STAFF_JOB_TRANSFER','STAFF_DEPART');
INSERT INTO hf_message_config_channel(config_id,channel,enable,create_by,create_time,modify_by,modify_time,is_del)
SELECT id,
       'DINGTALK',
       false,
       0,
       now(),
       0,
       now(),
       0
FROM hf_message_config where type IN ('STAFF_ENTRY','STAFF_JOB_TRANSFER','STAFF_DEPART');
INSERT INTO hf_message_config_channel(config_id,channel,enable,create_by,create_time,modify_by,modify_time,is_del)
SELECT id,
       'EMAIL',
       false,
       0,
       now(),
       0,
       now(),
       0
FROM hf_message_config where type IN ('STAFF_ENTRY','STAFF_JOB_TRANSFER','STAFF_DEPART');
INSERT INTO hf_message_type(type,remark,is_del,create_time,modify_time)
VALUES ('STAFF_ENTRY','人员入职提醒',0,now(),now()),('STAFF_JOB_TRANSFER','人员转岗提醒',0,now(),now()),('STAFF_DEPART','人员离职提醒',0,now(),now());

ALTER TABLE "security_environment"."hf_user"
    ADD COLUMN "permanent_effective" bool DEFAULT true,
    ADD COLUMN "effective_date" timestamp;

COMMENT ON COLUMN "security_environment"."hf_user"."permanent_effective" IS '账户有效期 t|永久有效 f|有效期至';

COMMENT ON COLUMN "security_environment"."hf_user"."effective_date" IS '有效期日期';
COMMIT;