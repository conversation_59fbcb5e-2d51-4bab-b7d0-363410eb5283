<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huafon.dao.mapper.UserDeptPostMapper">

    <insert id="batchInsert">
        INSERT INTO
        hf_user_dept_post(
        tenant_id,
        user_id,
        dept_id,
        post_id,
        create_by,
        create_time,
        data_from)
        VALUES
        <foreach collection="list" item="emp" separator=",">
            (#{emp.tenantId},
            #{emp.userId},
            #{emp.deptId},
            #{emp.postId},
            #{emp.createBy},
            #{emp.createTime},
            #{emp.dataFrom})
        </foreach>
    </insert>

    <update id="deleteByUserIdAndDeptId">
        UPDATE
        hf_user_dept_post
        SET
        is_del = 1,
        modify_by = #{modifyBy},
        modify_time = #{modifyDate}
        WHERE
        is_del = 0
        AND
        dept_id = #{deptId}
        AND
        user_id IN
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="deleteByUserIdAndTenantId">
        UPDATE
            hf_user_dept_post
        SET
            is_del = 1,
            modify_by = #{modifyBy},
            modify_time = #{modifyDate}
        WHERE
            is_del = 0
          AND
            tenant_id = #{tenantId}
          AND
            user_id = #{userId}
    </update>

    <update id="deleteByUserIdsAndTenantId">
        UPDATE
            hf_user_dept_post
        SET
            is_del = 1,
            modify_by = #{modifyBy},
            modify_time = #{modifyDate}
        WHERE
            is_del = 0
        <if test="tenantId != null">
            AND tenant_id = #{tenantId,jdbcType=INTEGER}
        </if>
          AND
            user_id IN
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="deleteByUserIdAndPostIdWithTenantId">
        UPDATE
            hf_user_dept_post
        SET
            is_del = 1,
            modify_by = #{modifyBy},
            modify_time = #{modifyDate}
        WHERE
            is_del = 0
          AND tenant_id = #{tenantId}
          AND user_id = #{userId}
          AND post_id = #{postId}
    </update>
    <update id="unbindingUserAndPost">
        UPDATE
            hf_user_dept_post
        SET
            post_id = null,
            modify_by = #{modifyBy},
            modify_time = #{modifyDate}
        WHERE
            is_del = 0
          AND tenant_id = #{tenantId}
          AND user_id = #{userId}
          AND post_id = #{postId}
    </update>

    <update id="deleteByUserIdsAndPostIdWithTenantId">
        UPDATE
            hf_user_dept_post
        SET
            is_del = 1,
            modify_by = #{modifyBy},
            modify_time = #{modifyDate}
        WHERE
            is_del = 0
          AND tenant_id = #{tenantId}
          AND user_id in
            <foreach collection="userIds" separator="," open="(" close=")" item="userId">
              #{userId}
            </foreach>
          and dept_id = #{deptId}
          AND post_id = #{postId}
    </update>

    <update id="deleteByDeptIdWithPostIdIsNull">
        UPDATE
        hf_user_dept_post
        SET
        is_del = 1,
        modify_by = #{modifyBy},
        modify_time = #{modifyDate}
        WHERE
        is_del = 0
        and dept_id in
        <foreach collection="deptIds" separator="," open="(" close=")" item="deptId">
            #{deptId}
        </foreach>
        AND post_id is null
    </update>

    <select id="selectByUserIdAndTenantId" resultType="com.huafon.models.entity.UserDeptPost">
        SELECT
            t1."id",
            t1.tenant_id tenantId,
            t1.user_id userId,
            t1.dept_id deptId,
            t1.post_id postId
        FROM
            hf_user_dept_post AS t1
        WHERE
            t1.is_del = 0
          AND
            t1.user_id = #{userId}
          AND
            t1.tenant_id = #{tenantId}
    </select>

    <select id="selectForDepartmentTree" resultType="com.huafon.models.vo.user.UserForDepartmentTreeVo">
        SELECT
            DISTINCT
            t1.user_id userId,
            t2.username,
            t2.name,
            t2.mobile,
            t2.email,
            t2.gender,
            t2.avatar,
            t1.dept_id departmentId
        FROM
            hf_user_dept_post AS t1 INNER JOIN hf_user t2 ON t1.user_id = t2.user_id
        WHERE
            t1.is_del = 0
          AND
            t2.is_del = 0
          AND
            t1.dept_id is not null
       <if test="tenantId != null">
          AND t1.tenant_id = #{tenantId}
       </if>
       <if test="deptIds != null">
          AND t1.dept_id IN
          <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
            #{deptId}
          </foreach>
       </if>
    </select>

    <select id="selectUserIdByDeptId" resultType="java.lang.Integer">
        SELECT
            t1.user_id
        FROM
            hf_user_dept_post AS t1
        WHERE
            t1.is_del = 0
          AND
            t1.dept_id = #{deptId}
    </select>

    <select id="selectBydeptIds" resultType="com.huafon.models.entity.UserDeptPost">
        SELECT
        DISTINCT
        t1.user_id userId,
        t1.dept_id deptId
        FROM
        hf_user_dept_post AS t1
        WHERE
        t1.is_del = 0
        AND t1.dept_id IN
        <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </select>
    <select id="selectCompleteNameByUserIdAndTenantId" resultType="com.huafon.support.dto.UserDeptPostDto">
        SELECT
            p.id,
            p.tenant_id tenantId,
            p.user_id userId,
            d.id deptId,
            d.department_name AS deptName,
            hop.id postId,
            hop.post_name AS postName
        FROM
            hf_user_dept_post AS p
            LEFT JOIN hf_org_department d ON p.dept_id = d.id AND d.is_del = 0
            LEFT JOIN hf_org_post hop ON p.post_id = hop.id AND d.is_del = 0
        WHERE
            p.is_del = 0
          AND
            p.user_id = #{userId}
          AND
            p.tenant_id = #{tenantId}
    </select>

    <select id="getByUserIdAndTenantId" resultType="com.huafon.portal.api.dto.dept.DeptDto">
        SELECT
            t2.id,
            t2.department_name AS name,
            t3.id AS postId,
            t3.post_name AS postName
        FROM
            hf_user_dept_post  t1
                LEFT JOIN hf_org_department t2 ON t1.dept_id = t2.id AND t2.is_del = 0
                LEFT JOIN hf_org_post t3 ON t1.post_id = t3.id AND t3.is_del = 0
        WHERE
            t1.is_del = 0
          AND
            t1.user_id = #{userId}
          AND
            t1.tenant_id = #{tenantId}
    </select>

    <select id="selectByTenantIds" resultType="com.huafon.models.entity.UserDeptPost">
        SELECT t2.id deptId,
        t1.user_id userId
        FROM (SELECT DISTINCT tenant_id,user_id,dept_id,is_del FROM hf_user_dept_post WHERE is_del = 0 AND dept_id IS NOT NULL) t1 INNER JOIN hf_org_department t2 ON t1.dept_id = t2.id
        INNER JOIN hf_user t3 ON t1.user_id = t3.user_id
        WHERE t1.is_del = 0
        AND t2.is_del = 0
        AND t3.is_del = 0
        AND t2.tenant_id IN
        <foreach collection="list" index="index" item="tenantId" open="(" separator="," close=")">
            #{tenantId}
        </foreach>
    </select>

    <select id="statisticsOnlineUser" resultType="com.huafon.models.dto.OnlineDeptUserDTO">
        select hudp.dept_id deptId,
        hvpc.target_user_id userId
        from hf_view_person_card hvpc
        left join (SELECT DISTINCT tenant_id,user_id,dept_id,is_del FROM hf_user_dept_post WHERE is_del = 0 AND dept_id IS NOT NULL) hudp on hvpc.target_user_id= hudp.user_id
        where hvpc.status = 'BIND'
        and hvpc.online_state = 1
        and hudp.is_del = 0
        <if test="list != null and list.size>0">
            AND hudp.tenant_id IN
            <foreach collection="list" index="index" item="tenantId" open="(" separator="," close=")">
                #{tenantId}
            </foreach>
        </if>
        <if test="list != null and list.size>0">
            AND hvpc.tenant_id IN
            <foreach collection="list" index="index" item="tenantId" open="(" separator="," close=")">
                #{tenantId}
            </foreach>
        </if>
    </select>

    <select id="getByUserIds" resultType="com.huafon.portal.api.dto.dept.DeptDto">
        SELECT
            t1.tenant_id AS tenantId,
            t1.user_id AS userId,
            t2.id,
            t2.department_name AS name,
            t3.id AS postId,
            t3.post_name AS postName
        FROM
            hf_user_dept_post t1
                LEFT JOIN hf_org_department t2 ON t1.dept_id = t2.id AND t2.is_del = 0
                LEFT JOIN hf_org_post t3 ON t1.post_id = t3.id AND t3.is_del = 0
        WHERE
            t1.is_del = 0
          AND
            t1.user_id IN
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findByTenantId" resultType="com.huafon.models.entity.UserDeptPost">
        SELECT
        distinct
        t2.id deptId,
        t1.user_id userId
        FROM "hf_user_dept_post" t1 INNER JOIN hf_org_department t2 ON t1.dept_id = t2.id
        INNER JOIN hf_user t3 ON t1.user_id = t3.user_id
        WHERE t1.is_del = 0
        AND t2.is_del = 0
        AND t3.is_del = 0
        AND t2.tenant_id = #{tenantId}
    </select>

    <select id="findByUserIdsAndTenantId" resultType="com.huafon.models.dto.DeptPostPushDTO">
        SELECT
            t1.user_id userId,
            t2.id deptId,
            t2.department_name AS deptName,
            t3.id AS postId,
            t3.post_name AS postName
        FROM
            hf_user_dept_post  t1
                LEFT JOIN hf_org_department t2 ON t1.dept_id = t2.id AND t2.is_del = 0
                LEFT JOIN hf_org_post t3 ON t1.post_id = t3.id AND t3.is_del = 0
        WHERE
            t1.is_del = 0
          AND
            t1.user_id IN
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
          AND
            t1.tenant_id = #{tenantId}
    </select>

</mapper>
