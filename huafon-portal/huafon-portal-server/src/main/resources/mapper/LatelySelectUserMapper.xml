<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huafon.dao.mapper.LatelySelectUserMapper">

    <resultMap id = "latelySelectUserMap" type ="com.huafon.models.entity.LatelySelectUser">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="select_user_id" property="selectUserId"/>
        <result column="lately_time" property="latelyTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <sql id = "commonSql">
        id,
        user_id,
        select_user_id,
        lately_time,
        tenant_id
    </sql>

    <select id="queryByPage" resultType="com.huafon.models.vo.user.UserV2PageRespVO">
        SELECT DISTINCT b.user_id,
        b.username,
        b."name",
        b.mobile,
        b.modify_time modifyTime,
        b.state,
        b.avatar,
        a.lately_time
        FROM (
            SELECT DISTINCT ON (z.select_user_id) z.select_user_id, z.lately_time
            FROM hf_lately_select_user z
            WHERE z.is_del = 0
            AND z.user_id = #{query.userId}
            AND z.tenant_id = #{query.tenantId}
            ORDER BY z.select_user_id ASC, z.lately_time DESC
            ) a
        LEFT JOIN hf_user b ON a.select_user_id = b.user_id
        LEFT JOIN hf_user_tenant c ON a.select_user_id = c.user_id
        WHERE b.is_del = 0
          AND c.tenant_id = #{query.tenantId}
        <if test="query.searchKey != null and query.searchKey != ''">
            AND (
            b.name LIKE CONCAT('%', #{query.searchKey},'%')
            OR b.username LIKE CONCAT('%', #{query.searchKey},'%')
            )
        </if>
        ORDER BY a.lately_time DESC, b.user_id DESC
        LIMIT #{query.threshold}
    </select>

    <insert id="batchInsert">
        INSERT INTO hf_lately_select_user
            (
                 user_id,
                 select_user_id,
                 lately_time,
                 tenant_id,
                 is_del,
                 create_by,
                 create_time,
                 modify_by,
                 modify_time
            )
        VALUES
        <foreach collection="source" item="item" separator=",">
            (
                #{item.userId},
                #{item.selectUserId},
                #{item.latelyTime},
                #{item.tenantId},
                0,
                #{item.createBy},
                #{item.createTime},
                #{item.modifyBy},
                #{item.modifyTime}
            )
        </foreach>
    </insert>

</mapper>
