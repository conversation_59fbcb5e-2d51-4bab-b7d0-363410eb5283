package com.huafon.service;

import com.huafon.models.entity.Permission;

import java.util.List;

/**
 * 菜单-模块映射
 * <AUTHOR>
 * @since 2023-02-08 14:37
 **/
public interface PermissionModuleService {

	/**
	 * 菜单信息转换为模块
	 * @param permissions 菜单列表
	 * @return 模块列表
	 */
	List<String> processPermissionModule(List<Permission> permissions);


	/**
	 * 新菜单映射的模块集合 - 旧菜单映射的集合
	 * @param newPermissions 新菜单列表
	 * @param oldPermissions 旧菜单列表
	 * @return
	 */
	List<String> subtract(List<Permission> newPermissions, List<Permission> oldPermissions);

}
