package com.huafon.service.impl.interceptor;

import com.huafon.portal.api.dto.UserDto;
import com.huafon.portal.api.enums.UserType;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024-05-20 16:55
 **/
@Component
public class UserAutowireInterceptorFactory implements ApplicationContextAware {


	private Map<String, UserAutowireInterceptor> interceptorMap = new HashMap<>();
	private ApplicationContext applicationContext;

	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		this.applicationContext = applicationContext;
		this.interceptorMap = applicationContext.getBeansOfType(UserAutowireInterceptor.class);
	}

	public UserAutowireInterceptor getUserInterceptor(UserType userType) {
		if (userType == null) {
			return null;
		}
		return interceptorMap.get(userType.toString());
	}

	public void interceptor(UserDto userDto) {
		UserType userType = userDto.getUserType();
		if (Objects.isNull(userType)) {
			return ;
		}
		UserAutowireInterceptor userInterceptor = this.getUserInterceptor(userDto.getUserType());
		if (userInterceptor == null) {
			return;
		}

		userInterceptor.interceptor(userDto);
	}
}
