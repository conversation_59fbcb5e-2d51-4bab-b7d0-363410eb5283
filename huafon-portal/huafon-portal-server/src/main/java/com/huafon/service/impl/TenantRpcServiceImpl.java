package com.huafon.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.huafon.dao.mapper.TenantMapper;
import com.huafon.dao.mapper.v2.RelationTenantMapper;
import com.huafon.models.constants.TenantConstants;
import com.huafon.models.entity.Tenant;
import com.huafon.portal.api.dto.TenantDto;
import com.huafon.portal.api.service.TenantRpcService;
import com.huafon.support.config.ProjectContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-09-21 13:38
 **/
@DubboService
@Slf4j
public class TenantRpcServiceImpl implements TenantRpcService {

	@Autowired
	TenantMapper tenantMapper;

	@Autowired
	RelationTenantMapper relationTenantMapper;

	@Autowired
	private RedisTemplate<String, String> redisTemplate;

	private final LoadingCache<Integer, List<Integer>> projectMappings = Caffeine.newBuilder()
			.maximumSize(5)
			.expireAfterWrite(10, TimeUnit.SECONDS)
			.build(this::queryTenantIdByProjectId);

	private List<Integer> queryTenantIdByProjectId(Integer projectId) {
		return tenantMapper.queryTenantIdByProjectId(projectId);
	}

	private final LoadingCache<Integer, Tenant> tenantMappings = Caffeine.newBuilder()
			.maximumSize(10)
			.expireAfterWrite(2, TimeUnit.SECONDS)
			.build(this::queryTenantInfo);

	private Tenant queryTenantInfo(Integer tenantId) {
		return tenantMapper.selectById(tenantId);
	}

	@Override
	public List<TenantDto> queryAllTenant() {
		List<Tenant> tenants = getTenantList();
		return tenants.stream().map(this::convert).collect(Collectors.toList());
	}

	private List<Tenant> getTenantList() {
		List<Tenant> tenants = null;
		String key = TenantConstants.CACHE_ALL_TENANT_INFO;
		String v = redisTemplate.opsForValue().get(key);

		if (v != null && !"[]".equals(v) && v != "") {
			tenants = JSONObject.parseArray(v, Tenant.class);
		}else {
			tenants = tenantMapper.selectAllList();
			if (!CollectionUtils.isEmpty(tenants)) {
				redisTemplate.opsForValue().set(key,
						JSON.toJSONString(tenants), TenantConstants.CACHE_ALL_TENANT_INFO_TIME, TimeUnit.SECONDS);
			}
		}
		return tenants;
	}

	private TenantDto convert(Tenant source) {
		TenantDto target = new TenantDto();
		target.setTenantId(source.getTenantId());
		target.setTenantName(source.getName());
		target.setState(source.getState());
		target.setAbbreviation(source.getAbbreviation());
		target.setProjectId(source.getProjectId());
		target.setCompanyCode(source.getCompanyCode());
		return target;
	}


	@Override
	public TenantDto findTenantById(Integer tenantId) {
		if (tenantId == null) return null;
		Tenant tenant = getTenantInfo(tenantId);
		if (Objects.isNull(tenant)) {
			return null;
		}
		return convert(tenant);
	}

	@Override
	public void updateCompanyCodeById(Integer tenantId, String companyCode) {
		if (Objects.isNull(tenantId)){
			return;
		}
		Tenant tenant = tenantMapper.selectByPrimaryKey(tenantId);
		if (Objects.isNull(tenant)) {
			return ;
		}
		tenant.setCompanyCode(companyCode);
		tenantMapper.updateById(tenant);
	}


	@Override
	public List<TenantDto> queryRelationTenants(Integer tenantId, boolean includeSelf) {
		List<Tenant> tenants = relationTenantMapper.queryRelationTenants(tenantId);
		if (includeSelf) {
			tenants.add(tenantMapper.selectByPrimaryKey(tenantId));
		}

		return tenants.stream().map(this::convert).collect(Collectors.toList());
	}

	@Override
	public List<Integer> queryMapRelationTenant(Integer tenantId) {
		if (Objects.isNull(tenantId)) {
			return new ArrayList<>();
		}

		Tenant tenant = getTenantInfo(tenantId);

		if (Objects.isNull(tenant)) {
			return Collections.singletonList(tenantId);
		} else if (Objects.isNull(tenant.getProjectId())) {
			return Collections.singletonList(tenantId);
		}

		List<Integer> allTenantIds = projectMappings.get(tenant.getProjectId());
		if (allTenantIds == null) {
			allTenantIds = new ArrayList<>();
		}
		if (CollectionUtils.isEmpty(allTenantIds)) {
			allTenantIds.add(tenantId);
		}

		return filterMapTenant(allTenantIds, tenant);
	}

	private Tenant getTenantInfo(Integer tenantId) {
		Tenant tenant = null;
		String key = String.format(TenantConstants.CACHE_TENANT_INFO,tenantId);
		String v = redisTemplate.opsForValue().get(key);
		if (v != null && !"[]".equals(v) && v != "") {
			tenant = JSONObject.parseObject(v, Tenant.class);
		}else {
			tenant = tenantMapper.selectById(tenantId);
			if (Objects.nonNull(tenant)) {
				redisTemplate.opsForValue().set(key,
						JSON.toJSONString(tenant), TenantConstants.CACHE_TENANT_INFO_TIME, TimeUnit.SECONDS);
			}
		}
		return tenant;
	}

	private List<Integer> filterMapTenant(List<Integer> allTenantIds, Tenant tenant) {
		if (CollectionUtils.isEmpty(allTenantIds)) {
			return new ArrayList<>();
		} else if (Objects.isNull(tenant)) {
			return allTenantIds;
		}

		HashSet<Integer> filterTenantIds = new HashSet<>(Optional.ofNullable(tenant.getMapPropertyTenants()).orElse(Collections.emptyList()));
		filterTenantIds.add(tenant.getTenantId());

		return allTenantIds.stream().filter(filterTenantIds::contains).collect(Collectors.toList());
	}

	@Override
	public List<Integer> queryTenantIdsByProjectId(Integer projectId) {
		if (projectId == null || Objects.equals(projectId, ProjectContext._NO_PROJECT_VALUE)) {
			return Collections.singletonList(-1);
		}

		List<Integer> tenantIds = projectMappings.get(projectId);
		if (CollectionUtils.isEmpty(tenantIds)) {
			return Collections.singletonList(-1);
		}

		return tenantIds;
	}

	@Override
	public List<TenantDto> queryTenantByProjectId(Integer projectId) {
		if (Objects.isNull(projectId)) {
			return new ArrayList<>();
		}

		List<Integer> tenantIds = projectMappings.get(projectId);
		if (CollectionUtils.isEmpty(tenantIds)) {
		    return new ArrayList<>();
		}

		return tenantMapper.selectBatchIds(tenantIds).stream().map(this::convert).collect(Collectors.toList());
	}

	@Override
	public List<Integer> queryTenantIdsByProjectIdAndTenant(Integer projectId, Integer tenantId) {
		List<Integer> allTenantIds = this.queryTenantIdsByProjectId(projectId);
		if (Objects.isNull(tenantId)) {
			log.info("通过projectId, tenantId 获取租户Id列表，租户ID为空：{}", projectId);
			return allTenantIds;
		}

		if (CollectionUtils.isEmpty(allTenantIds)) {
			allTenantIds.add(tenantId);
		}

		Tenant tenant = tenantMappings.get(tenantId);
		if (Objects.isNull(tenant)) {
			return allTenantIds;
		}

		return filterMapTenant(allTenantIds, tenant);
	}
}
