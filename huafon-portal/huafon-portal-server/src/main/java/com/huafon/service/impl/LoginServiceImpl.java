package com.huafon.service.impl;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.LineCaptcha;
import cn.hutool.captcha.generator.RandomGenerator;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.huafon.admin.api.constants.SysWindowConstants;
import com.huafon.admin.api.dto.KeepLoginRedisDTO;
import com.huafon.admin.api.dto.PasswordLimitDTO;
import com.huafon.admin.api.service.ISysWindowInfoRpcService;
import com.huafon.common.config.TenantContext;
import com.huafon.common.utils.BaseUtils;
import com.huafon.common.utils.bean.BeanUtils;
import com.huafon.config.UserConfig;
import com.huafon.dao.mapper.*;
import com.huafon.models.constants.LoginConstants;
import com.huafon.models.constants.PermissionConstants;
import com.huafon.models.constants.RedisConstants;
import com.huafon.models.dto.UserInfoDto;
import com.huafon.models.entity.*;
import com.huafon.models.enums.LoginOriginEnum;
import com.huafon.models.enums.MemberTypeEnum;
import com.huafon.models.login.YklEmsLoginReq;
import com.huafon.models.reqo.ReqLogin;
import com.huafon.models.reqo.ReqThirdLogin;
import com.huafon.models.reqo.V2ReqLogin;
import com.huafon.models.reqo.user.ReqUserDisable;
import com.huafon.models.vo.GraphicCodeVO;
import com.huafon.models.vo.permission.PermissionListVo;
import com.huafon.models.vo.permission.PermissionTypeVo;
import com.huafon.models.vo.permission.TenantPermissionListVo;
import com.huafon.models.vo.tenant.TenantVo;
import com.huafon.models.vo.user.UserDeptPostVO;
import com.huafon.models.vo.user.UserLoginVo;
import com.huafon.models.vo.user.UserResultVo;
import com.huafon.service.LoginService;
import com.huafon.service.PermissionService;
import com.huafon.service.v2.PermissionV2Service;
import com.huafon.service.v2.UserV2Service;
import com.huafon.support.config.UserContext;
import com.huafon.support.core.pojo.R;
import com.huafon.support.exceptions.ServiceException;
import com.huafon.thrd.YklEmsApiClient;
import com.huafon.thrd.dto.DingTalkTenantGetUserDetailRsp;
import com.huafon.thrd.dto.WechatGetUserDetailByTicketRsp;
import com.huafon.thrd.dto.ykl.YklEmsCheckTokenReq;
import com.huafon.thrd.dto.ykl.YklEmsGetTokenReq;
import com.huafon.thrd.dto.ykl.YklEmsRsp;
import com.huafon.thrd.service.SsoService;
import com.huafon.thrd.vo.HuafonSsoResponse;
import com.huafon.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jasig.cas.client.util.AssertionHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.huafon.models.enums.TenantStateEnum.ENABLE;

@Service
@Slf4j
public class LoginServiceImpl implements LoginService {

    @Resource
    private UserMapper userMapper;
    @Resource
    private JwtToken jwtToken;
    @Resource
    private PermissionMapper permissionMapper;
    @Resource
    private UserRoleMapper userRoleMapper;
    @Resource
    private PermissionService permissionService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private PostRoleMapper postRoleMapper;
    @Resource
    private UserTenantMapper userTenantMapper;
    @Resource
    private TenantMapper tenantMapper;
    @Resource
    private SsoService ssoService;
    @Resource
    private UserConfig userConfig;
    @Resource
    private PermissionV2Service permissionV2Service;
    @Resource
    private UserV2Service userV2Service;
    @DubboReference
    private ISysWindowInfoRpcService iSysWindowInfoRpcService;

    @Resource
    private YklEmsApiClient yklEmsApiClient;

    @Value("${hengyi-hse-web-url:http://xxxxx.com}")
    private String hengyiHseWebUrl;

    @Value("${ykl.ems-appKey}")
    private String yklAppKey;

    @Value("${ykl.ems-appSecret}")
    private String yklAppSecret;

    @Value("${ykl.ems-url}")
    private String yklEmsUrl;

    public static final String YKL_LOGIN_FORMAT = "YKLLogin:%s";

    @Override
    public UserResultVo login(ReqLogin reqLogin, LoginOriginEnum loginFrom) {
        String username = reqLogin.getUsername().trim();
        User user = userMapper.loginByName(username);
        if (null == user) {
            throw new ServiceException("登录失败，用户名错误，未找到该用户");
        }
        String password = Md5Util.stringToMD5(reqLogin.getPassword());
        if (!password.equals(user.getPassword())) {
            throw new ServiceException("登录失败，密码错误，请检查输入后再试");
        }
        if (LoginConstants.STATE_ONE == user.getState()) {
            throw new ServiceException("登录失败，您的账户已被禁用，请联系公司管理员启用");
        }
        return readUserResult(user, loginFrom);
    }

    @Override
    public UserResultVo v2Login(V2ReqLogin reqLogin, LoginOriginEnum loginFrom) {
        String username = reqLogin.getUsername().trim();
        User user = userMapper.loginByName(username);
        if (Objects.isNull(user)) {
            throw new ServiceException("登录失败，用户名错误，未找到该用户");
        }
        // 验证码校验
        verifyCode(reqLogin.getCodeId(), reqLogin.getCode());
        String password = null;

        if (StringUtils.isNotBlank(reqLogin.getMd5Password())) {
            password = reqLogin.getMd5Password();
        } else if (StringUtils.isNotBlank(reqLogin.getPassword())) {
            password = Md5Util.stringToMD5(reqLogin.getPassword());
        }

        PasswordLimitDTO passwordLimitDTO = iSysWindowInfoRpcService.get();
        checkUser(user);
        if (!Objects.equals(password, user.getPassword())) {
            if (user.getIsAdministrator() == 0) {
                // 判断用户是否租户联系人
                boolean isLinkman = userV2Service.isLinkman(user.getUserId());
                if (!isLinkman) {
                    if (Objects.nonNull(passwordLimitDTO) && Boolean.TRUE.equals(passwordLimitDTO.getPasswordLimit())) {
                        Integer maxFailCount = passwordLimitDTO.getPasswordLimitTime();
                        double failCount = redisUtil.hincr(LoginConstants.LOGIN_FAIL, username, 1);
                        if (failCount >= maxFailCount) {
                            redisUtil.hdel(LoginConstants.LOGIN_FAIL, username);
                            // 禁用用户账号
                            ReqUserDisable reqUserDisable = new ReqUserDisable();
                            reqUserDisable.setUserId(user.getUserId());
                            reqUserDisable.setState(2);
                            userV2Service.switchState(reqUserDisable);
                            log.info("passwordLimitDTO============={}", passwordLimitDTO);
                            if (Objects.equals(passwordLimitDTO.getAccountUnlockType(), 2) && Objects.nonNull(passwordLimitDTO.getAccountUnlockMinute()) &&
                                    passwordLimitDTO.getAccountUnlockMinute() > 0) {
                                //*分钟后自动解禁
                                LocalDateTime localDateTime = LocalDateTime.now().plusMinutes(passwordLimitDTO.getAccountUnlockMinute());
                                redisUtil.set(String.format(LoginConstants.ACCOUNT_RELIEVE_DISABLED_TIME, user.getUserId()), localDateTime.toString());
                                throw new ServiceException("登录失败，您的账户已被禁用，请" + passwordLimitDTO.getAccountUnlockMinute() + "分钟后重试");
                            }
                            throw new ServiceException("登录失败，您的账户已被禁用，请联系公司管理员启用");
                        } else {
                            int leftCount = (maxFailCount - (int) failCount);
                            throw new ServiceException("登录失败，密码错误，您还有" + leftCount + "次机会");
                        }
                    }
                }
            }
            throw new ServiceException("登录失败，密码错误，请检查输入后再试");
        } else {
            redisUtil.hdel(LoginConstants.LOGIN_FAIL, username);
        }
        return readUserResult(user, loginFrom);
    }

    @Override
    public UserResultVo baseCenterLogin() {
        com.huafon.support.dto.UserInfoDto userInfoDto = UserContext.get();
        User user = userMapper.loginByName(userInfoDto.getUsername());
        checkUser(user);
        return readUserResult(user, LoginOriginEnum.PC);
    }

    @Override
    public UserResultVo thirdLogin(ReqThirdLogin req) {
        if (Objects.isNull(req.getUsername())) {
            throw new ServiceException("登录失败！");
        }
        User user = userMapper.loginByName(req.getUsername());
        checkUser(user);
        return readUserResult(user, LoginOriginEnum.PC);
    }

    /**
     * @param user
     * @return com.huafon.models.vo.user.UserResultVo
     * <AUTHOR>
     * @describe: 返回登录信息
     * @date 2022/8/23 19:44
     */
    private UserResultVo readUserResult(User user, LoginOriginEnum loginFrom) {
        UserLoginVo userLoginVo = BeanUtils.convert(user, UserLoginVo.class);
        userLoginVo.setAdministrator(user.getIsAdministrator());
        //租户信息插入
        List<UserTenant> userTenantList = new ArrayList<>();
//        Integer tenantId = null;
        if (user.getIsAdministrator().equals(LoginConstants.IS_ADMIN_ONE)) {
            List<Tenant> tenantList = tenantMapper.findAllList();
            for (Tenant tenant : tenantList) {
                UserTenant userTenant = new UserTenant();
                userTenant.setName(tenant.getName());
                userTenant.setUserId(user.getUserId());
                userTenant.setTenantId(tenant.getTenantId());
                userTenant.setProjectId(tenant.getProjectId());
                userTenantList.add(userTenant);
            }
        } else {
            userTenantList = userTenantMapper.selectListByUserId(user.getUserId());
            if (!CollectionUtils.isEmpty(userTenantList)) {
                List<Integer> tenantIds = userTenantList.stream().map(UserTenant::getTenantId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                List<Tenant> tenants = tenantMapper.queryByTenantIds(tenantIds, ENABLE.getCode());
                if (CollectionUtils.isEmpty(tenants)) {
                    if (userTenantList.size() == 1) {
                        throw new ServiceException(String.format("企业“%s”无法正常使用，请联系管理员处理", userTenantList.get(0).getName()));
                    } else {
                        throw new ServiceException("账户关联的企业均无法正常使用，请联系管理员处理！");
                    }
                }
                Set<Integer> availableTenants = tenants.stream().map(Tenant::getTenantId).filter(Objects::nonNull).collect(Collectors.toSet());
                userTenantList = userTenantList.stream().filter(item -> availableTenants.contains(item.getTenantId())).collect(Collectors.toList());
            }
        }

        if (userTenantList.isEmpty()) {
            throw new ServiceException("账户未授予使用系统权限，请联系管理员处理");
        }
        List<TenantVo> tenantVoList = BeanUtils.convert(TenantVo.class, userTenantList);

        if (LoginOriginEnum.MOBILE.getClientType() == loginFrom.getClientType()) {
            tenantVoList.sort(Comparator.comparing(TenantVo::getSort, Comparator.nullsLast(Comparator.naturalOrder())));
        }

        userLoginVo.setTenantList(tenantVoList);
        userLoginVo.setTenantId(processUserTenant(user, userTenantList, loginFrom));

        List<UserDeptPostVO> userDeptPostVOS = userMapper.findByUserIdAndTenantId(user.getUserId(), null);
        userLoginVo.setUserDeptPostVOList(userDeptPostVOS);

        //人员组织信息
        UserInfoDto userInfo = new UserInfoDto();

        //token 生成

        userInfo.setUserId(user.getUserId());
        userInfo.setUsername(user.getUsername());
        userInfo.setName(user.getName());
        userInfo.setIsAdmin(user.getIsAdministrator());
        userInfo.setAvatar(user.getAvatar());
        userInfo.setMobile(user.getMobile());
        String token = jwtToken.encodeUserInfo(userInfo);

        //存入redies
        //redis操作
        String loginCacheKey = String.format(LoginConstants.loginListCacheKeyFormat, user.getUserId());
        Long listSize = redisUtil.lGetListSize(loginCacheKey);
        Integer userOnlineNum = userConfig.getUserOnlineNum();
        if (!(userOnlineNum > 0)) {
            userOnlineNum = 1;
        }
        if (userOnlineNum <= listSize) {
            for (int i = 0; i <= (listSize - userOnlineNum); i++) {
                redisUtil.lGet(loginCacheKey);
            }
        }
        redisUtil.lSet(loginCacheKey, token, convertLoginTime());
        //输出信息
        UserResultVo userResultVo = new UserResultVo();
        userResultVo.setToken(token);
        userResultVo.setUserLoginVo(userLoginVo);
        return userResultVo;
    }

    private void checkUser(User user) {
        if (Objects.isNull(user)) {
            throw new ServiceException("登录失败，用户名错误，未找到该用户");
        }
        if (LoginConstants.STATE_ONE == user.getState()) {
            Object accountRelieveDisabledTimeStr = redisUtil.get(String.format(LoginConstants.ACCOUNT_RELIEVE_DISABLED_TIME, user.getUserId()));
            log.info("accountRelieveDisabledTimeStr========{}", accountRelieveDisabledTimeStr);
            if (null != accountRelieveDisabledTimeStr) {
                LocalDateTime accountRelieveDisabledTime = LocalDateTime.parse(accountRelieveDisabledTimeStr.toString());
                Duration between = Duration.between(LocalDateTime.now(), accountRelieveDisabledTime);
                if (between.toMillis() > 0) {
                    if (between.toMinutes() == 0) {
                        throw new ServiceException("登录失败，您的账户已被禁用，请" + between.toMillis() / 1000 + "秒后重试");
                    }
                    throw new ServiceException("登录失败，您的账户已被禁用，请" + between.toMinutes() + "分钟后重试");
                } else {
                    // 启用用户账号
                    ReqUserDisable reqUserDisable = new ReqUserDisable();
                    reqUserDisable.setUserId(user.getUserId());
                    reqUserDisable.setState(1);
                    userV2Service.switchState(reqUserDisable);
                    user.setState(1);
                    redisUtil.del(String.format(LoginConstants.ACCOUNT_RELIEVE_DISABLED_TIME, user.getUserId()));
                }
            } else {
                throw new ServiceException("登录失败，您的账户已被禁用，请联系公司管理员启用");
            }
        }
    }

    private long convertLoginTime() {
        long length = LoginConstants.LOGIN_LENGTH;
        Object keepLoginConfig = redisUtil.get(SysWindowConstants.KEEP_LOGIN_CONFIG);
        if (Objects.nonNull(keepLoginConfig)) {
            KeepLoginRedisDTO dto = JSONObject.parseObject(keepLoginConfig.toString(), KeepLoginRedisDTO.class);
            if (Objects.equals(dto.getKeepLoginType(), 2)) {
                length = 60 * 60 * 48;
            } else if (Objects.equals(dto.getKeepLoginType(), 3)) {
                length = 60 * 60 * 24 * 7;
            } else if (Objects.equals(dto.getKeepLoginType(), 4) && Objects.nonNull(dto.getKeepLoginDay()) && dto.getKeepLoginDay() > 0) {
                length = 60 * 60 * 24 * dto.getKeepLoginDay();
            }
        }
        log.info("保持登录时长================================{}", length);
        return length;
    }

    /**
     * 处理登录选中租户逻辑
     *
     * @param user
     * @param userTenantList
     * @param loginFrom
     * @return
     */
    private Integer processUserTenant(User user, List<UserTenant> userTenantList, LoginOriginEnum loginFrom) {
        if (Objects.isNull(user) || CollectionUtils.isEmpty(userTenantList)) {
            return null;
        }

        Integer userId = user.getUserId();
        Integer recentTenant = null;
        try {
            Object value = redisUtil.get(RedisConstants.getRecentSelectTenantCacheKey(userId));
            if (Objects.nonNull(value)) {
                recentTenant = Integer.parseInt((String) value);
            }
        } catch (Exception e) {
            log.error("Redis 获取最近选择的租户错误：{}", e.getMessage());
        }

        List<Integer> sortTenant = new ArrayList<>();
        sortTenant.add(Optional.ofNullable(recentTenant).orElse(-1));
        Comparator<UserTenant> tenantVoComparator = (o1, o2) -> {
            int i1 = !sortTenant.contains(o1.getTenantId()) ? sortTenant.size() + o1.getTenantId() : sortTenant.indexOf(o1.getTenantId());
            int i2 = !sortTenant.contains(o2.getTenantId()) ? sortTenant.size() + o2.getTenantId() : sortTenant.indexOf(o2.getTenantId());

            return Integer.compare(i1, i2);
        };

        userTenantList.sort(tenantVoComparator);

        if (!Objects.equals(user.getIsAdministrator(), LoginConstants.IS_ADMIN_ONE)) {
            Integer firstHasPermissionTenant = findHasPermissionTenant(userTenantList, userId, loginFrom);
            if (Objects.nonNull(firstHasPermissionTenant) && !Objects.equals(sortTenant.get(0), firstHasPermissionTenant)) {
                sortTenant.add(0, firstHasPermissionTenant);
                userTenantList.sort(tenantVoComparator);
            }
        }

        return userTenantList.get(0).getTenantId();
    }

    private Integer findHasPermissionTenant(List<UserTenant> tenants, Integer userId, LoginOriginEnum loginFrom) {

        for (UserTenant userTenant : tenants) {
            if (Objects.equals(userTenant.getMemberType(), MemberTypeEnum.LINKMAN.getCode())) {
                List<TenantPermissionListVo> permissions = permissionV2Service.queryTenantPermission(userTenant.getTenantId(), 1, loginFrom.getClientType());
                if (CollectionUtils.isEmpty(permissions)) {
                    continue;
                }
                return userTenant.getTenantId();
            }
            List<UserRole> userRoleList = userRoleMapper.selectListByUserId(userId, Collections.singletonList(userTenant.getTenantId()));
            if (!CollectionUtils.isEmpty(userRoleList)) {
                Set<Integer> roleIds = userRoleList.stream().map(UserRole::getRoleId).collect(Collectors.toSet());
                List<TenantPermissionListVo> permissions = permissionV2Service.selectListByRoleId(roleIds, loginFrom.getClientType(), userTenant.getTenantId());
                if (!CollectionUtils.isEmpty(permissions)) {
                    return userTenant.getTenantId();
                }
            }
        }

        return null;
    }


    @Override
    public R logout() {
        com.huafon.support.dto.UserInfoDto userInfo = UserContext.get();
        if (null == userInfo) {
            throw new ServiceException("登录错误");
        }
        log.info("user == {}", JSONObject.toJSONString(userInfo));
        int userId = userInfo.getUserId().intValue();
        String loginCacheKey = String.format(LoginConstants.loginListCacheKeyFormat, userId);
        redisUtil.lRemove(loginCacheKey, 1, userInfo.getToken());
        return R.ok();
    }

    @Override
    public R getPermission(Integer clientType) {
        com.huafon.support.dto.UserInfoDto userInfo = UserContext.get();
        if (null == userInfo) {
            throw new ServiceException("登录错误");
        }

        List<Integer> tenantIds = (List<Integer>) TenantContext.get();
        Integer userId = userInfo.getUserId().intValue();
        int isAdmin = userInfo.getIsAdmin();

        List<PermissionListVo> treeList = new ArrayList<>();

        List<Permission> permissionList = new ArrayList<>();
        //超级管理员有所有权限
        if (isAdmin == LoginConstants.IS_ADMIN_ONE) {
            permissionList = permissionMapper.selectList(PermissionConstants.IS_USE_ONE, tenantIds, clientType);
        } else {
            Set<Integer> roleIds = new HashSet<>();
            User user = userMapper.selectByPrimaryKey(userId);

            Set<Long> postSet = new HashSet<>();
            //如果有岗位，查询岗位的权限
            if (null != user.getPostId() && user.getPostId() > 0) {
                postSet.add(user.getPostId());
            }
            List<UserPost> userPostList = postRoleMapper.selectUserPostList(userId);
            for (UserPost userPost : userPostList) {
                postSet.add(userPost.getPostId());
            }
            if (postSet.size() > 0) {
                List<PostRole> postRoleList = postRoleMapper.selectListByPostIds(new ArrayList<>(postSet), tenantIds);
                Set<Integer> roleIdsTmp1 = postRoleList.stream().map(PostRole::getRoleId).collect(Collectors.toSet());
                roleIds.addAll(roleIdsTmp1);
            }

            //获取用户所属角色的权限
            List<UserRole> userRoleList = userRoleMapper.selectListByUserId(userId, tenantIds);

            Set<Integer> roleIdsTmp2 = userRoleList.stream().map(UserRole::getRoleId).collect(Collectors.toSet());

            roleIds.addAll(roleIdsTmp2);

            if (roleIds.size() > 0) {
                //用户权限列表
                permissionList = permissionMapper.selectListByRoleId(roleIds, clientType);
            }
        }

        //组合成树形结构
        if (permissionList.size() > 0) {
            treeList = permissionService.listToTree(permissionList, 0);
        }
        return R.ok(treeList);
    }

    @Override
    public void hengyiLogin(HttpServletResponse response) {
        String username = AssertionHolder.getAssertion().getPrincipal().getName();
        log.info("hengyiLogin---------------------------------------------------------:{}", username);
        if (Objects.isNull(username)) {
            throw new ServiceException("登录失败！");
        }
        // 设置状态码为302表示临时重定向，或者使用301表示永久重定向
        response.setStatus(HttpServletResponse.SC_MOVED_TEMPORARILY);
        // 设置重定向的目标URL
        log.info("hengyiHseWebUrl---------------------------------------------------------:{}", hengyiHseWebUrl);
        String encrypt = null;
        try {
            encrypt = AESUtil.encrypt(username);
            encrypt = URLEncoder.encode(encrypt, "UTF-8");
        } catch (Exception e) {
            log.info("aes加密失败：-------------", e);
        }

        Cookie cookie = new Cookie("hengyiAccount", encrypt);
        // 设置 Cookie 的最大存活时间，单位为秒，这里设置为 1 小时
        cookie.setMaxAge(3600);
        // 设置 Cookie 的路径，该 Cookie 对整个应用都有效
        cookie.setPath("/");
        // 将 Cookie 添加到响应中
        response.addCookie(cookie);
        response.setHeader("Location", hengyiHseWebUrl + "&username=" + username);
    }

    @Override
    public UserResultVo yklEmsLogin(YklEmsLoginReq req) {
        log.info("弈科莱单点登录----token--------------:{}", req.getToken());
        String key = String.format(YKL_LOGIN_FORMAT, yklAppKey);
        Object token = redisUtil.get(key);
        if (Objects.isNull(token)) {
            YklEmsGetTokenReq getTokenReq = new YklEmsGetTokenReq();
            getTokenReq.setAppKey(yklAppKey);
            getTokenReq.setAppSecret(yklAppSecret);
            log.info("getToken,req----------------------------{}", JSONObject.toJSONString(getTokenReq));
            JSONObject res = yklEmsApiClient.getToken(getTokenReq);
            log.info("getToken,res----------------------------{}", JSONObject.toJSONString(res));
            if (Objects.nonNull(res)) {
                log.info("getToken,res1----------------------------{}", JSONObject.toJSONString(res));
                if(Objects.equals(res.getInteger("Code"), 0) && Objects.nonNull(res.getString("Data"))){
                    token = res.getString("Data");
                    redisUtil.set(key, String.valueOf(token), 60 * 60);
                }
            }
        }
        if (Objects.isNull(token)) {
            log.info("token为空---------------------------------------------------");
            throw new ServiceException("登录失败!");
        }
        YklEmsCheckTokenReq checkTokenReq = new YklEmsCheckTokenReq();
        checkTokenReq.setToken(req.getToken());
        JSONObject yklEmsRsp = yklEmsApiClient.checkUserToken(checkTokenReq, "Basic " + token);
        log.info("checkToken,res----------------------------{}", JSONObject.toJSONString(yklEmsRsp));
        if (Objects.nonNull(yklEmsRsp)) {
            if(Objects.equals(yklEmsRsp.getInteger("Code"), 0) && Objects.nonNull(yklEmsRsp.getString("Data"))){
                String thirdUserId = String.valueOf(yklEmsRsp.getString("Data"));
                User user = userMapper.findByThirdUserId(thirdUserId);
                checkUser(user);
                return readUserResult(user, LoginOriginEnum.PC);
            }
        }
        throw new ServiceException("登录失败!");
    }

    @Override
    public UserResultVo ssoLogin(String code) {
        HuafonSsoResponse.HuaFonUserInfoResponse huaFonUserInfo = ssoService.getUserInfo(code);
        String username = huaFonUserInfo.getUsername();
        User user = userMapper.loginByName(username);
        if (null == user) {
            throw new ServiceException("不存在该用户");
        }
        if (LoginConstants.STATE_ONE == user.getState()) {
            throw new ServiceException("用户已禁用，请联系管理员");
        }
        return readUserResult(user, LoginOriginEnum.PC);
    }

    @Override
    public UserResultVo ssoLoginByDingTalk(String code) {
        DingTalkTenantGetUserDetailRsp.UserDetailResponse userDetail = ssoService.getUserInfoByDingTalk(code);
        String username = userDetail.getJob_number();
        User user = userMapper.loginByName(username);
        if (null == user) {
            throw new ServiceException("不存在该用户");
        }
        if (LoginConstants.STATE_ONE == user.getState()) {
            throw new ServiceException("用户已禁用，请联系管理员");
        }
        UserResultVo info = readUserResult(user, LoginOriginEnum.PC);
        log.info("[单点登录] 钉钉 code {} , {}", code, JSON.toJSONString(info));
        return info;
    }

    @Override
    public UserResultVo ssoLoginByWechat(String code) {
        WechatGetUserDetailByTicketRsp userDetail = ssoService.getUserDetailByWechat(code);
        String mobile = userDetail.getMobile();
        List<User> userList = userMapper.selectUserListByMobile(mobile);
        BaseUtils.isEmptyThr(userList, "不存在该用户");
        BaseUtils.isThr(userList.size() > 1, "同一手机号存在多个用户");
        User user = userList.get(0);
        if (LoginConstants.STATE_ONE == user.getState()) {
            throw new ServiceException("用户已禁用，请联系管理员");
        }
        UserResultVo info = readUserResult(user, LoginOriginEnum.PC);
        log.info("[单点登录] 企业微信 code {} , {}", code, JSON.toJSONString(info));
        return info;
    }

    @Autowired
    private PermissionTypeMapper permissionTypeMapper;

    @Override
    public List getPermissionV2(com.huafon.support.dto.UserInfoDto userInfo, Integer tenantId, Integer clientType) {
        Integer inUsed = 1;
        if (null == userInfo) {
            throw new ServiceException("登录错误");
        }
        int isAdmin = userInfo.getIsAdmin();

        if (Objects.equals(isAdmin, LoginConstants.IS_ADMIN_ONE)) {
            return permissionV2Service.queryPermissionTreeList(inUsed, clientType);
        }

        Integer userId = userInfo.getUserId().intValue();
        UserTenant userTenant = userTenantMapper.selectByUserIdAndTenantId(userId, tenantId);
        if (Objects.isNull(userTenant)) {
            log.warn("用户({})和租户({})无关联关系", userId, tenantId);
            return new ArrayList<>();
        }
        if (Objects.equals(userTenant.getMemberType(), MemberTypeEnum.LINKMAN.getCode())) {
            return permissionV2Service.queryTenantPermissionTreeList(tenantId, inUsed, clientType);
        }

        List<UserRole> userRoleList = userRoleMapper.selectListByUserId(userId, Collections.singletonList(tenantId));
        if (!CollectionUtils.isEmpty(userRoleList)) {
            Set<Integer> roleIds = userRoleList.stream().map(UserRole::getRoleId).collect(Collectors.toSet());
            List<TenantPermissionListVo> permissions = permissionV2Service.selectListByRoleId(roleIds, clientType, tenantId);
            if (!CollectionUtils.isEmpty(permissions)) {
                Map<Integer, PermissionTypeVo> typeMappings = new HashMap<>();
                if (!CollectionUtils.isEmpty(permissions)) {
                    List<Integer> typeIds = permissions.stream().map(TenantPermissionListVo::getTypeId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(typeIds)) {
                        List<PermissionType> permissionTypes = permissionTypeMapper.selectBatchIds(typeIds);
                        typeMappings.putAll(permissionTypes.stream().collect(Collectors.toMap(PermissionType::getId, x -> {
                            PermissionTypeVo item = new PermissionTypeVo();
                            item.setClientType(x.getClientType());
                            item.setName(x.getName());
                            return item;
                        }, (a, b) -> b)));
                    }
                }
                return permissionV2Service.listToTree(permissions, 0, typeMappings);
            }
        }
        return new ArrayList<>();
    }

    @Override
    public GraphicCodeVO createCode() {
        RandomGenerator randomGenerator = new RandomGenerator("0123456789", 4);
        LineCaptcha lineCaptcha = CaptchaUtil.createLineCaptcha(100, 30, 4, 0);
        lineCaptcha.setGenerator(randomGenerator);
        String verifyCode = lineCaptcha.getCode();
        String codeId = UuidUtils.generateUuid();
        redisUtil.set(String.format(LoginConstants.LOGIN_CODE, codeId), verifyCode, 300);
        GraphicCodeVO graphicCodeVO = new GraphicCodeVO();
        graphicCodeVO.setCodeId(codeId);
        graphicCodeVO.setImage(lineCaptcha.getImageBase64Data());
        return graphicCodeVO;
    }

    /**
     * 校验验证码
     */
    private void verifyCode(String codeId, String verifyCode) {
        String redisKey = String.format(LoginConstants.LOGIN_CODE, codeId);
        String code = (String) redisUtil.get(redisKey);
        if (code == null) {
            throw new ServiceException("验证码已过期，请重新获取验证码后正确填写");
        } else {
            redisUtil.del(redisKey);
            if (!code.equals(verifyCode)) {
                throw new ServiceException("登录失败，验证码错误");
            }
        }
    }

}
