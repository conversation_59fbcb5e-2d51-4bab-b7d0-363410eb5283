package com.huafon.service;

import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.models.reqo.excel.ExcelQuery;
import com.huafon.models.vo.score.req.*;
import com.huafon.models.vo.score.res.BadHabitsReportListVO;
import com.huafon.models.vo.score.res.BadHabitsReportResVO;
import com.huafon.models.vo.score.res.BadHabitsScoreRankResVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName com.huafon.service.BadHabitsReportService
 * @Description
 * @createTime 2024年10月28日 20:08:00
 */
public interface BadHabitsReportService {
    Long submit(BadHabitsReportSubmitParam req);

    CommonPage<BadHabitsReportResVO> queryBadHabitsReportPage(BadHabitReportPageReqVO req);

    BadHabitsReportResVO detailBadHabitsReport(Long id);

    void deleteBadHabitsReport(Long id);

    CommonPage<BadHabitsReportListVO> queryBadHabitsReportList(BadHabitReportListReqVO req);

    CommonPage<BadHabitsScoreRankResVO> queryBadHabitsScoreRankPage(BadHabitScoreRankReqVO req);

    void exportHabitsScoreRank(HttpServletResponse response, ExcelQuery<BadHabitScoreRankReqVO> excelQuery);

    void exportBadHabitsReport(HttpServletResponse response, ExcelQuery<BadHabitReportPageReqVO> excelQuery);

    Integer myScore();

}
