package com.huafon.service.support.valid;

import com.huafon.common.utils.file.Strings;
import com.huafon.models.dto.PermissionUploadDTO;
import com.huafon.service.support.ImportValueValid;

import java.util.HashSet;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

import static com.huafon.models.dto.PermissionUploadDTO.BUTTON;
import static com.huafon.models.dto.PermissionUploadDTO.WEB_URL;
import static com.huafon.service.v2.PermissionV2Service.PERMISSION_TREE_SEPARATOR;

/**
 * 菜单权限重复校验
 * @implNote  如导入的表格内存在多条权限类型+权限名称+按钮code或前端路径，则把第二条之后的重复数据全部显示在错误列表，错误原因为“存在相同的权限名称”
 * <AUTHOR>
 * @since 2023-04-13 14:46
 **/
public class PermissionDuplicateValid implements ImportValueValid<PermissionUploadDTO> {

	public static final String SEPARATOR = "&#&";
	private static final String MESSAGE = "存在相同的权限名称";
	private static final String MESSAGE_WEB_URL = "同层级路径下存在相同的前端路径";
	private static final String MESSAGE_BUTTON_CODE = "同层级路径下存在相同的按钮Code";

	private final Set<String> duplicateButtonCode = new HashSet<>();
	private final Set<String> duplicateWebUrl = new HashSet<>();

	//同层级下前端路径或按钮Code不重复：1681
	private final Set<String> duplicateSameLevelButtonCode = new HashSet<>();
	private final Set<String> duplicateSameLevelWebUrl = new HashSet<>();

	@Override
	public String invokeValueValid(PermissionUploadDTO data) {
		if (Objects.nonNull(data)) {
			String permissionType = data.getPermissionType();
			permissionType = Optional.ofNullable(permissionType).map(String::trim).orElse("");
			if ((WEB_URL.contains(permissionType))) {
				String duplicateKey = Strings.join(new String[]{data.getPermissionType(), data.getPermissionName(), data.getWebUrl()}, SEPARATOR);
				if (duplicateWebUrl.contains(duplicateKey)) {
					return MESSAGE;
				}
				duplicateWebUrl.add(duplicateKey);

				duplicateKey = Strings.join(new String[]{data.getPermissionType(), getPrefix(data.getPermissionName()), data.getWebUrl()}, SEPARATOR);
				if (duplicateSameLevelWebUrl.contains(duplicateKey)) {
					return MESSAGE_WEB_URL;
				}
				duplicateSameLevelWebUrl.add(duplicateKey);
			} else if(BUTTON.contains(permissionType)) {
				String duplicateKey = Strings.join(new String[]{data.getPermissionType(), data.getPermissionName(), data.getButtonCode()}, SEPARATOR);
				if (duplicateButtonCode.contains(duplicateKey)) {
					return MESSAGE;
				}
				duplicateButtonCode.add(duplicateKey);

				duplicateKey = Strings.join(new String[]{data.getPermissionType(), getPrefix(data.getPermissionName()), data.getButtonCode()}, SEPARATOR);
				if (duplicateSameLevelButtonCode.contains(duplicateKey)) {
					return MESSAGE_BUTTON_CODE;
				}
				duplicateSameLevelButtonCode.add(duplicateKey);
			}
		}

		return null;
	}

	private String getPrefix(String permissionName) {
		int index = permissionName.lastIndexOf(PERMISSION_TREE_SEPARATOR);
		return index == -1 ? "" : permissionName.substring(0, index);
	}


}
