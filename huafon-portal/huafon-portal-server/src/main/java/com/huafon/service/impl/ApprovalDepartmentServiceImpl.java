package com.huafon.service.impl;

import com.google.common.base.Joiner;
import com.huafon.common.config.TenantContext;
import com.huafon.common.utils.bean.BeanUtils;
import com.huafon.dao.mapper.ApprovalLeaderConfigMapper;
import com.huafon.dao.mapper.UserMapper;
import com.huafon.models.dto.ApproveUserDownloadDTO;
import com.huafon.models.dto.ApproveUserUploadDTO;
import com.huafon.models.entity.ApprovalLeaderConfig;
import com.huafon.models.entity.Department;
import com.huafon.models.entity.User;
import com.huafon.models.reqo.approval.ReqDepartment;
import com.huafon.models.reqo.approval.ReqDepartmentAdd;
import com.huafon.models.vo.ApprovalFuzzyQueryReqVO;
import com.huafon.models.vo.approval.ApprovalLeaderConfigVo;
import com.huafon.models.vo.approval.DepartmentVo;
import com.huafon.models.vo.org.DepartmentVO;
import com.huafon.portal.api.constants.ApprovalLeaderConstants;
import com.huafon.portal.api.dto.UserMobileDto;
import com.huafon.service.ApprovalDepartmentService;
import com.huafon.service.DepartmentService;
import com.huafon.service.support.ExcelValidationError;
import com.huafon.service.support.ExcelValidationResult;
import com.huafon.support.config.UserContext;
import com.huafon.support.core.pojo.R;
import com.huafon.support.exceptions.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.huafon.portal.api.constants.ApprovalLeaderConstants.*;

/**
 * <AUTHOR>
 * @Date 2022/6/2 9:57
 */
@Service
@Slf4j
public class ApprovalDepartmentServiceImpl implements ApprovalDepartmentService {

    @Resource
    private ApprovalLeaderConfigMapper approvalLeaderConfigMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    DepartmentService departmentService;


    @Override
    @Transactional
    public R edit(ReqDepartmentAdd reqAdd) {
        updateData(reqAdd);
        return R.ok();
    }

    @Override
    public R<ApprovalLeaderConfigVo> getRow(ReqDepartment req) {
        List<ApprovalLeaderConfig> list = approvalLeaderConfigMapper.selectByDepartmentId(req.getDepartmentId());
        ApprovalLeaderConfigVo vo = convertList(list);
        vo.setDepartmentId(req.getDepartmentId());
        return R.ok(vo);
    }

    private void updateData(ReqDepartmentAdd reqAdd) {
        approvalLeaderConfigMapper.deleteByDepartmentId(reqAdd.getDepartmentId());
        List<ApprovalLeaderConfig> list = new ArrayList<>();
        Long userId = UserContext.getId();
        Integer tenantId = TenantContext.getOne();
        Date currentTime = new Date();
        convertUser(reqAdd.getPrincipalUserList(), list, PRINCIPAL_TYPE, currentTime, reqAdd.getDepartmentId(), tenantId, userId);
        convertUser(reqAdd.getHsePrincipalUserList(), list, HSE_PRINCIPAL_TYPE, currentTime, reqAdd.getDepartmentId(), tenantId, userId);
        convertUser(reqAdd.getFinancePrincipalUserList(), list, FINANCE_PRINCIPAL_TYPE, currentTime, reqAdd.getDepartmentId(), tenantId, userId);
        convertUser(reqAdd.getLeaderUserList(), list, LEADER_TYPE, currentTime, reqAdd.getDepartmentId(), tenantId, userId);
        convertUser(reqAdd.getDeviceSectionPrincipalUserList(), list, DEVICE_SECTION_PRINCIPAL_TYPE, currentTime, reqAdd.getDepartmentId(), tenantId, userId);
        convertUser(reqAdd.getTeamPrincipalUserList(), list, TEAM_PRINCIPAL_TYPE, currentTime, reqAdd.getDepartmentId(), tenantId, userId);
        convertUser(reqAdd.getWorkshopPrincipalUserList(), list, WORKSHOP_PRINCIPAL_TYPE, currentTime, reqAdd.getDepartmentId(), tenantId, userId);
        if (!CollectionUtils.isEmpty(list)) {
            approvalLeaderConfigMapper.insertBatch(list);
        }
    }

    private void convertUser(List<UserMobileDto> userList, List<ApprovalLeaderConfig> list, Integer type,
                             Date currentTime, Integer departmentId, Integer tenantId, Long userId) {
        if (!CollectionUtils.isEmpty(userList)) {
            for (UserMobileDto user : userList) {
                if (null == user || null == user.getUserId()) {
                    continue;
                }
                ApprovalLeaderConfig config = new ApprovalLeaderConfig();
                config.setUserId(user.getUserId());
                config.setName(user.getName());
                config.setMobile(user.getMobile());
                config.setDepartmentId(departmentId);
                config.setTenantId(tenantId);
                config.setCreateTime(currentTime);
                config.setModifyTime(currentTime);
                config.setCreateBy(userId);
                config.setType(type);
                list.add(config);
            }
        }
    }

    public ApprovalLeaderConfigVo convertList(List<ApprovalLeaderConfig> list) {
        ApprovalLeaderConfigVo vo = new ApprovalLeaderConfigVo();
        List<UserMobileDto> principalUserList = new ArrayList<>();
        List<UserMobileDto> hsePrincipalUserList = new ArrayList<>();
        List<UserMobileDto> financePrincipalUserList = new ArrayList<>();
        List<UserMobileDto> leaderUserList = new ArrayList<>();
        List<UserMobileDto> deviceSectionPrincipalUserList = new ArrayList<>();
        List<UserMobileDto> teamPrincipalUserList = new ArrayList<>();
        List<UserMobileDto> workshopPrincipalUserList = new ArrayList<>();//车间负责人
        for (ApprovalLeaderConfig config : list) {
            if (config.getType() == PRINCIPAL_TYPE) {
                principalUserList.add(BeanUtils.convert(config, UserMobileDto.class));
            } else if (config.getType() == HSE_PRINCIPAL_TYPE) {
                hsePrincipalUserList.add(BeanUtils.convert(config, UserMobileDto.class));
            } else if (config.getType() == FINANCE_PRINCIPAL_TYPE) {
                financePrincipalUserList.add(BeanUtils.convert(config, UserMobileDto.class));
            } else if (config.getType() == LEADER_TYPE) {
                leaderUserList.add(BeanUtils.convert(config, UserMobileDto.class));
            } else if (config.getType() == DEVICE_SECTION_PRINCIPAL_TYPE) {
                deviceSectionPrincipalUserList.add(BeanUtils.convert(config, UserMobileDto.class));
            } else if (config.getType() == TEAM_PRINCIPAL_TYPE) {
                teamPrincipalUserList.add(BeanUtils.convert(config, UserMobileDto.class));
            } else if (config.getType() == WORKSHOP_PRINCIPAL_TYPE) {
                workshopPrincipalUserList.add(BeanUtils.convert(config, UserMobileDto.class));
            }
        }
        vo.setPrincipalUserList(principalUserList);
        vo.setHsePrincipalUserList(hsePrincipalUserList);
        vo.setFinancePrincipalUserList(financePrincipalUserList);
        vo.setLeaderUserList(leaderUserList);
        vo.setDeviceSectionPrincipalUserList(deviceSectionPrincipalUserList);
        vo.setTeamPrincipalUserList(teamPrincipalUserList);
        vo.setWorkshopPrincipalUserList(workshopPrincipalUserList);
        return vo;
    }

    @Override
    public List<ApproveUserDownloadDTO> approveDownload(Integer departmentId) {
        if (Objects.isNull(departmentId)) {
            return new ArrayList<>();
        }
        List<Integer> departments = new ArrayList<>();
        List<Department> departmentInfos = new ArrayList<>();
        try {
            List<Department> queryResult = new ArrayList<>();
            if (departmentService.isDepartmentTopNode(departmentId)) {
                List<Department> result = departmentService.getByTenantId(TenantContext.getOne(), true);
                Map<Integer, List<Department>> collect = result.stream().collect(Collectors.groupingBy(Department::getParentId,
                        Collectors.collectingAndThen(Collectors.toList(), e -> e.stream().sorted((a, b) -> {
                            int firstCompareVal = a.getParentId().compareTo(b.getParentId());
                            if (firstCompareVal == 0) {
                                if (a.getSortOrder() == null) {
                                    return -1;
                                } else if (b.getSortOrder() == null) {
                                    return 1;
                                }
                                int secondCompareVal = b.getSortOrder().compareTo(a.getSortOrder());
                                if (secondCompareVal == 0) {
                                    return a.getId().compareTo(b.getId());
                                }
                                return secondCompareVal;
                            }

                            return firstCompareVal;
                        }).collect(Collectors.toList()))));
                sortDepartment(collect, queryResult, 0);
            } else {
                queryResult.addAll(departmentService.queryByParentId(departmentId, TenantContext.getOne()));
            }
            if (!CollectionUtils.isEmpty(queryResult)) {
                departmentInfos.addAll(queryResult);
                departments.addAll(queryResult.stream().map(Department::getId).collect(Collectors.toList()));
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("网络错误，请稍后重试。");
        }

        List<ApprovalLeaderConfig> sourceData = approvalLeaderConfigMapper.selectByDepartmentIds(departments);
        if (sourceData == null) {
            sourceData = new ArrayList<>();
        }
        Map<Integer, User> userInfoMappings = new HashMap<>();
        List<Integer> approvalUserIds = sourceData.stream().map(ApprovalLeaderConfig::getUserId).filter(Objects::nonNull).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(approvalUserIds)) {
            List<User> users = userMapper.selectByIds(approvalUserIds);
            userInfoMappings.putAll(users.stream().collect(Collectors.toMap(User::getUserId, Function.identity(), (a, b) -> b)));
        }

        Map<Integer, List<ApprovalLeaderConfig>> approvalMappings = sourceData.stream()
                .collect(Collectors.groupingBy(ApprovalLeaderConfig::getDepartmentId));
        List<ApproveUserDownloadDTO> result = new ArrayList<>();
        for (Department currentDepartment : departmentInfos) {
            ApproveUserDownloadDTO target = new ApproveUserDownloadDTO();
            target.setDepartmentName(currentDepartment.getDepartmentName());

            List<ApprovalLeaderConfig> currentApproveList = approvalMappings.getOrDefault(currentDepartment.getId(), Collections.emptyList());
            target.setPrincipal(processDownloadInfo(filterLeaderByType(currentApproveList, PRINCIPAL_TYPE), userInfoMappings));//部门负责人
            target.setHsePrincipal(processDownloadInfo(filterLeaderByType(currentApproveList, HSE_PRINCIPAL_TYPE), userInfoMappings));//部门HSE管理人员
            target.setFinancePrincipal(processDownloadInfo(filterLeaderByType(currentApproveList, FINANCE_PRINCIPAL_TYPE), userInfoMappings));//财务负责人
            target.setLeader(processDownloadInfo(filterLeaderByType(currentApproveList, LEADER_TYPE), userInfoMappings));//部门分管领导
            target.setDeviceSectionPrincipal(processDownloadInfo(filterLeaderByType(currentApproveList, DEVICE_SECTION_PRINCIPAL_TYPE), userInfoMappings));//装置/工段负责人
            target.setTeamPrincipal(processDownloadInfo(filterLeaderByType(currentApproveList, TEAM_PRINCIPAL_TYPE), userInfoMappings));//班组负责人
            target.setWorkshopPrincipal(processDownloadInfo(filterLeaderByType(currentApproveList, WORKSHOP_PRINCIPAL_TYPE), userInfoMappings));//车间负责人
            result.add(target);
        }

        return result;
    }

    /**
     * 查询该用户下
     * @param userId
     * @return
     */
    @Override
    public List<ApprovalLeaderConfig> findByUserId(Integer userId) {
        return approvalLeaderConfigMapper.queryByTenantIdAndUserId(TenantContext.getOne(), userId);
    }

    /**
     * 释放该用户下的配置
     * @param userId
     */
    @Override
    public void releaseConfigByUserId(Integer userId) {
        approvalLeaderConfigMapper.deleteByUserId(TenantContext.getOne(),userId);
    }

    @Override
    public List<DepartmentVO> queryDeptInfo(ApprovalFuzzyQueryReqVO reqVO) {
        List<DepartmentVO> list = new ArrayList<>();
        reqVO.setTenantId(TenantContext.getOne());
        Set<DepartmentVO> departmentVOS = approvalLeaderConfigMapper.queryDeptInfo(reqVO);
        if (!CollectionUtils.isEmpty(departmentVOS)){
            list = new ArrayList<>(departmentVOS);
        }
        return list;
    }

    private void sortDepartment(Map<Integer, List<Department>> mappings, List<Department> result, int deptId) {
        if (mappings.containsKey(deptId)) {
            for (Department department : mappings.get(deptId)) {
                result.add(department);
                sortDepartment(mappings, result, department.getId());
            }
        }
    }

    /**
     * @param type
     * @return
     * @see ApprovalLeaderConfig#type
     */
    private List<ApprovalLeaderConfig> filterLeaderByType(List<ApprovalLeaderConfig> currentApproveList, Integer type) {
        return currentApproveList.stream().filter(e -> e != null && Objects.equals(e.getType(), type)).collect(Collectors.toList());
    }

    private String processDownloadInfo(List<ApprovalLeaderConfig> principal, Map<Integer, User> userInfoMappings) {
        if (CollectionUtils.isEmpty(principal)) return Strings.EMPTY;

        try {
            List<String> dataList = new ArrayList<>();
            for (ApprovalLeaderConfig item : principal) {
                User user = userInfoMappings.get(item.getUserId());
                if (Objects.nonNull(user)) {
                    dataList.add(String.format("%s/%s", item.getName(), user.getUsername()));
                } else {
                    dataList.add(String.format("%s/%s", item.getName(), "用户未找到"));
                }
            }

            return Joiner.on("\r\n").skipNulls().join(dataList);
        } catch (Exception e) {
            log.error("[审批人导出：JSON解析错误] {}", principal);
        }

        return Strings.EMPTY;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExcelValidationResult<ApproveUserUploadDTO> validationImportExcel(List<ApproveUserUploadDTO> source) {
        if (CollectionUtils.isEmpty(source)) {
            log.warn("[导入审批人：元数据为空]");
            return new ExcelValidationResult<>(new ArrayList<>());
        }

        List<ApproveUserUploadDTO> success = new ArrayList<>();//成功
        List<ExcelValidationError<ApproveUserUploadDTO>> errors = new ArrayList<>();//失败

        List<Department> data = new ArrayList<>();
        try {
            List<Department> byTenantId = departmentService.getByTenantId(TenantContext.getOne(), true);
            if (!CollectionUtils.isEmpty(byTenantId)) {
                data.addAll(byTenantId);
            }
            log.info("[RPC查询租户下所有部门信息] tenant: {}, size: {}", TenantContext.getOne(), data.size());
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("网络错误，请稍后重试。");
        }
        Map<String, List<Integer>> departmentMapping = data.stream()
                .collect(Collectors.groupingBy(Department::getDepartmentName,
                        Collectors.collectingAndThen(Collectors.toList(), e -> e.stream().map(Department::getId).collect(Collectors.toList()))));

        Map<ApproveUserUploadDTO.UploadTransfer, ApproveUserUploadDTO> mapping = new HashMap<>();
        Set<String> workNumList = new HashSet<>();
        for (ApproveUserUploadDTO uploadData : source) {
            ApproveUserUploadDTO.UploadTransfer transfer = ApproveUserUploadDTO.transfer(uploadData);
            mapping.put(transfer, uploadData);
            workNumList.addAll(transfer.getAllWorkNums());
        }

        //通过工号查询所有用户信息
        List<User> users = new ArrayList<>();
        if (!CollectionUtils.isEmpty(workNumList)) {
            ArrayList<String> tmp = new ArrayList<>(workNumList);
            users.addAll(userMapper.queryUserByTenant(TenantContext.getOne(), tmp));
        }
        Map<String, User> userMapping = new HashMap<>();
        if (!CollectionUtils.isEmpty(users)) {
            userMapping = users.stream().collect(Collectors.toMap(User::getUsername, item -> item, (a, b) -> a));
        }

        List<ApprovalLeaderConfig> existApprovals = approvalLeaderConfigMapper.queryByTenantId(TenantContext.getOne());
        Map<Integer, List<ApprovalLeaderConfig>> approvalMapping = new HashMap<>();
        if (!CollectionUtils.isEmpty(existApprovals)) {
            approvalMapping.putAll(existApprovals.stream().collect(Collectors.groupingBy(ApprovalLeaderConfig::getDepartmentId)));
        }

        List<ApprovalLeaderConfig> importToData = new ArrayList<>();
        List<ApprovalLeaderConfig> updateData = new ArrayList<>();
        List<ApprovalLeaderConfig> deleteData = new ArrayList<>();

        Set<Integer> duplicateDepartmentIds = new HashSet<>();
        List<ApproveUserUploadDTO.UploadTransfer> keysArray = new ArrayList<>(mapping.keySet());
        keysArray.sort((a, b) -> b.getRow() - a.getRow());

        for (int i = 0; i < keysArray.size(); i++) {
            ApproveUserUploadDTO.UploadTransfer transfer = keysArray.get(i);
            ApproveUserUploadDTO value = mapping.get(transfer);

            List<Integer> currentDepartment = departmentMapping.get(value.getDepartmentName());
            StringBuilder checkResult = new StringBuilder();
            if (CollectionUtils.isEmpty(currentDepartment)) {
                checkResult.append("部门名称不存在").append("\r\n");
            } else if (currentDepartment.size() > 1) {
                checkResult.append("该部门名称多部门使用").append("\r\n");
            } else {
                transfer.setDepartmentId(currentDepartment.get(0));
            }

            checkResult.append(processValid(transfer, userMapping));
            if (Strings.isBlank(checkResult.toString())) {
                success.add(mapping.get(transfer));

                if (duplicateDepartmentIds.contains(transfer.getDepartmentId())) {
                    continue;
                }
                List<ApprovalLeaderConfig> newestList = convertLeaderConfig(transfer, userMapping);
                Map<Integer, List<ApprovalLeaderConfig>> newMappings = newestList.stream().collect(Collectors.groupingBy(ApprovalLeaderConfig::getType));
                List<ApprovalLeaderConfig> existList = approvalMapping.getOrDefault(transfer.getDepartmentId(), Collections.emptyList());
                Map<Integer, List<ApprovalLeaderConfig>> oldMappings = existList.stream().collect(Collectors.groupingBy(ApprovalLeaderConfig::getType));

                for (int j : ApprovalLeaderConstants.allApprovalLeader()) {
                    Set<Integer> existUser = new HashSet<>();
                    Set<Integer> existIdList = new HashSet<>();
                    List<ApprovalLeaderConfig> newList = newMappings.getOrDefault(j, Collections.emptyList());
                    List<ApprovalLeaderConfig> oldList = oldMappings.getOrDefault(j, Collections.emptyList());

                    Map<Integer, ApprovalLeaderConfig> oldUserMappings = oldList.stream().filter(Objects::nonNull).collect(Collectors.toMap(ApprovalLeaderConfig::getUserId, Function.identity(), (a, b) -> b));
                    for (ApprovalLeaderConfig leaderConfig : newList) {
                        if (existUser.contains(leaderConfig.getUserId())) {
                            log.warn("[审批负责人导入]：存在相同的人员");
                            continue;
                        }
                        ApprovalLeaderConfig existLeaderConfig = oldUserMappings.get(leaderConfig.getUserId());
                        if (Objects.isNull(existLeaderConfig)) {
                            importToData.add(leaderConfig);
                        } else {
                            existIdList.add(existLeaderConfig.getId());
                            existLeaderConfig.setName(leaderConfig.getName());
                            existLeaderConfig.setMobile(leaderConfig.getMobile());
                            existLeaderConfig.setModifyTime(leaderConfig.getModifyTime());
                            updateData.add(existLeaderConfig);
                        }
                        existUser.add(leaderConfig.getUserId());
                    }

                    List<ApprovalLeaderConfig> deleteList = oldList.stream().filter(e -> !existIdList.contains(e.getId())).collect(Collectors.toList());
                    deleteData.addAll(deleteList);
                }

                duplicateDepartmentIds.add(transfer.getDepartmentId());
            } else {
                ExcelValidationError<ApproveUserUploadDTO> error = new ExcelValidationError<>(value, checkResult.toString().trim());
                errors.add(error);
            }
        }

        if (!CollectionUtils.isEmpty(importToData)) {
            approvalLeaderConfigMapper.insertBatch(importToData);
        }

        if (!CollectionUtils.isEmpty(updateData)) {
            approvalLeaderConfigMapper.updateBatch(updateData);
        }

        List<Integer> deleteIds = deleteData.stream().map(ApprovalLeaderConfig::getId).filter(Objects::nonNull).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(deleteIds)) {
            approvalLeaderConfigMapper.deleteBatchByIds(deleteIds);
        }

        return new ExcelValidationResult<>(success, errors);
    }

    private List<ApprovalLeaderConfig> convertLeaderConfig(ApproveUserUploadDTO.UploadTransfer origin,
                                                           Map<String, User> userMapping) {
        List<ApprovalLeaderConfig> result = new ArrayList<>();
        Integer deptId = origin.getDepartmentId();
        result.addAll(batchConvert(origin.getPrincipal(), deptId, PRINCIPAL_TYPE, userMapping));
        result.addAll(batchConvert(origin.getHsePrincipal(), deptId, HSE_PRINCIPAL_TYPE, userMapping));
        result.addAll(batchConvert(origin.getFinancePrincipal(), deptId, FINANCE_PRINCIPAL_TYPE, userMapping));
        result.addAll(batchConvert(origin.getLeader(), deptId, LEADER_TYPE, userMapping));
        result.addAll(batchConvert(origin.getDeviceSectionPrincipal(), deptId, DEVICE_SECTION_PRINCIPAL_TYPE, userMapping));
        result.addAll(batchConvert(origin.getTeamPrincipal(), deptId, TEAM_PRINCIPAL_TYPE, userMapping));
        result.addAll(batchConvert(origin.getWorkshopPrincipal(), deptId, WORKSHOP_PRINCIPAL_TYPE, userMapping));

        return result;
    }

    private List<ApprovalLeaderConfig> batchConvert(List<ApproveUserUploadDTO.ApprovalUploadUserDTO> source,
                                                    Integer deptId,
                                                    Integer type,
                                                    Map<String, User> userMappings) {
        if (CollectionUtils.isEmpty(source)) return Collections.emptyList();

        return source.stream()
                .map(e -> convertToLeaderConfig(e, deptId, type, userMappings.get(e.getUsername())))
                .collect(Collectors.toList());
    }

    private ApprovalLeaderConfig convertToLeaderConfig(ApproveUserUploadDTO.ApprovalUploadUserDTO origin,
                                                       Integer deptId,
                                                       Integer type,
                                                       User user) {
        if (origin == null) {
            return null;
        }
        ApprovalLeaderConfig approvalLeaderConfig = new ApprovalLeaderConfig();
        approvalLeaderConfig.setUserId(user.getUserId());
        approvalLeaderConfig.setName(user.getName());
        approvalLeaderConfig.setMobile(user.getMobile());
        approvalLeaderConfig.setDepartmentId(deptId);
        approvalLeaderConfig.setTenantId(TenantContext.getOne());
        approvalLeaderConfig.setCreateBy(UserContext.getId());
        approvalLeaderConfig.setCreateTime(new Date());
        approvalLeaderConfig.setModifyTime(new Date());
        approvalLeaderConfig.setType(type);
        return approvalLeaderConfig;
    }


    private String processValid(ApproveUserUploadDTO.UploadTransfer transfer, Map<String, User> userMapping) {
        StringBuilder result = new StringBuilder();
        String message = validMultiple(transfer.getPrincipal(), userMapping);
        ;//部门负责人
        if (!message.isEmpty()) {
            result.append("部门负责人").append("(").append(message).append(")").append("\r\n");
        }
        message = validMultiple(transfer.getHsePrincipal(), userMapping);//部门HSE管理员
        if (!message.isEmpty()) {
            result.append("部门HSE管理员").append("(").append(message).append(")").append("\r\n");
        }

        message = validMultiple(transfer.getLeader(), userMapping);//部门分管领导
        if (!message.isEmpty()) {
            result.append("部门分管领导").append("(").append(message).append(")").append("\r\n");
        }

        message = validMultiple(transfer.getFinancePrincipal(), userMapping);//财务负责人
        if (!message.isEmpty()) {
            result.append("财务负责人").append("(").append(message).append(")").append("\r\n");
        }

        message = validMultiple(transfer.getDeviceSectionPrincipal(), userMapping);//装置/工段负责人
        if (!message.isEmpty()) {
            result.append("装置/工段负责人").append("(").append(message).append(")").append("\r\n");
        }

        message = validMultiple(transfer.getTeamPrincipal(), userMapping);//班组负责人
        if (!message.isEmpty()) {
            result.append("班组负责人").append("(").append(message).append(")");
        }

        message = validMultiple(transfer.getWorkshopPrincipal(), userMapping);//车间负责人
        if (!message.isEmpty()) {
            result.append("车间负责人").append("(").append(message).append(")");
        }
        return result.toString();
    }

    private String validMultiple(List<ApproveUserUploadDTO.ApprovalUploadUserDTO> approvalUserList, Map<String, User> userMappings) {
        if (CollectionUtils.isEmpty(approvalUserList)) {
            return Strings.EMPTY;
        }

        List<String> validMessages = new ArrayList<>();
        for (ApproveUserUploadDTO.ApprovalUploadUserDTO approvalUser : approvalUserList) {
            String valid = valid(approvalUser.getUsername(), approvalUser.getName(), userMappings);
            if (Strings.isNotBlank(valid)) {
                validMessages.add(valid);
            }
        }

        return Joiner.on("、").skipNulls().join(validMessages).trim();
    }

    private String valid(String workNum, String name, Map<String, User> userMapping) {
        if (Strings.isBlank(workNum) && Strings.isBlank(name)) {
            return Strings.EMPTY;
        }

        if (Strings.isBlank(workNum) || !userMapping.containsKey(workNum)) {
            StringBuilder message = new StringBuilder("不存在该人员");
            if (Strings.isNotBlank(name)) message.append("(").append(name).append(")");
            else if (Strings.isNotBlank(workNum)) message.append("(").append(workNum).append(")");
            return message.toString();
        }

        return Objects.equals(name, userMapping.get(workNum).getName()) ? Strings.EMPTY : "工号与姓名不匹配(" + workNum + ")";
    }


}
