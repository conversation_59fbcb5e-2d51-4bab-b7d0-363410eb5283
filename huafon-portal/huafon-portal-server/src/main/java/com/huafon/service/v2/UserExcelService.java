package com.huafon.service.v2;

import com.huafon.models.dto.UserUploadDTO;
import com.huafon.models.query.NoticeUserPageQuery;
import com.huafon.models.vo.user.UserDownloadReqVO;
import com.huafon.service.support.ExcelValidationManager;

import javax.servlet.http.HttpServletResponse;

/**
 * @Description: TODO
 * @Date: 2024/10/23 12:35 AM
 * @Author: zyf
 **/
public interface UserExcelService extends ExcelValidationManager<UserUploadDTO> {

    /**
     * 导出租户下的用户
     *
     * @param response
     * @param reqVO
     */
    void download(HttpServletResponse response, UserDownloadReqVO reqVO);


    void noticeUserDownload(HttpServletResponse response, NoticeUserPageQuery query);

}
