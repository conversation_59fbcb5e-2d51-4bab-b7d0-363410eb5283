package com.huafon.service;

/**
 * <AUTHOR>
 * @ClassName com.huafon.threeSimultaneity.entity.EntityVerify
 * @Description  实体检验规范，针对相关属性进行检验，实现该规范的数据表-实体必须包含相关字段：如版本号校验（乐观锁）、租户（越权）校验。
 *
 * @createTime 2023年09月04日 10:25:00
 */
public interface EntityVerify {
    void setId(Long id);
    Long getId();
    void setVersion(Integer version);
    Integer getVersion();
    void setTenantId(Integer id);
    Integer getTenantId();
}
