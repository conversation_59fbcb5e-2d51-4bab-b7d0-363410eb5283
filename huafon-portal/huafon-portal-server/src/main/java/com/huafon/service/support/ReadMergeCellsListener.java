package com.huafon.service.support;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.CellExtra;
import com.huafon.support.exceptions.ServiceException;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023-05-29 16:55
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Slf4j
public class ReadMergeCellsListener extends AnalysisEventListener<Map<Integer, String>> {
	public static final int DEFAULT_THRESHOLD = 1000;

	private Map<Integer, Map<Integer, String>> sheetData = new HashMap<>();
	private List<CellExtra> extraList = new ArrayList<>();
	private int threshold;

	public ReadMergeCellsListener() {
		this(DEFAULT_THRESHOLD);
	}

	public ReadMergeCellsListener(int threshold) {
		this.threshold = threshold;
	}

	@Override
	public void invoke(Map<Integer, String> data, AnalysisContext context) {
		Integer rowIndex = context.readRowHolder().getRowIndex();
		sheetData.put(rowIndex, data);
		if (sheetData.size() > threshold) {
			throw new ServiceException(String.format("文件中数据不能超过%d行", threshold));
		}
//		log.info("解析数据行：{}", rowIndex);
	}

	@Override
	public void doAfterAllAnalysed(AnalysisContext context) {
	}

	@Override
	public void onException(Exception exception, AnalysisContext context) throws Exception {
		log.error("Excel解析错误: message {}", exception.getMessage(), exception.getCause());
		if (exception instanceof ServiceException) {
			throw exception;
		}
	}

	@Override
	public void extra(CellExtra extra, AnalysisContext context) {
		switch (extra.getType()) {
			case MERGE:
				Integer fromRow = extra.getFirstRowIndex();
				Integer fromColumn = extra.getFirstColumnIndex();
				Integer toRow = extra.getLastRowIndex();
				Integer toColumn = extra.getLastColumnIndex();
//				log.info("合并单元格信息：FromRow: {}, FromColumn: {}, ToRow: {}, ToColumn: {}",fromRow, fromColumn, toRow, toColumn);
				extraList.add(extra);
			default:
		}
	}

}
