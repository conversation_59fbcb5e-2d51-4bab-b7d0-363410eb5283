package com.huafon.controller.v2;

import com.huafon.admin.api.annotation.OperationLog;
import com.huafon.admin.api.enums.OperationLogType;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.models.vo.user.*;
import com.huafon.service.v2.UserV2Service;
import com.huafon.support.core.pojo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description: 超级用户管理v2
 * @Date: 2022/12/13 10:55
 * @Author: zyf
 **/
@Api(tags = "超级用户管理v2")
@RestController
@RequestMapping("/v2/superUser")
public class SuperUserV2Controller {

    private final UserV2Service userV2Service;

    public SuperUserV2Controller(UserV2Service userV2Service) {
        this.userV2Service = userV2Service;
    }

    @PostMapping("/getNodeNums")
    @ApiOperation(value = "获取节点的数量")
    public R<StatisticsTotalAndUndeterminedVO> getNodeNums() {
        return R.ok(userV2Service.getNodeNumsForSuperUser());
    }

    @PostMapping("/page")
    @ApiOperation(value = "超级用户分页")
    public R<CommonPage<SuperUserV2PageRespVO>> superUserPage(@RequestBody @Valid SuperUserV2PageReqVO reqVO) {
        CommonPage<SuperUserV2PageRespVO> superUserPage = userV2Service.superUserPage(reqVO);
        return R.ok(superUserPage);
    }

    @PostMapping("/cancel")
    @ApiOperation(value = "注销")
    @OperationLog(type = OperationLogType.MODITY, notes = "注销")
    public void cancel(@RequestBody List<Integer> userIdList) {
        userV2Service.cancel(userIdList);
    }

    @GetMapping("/{userId}")
    @ApiOperation(value = "超级用户详情")
    public R<SuperUserV2VO> getByUserId(@Parameter(name = "userId") @PathVariable Integer userId) {
        return R.ok(userV2Service.getByUserIdForSuperUser(userId));
    }

    @PutMapping("/remove")
    @ApiOperation(value = "移出租户")
    @OperationLog(type = OperationLogType.MODITY, notes = "移出租户")
    public void remove(@RequestBody @Valid SuperUserRemoveVO reqVO) {
        userV2Service.remove(reqVO);
    }

    @PostMapping("/add")
    @ApiOperation(value = "新增用户")
    @OperationLog(type = OperationLogType.ADD, notes = "新增用户")
    public void add(@Valid @RequestBody SuperUserV2VO reqVO) {
        userV2Service.add(reqVO);
    }

    @PutMapping("/modify/{userId}")
    @ApiOperation(value = "编辑用户")
    @OperationLog(type = OperationLogType.MODITY, notes = "编辑用户")
    public void modify(@Parameter(name = "userId") @PathVariable Integer userId,@Valid @RequestBody SuperUserV2VO reqVO) {
        userV2Service.modify(userId,reqVO);
    }

    @PostMapping("/addMember")
    @ApiOperation(value = "添加成员")
    @OperationLog(type = OperationLogType.ADD, notes = "添加成员")
    public void addMember(@Valid @RequestBody UserTenantAddVO reqVO) {
        userV2Service.addMember(reqVO);
    }

}
