package com.huafon.controller;

import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.models.reqo.permission.*;
import com.huafon.models.vo.PageListResultVo;
import com.huafon.models.vo.permission.*;
import com.huafon.service.DataPermissionService;
import com.huafon.service.PermissionService;
import com.huafon.support.core.pojo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 权限控制器
 */
@Api(tags = "权限管理")
@RestController
@RequestMapping("/permission")
public class PermissionController {

    @Resource
    private PermissionService permissionService;

    @Resource
    private DataPermissionService dataPermissionService;

    /**
     * 新增权限
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增权限")
    public R add(@RequestBody @Valid ReqPermissionAdd reqPermissionAdd){
        return permissionService.add(reqPermissionAdd);
    }

    /**
     * 编辑权限
     */
    @PostMapping("/edit")
    @ApiOperation(value = "编辑权限")
    public R edit(@RequestBody @Valid ReqPermissionEdit reqPermissionEdit){
        return permissionService.edit(reqPermissionEdit);
    }

    /**
     * 批量删除权限
     */
    @PostMapping("/batchdel")
    @ApiOperation(value = "删除权限")
    public R batchDel(@RequestBody @Valid ReqPermissionBatchDel reqPermissionBatchDel){
        return permissionService.batchDel(reqPermissionBatchDel);
    }

    /**
     * 权限列表
     */
    @PostMapping("/getall")
    @ApiOperation(value = "权限列表")
    public R<List<PermissionListVo>> getAll(@RequestBody ReqPermissionList reqPermissionList){
        return permissionService.getList(reqPermissionList);
    }

    /**
     * 权限列表
     */
    @PostMapping("/findParentList")
    @ApiOperation(value = "权限列表")
    public R<List<PermissionListVo>> findParentList(@RequestBody ReqPermissionList reqPermissionList){
        return permissionService.findParentList(reqPermissionList);
    }

    /**
     * 权限详情
     * @return
     */
    @PostMapping("/getrow")
    @ApiOperation(value = "权限详情")
    public R<PermissionVo> getRow(@RequestBody @Valid ReqPermission reqPermission){
        return permissionService.getRow(reqPermission);
    }

    /**
     * 权限列表-分页
     */
    @PostMapping("/getlistbypage")
    @ApiOperation(value = "权限列表-分页")
    public R<PageListResultVo<PermissionListVo>> getListByPage(@RequestBody @Valid ReqPermissionListPage reqPermissionListPage) {
        return permissionService.getListByPage(reqPermissionListPage);
    }

    @PostMapping("/data/permission/create")
    @ApiOperation(value = "新建数据权限")
    public R<Void> createDataPermission(@RequestBody @Valid ReqDataPermissionCreate source) {
        dataPermissionService.createMenuDataPermission(source);
        return R.ok();
    }

    @PostMapping("/data/permission/delete")
    @ApiOperation(value = "删除数据权限")
    public R<Void> deleteDataPermission(@RequestBody @Valid ReqGetDataPermission query) {
        dataPermissionService.deleteDataPermissionById(query.getId());
        return R.ok();
    }

    @PostMapping("/data/permission/edit")
    @ApiOperation(value = "更新数据权限")
    public R<Void> editDataPermission(@RequestBody @Valid ReqDataPermissionEdit permissionEdit) {
        dataPermissionService.editDataPermission(permissionEdit);
        return R.ok();
    }

    @PostMapping("/data/permission/enable")
    @ApiOperation(value = "启用/禁用角色数据权限")
    public R<Void> enableDataPermission(@RequestBody @Valid ReqEnableDataPermission enableDataPermission) {
        dataPermissionService.enableDataPermission(enableDataPermission);
        return R.ok();
    }

    @PostMapping("/data/permission")
    @ApiOperation(value = "获取数据权限(详情)")
    public R<DataPermissionVo> queryDataPermission(@RequestBody @Valid ReqGetDataPermission query) {
        return R.ok(dataPermissionService.getDataPermission(query));
    }

    @PostMapping("/data/permission/list")
    @ApiOperation(value = "获取数据权限列表(菜单配置处)")
    public R<CommonPage<DataPermissionVo>> queryDataPermissionList(@RequestBody @Valid ReqDataPermissionQuery query) {
        return R.ok(dataPermissionService.queryList(query));
    }

//    @PostMapping("/data/permission/source")
//    @ApiOperation(value = "获取自定义权限的数据源")
//    public R<TreeNode> queryPermissionSource(@RequestBody @Valid ReqDataPermissionSource query) {
//        return R.ok(dataPermissionService.queryPermissionSourceTree(query));
//    }

    // --------------- 角色、菜单和数据权限
    @PostMapping("/data/permission/role/list")
    @ApiOperation(value = "获取数据权限列表(角色菜单处)")
    public R<RoleDataPermissionVo> queryRoleDataPermissionList(@RequestBody @Valid ReqRoleDataPermissionQuery query) {
        return R.ok(dataPermissionService.queryRoleMenuPermissionList(query));
    }

    @PostMapping("/data/permission/role/edit")
    @ApiOperation(value = "更新数据权限列表(角色菜单处)")
    public R<Void> editRoleDataPermissionList(@RequestBody @Valid ReqRolePermissionEdit source) {
        dataPermissionService.editRoleMenuPermissionList(source);
        return R.ok();
    }


}
