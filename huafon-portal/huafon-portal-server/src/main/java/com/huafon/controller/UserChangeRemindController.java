package com.huafon.controller;


import com.huafon.admin.api.annotation.OperationLog;
import com.huafon.admin.api.enums.OperationLogType;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.models.vo.remmind.UserChangeRemindConfirmVO;
import com.huafon.models.vo.remmind.UserChangeRemindReqVO;
import com.huafon.models.vo.remmind.UserChangeRemindRespVO;
import com.huafon.models.vo.remmind.UserChangeRemindStaticVO;
import com.huafon.service.UserChangeRemindService;
import com.huafon.support.core.pojo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 人员变动提醒
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-02-09
 */
@RestController
@RequestMapping("/userChangeRemind")
@Api(tags="人员变动提醒_记录")
public class UserChangeRemindController {
    @Autowired
    private UserChangeRemindService userChangeRemindService;

    @PostMapping("/page")
    @ApiOperation("分页")
    public R<CommonPage<UserChangeRemindRespVO>> page(@RequestBody UserChangeRemindReqVO params){
        CommonPage<UserChangeRemindRespVO> page = userChangeRemindService.pageRemindRecord(params);
        return R.ok(page);
    }

    @PostMapping("/confirm")
    @OperationLog(type = OperationLogType.MODITY, notes = "入职_转岗_离职确认")
    public R confirm(@RequestBody UserChangeRemindConfirmVO confirmVO){
        userChangeRemindService.confirm(confirmVO);
        return R.ok();
    }


    @PostMapping("/confirmBatch")
    @OperationLog(type = OperationLogType.MODITY, notes = "入职_转岗_离职批量确认")
    public R confirmBatch(@RequestBody List<Integer> ids){
        userChangeRemindService.confirmBatch(ids);
        return R.ok();
    }

    @PostMapping("/staticRemindNum")
    @ApiOperation(value = "统计人员流动提醒各状态数量")
    public R<UserChangeRemindStaticVO> staticRemindNum() {
        UserChangeRemindStaticVO staticVO = userChangeRemindService.staticRemindNum();
        return R.ok(staticVO);
    }

    @PostMapping("/cancelTransfer")
    @OperationLog(type = OperationLogType.MODITY, notes = "批量取消转岗")
    public R cancelTransfer(@RequestBody List<Integer> ids){
        userChangeRemindService.cancelTransfer(ids);
        return R.ok();
    }

}