package com.huafon.utils;

import com.huafon.models.dto.DingTalkPushDepartmentDTO;
import com.huafon.models.dto.DingTalkPushUserDTO;
import com.huafon.models.enums.DataFromEnum;
import com.huafon.thrd.dto.DingTalkTenantGetDeptDetailRsp;
import com.huafon.thrd.dto.DingTalkTenantGetDeptListRsp;
import com.huafon.thrd.dto.DingTalkTenantGetUserDetailRsp;
import com.huafon.thrd.dto.DingTalkTenantGetUserListRsp;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: TODO
 * @Date: 2024/4/18 19:47
 * @Author: zyf
 **/
public class DingTalkPushTransformUtil {

    public static List<DingTalkPushDepartmentDTO> convertDept(List<DingTalkTenantGetDeptDetailRsp.DeptGetResponse> deptList){
        List<DingTalkPushDepartmentDTO> list = null;
        if (!CollectionUtils.isEmpty(deptList)){
            list = deptList.stream().map(x->{
                DingTalkPushDepartmentDTO departmentDTO = new DingTalkPushDepartmentDTO();
                departmentDTO.setDepartmentCode(x.getDept_id()==null?"":String.valueOf(x.getDept_id()));
                departmentDTO.setDepartmentName(x.getName());
                departmentDTO.setParentCode(x.getParent_id()==null?"":String.valueOf(x.getParent_id()));
                return departmentDTO;
            }).collect(Collectors.toList());
        }
        return list;
    }

    public static List<DingTalkPushDepartmentDTO> convertDeptBase(List<DingTalkTenantGetDeptListRsp.DeptBaseResponse> deptList){
        List<DingTalkPushDepartmentDTO> list = null;
        if (!CollectionUtils.isEmpty(deptList)){
            list = deptList.stream().map(x->{
                DingTalkPushDepartmentDTO departmentDTO = new DingTalkPushDepartmentDTO();
                departmentDTO.setDepartmentCode(x.getDept_id()==null?"":String.valueOf(x.getDept_id()));
                departmentDTO.setDepartmentName(x.getName());
                departmentDTO.setParentCode(x.getParent_id()==null?"":String.valueOf(x.getParent_id()));
                return departmentDTO;
            }).collect(Collectors.toList());
        }
        return list;
    }

    public static List<DingTalkPushUserDTO> convertUserDetail(List<DingTalkTenantGetUserDetailRsp.UserDetailResponse> userList){
        List<DingTalkPushUserDTO> list = null;
        if (!CollectionUtils.isEmpty(userList)){
            list = userList.stream().map(x->{
                DingTalkPushUserDTO userDTO = new DingTalkPushUserDTO();
                userDTO.setUserId(x.getUserid());
                userDTO.setName(x.getName());
                userDTO.setAvatar(x.getAvatar());
                userDTO.setManagerUserId(x.getManager_userid());
                userDTO.setMobile(x.getMobile());
                userDTO.setWorkNum(x.getJob_number());
                userDTO.setPost(x.getTitle());
                userDTO.setEmail(x.getEmail());
                userDTO.setDeptIdList(x.getDept_id_list());
                userDTO.setEntryTime(x.getHired_date()==null?new Date():new Date(x.getHired_date()));
                userDTO.setIsAdmin(x.getAdmin());
                userDTO.setDataFrom(DataFromEnum.DD.getCode());
                return userDTO;
            }).collect(Collectors.toList());
        }
        return list;
    }

    public static List<DingTalkPushUserDTO> convertUserBase(List<DingTalkTenantGetUserListRsp.UserBaseResponse> userList){
        List<DingTalkPushUserDTO> list = null;
        if (!CollectionUtils.isEmpty(userList)){
            list = userList.stream().map(x->{
                DingTalkPushUserDTO userDTO = new DingTalkPushUserDTO();
                userDTO.setUserId(x.getUserid());
                userDTO.setName(x.getName());
                userDTO.setAvatar(x.getAvatar());
                userDTO.setMobile(x.getMobile());
                userDTO.setWorkNum(x.getJob_number());
                userDTO.setPost(x.getTitle());
                userDTO.setEmail(x.getEmail());
                userDTO.setDeptIdList(x.getDept_id_list());
                userDTO.setEntryTime(x.getHired_date()==null?new Date():new Date(x.getHired_date()));
                userDTO.setIsAdmin(x.getAdmin());
                userDTO.setDataFrom(DataFromEnum.DD.getCode());
                return userDTO;
            }).collect(Collectors.toList());
        }
        return list;
    }

}
