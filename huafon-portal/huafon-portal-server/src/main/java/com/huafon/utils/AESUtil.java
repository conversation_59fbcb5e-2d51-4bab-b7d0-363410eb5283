package com.huafon.utils;

import com.huafon.common.utils.BaseUtils;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class AESUtil {

    /**
     * AES 密钥长度（128, 192, 256）
     */
    private static final int KEY_SIZE = 128;

    /**
     * 算法名
     */
    private static final String ALGORITHM = "AES";

    private static final String key = "DbLUOBqOLs18qeizOYYAdQ==";

    /**
     * 生成 AES 密钥
     */
    public static String generateKey() throws Exception {
        KeyGenerator keyGen = KeyGenerator.getInstance(ALGORITHM);
        keyGen.init(KEY_SIZE);
        SecretKey secretKey = keyGen.generateKey();
        return Base64.getEncoder().encodeToString(secretKey.getEncoded());
    }

    /**
     * 加密防范，基础方法不处理一切异常，由调用方处理
     *
     * @param data
     * @param key
     * @return
     * @throws Exception
     */
    public static String encrypt(String data, String key) throws Exception {
        SecretKeySpec secretKey = new SecretKeySpec(Base64.getDecoder().decode(key), ALGORITHM);
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        byte[] encryptedBytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    public static String encrypt(String data) throws Exception {
        SecretKeySpec secretKey = new SecretKeySpec(Base64.getDecoder().decode(key), ALGORITHM);
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        byte[] encryptedBytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

//    public static void main(String[] args) throws Exception {
//
//        System.out.println(generateKey());
//
//        String encrypt = encrypt("123456");
//        System.out.println(encrypt);
//        System.out.println("-------------------------");
//
//        System.out.println(decrypt(encrypt, key));
//
//    }

    /**
     * 解密，基础方法不处理一切异常，由调用方处理
     */

    public static String decrypt(String encryptedData, String key) throws Exception {
        if (BaseUtils.isNull(encryptedData)) {
            return encryptedData;
        }
        SecretKeySpec secretKey = new SecretKeySpec(Base64.getDecoder().decode(key), ALGORITHM);
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, secretKey);
        byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedData));
        return new String(decryptedBytes, "UTF-8");
    }

    //public static void main(String[] args) {
    //    try {
    //        String phone = "18888888888";
    //        String key = generateKey();
    //        System.out.println("Generated Key: " + key);
    //
    //        String encryptedPhone = encrypt(phone, key);
    //        System.out.println("Encrypted Phone: " + encryptedPhone);
    //
    //        String decryptedPhone = decrypt(encryptedPhone, key);
    //        System.out.println("Decrypted Phone: " + decryptedPhone);
    //    } catch (Exception e) {
    //        e.printStackTrace();
    //    }
    //}
}
