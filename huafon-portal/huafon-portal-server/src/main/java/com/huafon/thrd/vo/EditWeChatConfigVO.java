package com.huafon.thrd.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description 编辑企业微信对象
 * <AUTHOR>
 * @Date 2023/8/23 15:22
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "EditWeChatConfigVO", description = "编辑企业微信对象")
public class EditWeChatConfigVO {

    @ApiModelProperty(value = " 企业微信应用AgentID")
    @NotNull(message = "请输入企业微信应用AgentID")
    private Long agentId;
    @ApiModelProperty(value = "企业微信应用secret")
    @NotBlank(message = "请输入企业微信应用secret")
    private String secret;
    @ApiModelProperty(value = "企业微信企业id")
    @NotBlank(message = "请输入企业微信企业id")
    private String cropId;

    @ApiModelProperty(value = "是否打开通讯录")
    @NotNull(message = "是否打开通讯录必传")
    private Boolean isOpenContacts;
}