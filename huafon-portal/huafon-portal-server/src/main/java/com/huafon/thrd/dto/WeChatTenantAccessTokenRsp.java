package com.huafon.thrd.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Description 企业微信获取token接口响应
 * <AUTHOR>
 * @Date 2023/8/23 15:12
 * @Version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class WeChatTenantAccessTokenRsp {
    @ApiModelProperty(value = "企业微信应用的accessToken")
    @JsonProperty("access_token")
    private String accessToken;

    @ApiModelProperty(value = "错误码")
    private Long errcode;

    @ApiModelProperty(value = "错误信息")
    private String errmsg;

    @ApiModelProperty(value = "错误描述")
    @JsonProperty("expires_in")
    private Long expiresIn;
}
