package com.huafon.thrd.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description 钉钉订阅事件类型枚举值
 * <AUTHOR>
 * @Date 2024/4/15 13:31
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum DingTalkEventTypeEnum {

    ORG_DEPT_CREATE("org_dept_create", "企业部门创建"),
    ORG_DEPT_MODIFY("org_dept_modify", "企业部门修改"),
    ORG_DEPT_REMOVE("org_dept_remove", "企业部门删除"),
    USER_ADD_ORG("user_add_org", "用户增加"),
    USER_LEAVE_ORG("user_leave_org", "用户离职"),
    USER_MODIFY_ORG("user_modify_org", "用户更改");

    private final String code;

    private final String desc;

    public static DingTalkEventTypeEnum getByCode(String code) {
        for (DingTalkEventTypeEnum en : DingTalkEventTypeEnum.values()) {
            if (en.getCode().equals(code)) {
                return en;
            }
        }
        return null;
    }

}
