package com.huafon.thrd.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/20 9:41
 */
@Data
public class HuaFonBaseCenterUserAddReq {

    private Integer userId;

    private String name;

    /**
     * Column: username
     * Type: varchar(150)
     * Remark: 用户名
     */
    private String username;

    /**
     * Column: password
     * Type: varchar(150)
     * Remark: 密码
     */
    private String password;

    /**
     * Column: mobile
     * Type: varchar(30)
     * Remark: 手机号码
     */
    private String mobile;

    /**
     * Column: email
     * Type: varchar(255)
     * Remark: 邮箱
     */
    private String email;

    /**
     * Column: avatar
     * Type: varchar(500)
     * Default value: ''::character varying
     * Remark: 头像
     */
    private String avatar;

    /**
     * Column: gender
     * Type: int2
     * Remark: 性别，1|男 2|女
     */
    private Integer gender;

    /**
     * Column: id_number
     * Type: varchar(32)
     * Remark: 身份证号码
     */
    private String idNumber;

    /**
     * Column: permanent_effective
     * Type: bool
     * Default value: true
     * Remark: 账户有效期 t|永久有效 f|有效期至
     */
    private Boolean permanentEffective;

    /**
     * Column: effective_date
     * Type: timestamp
     * Remark: 有效期日期
     */
    private Date effectiveDate;


    /**
     * Column: state
     * Type: int2
     * Default value: 1
     * Remark: 状态，1|启用 2|禁用
     */
    private Integer state;

    /**
     * Column: create_by
     * Type: int8
     * Remark: 创建人
     */
    private Long createBy;

    /**
     * Column: modify_by
     * Type: int8
     * Remark: 编辑人
     */
    private Long modifyBy;

    /**
     * Column: is_del
     * Type: int2
     * Default value: 0
     * Remark: 是否删除 0|否 1|是
     */
    private Integer isDel;

    private String companyCode;

    private List<String> tenantCode;

    private List<Integer> tenantIds;
}
