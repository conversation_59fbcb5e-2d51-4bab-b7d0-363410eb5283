package com.huafon.requestOrigin;

import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 请求来源拦截
 *
 * <AUTHOR>
 * @since 2022-11-09 10:25
 **/
@Slf4j
@Component
public class RequestOriginInterceptor implements HandlerInterceptor {

	public static final String REQUEST_ORIGIN = "requestOrigin";

	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
		String requestOrigin = request.getHeader(REQUEST_ORIGIN);
		if (Strings.isNotBlank(requestOrigin)) {
			try{
				int requestOriginMenu = Integer.parseInt(requestOrigin);
				RequestOriginContext.bind(requestOriginMenu);;
			} catch (NumberFormatException e) {
				log.error("[解析请求来源错误] requestOrigin: {}", requestOrigin);
			}
		}
		return true;
	}

	@Override
	public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
		RequestOriginContext.unBind();
	}
}
