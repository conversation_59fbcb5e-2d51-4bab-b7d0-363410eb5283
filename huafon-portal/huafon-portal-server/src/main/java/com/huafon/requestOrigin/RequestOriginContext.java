package com.huafon.requestOrigin;

/**
 * 请求来源
 *
 * <AUTHOR>
 * @since 2022-11-09 10:29
 **/
public class RequestOriginContext {

	private static final ThreadLocal<Integer> requestOriginThreadLocal = new ThreadLocal<>();

	public static void bind(Integer requestOrigin) {
		requestOriginThreadLocal.set(requestOrigin);
	}

	public static Integer getRequestOrigin() {
		return requestOriginThreadLocal.get();
	}

	public static void unBind() {
		requestOriginThreadLocal.remove();
	}
}
