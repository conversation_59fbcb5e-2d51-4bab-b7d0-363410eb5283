package com.huafon.dao.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.huafon.common.config.TenantContext;
import com.huafon.models.entity.CommonSystemFunction;
import com.huafon.support.config.UserContext;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @since 2023-06-13 16:29
*/
@Mapper
public interface CommonSystemFunctionMapper extends BaseMapper<CommonSystemFunction> {

    /**
    * 批量生成
    */
    void batchInsert(@Param("source") List<CommonSystemFunction> source);


    default LambdaQueryWrapper<CommonSystemFunction> lambdaQuery() {
        return new LambdaQueryWrapper<CommonSystemFunction>()
                .eq(CommonSystemFunction::getTenantId, TenantContext.getOne())
                .eq(CommonSystemFunction::getUserId, UserContext.getId());
    }

    default CommonSystemFunction queryUserCommonFunction() {
        LambdaQueryWrapper<CommonSystemFunction> query = lambdaQuery().orderByAsc(CommonSystemFunction::getId).last(" limit 1");
        return this.selectOne(query);
    }

}
