package com.huafon.dao.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.huafon.common.config.TenantContext;
import com.huafon.models.entity.RoleType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @since 2023-10-13 14:47
*/
@Mapper
public interface RoleTypeMapper extends BaseMapper<RoleType> {

    /**
    * 批量生成
    */
    void batchInsert(@Param("source") List<RoleType> source);


    default LambdaQueryWrapper<RoleType> lambdaQuery() {
        return new LambdaQueryWrapper<RoleType>().eq(RoleType::getTenantId, TenantContext.getOne());
    }

}
