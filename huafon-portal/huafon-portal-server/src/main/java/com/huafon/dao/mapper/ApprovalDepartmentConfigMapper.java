package com.huafon.dao.mapper;

import com.huafon.models.entity.ApprovalDepartmentConfig;
import com.huafon.portal.api.dto.ApprovalDepartmentWithUserIdsDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ApprovalDepartmentConfigMapper {

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int insertSelective(ApprovalDepartmentConfig row);



    int updateByPrimaryKey(ApprovalDepartmentConfig row);

    ApprovalDepartmentConfig selectByDepartmentId(Long departmentId);

    List<ApprovalDepartmentWithUserIdsDto> selectDepartmentWithUserIds();

    List<ApprovalDepartmentConfig> selectByDepartmentIds(@Param("departmentIds") List<Long> departmentIds);


    List<ApprovalDepartmentConfig> queryAllExistApproval();

    void insetBatch(@Param("source") List<ApprovalDepartmentConfig> source);

    void updateBatch(@Param("source") List<ApprovalDepartmentConfig> source);
}
