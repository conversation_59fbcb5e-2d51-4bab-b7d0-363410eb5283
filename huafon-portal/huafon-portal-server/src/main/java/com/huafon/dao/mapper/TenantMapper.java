package com.huafon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huafon.models.entity.Tenant;
import com.huafon.models.query.TenantPageQuery;
import com.huafon.models.vo.tenant.*;
import com.huafon.models.vo.user.UserTenantVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TenantMapper extends BaseMapper<Tenant> {

    Tenant selectByPrimaryKey(Integer tenantId);

    List<Tenant> selectAllList();

    /**
     * 分页查询租户信息
     *
     * @param page
     * @param reqVO
     * @return
     */
    IPage<TenantPageRespVo> findTenantListPage(Page<TenantPageRespVo> page, @Param("reqVO") TenantPageReqVo reqVO);

    /**
     * 根据id查询租户详情
     *
     * @param tenantId
     * @return
     */
    TenantVo getByTenantId(Integer tenantId);

    /**
     * 批量假删除
     *
     * @param list
     */
    void updateDelFlagById(@Param("list") List<Tenant> list);

    /**
     * 根据名称查询是否已经存在
     *
     * @param name
     * @return
     */
    Tenant selectByName(String name);

    /**
     * 查询全量
     *
     * @return
     */
    List<Tenant> findAllList();

    //---------------------------------------------version: v2 start------------------------------------------

    /**
     * 租户管理分页 v2
     *
     * @param page
     * @param query
     * @return
     */
    Page<TenantV2CommonVo> commonPage(@Param("page") IPage<TenantV2CommonVo> page, @Param("query") TenantPageQuery query);

    /**
     * 通过用户id查询
     *
     * @param list
     * @return
     */
    List<UserTenantVO> findByUserIdList(List<Integer> list);

    /**
     * 获取用户下关联的租户列表
     *
     * @param userId
     * @return
     */
    List<TenantVo> findByUserId(Integer userId);

    /**
     * 获取用户下关联的租户列表
     *
     * @param userId
     * @return
     */
    List<TenantVo> findByUserIdAndTenantId(@Param("userId") Integer userId, @Param("tenantId") Integer tenantId);

    /**
     * 查询租户信息
     *
     * @param tenantIds
     * @return
     */
    List<Tenant> queryByTenantIds(@Param("tenantIds") List<Integer> tenantIds, @Param("state") Integer state);

    /**
     * 通过项目ID查询关联的租户列表
     *
     * @param projectId
     * @return
     */
    List<Integer> queryTenantIdByProjectId(Integer projectId);

    Page<TenantSimplePageRespVO> querySimpleCommonPage(IPage<TenantSimplePageRespVO> page, @Param("reqVO") TenantSimplePageReqVO reqVO);


    Page<TenantSimplePageRespVO> querySimpleAdminPage(IPage<TenantSimplePageRespVO> page, @Param("reqVO") TenantSimplePageReqVO reqVO);

    void updateMapPropertyTenant(@Param("tenantId") Integer tenantId, @Param("tenantIds") List<Integer> tenantIds);


    List<Tenant> selectAllPushBaseCenter();

    int updateTenantPushBaseCenter(@Param("tenantId") Integer tenantId, @Param("isPushBaseCenter") Integer isPushBaseCenter);


}
