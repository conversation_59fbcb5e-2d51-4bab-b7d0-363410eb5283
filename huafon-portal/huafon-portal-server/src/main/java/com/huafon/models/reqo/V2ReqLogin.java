package com.huafon.models.reqo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class V2ReqLogin {

    @ApiModelProperty(value = "用户名",required = true)
    @NotBlank(message = "用户名不能为空")
    private String username;

    @ApiModelProperty(value = "密码",required = true)
    private String password;

    @ApiModelProperty(value = "验证码id",required = true)
    @NotBlank(message = "验证码id不能为空")
    private String codeId;

    @ApiModelProperty(value = "验证码",required = true)
    @NotBlank(message = "验证码不能为空")
    private String code;

    @ApiModelProperty(value = "公网ip")
    private String publicIp;

    @ApiModelProperty(value = "地点，需要拼接，如：中国|浙江省|杭州|上城区")
    private String site;

    @ApiModelProperty(value = "操作系统")
    private String operateSystem;

    @ApiModelProperty(value = "浏览器")
    private String browser;

    @ApiModelProperty(value = "客户端类型 1:PC|2:APP")
    private Integer clientType;

    @ApiModelProperty(value = "应用类型0:飞书｜1:钉钉｜2:企业微信|3:账号密码")
    private Integer applicationType;

    @ApiModelProperty(value = "md5密码")
    private String md5Password;

}
