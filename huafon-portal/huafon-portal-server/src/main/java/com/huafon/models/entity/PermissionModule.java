package com.huafon.models.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import lombok.Data;

/**
* 菜单-模块关联表
* <AUTHOR>
* @since 2023-02-08 14:27
**/
@Data
@TableName("hf_permission_module")
public class PermissionModule extends BaseEntity {

	private static final long serialVersionUID = 2877545727387072062L;

	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;

	//菜单前端路由
	@TableField(value = "url")
	private String url;

	//业务模块
	@TableField(value = "module")
	private String module;

	@TableField(value = "description")
	private String description;

}
