package com.huafon.models.enums;

import lombok.Getter;
import org.apache.logging.log4j.util.Strings;
import org.springframework.http.HttpHeaders;

/**
 * <AUTHOR>
 * @since 2023-03-13 13:40
 **/
@Getter
public enum LoginOriginEnum {
	PC(1, "网站"), MOBILE(2, "手机客户端");

	final int clientType;
	final String desc;

	LoginOriginEnum(int clientType, String desc) {
		this.clientType = clientType;
		this.desc = desc;
	}

	/**
	 * 根据请求头获取来源
	 * @param headers
	 * @return
	 */
	public static LoginOriginEnum processLoginOrigin(HttpHeaders headers) {
		if (headers != null) {
			String platform = headers.getFirst("platform");
			if (Strings.isNotBlank(platform) && (platform.contains("ios") || platform.contains("android"))) {
				return MOBILE;
			}
		}

		return PC;
	}
}
