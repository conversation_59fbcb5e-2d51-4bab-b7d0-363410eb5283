package com.huafon.models.vo.score.req;

import com.huafon.framework.mybatis.pojo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName com.huafon.models.vo.score.req.PersonalScoreRankPageReqVO
 * @Description
 * @createTime 2023年10月23日 16:11:00
 */
@Data
@ApiModel(value = "PersonalScoreRankPageReqVO", description = "个人积分排名请求体")
public class PersonalScoreRankPageReqVO extends PageRequest {

    @ApiModelProperty(value = "模糊搜索字段，支持姓名搜索")
    private String fuzzyField;

    @ApiModelProperty(value = "排名角色：0-员工，1-HSE管理员")
    private Integer rankRole;

    @ApiModelProperty(value = "部门id列表（使用list结构防止后续变更为多选）")
    private List<Integer> departmentIds;
}
