package com.huafon.models.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 员工个人附加信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Data
@TableName(value = "hf_user_additional_information")
public class UserAdditionalInformation extends BaseEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 籍贯
     */
    private String nativePlace;

    /**
     * 职务类型
     */
    private String jobType;

    /**
     * 职务名称
     */
    private String jobName;

    /**
     * 任当前职务时间
     */
    private Date jobTime;

    /**
     * 职称名称
     */
    private String titleName;

    /**
     * 职称等级
     */
    private String titleLevel;

    /**
     * 最后毕业院校
     */
    private String finalGraduationSchool;

    /**
     * 专业名称
     */
    private String majorName;

    /**
     * 是否取得主要负责人安全资格证书
     */
    private Boolean isPrincipal;

    /**
     * 是否取得安全管理人员资格证书
     */
    private Boolean isManage;

    /**
     * 是否化学、化工安全等相关专业
     */
    private Boolean isMajor;

    /**
     * 是否化工安全类注册安全工程师
     */
    private Boolean isEngineer;

    /**
     * 是否特种作业岗位在岗人员
     */
    private Boolean isSpecial;

    /**
     * 出生日期
     */
    private Date birthday;

    /**
     * 修改人
     */
    private String modifyByName;

    /**
     * 租户id
     */
    private Integer tenantId;

}
