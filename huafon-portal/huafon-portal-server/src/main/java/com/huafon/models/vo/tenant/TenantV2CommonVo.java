package com.huafon.models.vo.tenant;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022-12-12 14:39
 **/
@Data
@ApiModel(value = "租户V2列表展示信息")
public class TenantV2CommonVo {

	@ApiModelProperty(value = "租户ID")
	private Integer tenantId;

	@ApiModelProperty(value = "租户名称")
	private String name;

	@ApiModelProperty(value = "简称(缩写)")
	private String abbreviation;

	@ApiModelProperty(value = "到期日期")
	@JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
	private Date maturityDate;

	@ApiModelProperty(value = "联系人用户名")
	private String linkmanUserName;

	@ApiModelProperty(value = "联系人姓名")
	private String linkmanName;

	@ApiModelProperty(value = "联系人电话")
	private String linkmanMobile;

	@ApiModelProperty(value = "联系人UserId")
	private Integer linkmanId;

	@ApiModelProperty(value = "备注")
	private String remark;

	@ApiModelProperty(value = "状态：1:启用，2：禁用")
	private Integer state;

	@ApiModelProperty(value = "是否过期: true表示已过期")
	private Boolean isExpired;

	@ApiModelProperty(value = "企业编码")
	private String companyCode;

}
