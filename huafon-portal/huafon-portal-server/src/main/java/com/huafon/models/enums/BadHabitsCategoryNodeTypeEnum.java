package com.huafon.models.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum BadHabitsCategoryNodeTypeEnum {
    ROOT_NODE("ROOT", "根"),
    TOP_NODE("TOP", "顶级"),
    ORDINARY_NODE("ORDINARY", "普通"),
    UNDETERMINED_NODE("UNDETERMINED", "待分类"),
    ;

    private final String code;

    private final String desc;

    public static BadHabitsCategoryNodeTypeEnum getByCode(String code) {
        for (BadHabitsCategoryNodeTypeEnum en : BadHabitsCategoryNodeTypeEnum.values()) {
            if (en.getCode().equals(code)) {
                return en;
            }
        }
        return null;
    }
}