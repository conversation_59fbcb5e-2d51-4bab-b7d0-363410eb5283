package com.huafon.models.vo;

import com.huafon.models.entity.CommonUserGroupRelation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 常用组和用户的关系VO
 * @Date: 2022/11/11 3:59 PM
 * @Author: zyf
 **/
@Data
@ApiModel(value = "CommonUseGroupRelationVO", description = "常用组和用户的关系VO")
public class CommonUseGroupRelationVO {
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "职员组id")
    private Long staffGroupId;

    @ApiModelProperty(value = "关联的用户ID")
    private Integer relationUserId;

    @ApiModelProperty(value = "头像地址")
    private String avatarUrl;

    @ApiModelProperty(value = "员工名称")
    private String staffName;

    @ApiModelProperty(value = "移动号码")
    private String mobile;

    @ApiModelProperty(value = "组织机构id")
    private Long departmentId;

    @ApiModelProperty(value = "组织机构名称")
    private String departmentName;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "性别")
    private Integer gender;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "身份证号")
    private String idNumber;

    @ApiModelProperty(value = "工号")
    private String workNum;

    public static CommonUserGroupRelation convertToStaffGroupRelation(CommonUseGroupRelationVO item) {
        if (item == null) {
            return null;
        }
        CommonUserGroupRelation commonUserGroupRelation = new CommonUserGroupRelation();
        commonUserGroupRelation.setId(item.getId());
        commonUserGroupRelation.setStaffGroupId(item.getStaffGroupId());
        commonUserGroupRelation.setRelationUserId(item.getRelationUserId());
        return commonUserGroupRelation;
    }

}
