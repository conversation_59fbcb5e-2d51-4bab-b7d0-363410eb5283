package com.huafon.models.vo.score.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel(value="BadHabitsCategorySubmitParam", description="陋习分类提交")
public class BadHabitsCategorySubmitParam {
    @ApiModelProperty(value = "id，编辑必填")
    private Long id;

    @ApiModelProperty(value = "分类名称")
    @NotBlank(message = "分类名称必传")
    private String name;

    @ApiModelProperty(value = "图标")
    private String icon;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "父节点")
    @NotNull(message = "parentId必传")
    private Long parentId;

    @ApiModelProperty(value = "排序值")
    private Integer sortOrder;

    @ApiModelProperty(value = "类型：TOP-顶级节点，ORDINARY-普通节点，UNDETERMINED-待分类节点")
    @NotBlank(message = "nodeType必传")
    private String nodeType;
}
