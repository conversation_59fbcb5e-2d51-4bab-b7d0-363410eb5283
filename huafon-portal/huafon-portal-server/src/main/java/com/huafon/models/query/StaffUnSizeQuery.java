package com.huafon.models.query;

import com.huafon.framework.mybatis.pojo.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-06-18 10:16
 **/
@Data
public class StaffUnSizeQuery extends PageRequest {

	@ApiModelProperty(value = "模糊查询")
	private String searchKey;

	@ApiModelProperty(value = "账户ID过滤", hidden = true)
	private List<Integer> filterDeptIds;

	@ApiModelProperty(value = "部门")
	private Integer deptId;

	@ApiModelProperty(value = "租户", hidden = true)
	private Integer tenantId;
}
