package com.huafon.models.vo.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 统计各个部门下的用户数请求VO
 * @Date: 2023/2/1 16:14
 * @Author: zyf
 **/
@Data
@ApiModel(value = "StatisticsUserByDeptReqVO", description = "统计各个部门下的用户数请求VO")
public class StatisticsUserByDeptReqVO {

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private Integer tenantId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @NotNull(message = "项目id不能为空")
    private Integer projectId;

}
