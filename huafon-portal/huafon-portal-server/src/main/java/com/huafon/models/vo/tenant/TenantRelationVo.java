package com.huafon.models.vo.tenant;

import com.huafon.models.vo.org.DepartmentTreeVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 关联企业
 * <AUTHOR>
 * @since 2022-12-22 13:35
 **/
@Data
@ApiModel(value = "租户关联企业信息")
public class TenantRelationVo {

	@ApiModelProperty(value = "关联租户的信息")
	private TenantV2ListVo relationTenant;

	@ApiModelProperty(value = "关联租户的组织机构")
	private List<DepartmentTreeVO> departmentTree;
}
