package com.huafon.models.reqo.approval;

import com.huafon.portal.api.dto.UserMobileDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/2 10:17
 */
@Data
public class ReqDepartmentAdd {

    @ApiModelProperty(value = "部门id", required = true)
    @NotNull(message = "部门id不能为空")
    private Integer departmentId;

    @ApiModelProperty(value = "部门负责人")
    private List<UserMobileDto> principalUserList;

    @ApiModelProperty(value = "hse负责人")
    private List<UserMobileDto> hsePrincipalUserList;

    @ApiModelProperty(value = "财务负责人")
    private List<UserMobileDto> financePrincipalUserList;

    @ApiModelProperty(value = "分管领导")
    private List<UserMobileDto> leaderUserList;

    @ApiModelProperty(value = "装置/工段负责人")
    private List<UserMobileDto> deviceSectionPrincipalUserList;

    @ApiModelProperty(value = "班组负责人")
    private List<UserMobileDto> teamPrincipalUserList;

    @ApiModelProperty(value = "车间负责人")
    private List<UserMobileDto> workshopPrincipalUserList;
}
