package com.huafon.models.vo.tenant;

import com.huafon.models.entity.Tenant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022-12-13 10:21
 **/
@Data
@ApiModel(value = "租户列表展示")
public class TenantV2ListVo {
	@ApiModelProperty(value = "租户ID")
	private Integer tenantId;

	@ApiModelProperty(value = "租户名称")
	private String name;

	@ApiModelProperty(value = "状态：1:启用，2：禁用")
	private Integer state;

	@ApiModelProperty(value = "简称(缩写)")
	private String abbreviation;

	@ApiModelProperty(value = "企业编码")
	private String companyCode;

	@ApiModelProperty(value = "关联的地图ID")
	private Integer projectId;

	@ApiModelProperty(value = "是否选中")
	private boolean isSelect = true;

	public TenantV2ListVo() {
	}

	public TenantV2ListVo(Tenant tenant) {
		this.tenantId = tenant.getTenantId();
		this.name = tenant.getName();
		this.state = tenant.getState();
		this.companyCode = tenant.getCompanyCode();
		this.projectId = tenant.getProjectId();
	}
}
