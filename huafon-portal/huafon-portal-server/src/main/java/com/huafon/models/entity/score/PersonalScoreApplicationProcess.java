package com.huafon.models.entity.score;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import com.huafon.service.EntityVerify;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @ClassName com.huafon.models.entity.score.PersonalScoreApplicationProcess
 * @Description  个人积分审批流程
 * @createTime 2023年10月23日 13:54:00
 */
@Data
@TableName(value = "hf_personal_score_application_process", autoResultMap = true)
public class PersonalScoreApplicationProcess extends BaseEntity implements EntityVerify {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    private Integer tenantId;

    /**
     * 积分申请编码
     */
    private String processCode;

    /**
     * 申请对象列表，json字符串（直接使用json字符串而非jsonb是为了方便like检索）
     */
    private String targetUserJsonArr;

    /**
     * 申请原因
     */
    private String reason;

    /**
     * 积分类型：1-加分/-1-减分
     */
    private Integer type;

    /**
     * 申请积分
     */
    private BigDecimal applyScore;

    /**
     * 确认积分
     */
    private BigDecimal confirmScore;

    /**
     * 创建分姓名
     */
    private String createName;

    /**
     * 修改人姓名
     */
    private String modifyName;

    /**
     * 流程状态：1-流程中、999-已完成、-1-已取消
     */
    private Integer processStatus;

    /**
     * 流程id
     */
    private String workflowId;

    /**
     * 流程签名
     */
    private String sign;

    /**
     * 流程备注内容
     */
    private String content;

    /**
     * 附件
     */
    private String attachment;

    /**
     * 版本号
     */
    @Version
    private Integer version;
}
