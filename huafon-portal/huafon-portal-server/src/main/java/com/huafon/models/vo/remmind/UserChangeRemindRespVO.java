package com.huafon.models.vo.remmind;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 *
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-02-09
 */
@Data
@ApiModel(value="UserChangeRemindRespVO对象", description="人员流动提醒响应VO")
public class UserChangeRemindRespVO {

	@ApiModelProperty(value = "id")
	private Integer id;

	@ApiModelProperty(value = "入职为null，转岗/离职为用户表id")
	private Integer userId;

	@ApiModelProperty(value = "用户名称")
	private String userName;

	@ApiModelProperty(value = "工号")
	private String workNum;

	@ApiModelProperty(value = "租户id")
	private Integer tenantId;

	@ApiModelProperty(value = "提醒类型（1：入职提醒，2：转岗提醒，3：离职提醒）")
	private Integer dataType;

	@ApiModelProperty(value = "变动时间（入职，离职，转岗时间）")
	private Date changeDate;

	@ApiModelProperty(value = "离岗数据")
	private String leaveData;

	@ApiModelProperty(value = "就岗数据")
	private String entryData;

	@ApiModelProperty(value = "变动说明")
	private String remark;

	@ApiModelProperty(value = "提醒确认状态（0：未确认，1：已确认）")
	private Integer confirmStatus;

	@ApiModelProperty(value = "是否接害岗位（1：是，0：否）")
	private Integer isHarmPost;

	@ApiModelProperty(value = "数据来源")
	private String dataFrom;

	@ApiModelProperty(value = "状态，1|启用 2|禁用")
	private Integer state;

}