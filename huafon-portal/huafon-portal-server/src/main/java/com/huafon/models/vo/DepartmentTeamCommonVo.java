package com.huafon.models.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
/**
* <AUTHOR>
* @since 2023-04-10 17:28
*/
@Data
@ApiModel(value = "部门-班组信息列表")
public class DepartmentTeamCommonVo implements Serializable {

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "班组名称")
    private String teamName;

    @ApiModelProperty(value = "所属部门")
    private Integer departmentId;

    @ApiModelProperty(value = "所属部门名称")
    private String departmentName;

    @ApiModelProperty(value = "班组负责人(UserId)")
    private Integer principalUserId;

    @ApiModelProperty(value = "班组负责人姓名")
    private String principalUserName;

}
