package com.huafon.models.vo.role;

import com.huafon.common.config.TenantContext;
import com.huafon.models.entity.Role;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @Description: 角色复制VO
 * @Date: 2024/4/3 9:32
 * @Author: zyf
 **/
@Data
@ApiModel(value = "RoleDuplicationVO", description = "角色复制VO")
public class RoleDuplicationVO {

    @ApiModelProperty(value = "被选中的角色id")
    @NotNull(message = "请选择被复制的角色")
    private Integer originalRoleId;

    @ApiModelProperty(value = "角色名称")
    @NotBlank(message = "角色名称不能为空")
    private String roleName;

    @ApiModelProperty(value = "角色类型名称")
    private String roleTypeName;

    @ApiModelProperty(value = "角色类型ID")
    private Integer roleTypeId;

    @ApiModelProperty(value = "备注")
    private String remark;

    public static Role convert(RoleDuplicationVO temp) {
        if (temp == null) {
            return null;
        }
        Date date = new Date();
        Role role = new Role();
        role.setRoleName(temp.getRoleName());
        role.setRoleTypeId(temp.getRoleTypeId());
        role.setRemark(temp.getRemark());
        role.setTenantId(TenantContext.getOne().longValue());
        role.setCreateTime(date);
        role.setModifyTime(date);
        return role;
    }

}
