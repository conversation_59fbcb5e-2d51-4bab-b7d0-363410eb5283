package com.huafon.models.entity;

import lombok.Data;

import java.util.Date;

/**
 * Table: hf_role_permission
 */
@Data
public class RolePermission {
    /**
     * Column: id
     * Type: serial
     * Default value: nextval('role_permission_id_seq'::regclass)
     * Remark: 主键
     */
    private Integer id;

    /**
     * Column: role_id
     * Type: int4
     * Remark: 角色id
     */
    private Integer roleId;

    /**
     * Column: permission_id
     * Type: int4
     * Remark: 权限id
     */
    private Integer permissionId;

    /**
     * Column: create_time
     * Type: timestamp
     * Remark: 创建时间
     */
    private Date createTime;

    /**
     * Column: update_time
     * Type: timestamp
     * Remark: 更新时间
     */
    private Date modifyTime;

    /**
     * Column: modify_by
     * Type: int8
     * Remark: 修改人id
     */
    private Long modifyBy;
}