package com.huafon.models.entity;

import com.huafon.framework.mybatis.pojo.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 角色、菜单和数据权限
 *
 * <AUTHOR>
 * @since 2022-11-07 17:21
 **/
@Data
@ApiModel(value = "角色、菜单和数据权限")
public class RolePermissionDataRule extends BaseEntity {

	@ApiModelProperty(value = "id")
	private Integer id;

	@ApiModelProperty(value = "角色ID")
	private Integer roleId;

	@ApiModelProperty(value = "菜单ID")
	private Integer permissionId;

	@ApiModelProperty(value = "数据权限ID")
	private Integer permissionDataRule;

	@ApiModelProperty(value = "租户")
	private Integer tenantId;
}
