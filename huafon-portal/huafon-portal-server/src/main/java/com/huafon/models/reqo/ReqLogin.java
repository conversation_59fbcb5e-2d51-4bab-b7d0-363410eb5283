package com.huafon.models.reqo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ReqLogin {

    @ApiModelProperty(value = "用户名",required = true)
    @NotBlank(message = "用户名不能为空")
    private String username;

    @ApiModelProperty(value = "密码",required = true)
    @NotBlank(message = "密码不能为空")
    private String password;

    @ApiModelProperty(value = "公网ip")
    private String publicIp;

    @ApiModelProperty(value = "地点，需要拼接，如：中国|浙江省|杭州|上城区")
    private String site;

    @ApiModelProperty(value = "操作系统")
    private String operateSystem;

    @ApiModelProperty(value = "浏览器")
    private String browser;

    @ApiModelProperty(value = "客户端类型 1:PC|2:APP")
    private Integer clientType;

}
