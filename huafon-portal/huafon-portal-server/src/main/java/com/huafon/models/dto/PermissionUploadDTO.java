package com.huafon.models.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Sets;
import com.huafon.service.support.MergeCustomRow;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.apache.logging.log4j.util.Strings;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023-04-13 13:06
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class PermissionUploadDTO extends MergeCustomRow {

	public interface PC {}
	public interface H5 {}

	@JsonIgnore
	public final static Set<String> WEB_URL = Sets.newHashSet("页面", "菜单");
	@JsonIgnore
	public final static Set<String> BUTTON = Sets.newHashSet("按钮");

	@ExcelProperty(value = "*菜单按钮类型")
	@Pattern(regexp = "菜单|页面|按钮", message = "权限类型为:菜单、页面、按钮", groups = {PC.class, H5.class})
	@NotEmpty(message = "菜单按钮类型不能为空", groups = {PC.class, H5.class})
	private String permissionType;//MENU|菜单,FUN:页面,BUTTON|按钮

	@ExcelProperty(value = "*菜单路径")
	@Length(max = 1000, message = "菜单路径最多1000字", groups = {PC.class, H5.class})
	@NotEmpty(message = "菜单路径不能为空", groups = {PC.class, H5.class})
	private String permissionName;

	@ExcelProperty(value = "*名称")
	@NotEmpty(message = "名称不能为空", groups = {PC.class})
	@Length(max = 100, message = "名称最多100字", groups = {PC.class})
	private String name;

	@ExcelProperty(value = "*菜单按钮code")
	@Length(max = 100, message = "菜单按钮code最多100字", groups = {PC.class, H5.class})
	@NotEmpty(message = "菜单按钮code不能为空", groups = {PC.class, H5.class})
	private String buttonCode;

	@ExcelProperty(value = "访问路径")
	@Length(max = 100, message = "访问路径最多100字", groups = {PC.class, H5.class})
	private String webUrl;

	@ExcelProperty(value = "*排序值")
	@Pattern(regexp = "^\\d+$", message = "排序值需为非负整数", groups = {PC.class, H5.class})
	@NotNull(message = "排序值不能为空", groups = {PC.class, H5.class})
	@NumberFormat("#")
	private String sortVal;

	@ExcelProperty(value = "功能类目")//(仅移动端菜单使用)2024-04-28
	private String feature;

	@JsonIgnore
	private Integer typeId;

	@ExcelProperty(value = "*是否启用")
	@Pattern(regexp = "是|否", message = "是否启用:是、否", groups = {PC.class, H5.class})
	@NotEmpty(message = "是否启用不能为空", groups = {PC.class, H5.class})
	private String isUsed;//0禁用;1启用

	@ExcelProperty(value = "*是否在移动端首页显示")
	private String isHomePage;

	@ExcelProperty(value = "语言类型")
	private String languageType;

	@ExcelProperty(value = "语言显示")
	private String languageName;

	@JsonIgnore
	public Integer getIsUsedIntValue() {
		return Objects.equals("是", isUsed) ? 1 : 0;
	}

	@JsonIgnore
	public Integer getIsHomePageIntValue() {
		return Objects.equals("否", isHomePage) ? 0 : 1;
	}

	@AssertTrue(message = "权限类型为菜单、页面时，前端路径不能为空", groups = {PC.class, H5.class})
	@JsonIgnore
	public boolean isWebUrlSetIfPermissionTypeInMenuOrFunIsTrue() {
		if (Strings.isNotBlank(permissionType)) {
			if (WEB_URL.contains(permissionType) && Strings.isBlank(webUrl)) {
				return false;
			}
		}
		return true;
	}

	@AssertTrue(message = "权限类型为按钮时，按钮code不能为空", groups = {PC.class, H5.class})
	@JsonIgnore
	public boolean isWebUrlSetIfPermissionTypeButtonIsTrue() {
		if (Strings.isNotBlank(permissionType)) {
			if (BUTTON.contains(permissionType) && Strings.isBlank(buttonCode)) {
				return false;
			}
		}
		return true;
	}

	@AssertTrue(message = "语言类型不为空，语言描述不能为空", groups = {PC.class})
	@JsonIgnore
	public boolean isLanguageTypeIsNotEmptyNameIsNotEmptyIsTrue() {
		if (Strings.isNotBlank(languageType)) {
			return Strings.isNotEmpty(languageName);
		}
		return true;
	}

	@AssertTrue(message = "语言描述不为空，语言类型不能为空", groups = {PC.class})
	@JsonIgnore
	public boolean isLanguageNameIsNotEmptyTypeIsNotEmptyIsTrue() {
		if (Strings.isNotBlank(languageName)) {
			return Strings.isNotEmpty(languageType);
		}
		return true;
	}

	@Getter
	@Setter
	public static class PermissionUploadDTOMergeDTO extends MergeCustomRow {
		@ExcelProperty(value = "菜单按钮类型")
		private String permissionType;//MENU|菜单,FUN:页面,BUTTON|按钮

		@ExcelProperty(value = "菜单路径")
		private String permissionName;

		@ExcelProperty(value = "名称")
		private String name;

		@ExcelProperty(value = "菜单按钮code")
		private String buttonCode;

		@ExcelProperty(value = "访问路径")
		private String webUrl;

		@ExcelProperty(value = "排序值")
		private String sortVal;

		@ExcelProperty(value = "功能类目")//(仅移动端菜单使用)2024-04-28
		private String feature;

		@ExcelProperty(value = "是否启用")
		private String isUsed;//0禁用;1启用

		@ExcelProperty(value = "是否在首页显示")
		private String isHomePage;

		List<PermissionUploadDTOItemDTO> items;

		private String errorMessage;

		private Integer clientType;
	}

	@Getter
	@Setter
	public static class PermissionUploadDTOItemDTO {
		@ExcelProperty(value = "语言类型")
		private String languageType;

		@ExcelProperty(value = "语言显示")
		private String languageName;

		@ExcelProperty(value = "错误原因")
		private String errorMessage;
	}


}
