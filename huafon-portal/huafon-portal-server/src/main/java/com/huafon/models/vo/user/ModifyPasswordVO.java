package com.huafon.models.vo.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description: 修改密码VO
 * @Date: 2023/1/13 16:31
 * @Author: zyf
 **/
@Data
@ApiModel(value = "ModifyPasswordVO", description = "修改密码VO")
public class ModifyPasswordVO {

    @ApiModelProperty(value = "用户id", required = true)
    @NotNull(message = "用户id不能为空")
    private Integer userId;

    @ApiModelProperty(value = "原有密码")
    @NotBlank(message = "原有密码不能为空")
    private String oldPassword;

    @ApiModelProperty(value = "密码")
    @NotBlank(message = "密码不能为空")
    private String password;

}
