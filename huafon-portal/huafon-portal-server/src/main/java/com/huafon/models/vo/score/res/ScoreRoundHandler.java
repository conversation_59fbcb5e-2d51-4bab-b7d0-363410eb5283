package com.huafon.models.vo.score.res;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @ClassName com.huafon.models.vo.score.res.ScoreRoundHanler
 * @Description
 * @createTime 2024年03月18日 09:39:00
 */
public interface ScoreRoundHandler {

    /**
     * 对小数点进行n位的四舍五入
     * @param score  输入分数
     * @param n  小数点后n位
     * @return  输出分数
     */
    default BigDecimal round(BigDecimal score, Integer n){
        if (score == null) {
            return BigDecimal.ZERO;
        }
        return score.setScale(1, BigDecimal.ROUND_HALF_UP);
    }
}
