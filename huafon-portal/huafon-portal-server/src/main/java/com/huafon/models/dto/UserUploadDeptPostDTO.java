package com.huafon.models.dto;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: TODO
 * @Date: 2024/10/23 10:40
 * @Author: zyf
 **/
@Data
public class UserUploadDeptPostDTO {

    private String deptStructureName;

    private String postName;

    public List<String> valid() {
        List<String> errorMsg = new ArrayList<>();
        if (StringUtils.isNotBlank(deptStructureName) && deptStructureName.length()>100) {
            errorMsg.add("部门最长100个字符");
        }
        if (StringUtils.isNotBlank(postName) && postName.length()>100) {
            errorMsg.add("岗位最长100个字符");
        }
        return errorMsg;
    }

}
