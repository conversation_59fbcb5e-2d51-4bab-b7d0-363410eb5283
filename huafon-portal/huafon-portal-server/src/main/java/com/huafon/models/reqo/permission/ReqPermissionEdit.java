package com.huafon.models.reqo.permission;

import com.huafon.models.vo.permission.MultiLanguageVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class ReqPermissionEdit {

    @ApiModelProperty(value = "权限id",required = true)
    @NotNull(message = "权限id不能为空")
    private Integer permissionId;

    @ApiModelProperty(value = "权限名称")
//    @NotBlank(message = "权限名称不能为空")
    private String permissionName;

    @ApiModelProperty(value = "前端url")
    private String webUrl;

    @ApiModelProperty(value = "后端url")
    private String apiUrl;

    @ApiModelProperty(value = "图标url")
    private String imgUrl;

    @ApiModelProperty(value = "新图标url")
    private String imgNewUrl;

    @ApiModelProperty(value = "按钮code")
    private String buttonCode;

    @ApiModelProperty(value = "排序值")
    private Integer sortVal;

    @ApiModelProperty(value = "是否启用，1|是 0|否")
    @Range(message = "是否启用", min = 0, max = 1)
    private Integer isUsed;

    @ApiModelProperty(value = "父级id，顶级的传0")
    private Integer parentId;

    @ApiModelProperty(value = "菜单分类ID")
    private Integer typeId;

    @ApiModelProperty(value = "名称",required = true)
//    @NotBlank(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "多语言配置")
    private List<MultiLanguageVo> multiLanguageList;

    @ApiModelProperty(value = "是否在首页展示：1是;0否")
    private Integer isHomePage;

}
