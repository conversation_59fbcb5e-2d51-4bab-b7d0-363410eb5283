package com.huafon.models.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @ClassName com.huafon.models.dto.PersonalScoreRankExportExcelDTO
 * @Description
 * @createTime 2023年10月27日 10:28:00
 */
@Data
@ApiModel(value = "积分雷达图导出实体")
@ContentRowHeight(30)
@HeadRowHeight(30)
@ColumnWidth(15)
@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 42) // 设置单元格填充为淡绿色（色号42是Excel中的淡绿色）
@HeadFontStyle(fontHeightInPoints = 11, bold = BooleanEnum.TRUE, fontName = "Arial") // 设置标题字体样式
public class PersonalScoreRadarExportExcelDTO {
    @ExcelProperty(value = "涉及模块")
    private String moduleName;

    @ExcelProperty(value = "积分")
    private BigDecimal totalScore = BigDecimal.ZERO;

    @ExcelProperty(value = "今年")
    private BigDecimal thisYear = BigDecimal.ZERO;

    @ExcelProperty(value = "去年")
    private BigDecimal lastYear = BigDecimal.ZERO;

    @ExcelProperty(value = "本月")
    private BigDecimal thisMonth = BigDecimal.ZERO;

    @ExcelProperty(value = "上月")
    private BigDecimal lastMonth = BigDecimal.ZERO;
}
