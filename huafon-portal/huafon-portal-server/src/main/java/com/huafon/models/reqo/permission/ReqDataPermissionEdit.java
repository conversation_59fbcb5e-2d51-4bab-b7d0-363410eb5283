package com.huafon.models.reqo.permission;

import com.huafon.portal.api.enums.CompareOperator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * 编辑角色数据权限
 *
 * <AUTHOR>
 * @since 2022-11-01 14:27
 **/
@Data
@ApiModel(value = "编辑数据权限")
public class ReqDataPermissionEdit {

	@ApiModelProperty(value = "数据权限ID")
	@NotNull(message = "数据权限ID不能为空")
	private Integer id;

	@ApiModelProperty(value = "权限名称")
	@NotNull(message = "权限名称不能为空")
	private String dataRuleName ;

	@ApiModelProperty(value = "数据权限类型: 1全部数据,2自定义数据,3本部门数据,4本部门及以下数据,5仅本人数据")
	@NotNull(message = "数据权限类型不能为空")
	private Integer dataRuleType;

	@ApiModelProperty(value = "是否启用(状态):1启用(有效)，0不启用(无效)")
	@NotNull(message = "状态不能为空")
	private Integer enable = 0;

	@ApiModelProperty(value = "自定义权限：字段")
	private String customizeColumn;

	/**
	 * @see CompareOperator
	 */
	@ApiModelProperty(value = "自定义权限(比较符):1.相等:=; 2.不相等:<>; 3.小于等于:<=; 4.小于:<; 5.大于:>; 6.大于等于:>=; 7.包含:in;", hidden = true)
	private String customizeCompareOperator = "in";

	@ApiModelProperty(value = "自定义数据")
	private List<Integer> customizeData = new ArrayList<>();
}
