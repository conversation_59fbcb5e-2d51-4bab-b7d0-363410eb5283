package com.huafon.models.vo.user;

import com.huafon.framework.mybatis.pojo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 租户-角色-添加账户
 *
 * <AUTHOR>
 * @since 2023-02-08 11:06
 **/
@Data
@ApiModel(value = "UserRoleSelectPageReqVO", description = "租户-角色-添加账户分页查询请求VO")
public class UserRoleSelectPageReqVO extends PageRequest {

	@ApiModelProperty(value = "用户名,电话,手机号模糊搜索")
	private String condition;

	@ApiModelProperty(value = "角色ID")
	private Integer roleId;

	@ApiModelProperty(value = "租户ID", hidden = true)
	private Integer tenantId;
}
