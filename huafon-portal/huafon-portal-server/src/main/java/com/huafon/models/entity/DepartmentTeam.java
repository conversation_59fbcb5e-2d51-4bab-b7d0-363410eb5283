package com.huafon.models.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import lombok.Data;

/**
* 部门-班组信息
* <AUTHOR>
* @since 2023-04-10 17:28
*/
@Data
@TableName("hf_department_team")
public class DepartmentTeam extends BaseEntity{

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
    * 班组名称
    */
    @TableField(value = "team_name")
    private String teamName;

    /**
    * 所属部门
    */
    @TableField(value = "department_id", updateStrategy = FieldStrategy.IGNORED)
    private Integer departmentId;

    /**
    * 班组负责人(UserId)
    */
    @TableField(value = "principal_user_id")
    private Integer principalUserId;

    /**
    * 租户ID
    */
    @TableField(value = "tenant_id")
    private Integer tenantId;


}
