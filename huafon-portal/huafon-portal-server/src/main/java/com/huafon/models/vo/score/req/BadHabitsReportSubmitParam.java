package com.huafon.models.vo.score.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "BadHabitsReportSubmitParam", description = "陋习上报请求")
public class BadHabitsReportSubmitParam {

    @ApiModelProperty(value = "发现人id")
    @NotNull(message = "发现人id不可为空")
    private Long discovererId;

    @ApiModelProperty(value = "发现人姓名")
    @NotBlank(message = "发现人姓名不可为空")
    private String discovererName;

    @ApiModelProperty(value = "发现时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "发现时间不可为空")
    private Date discoverTime;

    @ApiModelProperty(value = "发现地点")
    @NotBlank(message = "发现地点不可为空")
    private String discoverPlace;

    @ApiModelProperty(value = "陋习分类")
    @NotBlank(message = "陋习分类不可为空")
    private String categoryName;

    @ApiModelProperty(value = "问题描述")
    @NotBlank(message = "问题描述不可为空")
    private String description;

    @ApiModelProperty(value = "问题照片")
    private List<FileParam> pictures;

    @ApiModelProperty(value = "涉及人员")
    @Valid
    private List<UserPram> involvedPersonList;

    @ApiModelProperty(value = "奖励积分")
    private Integer score;

    @Data
    @ApiModel(value = "UserPram",description = "用户信息数据请求实体")
    @AllArgsConstructor
    @NoArgsConstructor
    @Valid
    public static class UserPram{

        @NotNull(message = "userId不可为空")
        private Long userId;

        @NotNull(message = "userName不可为空")
        private String userName;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "FileParam",description = "文件数据请求实体")
    public static class FileParam{
        private String uid;
        private String name;
        private String url;
        private String md5;
        private String fileName;
        private String code;
    }
}
