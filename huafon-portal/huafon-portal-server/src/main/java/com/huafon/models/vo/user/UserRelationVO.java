package com.huafon.models.vo.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 职员联系人VO
 * @Date: 2022/3/21 13:00
 * @Author: zyf
 **/
@Data
@ApiModel(value = "UserRelationVO", description = "用户联系人VO")
public class UserRelationVO {

    /**
     * 关系人主键id
     */
    @ApiModelProperty(value = "关系人主键id")
    private Long id;

    /**
     * 职员id
     */
    @ApiModelProperty(value = "关系人主键id")
    private Long userId;

    /**
     * 联系人姓名
     */
    @ApiModelProperty(value = "联系人姓名")
    private String linkman;

    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话")
    private String linkmanMobile;

    /**
     * 关系
     */
    @ApiModelProperty(value = "关系")
    private String relation;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
