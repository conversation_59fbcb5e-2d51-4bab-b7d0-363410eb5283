package com.huafon.models.dto;

import com.huafon.portal.api.dto.dept.DeptDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;
import java.util.List;
import java.util.Set;


/**
 * ${comments}
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-02-09
 */
@Data
@ApiModel(value="UserReceive对象", description="UserReceive对象")
public class RemindConfirmEventDTO {

	@ApiModelProperty(value = "用户id")
	private Integer userId;

	@ApiModelProperty(value = "用户名称")
	private String userName;

	@ApiModelProperty(value = "用户账号")
	private String userAccount;

	@ApiModelProperty(value = "头像地址")
	private String avatarUrl;

	@ApiModelProperty(value = "工号")
	private String workNum;

	@ApiModelProperty(value = "身份证号")
	private String idNumber;

	@ApiModelProperty(value = "性别")
	private Integer gender;

	@ApiModelProperty(value = "电子邮件")
	private String email;

	@ApiModelProperty(value = "移动号码")
	private String mobile;

	@ApiModelProperty(value = "提醒类型（1：入职提醒，2：转岗提醒，3：离职提醒）")
	private Integer dataType;

	@ApiModelProperty("租户id")
	private Integer tenantId;

	@ApiModelProperty(value = "三级培训级别 DEPARTMENT:部门级 TEAM:班组级")
	private String threeLevelSubject;

	@ApiModelProperty(value = "是否接害岗位（1：是，0：否）")
	private Integer isHarmPost;

	@ApiModelProperty(value = "就职岗位")
	private String entryPost;

	@ApiModelProperty(value = "离岗数据")
	private String leavePost;

	@ApiModelProperty(value = "操作用户id")
	private Integer operatorUserId;

	@ApiModelProperty(value = "上级领导id")
	private Integer leadUserId;

	private Date operateTime;

	@ApiModelProperty("部门岗位")
	private List<DeptPostMqDTO> deptDtoList;

}