package com.huafon.models.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.huafon.service.support.CustomRow;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @Description: 组织成员导入
 * @Date: 2023/6/5 9:23
 * @Author: zyf
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class PostUploadDTO extends CustomRow {

    @ExcelProperty(index = 0, value = "岗位名称*")
    @NotBlank(message = "岗位名称不能为空")
    @Length(max = 15, message = "岗位名称最长15个字符")
    @ColumnWidth(20)
    private String postName;

    @ExcelProperty(index = 1, value = "岗位编码*")
    @NotBlank(message = "岗位编码不能为空")
    @Length(max = 50, message = "岗位编码最长50个字符")
    @ColumnWidth(20)
    private String postCode;

    @ExcelProperty(index = 2, value = "所属部门")
    @Length(max = 100, message = "所属部门最长100个字符")
    @ColumnWidth(30)
    private String deptStructureName;

    @ExcelProperty(index = 3, value = "岗位必要证书")
    @Length(max = 100, message = "岗位必要证书最长100个字符")
    @ColumnWidth(20)
    private String licenceTypeName;

    @ExcelProperty(index = 4, value = "备注")
    @Length(max = 1000, message = "备注最长1000个字符")
    @ColumnWidth(30)
    private String postDesc;

    @ExcelProperty(index = 5, value = "关联账户（不同用户名以“、”分割）")
    @ColumnWidth(30)
    private String userNames;

    /**
     * 证书类型id集合
     */
    private List<Integer> licenceTypeIds;

    /**
     * 部门id
     */
    private Integer departmentId;

    /**
     * 用户id集合
     */
    private List<Integer> userIds;

}
