package com.huafon.models.vo.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 用户卡片返回VO
 */
@Data
@ApiModel(value = "UserCardCollectRespVO", description = "用户卡片返回VO")
public class UserCardCollectRespVO {

    @ApiModelProperty(value = "是否默认")
    private Boolean isDefault;

    @ApiModelProperty(value = "列表")
    private List<String> types;

}
