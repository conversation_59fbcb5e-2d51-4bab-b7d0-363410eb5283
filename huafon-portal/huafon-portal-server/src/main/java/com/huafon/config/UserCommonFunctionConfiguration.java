package com.huafon.config;

import com.huafon.models.dto.CommonFunctionDTO;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 用户-我的工作-常用功能配置
 *
 * <AUTHOR>
 * @since 2023-06-13 16:11
 **/
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix="user.common")
public class UserCommonFunctionConfiguration {

	private List<CommonFunctionDTO> function;
}
