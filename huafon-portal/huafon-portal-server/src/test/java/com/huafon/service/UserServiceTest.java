package com.huafon.service;

import com.alibaba.fastjson.JSON;
import com.huafon.PortalApplication;
import com.huafon.common.config.TenantContext;
import com.huafon.dao.mapper.UserMapper;
import com.huafon.models.query.UserDTOQuery;
import com.huafon.models.reqo.user.ReqTenant;
import com.huafon.models.reqo.user.ReqUser;
import com.huafon.models.reqo.user.ReqUserAdd;
import com.huafon.models.reqo.user.ReqUserEdit;
import com.huafon.models.vo.permission.PermissionListVo;
import com.huafon.portal.api.dto.UserDto;
import com.huafon.portal.api.dto.query.UserQuery;
import com.huafon.portal.api.enums.UserType;
import com.huafon.portal.api.service.UserRpcV2Service;
import com.huafon.support.config.UserContext;
import com.huafon.support.core.pojo.R;
import com.huafon.support.dto.UserInfoDto;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-11-07 15:20
 **/
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = PortalApplication.class)
public class UserServiceTest {

	@Autowired
	private UserService userService;
	@Autowired
	private ApplicationContext applicationContext;
	@Autowired
	private UserRpcV2Service userRpcV2Service;
	@Autowired
	private LoginService loginService;
	@Autowired
	private UserMapper userMapper;
	@Autowired
	private CompanyStaffStatisticsService staffStatisticsService;


	@Test
	public void testCreateAutowired() {
		UserRpcV2Service userRpcV2Service = applicationContext.getBean(UserRpcV2Service.class);
		UserDto userDto = new UserDto();
		userDto.setName("瑞讯-承包商");
		userDto.setMobile("");
		userDto.setUsername("202407090000003");
		userDto.setIdNumber("");
		userDto.setAvatar("");
		userDto.setUserType(UserType.CONTRACTOR);
		userDto.setDefaultPassword("CBS@123456");
		userDto.setTenantId(1);
		Integer userId = userRpcV2Service.createUserAutowire(userDto);
		System.out.println(userId);
	}

	@Test
	public void testTenantUser() {
		UserDTOQuery query = UserDTOQuery.builder().tenantId(1).build();
		List<UserDto> userInfos = userMapper.queryUserDTO(query);
		System.out.println(userInfos.size());
	}

	@Test
	public void testQueryTenantUser() {
		UserRpcV2Service userRpcV2Service = applicationContext.getBean(UserRpcV2Service.class);
		List<UserDto> userDtos = userRpcV2Service.queryUserInfoByTenant(1);
		System.out.println("size: " + userDtos.size());

	}

	@Test
	public void testStaffStatistic() {
		staffStatisticsService.generateYearMonthCompanyStaffStatistic();
	}

	@Test
	public void testUserCount() {
		List<UserDto> userList = userMapper.queryUserDTO(UserDTOQuery.builder().tenantId(1).build());
		System.out.println(userList.size());
	}

	@Test
	public void test(){
//		List<String> levelList = Arrays.asList("LEVELUP2","LEVELUP1","SELF","LEVELUP3","LEVELUP4","LEVELUP5");
//		List<String> levelList = Arrays.asList("LEVELUP1");
//		Set<UserDto> byLevels1 = userRpcV2Service.getByLevels(59, 1, levelList);
//		System.out.println(byLevels1);
//		Map<Integer, List<Integer>> integerListMap = userRpcV2Service.queryGroupByDepartmentIds(Arrays.asList(10, 217));
//		System.out.println(integerListMap);
//		ReqUserListWithRole reqUserListWithRole = new ReqUserListWithRole();
//		reqUserListWithRole.setRoleId(462);
//		reqUserListWithRole.setPageNo(1);
//		reqUserListWithRole.setPageSize(10);
//		R listWithRole = userService.getListWithRole(reqUserListWithRole);
//		System.out.println(JSONObject.toJSONString(listWithRole.getData()));
	}

//	@BeforeAll
	public static void beforeAll() {
		UserInfoDto user = new UserInfoDto();
		user.setUserId(47L);
		user.setUsername("wjz");
		user.setName("魏家政");
		UserContext.bind(user);

		TenantContext.bind(Collections.singletonList(1));
	}

	@Test
	public void testAdd() {
		ReqUserAdd create = new ReqUserAdd();
		create.setUsername("20221310");
		create.setPassword("123456");
		create.setName("邵学碰1");
		create.setGender(1);
		create.setRoleIds(Collections.singletonList(71));
		create.setMobile("19987654321");
		create.setDepartmentId(120L);
		ReqTenant tenant1 = new ReqTenant();
		tenant1.setTenantId(1);
		tenant1.setName("聚酰胺事业部");
		create.setTenantList(Collections.singletonList(tenant1));

		userService.add(create);
	}

	@Test
	public void testGet() {
		ReqUser reqUser = new ReqUser();
		reqUser.setUserId(2783);
		R row = userService.getRow(reqUser);
		System.out.println(row.getData());
	}

	@Test
	public void testEdit() {
		ReqUserEdit edit = new ReqUserEdit();
		edit.setUsername("20221310");
//		edit.setPassword("123456");
		edit.setName("邵学碰1");
		edit.setGender(1);
		edit.setRoleIds(Collections.singletonList(71));
		edit.setMobile("19987654321");
		edit.setDepartmentId(120L);
		ReqTenant tenant1 = new ReqTenant();
		tenant1.setTenantId(1);
		tenant1.setName("聚酰胺事业部");
		edit.setTenantList(Collections.singletonList(tenant1));
		edit.setUserId(2783);
		userService.edit(edit);
	}

	@Test
	public void testRpcUserService() {
		UserRpcV2Service userRpcV2Service = applicationContext.getBean(UserRpcV2Service.class);
		UserDto userId = userRpcV2Service.getById(2755, 1);
		System.out.println("userId:" + JSON.toJSONString(userId));
		System.out.println(userId.getDepartmentIds());
		System.out.println(userId.getPostIds());
		System.out.println(userId.getDepartPostInfo());
		UserDto username = userRpcV2Service.getByUsername("lyf", 1);
		System.out.println("username:" + JSON.toJSONString(username));
		List<Integer> usernameLike = userRpcV2Service.fuzzyQueryByName("lyf", 1);
		System.out.println("usernameLike:" + JSON.toJSONString(usernameLike));
		List<UserDto> departmentId = userRpcV2Service.getByDepartmentId(55, true, 1);
		System.out.println("departmentId:" + JSON.toJSONString(departmentId));
		List<UserDto> postId = userRpcV2Service.getByPostId(353, 1);
		System.out.println("postId:" + JSON.toJSONString(postId));
		List<Integer> deptPostUserIds = userRpcV2Service.queryByDeptAndPostAndNameLike(UserQuery.builder().nameLike("吕").postId(Collections.singletonList(353)).departmentId(359).includeSubDept(true).tenantId(1).build());
		System.out.println("deptPostUserIds:" + deptPostUserIds);
		UserDto getById = userRpcV2Service.getById(2755);
		System.out.println("getById:" + getById);

		System.out.println();
		System.out.println("=============================================");
		UserDto username2 = userRpcV2Service.getByUsername("lyf", 19);
		System.out.println("userName:" + JSON.toJSONString(username2));
		Map<String, UserDto> userNamMapping= userRpcV2Service.queryMappingByUsernames(Collections.singletonList("lyf"), 19);
		System.out.println("userName:" + JSON.toJSONString(userNamMapping));
		UserDto userById = userRpcV2Service.getById(2755, 19);
		System.out.println("userById:" + JSON.toJSONString(userById));
		List<UserDto> byUserIds = userRpcV2Service.getByIds(Collections.singletonList(2755), 19);
		System.out.println("userName:" + JSON.toJSONString(byUserIds));
	}

	@Test
	public void testV2Permission() {
		UserInfoDto userInfo = new UserInfoDto();
		userInfo.setUserId(2790L);
		userInfo.setIsAdmin(0);
		UserContext.bind(userInfo);
		TenantContext.bind(Collections.singletonList(1));
		List<PermissionListVo> permissionV2 = loginService.getPermissionV2(userInfo, 1,1);
		System.out.println(JSON.toJSONString(permissionV2));
		Map<String, List<PermissionListVo>> collect = permissionV2.stream().collect(Collectors.groupingBy(PermissionListVo::getPermissionName));
		for (Map.Entry<String, List<PermissionListVo>> entry : collect.entrySet()) {
			System.out.println("KEY: " + entry.getKey() + " : " + entry.getValue().size());
		}
	}

}
