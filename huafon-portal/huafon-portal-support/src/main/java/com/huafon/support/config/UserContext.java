package com.huafon.support.config;

import com.huafon.support.dto.UserInfoDto;
import com.huafon.support.exceptions.ServiceException;

import java.util.Optional;

public class UserContext {
    private static final ThreadLocal<UserInfoDto> userContext = new ThreadLocal();

    private UserContext() {
    }

    public static void bind(UserInfoDto user) {
        userContext.set(user);
    }

    public static UserInfoDto get() {
        return Optional.ofNullable(userContext.get()).orElse(null);
    }

    public static Long getId() {
        return Optional.ofNullable(userContext.get()).map(UserInfoDto::getUserId).orElse(0L);
    }

    public static UserInfoDto getOrElseThrow() {
        return Optional.ofNullable(userContext.get()).orElseThrow(() -> new ServiceException("Header：UserInfoDto缺失。"));
    }

    public static void unbind() {
        userContext.remove();
    }
}