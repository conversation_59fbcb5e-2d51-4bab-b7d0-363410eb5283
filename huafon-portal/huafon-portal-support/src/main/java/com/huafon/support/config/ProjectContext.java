package com.huafon.support.config;

import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023-02-24 14:13
 **/
public class ProjectContext {

	public static final Integer _NO_PROJECT_VALUE = -1;

	private static final ThreadLocal<Integer> projectContext = new ThreadLocal<>();

	private ProjectContext() {
	}

	public static void bind(Integer projectId) {
		projectContext.set(projectId);
	}

	public static Integer get() {
		return Optional.ofNullable(projectContext.get()).orElse(_NO_PROJECT_VALUE);
	}

	public static void unbind() {
		projectContext.remove();
	}
}
