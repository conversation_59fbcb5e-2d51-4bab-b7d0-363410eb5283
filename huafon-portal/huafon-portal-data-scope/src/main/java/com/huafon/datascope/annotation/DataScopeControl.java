package com.huafon.datascope.annotation;

import java.lang.annotation.*;

@Documented
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface DataScopeControl {

	/**
	 * 强制重定向(hf_permission.web_url)
	 * @return
	 */
	String redirect() default "";

	/**
	 * 自定义字段进行替换
	 */
	CustomMapping[] customMapping() default {};

	/**
	 * 忽略(排除)指定的属性
	 */
	String[] excludeProperty() default {};

	/**
	 * 仅应用的属性
	 */
	String[] includeProperty() default {};

	/**
	 * 额外的过滤条件
	 * (数据权限) AND/OR (a = 1)
	 */
	AdditionalExpression[] dataScopeExtraExpressions() default {};

	/**
	 * 数据权限追加的过滤条件:( 数据权限 AND/OR (a = 1) )
	 */
	AdditionalExpression[] dataScopeAdditionalExpressions() default {};

}
