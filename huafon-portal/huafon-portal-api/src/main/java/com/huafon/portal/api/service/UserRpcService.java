package com.huafon.portal.api.service;

import com.huafon.portal.api.dto.*;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface UserRpcService {
    /**
     * 添加用户
     *
     * @return
     */
    boolean rpcAdd(UserAddDto userAddDto);

    /**
     * 岗位-角色中间表
     *
     * @param postRoleAddDto
     * @return
     */
    boolean rpcAddPostRole(PostRoleAddListDto postRoleAddDto);

    /**
     * 删除用户
     *
     * @return
     */
    boolean rpcDel(UserDelDto userDelDto);

    /**
     * 根据userIds查询用户
     *
     * @param userIds
     * @return
     */
    Map<String, UserDto> getUserMap(List<Integer> userIds);

    Map<String, UserDto> getUserMapAndNoDel(List<Integer> userIds);

    /**
     * 根据username集合查询用户
     *
     * @return
     */
    Map<Integer, UserDto> getUserMapByUsernames(List<String> usernames);

    /**
     * 根据username集合查询用户id
     *
     * @return
     */
    Map<String, Integer> getUserIdMapByUsernames(List<String> usernames);

    UserDto getById(Integer userId);

    /**
     * 根据用户名（工号）获取userId
     *
     * @param username
     * @return
     */
    Integer getUserIdByUsername(String username);


    UserDto getUserByUsername(String username);


    List<UserDto> getUserByRoleId(Long roleId);

    Map<Integer, List<UserRoleDto>> getUserByRoleIds(List<Integer> roleIds,Integer tenantId);
}