package com.huafon.portal.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class PostRoleAddListDto implements Serializable {

    private static final long serialVersionUID = 2876992323460507393L;

    private List<PostRoleAddDto> postRoleAddList;

    @Data
    public static class PostRoleAddDto implements Serializable{

        private static final long serialVersionUID = -5686914780642085187L;

        private Long postId;

        private List<Integer> roleIds = new ArrayList<>();
    }
}
