package com.huafon.portal.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/6 15:55
 */
@Data
public class ApprovalDepartmentDto implements Serializable {

    private static final long serialVersionUID = 8572200559624551360L;
    /**
     * Column: department_id
     * Type: int8
     * Remark: 部门id
     */
    private Integer departmentId;

    /**
     * Column: principal
     * Type: text(2147483647)
     * Remark: 部门负责人
     */
    private List<UserMobileDto> principalUserList;

    /**
     * Column: hse_principal
     * Type: text(2147483647)
     * Remark: hse负责人
     */
    private List<UserMobileDto> hsePrincipalUserList;

    /**
     * Column: finance_principal
     * Type: text(2147483647)
     * Remark: 财务负责人
     */
    private List<UserMobileDto> financePrincipalUserList;

    /**
     * Column: leader
     * Type: text(2147483647)
     * Remark: 分管领导
     */
    private List<UserMobileDto> leaderUserList;

    /**
     * 装置/工段负责人
     */
    private List<UserMobileDto> deviceSectionPrincipalUserList;

    /**
     * 班组负责人
     */
    private List<UserMobileDto> teamPrincipalUserList;

    /**
     * 车间负责人
     */
    private List<UserMobileDto> workshopPrincipalUserList;
}
