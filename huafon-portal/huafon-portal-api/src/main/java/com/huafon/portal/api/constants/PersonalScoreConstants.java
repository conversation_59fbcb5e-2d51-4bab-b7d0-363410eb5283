package com.huafon.portal.api.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName com.huafon.portal.api.constants.PersonalScoreEnums
 * @Description
 * @createTime 2023年10月24日 09:58:00
 */
public interface PersonalScoreConstants {

    /**
     * 个人积分业务前缀
     */
    String PERSONAL_SCORE_BUSINESS_PREFIX = "personal_score_business_prefix_";

    /**
     * 积分规则编码前缀
     */
    String RULE_CODE_PREFIX = "SYJF-";

    /**
     * 积分申请编码浅醉
     */
    String APPLICATION_CODE_PREFIX = "JFSQ-";

    /**
     * 积分统计：总积分
     */
    String TOTAL_SCORE = "totalScore";
    /**
     * 积分统计：本年积分
     */
    String ANNUAL_SCORE = "annualScore";
    /**
     * 积分统计：本月积分
     */
    String MONTHLY_SCORE = "monthlyScore";

    /**
     * 积分申请工作流id
     */
    String APPLICATION_WORKFLOW_KEY = PERSONAL_SCORE_BUSINESS_PREFIX + "score_application";
    /**
     * 积分申请工作流业务描述
     */
    String APPLICATION_WORKFLOW_BUSINESS_DESC = "积分申请";

    String SCORE_RANK_EXPORT_FILE_NAME = "素养积分排名";

    String SCORE_RANK_ERROR_INFO_FILE_NAME = "素养积分排名错误信息";

    String SCORE_LIST_EXPORT_FILE_NAME = "积分记录";

    /**
     * 系统设置导入积分模块定义
     */
    Integer SYSTEM_IMPORT_MODULE = 0;
    /**
     * Excel导入积分事件
     */
    Integer EXCEL_IMPORT_EVENT = 1;
    /**
     * 新员工入职初始积分导入事件
     */
    Integer EMPLOYEE_SCORE_INITIALIZE_EVENT = 2;

    /**
     * 员工初始积分调整事件
     */
    Integer EMPLOYEE_SCORE_ADJUST_EVENT = 3;

    /**
     * 积分商城兑换
     */
    Integer SCORE_SHOP_EXCHANGE = 4;

    /**
     * 积分方式枚举
     */
    enum IntegrationModeEnum {
        MANUAL(0, "手动"),
        AUTO(1, "自动"),
        OTHERS(1000, "其他"),
        ;
        final Integer code;
        final String desc;

        IntegrationModeEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static IntegrationModeEnum parseByCode(Integer code) {
            for (IntegrationModeEnum value : values()) {
                if (Objects.equals(value.getCode(), code)) {
                    return value;
                }
            }
            return OTHERS;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 积分类型枚举
     */
    enum IntegrationTypeEnum {
        INCREASE(1, "加分"),
        DECREASE(-1, "减分");
        final Integer code;
        final String desc;

        IntegrationTypeEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static IntegrationTypeEnum parseByCode(Integer code) {
            for (IntegrationTypeEnum value : values()) {
                if (Objects.equals(code, value.getCode())) {
                    return value;
                }
            }
            return INCREASE;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 规则启用状态
     */
    enum RuleStatusEnum {
        ENABLE(1, "启用"),
        DISABLE(0, "停用");
        final Integer code;
        final String desc;

        RuleStatusEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 积分排名角色
     */
    enum RankRoleEnum {
        USER(0, "员工"),
        HES_MANAGER(1, "HSE管理员");

        RankRoleEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        final Integer code;
        final String desc;

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 流程选项枚举
     */
    enum ApplicationProcessEnum {
        PROCESSING(1, "流程中"),
        COMPLETED(999, "已完成"),
        OTHERS(1000, "其他"),
        CANCELED(-1, "已取消"),
        ;

        ApplicationProcessEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        final Integer code;
        final String desc;

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static ApplicationProcessEnum parseByCode(Integer code) {
            for (ApplicationProcessEnum value : values()) {
                if (Objects.equals(value.getCode(), code)) {
                    return value;
                }
            }
            return OTHERS;
        }
    }

    @AllArgsConstructor
    @Getter
    enum ModuleEnum {
        RISK(1, "隐患模块"),
        LAWS(2, "法律法规"),
        SYSTEM_LIST(3, "制度清单"),
        EMERGENCY_MANAGEMENT(4, "应急管理"),
        EDUCATION_AND_TRAINING(5, "教育培训"),
        TEAM_SAFETY_ACTIVITIES(6, "班组安全活动"),
        SAFETY_AUDIT(7, "安全稽核"),
        ACCIDENT_MANAGEMENT(8, "事故管理"),
        PATROL(9, "巡检"),
        /**
         * 用于防空
         */
        OTHERS(9999, "其他");
        private Integer moduleCode;
        private String moduleDesc;

        public static ModuleEnum parseByCode(Integer moduleCode) {
            for (ModuleEnum value : values()) {
                if (Objects.equals(value.getModuleCode(), moduleCode)) {
                    return value;
                }
            }
            return OTHERS;
        }

        /**
         * 获取所有模块
         * @return
         */
        public static Set<ModuleEnum> getAllModule() {
            return Arrays.stream(values()).filter(module -> !Objects.equals(OTHERS, module)).collect(Collectors.toSet());
        }
    }

    /**
     * 积分规则模块&事件配置美剧
     */
    enum ModuleEventEnum {
        RISK_1ST(ModuleEnum.RISK.getModuleCode(), ModuleEnum.RISK.getModuleDesc(), 3, "完成自查任务"),
        RISK_2ND(ModuleEnum.RISK.getModuleCode(), ModuleEnum.RISK.getModuleDesc(), 2, "完成督查任务"),
        RISK_3RD(ModuleEnum.RISK.getModuleCode(), ModuleEnum.RISK.getModuleDesc(), 1, "隐患整改完成"),

        LAWS_1ST(ModuleEnum.LAWS.getModuleCode(), ModuleEnum.LAWS.getModuleDesc(), 1, "完成法规辨识"),
        LAWS_2ND(ModuleEnum.LAWS.getModuleCode(), ModuleEnum.LAWS.getModuleDesc(), 2, "完成周期性评价"),

        SYSTEM_LIST_1ST(ModuleEnum.SYSTEM_LIST.getModuleCode(), ModuleEnum.SYSTEM_LIST.getModuleDesc(), 1, "完成适用性评审"),

        EMERGENCY_MANAGEMENT_1ST(ModuleEnum.EMERGENCY_MANAGEMENT.getModuleCode(), ModuleEnum.EMERGENCY_MANAGEMENT.getModuleDesc(), 1, "完成班组级演练活动"),
        EMERGENCY_MANAGEMENT_2ND(ModuleEnum.EMERGENCY_MANAGEMENT.getModuleCode(), ModuleEnum.EMERGENCY_MANAGEMENT.getModuleDesc(), 2, "完成部门级演练活动"),
        EMERGENCY_MANAGEMENT_3RD(ModuleEnum.EMERGENCY_MANAGEMENT.getModuleCode(), ModuleEnum.EMERGENCY_MANAGEMENT.getModuleDesc(), 3, "完成公司级演练活动"),

        EDUCATION_AND_TRAINING_1ST(ModuleEnum.EDUCATION_AND_TRAINING.getModuleCode(), ModuleEnum.EDUCATION_AND_TRAINING.getModuleDesc(), 1, "新增课程"),
        EDUCATION_AND_TRAINING_2ND(ModuleEnum.EDUCATION_AND_TRAINING.getModuleCode(), ModuleEnum.EDUCATION_AND_TRAINING.getModuleDesc(), 2, "新增讲师"),
        EDUCATION_AND_TRAINING_3RD(ModuleEnum.EDUCATION_AND_TRAINING.getModuleCode(), ModuleEnum.EDUCATION_AND_TRAINING.getModuleDesc(), 3, "完成培训活动"),
        EDUCATION_AND_TRAINING_4TH(ModuleEnum.EDUCATION_AND_TRAINING.getModuleCode(), ModuleEnum.EDUCATION_AND_TRAINING.getModuleDesc(), 4, "讲师完成培训活动并获评3分及以上"),
        EDUCATION_AND_TRAINING_5TH(ModuleEnum.EDUCATION_AND_TRAINING.getModuleCode(), ModuleEnum.EDUCATION_AND_TRAINING.getModuleDesc(), 5, "讲师完成培训活动并获评5分"),
        EDUCATION_AND_TRAINING_6TH(ModuleEnum.EDUCATION_AND_TRAINING.getModuleCode(), ModuleEnum.EDUCATION_AND_TRAINING.getModuleDesc(), 6, "学员完成培训活动并考试及格"),
        EDUCATION_AND_TRAINING_7TH(ModuleEnum.EDUCATION_AND_TRAINING.getModuleCode(), ModuleEnum.EDUCATION_AND_TRAINING.getModuleDesc(), 7, "学员完成培训活动并考试满分"),
        EDUCATION_AND_TRAINING_8TH(ModuleEnum.EDUCATION_AND_TRAINING.getModuleCode(), ModuleEnum.EDUCATION_AND_TRAINING.getModuleDesc(), 8, "完成选修课程"),
        EDUCATION_AND_TRAINING_9TH(ModuleEnum.EDUCATION_AND_TRAINING.getModuleCode(), ModuleEnum.EDUCATION_AND_TRAINING.getModuleDesc(), 9, "完成岗位能力矩阵的必修课程"),

        TEAM_SAFETY_ACTIVITIES_1ST(ModuleEnum.TEAM_SAFETY_ACTIVITIES.getModuleCode(), ModuleEnum.TEAM_SAFETY_ACTIVITIES.getModuleDesc(), 1, "完成安全活动"),

        SAFETY_AUDIT_1ST(ModuleEnum.SAFETY_AUDIT.getModuleCode(), ModuleEnum.SAFETY_AUDIT.getModuleDesc(), 1, "完成稽核任务"),

        ACCIDENT_MANAGEMENT_1ST(ModuleEnum.ACCIDENT_MANAGEMENT.getModuleCode(), ModuleEnum.ACCIDENT_MANAGEMENT.getModuleDesc(), 1, "完成事故报告"),
        PATROL_1ST(ModuleEnum.PATROL.getModuleCode(), ModuleEnum.PATROL.getModuleDesc(), 1, "完成巡检任务"),
        ;

        private final Integer moduleCode;
        private final String moduleDesc;
        private final Integer eventCode;
        private final String eventDesc;

        public Integer getModuleCode() {
            return moduleCode;
        }

        public String getModuleDesc() {
            return moduleDesc;
        }

        public Integer getEventCode() {
            return eventCode;
        }

        public String getEventDesc() {
            return eventDesc;
        }

        public static ModuleEventEnum parseByCode(Integer moduleCode, Integer eventCode) {
            for (ModuleEventEnum value : values()) {
                if (Objects.equals(value.getModuleCode(), moduleCode) && Objects.equals(value.getEventCode(), eventCode)) {
                    return value;
                }
            }
            return null;
        }

        ModuleEventEnum(Integer moduleCode, String moduleDesc, Integer eventCode, String eventDesc) {
            this.moduleCode = moduleCode;
            this.moduleDesc = moduleDesc;
            this.eventCode = eventCode;
            this.eventDesc = eventDesc;
        }
    }

    enum TargetUserTypeEnum {
        EXECUTOR(1, "执行人"),
        DISCOVERER(2, "发现人"),
        INFORMANT(3, "上报人"),
        CORRECTOR(4, "整改人"),
        IDENTIFICATION_EVALUATOR(5, "辨识评价人"),
        INITIATOR(6, "发起人"),
        EVALUATOR(7, "评价人"),
        COMMENTER(8, "参评人"),
        PARTICIPATING_PERSONNEL(9, "参演人员"),
        DRILL_LEADER(10, "演练负责人"),
        CREATOR(11, "创建人员"),
        LECTURER(12, "讲师"),
        TRAINEE(13, "学员"),
        ACTIVITY_PARTICIPANT(14, "活动人员"),
        RECORDER(15, "记录人"),
        PARTICIPATING_MANAGERS(16, "参加管理人员"),
        ACCIDENT_PRINCIPAL(17, "事故责任人"),
        RECTIFICATION_PRINCIPAL(18, "整改负责人");
        private final Integer type;
        private final String desc;

        public Integer getType() {
            return type;
        }

        public String getDesc() {
            return desc;
        }

        TargetUserTypeEnum(Integer type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public static TargetUserTypeEnum parseByType(Integer type) {
            for (TargetUserTypeEnum value : values()) {
                if (Objects.equals(value.getType(), type)) {
                    return value;
                }
            }
            return null;
        }
    }
}
