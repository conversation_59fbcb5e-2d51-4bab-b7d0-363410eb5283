package com.huafon.portal.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 租户同步（新增、删除）mqDto
 * @Date: 2022/5/30 10:06
 * @Author: zyf
 **/
@Data
public class TenantSyncMqDto implements Serializable {
    private static final long serialVersionUID = -6442066761838413537L;

    /**
     * 0：新增，1：删除: 2：更新
     */
    private Integer operate;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 租户列表
     */
    private List<TenantDto> tenantDtoList;

}
