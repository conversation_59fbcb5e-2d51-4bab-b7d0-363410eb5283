package com.huafon.portal.api.service;

import com.huafon.portal.api.dto.dept.DepartmentTreeDTO;
import com.huafon.portal.api.dto.dept.DeptDto;

import java.util.List;
import java.util.Map;

public interface DeptRpcService {

    /**
     * 详情
     * @param id
     * @param tenantId
     * @return
     */
    DeptDto getById(Integer id, Integer tenantId);

    /**
     * 根据组织机构id获取组织机构列表
     *
     * @param idList 组织机构id集合
     * @return
     */
    List<DeptDto> getByIds(List<Integer> idList, Integer tenantId);

    /**
     * 获取该组织机构下的所有子节点的id
     *
     * @param id 组织机构id
     * @return
     */
    List<Integer> getChildNodeIdsById(Integer id);

    /**
     * 获取租户下的所有组织机构
     *
     * @param tenantId
     * @return
     */
    List<DeptDto> getByTenantId(Integer tenantId,Boolean includeTop);

    /**
     * 根据当前节点id获取下一节点的id，包含本级
     *
     * @param id
     * @return
     */
    List<DeptDto> getNextNodeId(Integer id);

    /**
     * 获取租户下的二级节点
     *
     * @return
     */
    List<DeptDto> getSecondNodes(Integer tenantId);

    /**
     * 根据id获取结构名称（联级别名称）
     *
     * @param ids
     * @return
     */
    Map<Integer,String> getStructureNameById(List<Integer> ids);

    /**
     * 获取结构名称
     *
     * @param tenantId
     * @return
     */
    Map<String,Integer> getAllStructureName(Integer tenantId);

    /**
     * 根据id获取当前节点下的所有id（包含本节点）
     *
     * @param ids
     * @return
     */
    Map<Integer,List<Integer>> getChildIds(List<Integer> ids);

    /**
     * 根据租户id和部门名称搜索
     *
     * @param tenantId
     * @param deptName
     * @return
     */
    List<DeptDto> getByName(Integer tenantId,String deptName);


    DeptDto findByThirdCode(String departmentCode);

    /**
     * 根据租户id和部门名称模糊搜索
     *
     * @param tenantId
     * @param deptName
     * @return
     */
    List<DeptDto> queryFuzzyByName(Integer tenantId,String deptName);

    /**
     * 获取部门相关的属性结构
     *
     * @param tenantId
     * @param deptIdList
     * @return
     */
    List<DepartmentTreeDTO> getCustomDepartmentTree(Integer tenantId, List<Integer> deptIdList);

    /**
     * 根据ids查询
     * @param ids
     * @return
     */
    List<Integer> findIdsByParentIds(List<Integer> ids);

    /**
     * 根据租户id和部门名称批量搜索
     *
     * @param tenantId
     * @param nameList
     * @return
     */
    List<DeptDto> getByNameList(Integer tenantId,List<String> nameList);

    /**
     *
     * @param idList
     * @return
     */
    List<DeptDto> findThirdCodeByIds(List<Integer> idList);

    /**
     * 统计部门下的用户数量
     * @param tenantId
     * @param id
     * @return
     */
    List<DeptDto> countDeptWithUser(Integer tenantId,Integer id);

}