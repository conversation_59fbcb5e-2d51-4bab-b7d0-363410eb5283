package com.huafon.task;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.huafon.config.HaiLiPushDataTaskConfig;
import com.huafon.config.HaiLiRootCloudOpenApiConfig;
import com.huafon.moldes.entity.RootCloudRealTimeResponse;
import com.huafon.moldes.vo.req.RootCloudRealTimeRequest;
import com.huafon.moldes.vo.req.PointDataReq;
import com.huafon.service.HaiLiRootCloudApiService;
import com.huafon.utils.HttptUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 第三方海力测点数据同步
 *
 */
@Slf4j
@Component
public class HaiLiRootCloudRealTimePushDataTask {

    @Resource
    private HaiLiRootCloudApiService haiLiRootCloudApiService;

    @Resource
    private HaiLiPushDataTaskConfig pushDataTaskConfig;

    @Resource
    private HaiLiRootCloudOpenApiConfig haiLiRootCloudOpenApiConfig;

    @XxlJob("haiLiRootCloudRealTimeDataPush")
    public void haiLiRootCloudRealTimeDataPush() {
        log.info("第三方海力根云测点实时数据推送任务开始, taskConfig:{}", pushDataTaskConfig);
        StopWatch stopWatch = StopWatch.createStarted();
        List<RootCloudRealTimeResponse.PayLoad> payloadDataList = supplyData(pushDataTaskConfig.getModelId(),
                pushDataTaskConfig.getThingId());

        int payloadDataSize = Objects.isNull(payloadDataList) ? 0 : payloadDataList.size();
        log.info("第三方海力根云测点实时数据推送任务, timeCost:{}, supplyDataSize:{}", stopWatch.getTime(TimeUnit.MILLISECONDS),
                payloadDataSize);

        List<PointDataReq> pushDataList = convertData(payloadDataList);

        int pushDataListSize = Objects.isNull(pushDataList) ? 0 : pushDataList.size();
        log.info("第三方海力根云测点实时数据推送任务, timeCost:{}, pushDataListSize:{}",
                stopWatch.getTime(TimeUnit.MILLISECONDS), pushDataListSize);

        int successPushNum = reportData(pushDataList);
        log.info("第三方海力根云测点实时数据推送任务结束, timeCost:{}, payLoadNum:{}, pointItemNum:{}, successPushNum:{}",
                stopWatch.getTime(TimeUnit.MILLISECONDS), payloadDataSize, pushDataListSize, successPushNum);
    }

    /**
     * 提供数据
     *
     * @return
     */
    public List<RootCloudRealTimeResponse.PayLoad> supplyData(String modelId, String thingId) {
        try {
            RootCloudRealTimeRequest request = RootCloudRealTimeRequest.builder()
                    .modelId(modelId)
                    .thingId(thingId)
                    .build();
            RootCloudRealTimeResponse haiLiApiResponse = haiLiRootCloudApiService.getRealTimeData(request);
            if (Objects.nonNull(haiLiApiResponse)) {
                return haiLiApiResponse.getPayload();
            }
        } catch (Exception e) {
            log.error("supplyData exception, modelId:{}, thingId: {}", modelId, thingId, e);
        }

        return null;
    }

    /**
     * 转换数据
     *
     * @param payLoads
     * @return
     */
    public List<PointDataReq> convertData(List<RootCloudRealTimeResponse.PayLoad> payLoads) {
        if (CollectionUtils.isEmpty(payLoads)) {
            return Collections.emptyList();
        }

        List<PointDataReq> pointDataReqList = new ArrayList<>();
        for (RootCloudRealTimeResponse.PayLoad payLoad : payLoads) {
            Map<String, RootCloudRealTimeResponse.FieldData> data = payLoad.getData();
            if (CollectionUtils.isEmpty(data)) {
                continue;
            }

            for (Map.Entry<String, RootCloudRealTimeResponse.FieldData> entry : data.entrySet()) {
                String pointItemCode = entry.getKey();
                RootCloudRealTimeResponse.FieldData fieldData = entry.getValue();

                if (StringUtils.isEmpty(pointItemCode)) {
                    continue;
                }

                PointDataReq pointDataReq = new PointDataReq();
                pointDataReq.setPointCode(pointItemCode);
                if (Objects.nonNull(fieldData)) {
                    pointDataReq.setRealData(String.valueOf(fieldData.getValue()));
                } else {
                    pointDataReq.setRealData(null);
                }

                pointDataReqList.add(pointDataReq);
            }
        }

        return pointDataReqList;
    }

    /**
     * 同步数据
     *
     * @param pushDataReqList
     * @return
     */
    public int reportData(List<PointDataReq> pushDataReqList) {
        if (CollectionUtils.isEmpty(pushDataReqList)) {
            log.warn("reportData size zero");
            return 0;
        }

        try {
            //数据上报
            StopWatch stopWatch = StopWatch.createStarted();
            String url = pushDataTaskConfig.getReportHost() + pushDataTaskConfig.getReportUrl();
            String jsonBody = JSONObject.toJSONString(pushDataReqList);
            if (StringUtils.isNoneBlank(jsonBody)) {
                log.info("reportData start, url:{}", url);
                JSONObject jsonObject = HttptUtil.doHttpPostJsonV2(url, jsonBody);
                log.info("reportData end, timeCost:{}, url:{}, result:{}", stopWatch.getTime(TimeUnit.MILLISECONDS),
                        url, Objects.isNull(jsonObject) ? jsonObject : jsonObject.toJSONString());
            }
            // 成功数量
            return pushDataReqList.size();
        } catch (Exception e) {
            log.error("reportData exception, taskConfig: {}, pushDataReqList:{}",
                    pushDataTaskConfig, pushDataReqList, e);
        }

        return 0;
    }
}
