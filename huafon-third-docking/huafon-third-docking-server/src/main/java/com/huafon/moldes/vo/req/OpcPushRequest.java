package com.huafon.moldes.vo.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description: opc 上位机采集数据推送到智慧安环请求体
 * @date 2023-03-10 11:21:35
 */
public interface OpcPushRequest {

    @Data
    @ApiModel("opc推送设备数据")
    @NoArgsConstructor
    @AllArgsConstructor
    class OpcPushDeviceData {

        @ApiModelProperty(value = "设备编码IMEI")
        private String deviceCode;

        @ApiModelProperty(value = "最近接收时间")
        private Date receiveTime;

        @ApiModelProperty(value = "设备状态")
        private Integer deviceState;

        @ApiModelProperty(value = "设备状态描述")
        private String deviceStateDesc;

        @ApiModelProperty(value = "测点列表数据")
        private List<OpcPushDeviceItemData> deviceItemDataList;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Accessors(chain = true)
    @Data
    public static class OpcPushDeviceItemData {

        @ApiModelProperty(value = "通道")
        private String channel;

        @ApiModelProperty(value = "气体名称")
        private String gasName;

        @ApiModelProperty(value = "单位编码")
        private String unitCode;

        @ApiModelProperty(value = "单位")
        private String unit;

        @ApiModelProperty(value = "低报值")
        private String lowValue;

        @ApiModelProperty(value = "高报值")
        private String highValue;

        @ApiModelProperty(value = "声报值")
        private String voiceValue;

        @ApiModelProperty(value = "量程")
        private String range;

        @ApiModelProperty(value = "浓度")
        private String concentration;

        @ApiModelProperty(value = "浓度小数点格式")
        private String concentrationFormat;

        @ApiModelProperty(value = "通道状态")
        private Integer channelState;

        @ApiModelProperty(value = "通道状态描述")
        private String channelStateDesc;

    }
}