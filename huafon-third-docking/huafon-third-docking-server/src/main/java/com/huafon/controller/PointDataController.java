package com.huafon.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.huafon.moldes.enums.ChannelEnum;
import com.huafon.moldes.vo.req.BenAnData;
import com.huafon.moldes.vo.req.HikVisionData;
import com.huafon.moldes.vo.req.JldData;
import com.huafon.moldes.vo.req.YtyData;
import com.huafon.moldes.vo.response.JldDataVO;
import com.huafon.moldes.vo.response.JldResponse;
import com.huafon.service.ChannelPushService;
import com.huafon.service.ChannelPushServiceStrategyFactory;
import com.huafon.service.JldDataService;
import com.huafon.service.TcpDataReceiverService;
import com.huafon.support.core.pojo.R;
import com.huafon.utils.JsonUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * @Description 测点数据对接第三方(我方提供接口接收数据)
 * <AUTHOR>
 * @Date 2023/9/26 16:07
 * @Version 1.0
 */
@Api(tags = "测点数据对接第三方(我方提供接口接收数据)")
@RestController
@RequestMapping("/pointData")
@Slf4j
public class PointDataController {

    @Resource
    private TcpDataReceiverService tcpDataReceiverService;

    @Resource
    JldDataService jldDataService;

    @PostMapping("/ba")
    @ApiOperation(value = "本安科技测点数据推送")
    public R ba(@RequestBody @Valid BenAnData data) {
        log.info(JSON.toJSONString(data));
        ChannelPushService channelPushService = ChannelPushServiceStrategyFactory.getByChannel(ChannelEnum.BEN_AN.getCode());
        channelPushService.pushPointData(data);
        return R.ok();
    }

    @PostMapping("/yty")
    @ApiOperation(value = "深圳逸天云测点数据推送")
    public R ba(@RequestBody @Valid YtyData data) {
        log.info(JSON.toJSONString(data));
        ChannelPushService channelPushService = ChannelPushServiceStrategyFactory.getByChannel(ChannelEnum.DATA_CENTER.getCode());
        channelPushService.pushPointData(data);
        return R.ok();
    }

    @PostMapping("/hikVision")
    @ApiOperation(value = "海康测点数据推送")
    public R hikVision(@RequestBody @Valid HikVisionData data) {
        log.info("海康测点数据接收：{}",JSON.toJSONString(data));
        String analyzeJson = JsonUtil.analyzeJson(JSON.toJSONString(data));
        HikVisionData analyzeData = JSONObject.parseObject(analyzeJson,HikVisionData.class);
        ChannelPushService channelPushService = ChannelPushServiceStrategyFactory.getByChannel(ChannelEnum.HIK_VISION.getCode());
        channelPushService.pushPointData(analyzeData);
        return R.ok();
    }


    @GetMapping("/start")
    public String start() {
        tcpDataReceiverService.startServer();
        return "Server started!";
    }

    @PostMapping("/uploadSensorDataList")
    @ApiOperation(value = "成都吉联达测点数据推送")
    public void uploadSensorDataList(@RequestBody @Valid JldData data, HttpServletResponse httpServletResponse) throws IOException {
        JldResponse jldResponse = new JldResponse();
        try {
            ChannelPushService channelPushService = ChannelPushServiceStrategyFactory.getByChannel(ChannelEnum.JLD_DATA.getCode());
            channelPushService.pushPointData(data);
            jldResponse.setData("add ok!");
            jldResponse.setCode(200);
            jldResponse.setInfo("success");
        } catch (Exception e) {
            jldResponse.setData("add fail!");
            jldResponse.setCode(400);
            jldResponse.setInfo(e.getMessage());
        }
        String response = JSONObject.toJSONString(jldResponse);
        httpServletResponse.getWriter().write(response);
    }

    @GetMapping("/getJldDataList")
    @ApiOperation(value = "成都吉联达测点数据列表")
    public R<List<JldDataVO>> getJldDataList(@RequestParam String wholeId) {
        return R.ok(jldDataService.getJldDataList(wholeId));
    }

}
