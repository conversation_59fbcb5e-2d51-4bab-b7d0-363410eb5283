package com.huafon.service.ipml;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.huafon.config.HaiLiRootCloudOpenApiConfig;
import com.huafon.constants.HaiLiApiConstants;
import com.huafon.moldes.entity.RootCloudAuthLoginResponse;
import com.huafon.moldes.entity.RootCloudRealTimeResponse;
import com.huafon.moldes.vo.req.RootCloudAuthLoginRequest;
import com.huafon.moldes.vo.req.RootCloudRealTimeRequest;
import com.huafon.service.HaiLiRootCloudApiService;
import com.huafon.utils.HttptUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class HaiLiRootCloudApiServiceImpl implements HaiLiRootCloudApiService {

    @Resource
    private HaiLiRootCloudOpenApiConfig haiLiRootCloudOpenApiConfig;

    @Override
    public RootCloudRealTimeResponse getRealTimeData(RootCloudRealTimeRequest request) {
        RootCloudAuthLoginResponse rootCloudAuthLoginResponse = getAuthLoginToken();
        if (! validAuthLogin(rootCloudAuthLoginResponse)) {
            return null;
        }

        try {
            String uri = String.format(HaiLiApiConstants.getRealTimeUri, request.getModelId(), request.getThingId());
            String host = haiLiRootCloudOpenApiConfig.getHost();
            String url = host + uri;

            // 请求头
            Map<String,String> header = new HashMap<>();
            header.put("Authorization", "Bearer " + rootCloudAuthLoginResponse.getAccessToken());
            log.info("getRealTimeData start, url:{}", url);
            String result = HttptUtil.httpGet(url, header, null);
            log.info("getRealTimeData end, url:{}, result:{}", url, result);

            if (StringUtils.isBlank(result)) {
                log.error("getRealTimeData null, apiClientConfig:{}, request:{}, response:{}",
                        haiLiRootCloudOpenApiConfig, request, result);
                return null;
            }

            RootCloudRealTimeResponse response = JSON.parseObject(result,
                    new TypeReference<RootCloudRealTimeResponse>() {});

            if (Objects.isNull(response)) {
                log.error("getRealTimeData parse null, apiClientConfig:{}, request:{}, response:{}",
                        haiLiRootCloudOpenApiConfig, request, result);
                return null;
            }

            if (CollectionUtils.isEmpty(response.getPayload())) {
                log.warn("getRealTimeData fail, apiClientConfig:{}, request:{}, response:{}",
                        haiLiRootCloudOpenApiConfig, request, result);
                return response;
            }

            return response;
        } catch (Exception e) {
            log.error("getRealTimeData exception, apiClientConfig:{}, request:{}",
                    haiLiRootCloudOpenApiConfig, request, e);
        }

        return null;
    }

    @Override
    public RootCloudAuthLoginResponse authLogin(RootCloudAuthLoginRequest request) {
        try {
            String host = haiLiRootCloudOpenApiConfig.getHost();
            String url = host + HaiLiApiConstants.authLoginUri;
            log.info("authLogin begin, url:{}, body:{}", url, JSON.toJSON(request));
            JSONObject jsonResult = HttptUtil.doHttpPostJsonV2(url, JSON.toJSONString(request));
            log.info("authLogin end, url:{}, result:{}",
                    url, Objects.isNull(jsonResult) ? null : jsonResult.toJSONString());

            if (Objects.isNull(jsonResult)) {
                log.error("authLogin null, apiClientConfig:{}, request:{}, response:{}", haiLiRootCloudOpenApiConfig,
                        request, jsonResult);
                return null;
            }

            RootCloudAuthLoginResponse response = JSON.parseObject(jsonResult.toJSONString(),
                    new TypeReference<RootCloudAuthLoginResponse>() {});

            if (Objects.isNull(response)) {
                log.error("authLogin parse null, apiClientConfig:{}, request:{}, response:{}",
                        haiLiRootCloudOpenApiConfig, request, response);
                return null;
            }

            return response;
        } catch (Exception e) {
            log.error("authLogin exception, apiClientConfig:{}, request:{}", haiLiRootCloudOpenApiConfig, request, e);
        }

        return null;
    }

    @Override
    public RootCloudAuthLoginResponse getAuthLoginToken() {
        RootCloudAuthLoginRequest request = RootCloudAuthLoginRequest.builder()
                .clientId(haiLiRootCloudOpenApiConfig.getClientId())
                .clientSecret(haiLiRootCloudOpenApiConfig.getClientSecret())
                .grantType(haiLiRootCloudOpenApiConfig.getGrantType())
                .build();

        return authLogin(request);
    }

    private boolean validAuthLogin(RootCloudAuthLoginResponse rootCloudAuthLoginResponse) {
        if (Objects.isNull(rootCloudAuthLoginResponse) || StringUtils.isBlank(rootCloudAuthLoginResponse.getAccessToken())) {
            return false;
        }

        return true;
    }

}
