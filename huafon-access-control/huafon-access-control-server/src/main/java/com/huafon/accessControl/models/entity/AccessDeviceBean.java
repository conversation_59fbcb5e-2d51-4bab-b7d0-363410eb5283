package com.huafon.accessControl.models.entity;

import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2022-04-12 18:23
 **/
@Data
public class AccessDeviceBean {

    private Integer deviceId;
    private String serialNo;
    private String gateway;
    private String ip;
    private Short port;
    private String mac;
    private Boolean online;//是否在线
    private Integer deviceType;
    private String deviceDesc;
    private String bootTime;//启动时间
    private String username;
    private String password;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AccessDeviceBean that = (AccessDeviceBean) o;
        return Objects.equals(serialNo, that.serialNo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(serialNo);
    }
}
