package com.huafon.accessControl.controller;


import com.huafon.accessControl.models.entity.DeviceGateway;
import com.huafon.accessControl.service.DeviceGatewayService;
import com.huafon.support.core.pojo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-04-12 16:42
 **/
@RestController
@RequestMapping("/hik/access")
@Api(tags = "门禁控制设备")
@Slf4j
public class DeviceGatewayController {

    private final DeviceGatewayService deviceGatewayService;

    @Autowired
    public DeviceGatewayController(DeviceGatewayService deviceGatewayService) {
        this.deviceGatewayService = deviceGatewayService;
    }

    @GetMapping("/transfer")
    @ApiOperation("获取全部中转(网关)设备")
    public R<List<DeviceGateway>> searchAllDeviceGateway(){
        return R.ok(deviceGatewayService.searchAllDeviceGateway());
    }

    @ApiOperation("手动刷新发现设备")
    @PostMapping("/transfer/{transferId}/refresh")
    public R<Void> refreshDeviceFind(@PathVariable("transferId") Integer transferId){
        log.info("refresh device find: {}", transferId);
        deviceGatewayService.refreshDeviceFind(transferId);
        return R.ok();
    }


}
