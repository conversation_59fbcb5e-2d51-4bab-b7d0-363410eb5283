package com.huafon.accessControl.service.impl;

import com.huafon.accessControl.dao.mapper.AccessDeviceMapper;
import com.huafon.accessControl.models.constants.WebSocketConstants;
import com.huafon.accessControl.models.dto.*;
import com.huafon.accessControl.models.entity.AccessDeviceBean;
import com.huafon.accessControl.models.entity.UserInfo;
import com.huafon.accessControl.models.enums.DeviceOperation;
import com.huafon.accessControl.service.AccessDeviceService;
import com.huafon.accessControl.service.RemoteOperationRecordService;
import com.huafon.accessControl.service.UserInfoService;
import com.huafon.accessControl.service.WebSocketService;
import com.huafon.accessControl.utils.CommonUtil;
import com.huafon.support.exceptions.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-04-12 19:17
 **/
@Service
@Slf4j
public class AccessDeviceServiceImpl implements AccessDeviceService {

    private final AccessDeviceMapper accessDeviceMapper;
    private final WebSocketService webSocketService;
    private final UserInfoService userInfoService;
    private final RemoteOperationRecordService remoteOperationRecordService;

    @Autowired
    public AccessDeviceServiceImpl(AccessDeviceMapper accessDeviceMapper, WebSocketService webSocketService, @Lazy UserInfoService userInfoService, RemoteOperationRecordService remoteOperationRecordService) {
        this.accessDeviceMapper = accessDeviceMapper;
        this.webSocketService = webSocketService;
        this.userInfoService = userInfoService;
        this.remoteOperationRecordService = remoteOperationRecordService;
    }

    @Override
    public List<AccessDeviceBean> searchDeviceList(Integer gateway) {
        return accessDeviceMapper.searchAllDevice(gateway);
    }

    @Override
    public List<AccessDeviceBean> searchDeviceListByGatewayUser(String gateway) {
        if(gateway == null || gateway.isEmpty()) return new ArrayList<>();
        return accessDeviceMapper.searchDeviceListByGatewayUser(gateway);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLoginInfo(Integer id, String username, String password) {
        accessDeviceMapper.updateLoginInfo(id, username, password);
        AccessDeviceBean deviceBean= accessDeviceMapper.findById(id);
        if(deviceBean == null) return false;

        webSocketService.sendMessage2User(deviceBean.getGateway(), WebSocketConstants.UPDATE_DEVICE_LOGIN, deviceBean);
        return true;
    }

    @Override
    public void remoteOperation(Integer deviceId, Integer opt) {
        AccessDeviceBean deviceBean = accessDeviceMapper.findById(deviceId);
        if(deviceBean == null){
            throw new ServiceException("控门失败，门禁设备不存在。");
        }


        HikRemoteOptDTO remoteOpt = new HikRemoteOptDTO();
        DeviceLoginInfoDTO deviceLogin = new DeviceLoginInfoDTO();
        deviceLogin.setIp(deviceBean.getIp());
        deviceLogin.setPort(deviceBean.getPort());
        deviceLogin.setUsername(deviceBean.getUsername());
        deviceLogin.setPassword(deviceBean.getPassword());
        remoteOpt.setDeviceLoginInfo(deviceLogin);
        remoteOpt.setOpt(opt);
        if(!remoteOpt.isLegal()){
            log.warn("设备控门信息缺少");
            throw new ServiceException("控门失败，门禁设备信息不正确。");
        }
        webSocketService.sendMessage2User(deviceBean.getGateway(), WebSocketConstants.OPT_DESTINATION, remoteOpt);
    }


    @Override
    public List<AccessDeviceBean> searchByIds(List<Integer> ids) {
        if(ids == null || ids.isEmpty()){
            return new ArrayList<>();
        }
        return accessDeviceMapper.queryList(ids);
    }

    @Override
    public List<AccessDeviceBean> searchByUserId(Integer userId) {
        return accessDeviceMapper.queryListByUserId(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setDevicePermission(AccessDevicePermissionDTO devicePermission) {
        AccessDeviceBean deviceBean = accessDeviceMapper.findById(devicePermission.getDeviceId());
        if(deviceBean == null){
            log.info("set device permission device is null {}", devicePermission.getDeviceId());
            throw new ServiceException("设备不存在");
        }
        List<UserInfo> oldUserInfo = userInfoService.searchUsersByDevice(devicePermission.getDeviceId());
        List<UserInfo> newUserInfo = userInfoService.searchUserByIds(devicePermission.getUserIds());
        Collection<UserInfo> needDelete = CollectionUtils.subtract(oldUserInfo, newUserInfo);//在A不在B
        Collection<UserInfo> needAdd = CollectionUtils.subtract(newUserInfo, oldUserInfo);//在B不在A

        if(!needDelete.isEmpty()){
            processPermission(needDelete, deviceBean, DeviceOperation.DELETE);
        }

        if(!needAdd.isEmpty()){
            processPermission(needAdd, deviceBean, DeviceOperation.ADD);
        }
    }

    private void processPermission(Collection<UserInfo> userInfos, AccessDeviceBean device, DeviceOperation operation){
        String requestNo =  CommonUtil.generateWebSocketRequestNo();
        log.info("operation remote permission: {} requestNo {}", operation, requestNo);

        remoteOperationRecordService.recordRemoteOperation(userInfos, device, operation,requestNo);
        HikDevicePermissionDTO permission = new HikDevicePermissionDTO();
        permission.setDevice(new DeviceLoginInfoDTO(device));
        List<HikUserDTO> users = userInfos.stream().map(HikUserDTO::new).collect(Collectors.toList());
        permission.setUsers(users);
        permission.setOperation(operation);
        permission.setRequestNo(requestNo);
        webSocketService.sendMessage2User(device.getGateway(), WebSocketConstants.DEVICE_PERMISSION_DESTINATION, permission);
    }

}
