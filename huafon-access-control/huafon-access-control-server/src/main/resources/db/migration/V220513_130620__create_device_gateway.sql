DROP TABLE IF EXISTS hf_ac_device_gateway;
create table if not exists hf_ac_device_gateway
(
    id          serial
        constraint hf_ac_device_gateway_pk
            primary key,
    description varchar,
    online      boolean default false,
    "user"      varchar not null,
    create_time timestamp,
    modify_time timestamp,
    is_del      integer default 0
);

comment on table hf_ac_device_gateway is '门禁设备网关';

alter table hf_ac_device_gateway
    owner to postgres;

INSERT INTO hf_ac_device_gateway (id, description, online, "user", create_time, modify_time, is_del)
VALUES (1, '网关1', true, 'huafon', now(), null, 0);
