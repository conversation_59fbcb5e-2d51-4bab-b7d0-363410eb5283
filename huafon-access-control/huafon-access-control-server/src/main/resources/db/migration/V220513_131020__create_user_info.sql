drop table if exists hf_ac_user_info;
create table hf_ac_user_info
(
    id          serial
        constraint hf_ac_user_info_pk
            primary key,
    employee_no varchar,
    name        varchar,
    face_image  bytea,
    create_time timestamp,
    modify_time timestamp,
    is_del      integer default 0
);

comment on table hf_ac_user_info is '用户信息';

comment on column hf_ac_user_info.employee_no is '员工编号
';

comment on column hf_ac_user_info.name is '员工名称';

comment on column hf_ac_user_info.face_image is '人脸信息
';

alter table hf_ac_user_info
    owner to postgres;

create index hf_ac_user_info__employee_no_idx
    on hf_ac_user_info (employee_no);

