package com.huafon.models.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Description: 转岗提醒分页返回VO
 * @Date: 2022/3/17 17:12
 * @Author: zyf
 **/
@Data
@ApiModel(value = "TransformPostRemindPageRespVO", description = "转岗提醒分页返回VO")
public class TransformPostRemindPageRespVO {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "转岗提醒id")
    private Long id;

    /**
     * 职员id
     */
    @ApiModelProperty(value = "职员id")
    private Long staffId;

    /**
     * 员工名称
     */
    @ApiModelProperty(value = "员工名称")
    private String staffName;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    private String workNum;

    /**
     * 原所属组织机构id
     */
    @ApiModelProperty(value = "原所属组织机构id")
    private Long originalDepartmentId;

    /**
     * 原所属组织机构名称
     */
    @ApiModelProperty(value = "原所属组织机构名称")
    private String originalDepartmentName;

    /**
     * 原岗位id
     */
    @ApiModelProperty(value = "原岗位id")
    private Long originalPostId;

    /**
     * 原岗位名称
     */
    @ApiModelProperty(value = "原岗位名称")
    private String originalPostName;

    /**
     * 应转入所属组织机构id
     */
    @ApiModelProperty(value = "应转入所属组织机构id")
    private Long transformDepartmentId;

    /**
     * 应转入连级所属组织机构ids
     */
    @ApiModelProperty(value = "应转入连级所属组织机构ids")
    private String transformDepartmentIds;

    /**
     * 应转入所属组织机构连接名称
     */
    @ApiModelProperty(value = "应转入所属组织机构连接名称")
    private String transformDepartmentNames;

    /**
     * 应转入岗位id
     */
    @ApiModelProperty(value = "应转入岗位id")
    private Long transformPostId;

    /**
     * 应转入岗位名称
     */
    @ApiModelProperty(value = "应转入岗位名称")
    private String transformPostName;

    /**
     * 转岗状态，0:未确认，1:已确认
     */
    @ApiModelProperty(value = "转岗状态，0:未确认，1:已确认")
    private Integer transformStatus;

    /**
     * 转岗日期
     */
    @ApiModelProperty(value = "转岗日期")
    private Date transformTime;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String mobile;
}
