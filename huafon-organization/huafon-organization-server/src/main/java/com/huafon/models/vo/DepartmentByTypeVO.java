package com.huafon.models.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 根据组织机构类型查询组织机构信息
 * @Date: 2022/4/11 19:53
 * @Author: zyf
 **/
@Data
@ApiModel(value = "DepartmentByTypeVO", description = "根据组织机构类型查询组织机构信息")
public class DepartmentByTypeVO {

    /**
     * 组织机构id
     */
    @ApiModelProperty(value = "组织机构id")
    private Long departmentId;

    /**
     * 组织机构名称
     */
    @ApiModelProperty(value = "组织机构名称")
    private String departmentName;

}
