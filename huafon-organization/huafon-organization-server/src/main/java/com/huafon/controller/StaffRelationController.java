package com.huafon.controller;

import com.huafon.admin.api.annotation.OperationLog;
import com.huafon.admin.api.enums.OperationLogType;
import com.huafon.models.entity.StaffRelation;
import com.huafon.models.vo.StaffRelationVO;
import com.huafon.service.StaffRelationService;
import com.huafon.support.core.pojo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description: 职员联系人
 * @Date: 2022/3/21 12:57
 * @Author: zyf
 **/
@Api(tags = "职员联系人")
@RestController
@RequestMapping("/staffRelation")
public class StaffRelationController {

    private final StaffRelationService staffRelationService;

    public StaffRelationController(StaffRelationService staffRelationService) {
        this.staffRelationService = staffRelationService;
    }

    @PostMapping("/{staffId}")
    @ApiOperation(value = "获取职员联系人列表")
    public R<List<StaffRelationVO>> list(@PathVariable("staffId") @NotBlank(message = "员工主键不能为空") Long staffId){
        List<StaffRelationVO> list = staffRelationService.list(staffId);
        return R.ok(list);
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "获取职员联系人详情")
    public R<StaffRelationVO> getInfoById(@PathVariable("id") @NotBlank(message = "职员联系人主键不能为空") Long id){
        StaffRelation staffRelation = staffRelationService.getById(id);
        StaffRelationVO staffRelationVO = null;
        if (null != staffRelation) {
            staffRelationVO = new StaffRelationVO();
            BeanUtils.copyProperties(staffRelation, staffRelationVO);
        }
        return R.ok(staffRelationVO);
    }

    @PostMapping("/save")
    @ApiOperation(value = "新增、修改职员联系人")
    @OperationLog(type = OperationLogType.MODITY, notes = "新增、修改职员联系人")
    public R save(@Valid @RequestBody StaffRelationVO reqVO) {
        staffRelationService.save(reqVO);
        return R.ok();
    }

    @DeleteMapping("/{ids}")
    @ApiOperation(value = "通过id删除职员联系人信息")
    @OperationLog(type = OperationLogType.DELETE, notes = "删除职员联系人")
    public R delByIds(@PathVariable("ids") @Valid @NotNull(message = "职员联系人主键不能为空") Long[] ids) {
        staffRelationService.delById(ids);
        return R.ok();
    }

}
