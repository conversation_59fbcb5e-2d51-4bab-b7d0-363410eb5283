package com.huafon.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huafon.models.entity.StaffLeaveRemind;
import com.huafon.models.vo.StaffLeaveRemindPageReqVO;
import com.huafon.models.vo.StaffLeaveRemindPageRespVO;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 职员变动提醒（入职、离职）
 * @Date: 2022/3/26 12:53
 * @Author: zyf
 **/
public interface StaffLeaveRemindMapper extends BaseMapper<StaffLeaveRemind> {

    /**
     * 用于查询离职提醒分页
     *
     * @param reqVO
     * @return
     */
    int countTotal(StaffLeaveRemindPageReqVO reqVO);

    /**
     * 分页查询离职提醒
     *
     * @param reqVO
     * @return
     */
    IPage<StaffLeaveRemindPageRespVO> findLeaveRemindPage(Page<StaffLeaveRemindPageRespVO> page,@Param("reqVO") StaffLeaveRemindPageReqVO reqVO);

    /**
     * 根据工号软删除离职提醒
     *
     * @param staffLeaveRemind
     */
    void updateByWorkNum(StaffLeaveRemind staffLeaveRemind);

    /**
     * 根据id软删
     *
     * @param staffLeaveRemind
     * @return
     */
    int updateIsDelById(StaffLeaveRemind staffLeaveRemind);

    /**
     * 根据职员id删除对应的离职提醒
     *
     * @param ids
     */
    void updateDelByStaffId(@Param("ids") Long[] ids);

    /**
     * 根据工号查询未确认的离职提醒
     *
     * @param workNum
     * @return
     */
    StaffLeaveRemind findByWorkNum(@Param("workNum") String workNum,@Param("tenantId") Integer tenantId);
}
