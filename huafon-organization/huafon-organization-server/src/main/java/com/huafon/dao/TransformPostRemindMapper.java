package com.huafon.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huafon.models.entity.TransformPostRemind;
import com.huafon.models.vo.TransformPostRemindPageReqVO;
import com.huafon.models.vo.TransformPostRemindPageRespVO;
import com.huafon.models.vo.TransformPostRemindVO;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 转岗提醒
 * @Date: 2022/3/17 15:34
 * @Author: zyf
 **/
public interface TransformPostRemindMapper extends BaseMapper<TransformPostRemind> {

    /**
     * 计算分页总数
     *
     * @param reqVO
     * @return
     */
    int countTotal(TransformPostRemindPageReqVO reqVO);

    /**
     * 分页查询
     *
     * @param reqVO
     * @return
     */
    IPage<TransformPostRemindPageRespVO> findTransformPostRemindPage(Page<TransformPostRemindPageRespVO> page, @Param("reqVO") TransformPostRemindPageReqVO reqVO);

    /**
     * 根据详情获取
     *
     * @param id
     * @return
     */
    TransformPostRemindVO getInfoById(Long id);

    /**
     * 根据工号软删
     *
     * @param transformPostRemind
     */
    void updateByWorkNum(TransformPostRemind transformPostRemind);

    /**
     * 根据工号查询转岗信息
     *
     * @param workNum
     * @return
     */
    TransformPostRemind findByWorkNum(@Param("workNum") String workNum,@Param("tenantId") Integer tenantId);

    /**
     * 根据职员id批量删除职员的转岗信息
     *
     * @param ids
     */
    void updateDelByStaffId(@Param("ids") Long[] ids);
}
