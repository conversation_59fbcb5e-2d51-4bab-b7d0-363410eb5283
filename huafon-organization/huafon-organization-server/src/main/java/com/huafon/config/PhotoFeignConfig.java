package com.huafon.config;

import feign.Feign;
import feign.Logger;
import feign.Request;
import feign.Retryer;
import feign.form.FormEncoder;
import feign.jackson.JacksonDecoder;
import feign.okhttp.OkHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class PhotoFeignConfig {

    @Value("${huafon.base.url}")
    private String HUAFON_BASE_URL;

    @Bean(name = "photoApiClient")
    public PhotoApiClient photoApiClient() {
        return Feign.builder()
                .options(new Request.Options(1000, 3500))
                .retryer(new Retryer.Default(5000, 5000, 3))
                .logLevel(Logger.Level.BASIC)
                .encoder(new FormEncoder())
                .decoder(new JacksonDecoder())
                .client(new OkHttpClient())
                .target(PhotoApiClient.class, HUAFON_BASE_URL);
    }

}