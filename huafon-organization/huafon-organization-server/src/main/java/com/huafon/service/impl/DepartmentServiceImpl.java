package com.huafon.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.huafon.common.config.TenantContext;
import com.huafon.common.utils.bean.BeanUtils;
import com.huafon.dao.DepartmentMapper;
import com.huafon.dao.PostMapper;
import com.huafon.dao.StaffMapper;
import com.huafon.framework.mybatis.enums.DelFlag;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.framework.mybatis.service.MybatisServiceImpl;
import com.huafon.models.constants.BaseContent;
import com.huafon.models.constants.DepartmentContent;
import com.huafon.models.dto.third.EhrPushDataDTO;
import com.huafon.models.dto.third.EhrPushDepartmentDTO;
import com.huafon.models.entity.Department;
import com.huafon.models.entity.Staff;
import com.huafon.models.enums.EhrOperateTypeEnum;
import com.huafon.models.enums.OrganizationSystemCode;
import com.huafon.models.query.OrgDepartmentIdWithPost;
import com.huafon.models.query.OrgDepartmentIdWithStaff;
import com.huafon.models.query.OrgParentIdWithDepartment;
import com.huafon.models.vo.*;
import com.huafon.portal.api.dto.UserDto;
import com.huafon.portal.api.service.UserRpcService;
import com.huafon.service.DepartmentService;
import com.huafon.support.config.UserContext;
import com.huafon.support.core.pojo.SystemCode;
import com.huafon.support.dto.UserInfoDto;
import com.huafon.support.exceptions.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description: 公司、部门、组织 业务层实现类
 * @Date: 2022/2/17 10:59
 * @Author: zyf
 **/
@Slf4j
@Service
public class DepartmentServiceImpl extends MybatisServiceImpl<DepartmentMapper, Department> implements DepartmentService {

    private final DepartmentMapper departmentMapper;

    private final PostMapper postMapper;

    private final StaffMapper staffMapper;

    private final RedisTemplate<String, String> redisTemplate;

    @DubboReference
    private UserRpcService userRpcService;

    public DepartmentServiceImpl(DepartmentMapper departmentMapper, PostMapper postMapper, StaffMapper staffMapper, RedisTemplate<String, String> redisTemplate) {
        this.departmentMapper = departmentMapper;
        this.postMapper = postMapper;
        this.staffMapper = staffMapper;
        this.redisTemplate = redisTemplate;
    }

    /**
     * 获取树形结构的组织机构数据
     *
     * @return
     */
    @Override
    public List<DepartmentTreeVO> getDepartmentTree() {
        List<DepartmentTreeVO> result = new ArrayList<>();
        //获取租户id
        Integer tenantId = TenantContext.getOne();
        String key = DepartmentContent.DEPARTMENT_TREE_KEY+tenantId;
        String v = redisTemplate.opsForValue().get(key);

        if (v != null && !"[]".equals(v) && v != "") {
            result = JSONObject.parseArray(v, DepartmentTreeVO.class);
            return result;
        }

        //查询组织机构数据
        List<OrgParentIdWithDepartment> orgParentIdWithDepartment = departmentMapper.findOrgParentIdWithDepartment(tenantId);
        //构建树形结构
        result = buildDepartmentTree(orgParentIdWithDepartment);
        redisTemplate.opsForValue().set(key,
                JSON.toJSONString(result), DepartmentContent.DEPARTMENT_TREE_TIME, TimeUnit.DAYS);
        return result;
    }

    /**
     * 获取树形结构的组织机构和岗位数据
     *
     * @return
     */
    @Override
    public List<DepartmentWithPostTreeVO> getDepartmentWithPostTree() {
        List<DepartmentWithPostTreeVO> result = new ArrayList<>();
        //获取租户id
        Integer tenantId = TenantContext.getOne();
        String key = DepartmentContent.DEPARTMENT_POST_TREE_KEY+tenantId;
        String v = redisTemplate.opsForValue().get(key);
        if (v != null && !"[]".equals(v) && v != "") {
            result = JSONObject.parseArray(v, DepartmentWithPostTreeVO.class);
            return result;
        }
        //查询组织机构数据
        DepartmentTreeReqVO departmentTreeReqVO = new DepartmentTreeReqVO();
        departmentTreeReqVO.setTenantId(tenantId);
        List<OrgParentIdWithDepartment> orgParentIdWithDepartment = departmentMapper.findSimpleDepartmentTree(departmentTreeReqVO);
        List<OrgDepartmentIdWithPost> orgDepartmentIdWithPost = postMapper.findOrgDepartmentIdWithPost(tenantId);

        //构建树形结构
        result = buildDepartmentWithPostTree(orgParentIdWithDepartment,orgDepartmentIdWithPost,null,null);
        redisTemplate.opsForValue().set(key,
                JSON.toJSONString(result), DepartmentContent.DEPARTMENT_POST_TREE_TIME, TimeUnit.DAYS);
        return result;
    }


    /**
     * 子级组织机构分页
     *
     * @param reqVO
     * @return
     */
    @Override
    public CommonPage<DepartmentPageRespVO> departmentPage(DepartmentPageReqVO reqVO) {
        LambdaQueryWrapper<Department> wrapper = this.getLambdaQuery();
        wrapper.eq(Department::getIsDel, DelFlag.SAVE.getValue());
        if (StringUtils.isNotBlank(reqVO.getCondition())) {
            wrapper.like(Department::getDepartmentName, reqVO.getCondition());
        }
        if (null != reqVO.getParentId() && reqVO.getParentId()>0) {
            wrapper.eq(Department::getParentId, reqVO.getParentId());
        }
        //获取租户id
        Integer tenantId = TenantContext.getOne();
        wrapper.eq(Department::getTenantId, tenantId);
        wrapper.orderByDesc(Department::getSortOrder,Department::getModifyTime);
        CommonPage<Department> page = this.commonPage(reqVO.getPageNo(), reqVO.getPageSize(), wrapper);
        CommonPage<DepartmentPageRespVO> result = CommonPage.copy(page);
        result.setList(BeanUtils.convert(DepartmentPageRespVO.class, page.getList()));
        return result;
    }

    /**
     * 根据ids删除组织机构
     *
     * @param ids
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delByIds(Long[] ids) {
        if (null == ids || ids.length < 1) {
            throw new ServiceException(SystemCode.PARAM_MISS);
        }
        //准备需要删除的结果集
        List<Department> departmentList = new ArrayList<>();

        UserInfoDto userInfo = UserContext.get();
        Long userId = userInfo.getUserId();
        Date modifyTime = new Date();
        for (Long id : ids) {

            Department department = new Department();
            department.setId(id);
            department.setIsDel(DelFlag.DELELTED.getValue());
            department.setModifyBy(userId);
            department.setModifyTime(modifyTime);
            departmentList.add(department);

            List<Department> childrensById = getChildrensById(department.getId(),userId);
            departmentList.addAll(childrensById);

        }

        for (Department d : departmentList){
            List<PostForStaffChooseVO> byDepartmentId = postMapper.findByDepartmentId(d.getId());
            if (null != byDepartmentId && byDepartmentId.size()>0){
                throw new ServiceException(SystemCode.DATA_DELETE_FAILED,OrganizationSystemCode.ORG_DEPARTMENT_DELETE_ERROR_POST.getMsg());
            }

            List<Staff> staffList = staffMapper.findByDepartmentId(d.getId());
            if (null != staffList && staffList.size()>0){
                throw new ServiceException(SystemCode.DATA_DELETE_FAILED,OrganizationSystemCode.ORG_DEPARTMENT_DELETE_ERROR_STAFF.getMsg());
            }
        }
        departmentMapper.updateDelFlagById(departmentList);
        //清空树节点缓存
        //获取租户id
        Integer tenantId = TenantContext.getOne();
        redisTemplate.delete(DepartmentContent.DEPARTMENT_TREE_KEY+tenantId);
        redisTemplate.delete(DepartmentContent.DEPARTMENT_POST_TREE_KEY+tenantId);

    }

    /**
     * 递归调用，获取当前id下的所有子级别
     */
    private List<Department> getChildrensById(Long id,Long userId) {
        List<Department> childrenList = new ArrayList<>();
        Date modifyTime = new Date();
        List<Long> ids = departmentMapper.findByParentId(id);
        for (Long departmentId : ids) {
            Department children = new Department();
            children.setId(departmentId);
            children.setIsDel(DelFlag.DELELTED.getValue());
            children.setModifyBy(userId);
            children.setModifyTime(modifyTime);
            childrenList.add(children);
            List<Department> childrensById = getChildrensById(children.getId(),userId);
            childrenList.addAll(childrensById);
        }
        return childrenList;
    }


    /**
     * 新增和修改组织机构信息
     *
     * @param reqVO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(DepartmentVO reqVO) {

        //获取租户id
        Integer tenantId = TenantContext.getOne();
        if (null != reqVO.getParentId() && reqVO.getParentId() > 0) {
            Department selectById = departmentMapper.selectById(reqVO.getParentId());
            if (null == selectById) {
                throw new ServiceException(SystemCode.DATA_NOT_EXIST,OrganizationSystemCode.ORG_DEPARTMENT_PARENT_NOT_EXIST.getMsg());
            }
            Integer departmentType = selectById.getDepartmentType();
            if (departmentType.equals(DepartmentContent.DEPARTMENT)){
                if (!reqVO.getDepartmentType().equals(departmentType)){
                    throw new ServiceException(SystemCode.PARAM_VALID_ERROR,OrganizationSystemCode.ORG_DEPARTMENT_TYPE_ERROR.getMsg());
                }
            }
        }

        if (null == reqVO.getParentId()){
            reqVO.setParentId(DepartmentContent.PARENT_ID);
        }

        //校验组织机构code
        Department byOrgCode = departmentMapper.findByDepartmentCodeAndTenantId(reqVO.getDepartmentCode(),tenantId);
        if (null != byOrgCode) {
            if (!byOrgCode.getId().equals(reqVO.getId())){
                throw new ServiceException(SystemCode.DATA_EXISTED,OrganizationSystemCode.ORG_DEPARTMENT_CODE_EXIST.getMsg());
            }
        }

        Department department = new Department();
        BeanUtils.copyProperties(reqVO, department);
        Date date = new Date();
        UserInfoDto userInfo = UserContext.get();
        Long userId = userInfo.getUserId();

        department.setModifyBy(userId);
        department.setModifyTime(date);
        if (null != reqVO.getId() && reqVO.getId() > 0) {//修改操作
            departmentMapper.updateById(department);
        } else {//新增操作
            department.setTenantId(tenantId);
            department.setCreateBy(userId);
            department.setCreateTime(date);
            departmentMapper.insert(department);
        }

        //清空树节点缓存
        redisTemplate.delete(DepartmentContent.DEPARTMENT_TREE_KEY+tenantId);
        redisTemplate.delete(DepartmentContent.DEPARTMENT_POST_TREE_KEY+tenantId);
    }

    /**
     * 处理ehr推送的组织机构数据
     *
     * @param data
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void ehrProcess(EhrPushDataDTO<EhrPushDepartmentDTO> data) {
        log.info("EHR推送组织机构信息：{}", JSONObject.toJSONString(data));
        EhrOperateTypeEnum ehrOperateTypeEnum = EhrOperateTypeEnum.valueOf(data.getOperateType());
        Date date = new Date();
        UserInfoDto userInfo = UserContext.get();
        Long userId = userInfo.getUserId();

        List<Integer> tenantIdList = new ArrayList<>();

        switch (ehrOperateTypeEnum) {
            case SAVE://新增或者修改
                for (EhrPushDepartmentDTO departmentDTO:data.getData()){
                    Department department = departmentMapper.findByThirdCode(departmentDTO.getDepartmentCode());
                    Long parentId = null;
                    String parentName = null;
                    Integer tenantId = null;
                    if (StringUtils.isNotBlank(departmentDTO.getParentCode())){
                        Department parentDepartment = departmentMapper.findByThirdCode(departmentDTO.getParentCode());
                        if ( null != parentDepartment) {
                            parentId = parentDepartment.getId();
                            parentName = parentDepartment.getDepartmentName();
                            tenantId = parentDepartment.getTenantId();
                            if (parentDepartment.getTenantId() != null && !tenantIdList.contains(parentDepartment.getTenantId())){
                                tenantIdList.add(parentDepartment.getTenantId());
                            }
                        }
                    }else {
                        //查询顶级节点是否存在
                        Department topNode = departmentMapper.findTopNode();
                        if (null == topNode){
                            topNode = new Department();
                            topNode.setDepartmentCode(DepartmentContent.TOP_DEPT_CODE);
                            topNode.setDepartmentName(DepartmentContent.TOP_DEPT_NAME);
                            topNode.setDepartmentDesc(DepartmentContent.TOP_DEPT_DESC);
                            topNode.setParentId(DepartmentContent.PARENT_ID);
                            topNode.setDepartmentType(DepartmentContent.GROUP);
                            topNode.setSortOrder(BaseContent.DEFAULT_SORT);
                            topNode.setCreateTime(date);
                            topNode.setCreateBy(userId);
                            topNode.setModifyBy(userId);
                            topNode.setModifyTime(date);
                            topNode.setIsDel(DelFlag.SAVE.getValue());
                            departmentMapper.insert(topNode);
                        }
                        parentId = topNode.getId();
                        parentName = topNode.getDepartmentName();
                    }
                    if (null != department){
                        BeanUtils.copyProperties(departmentDTO,department);
                        department.setDepartmentCode(null);
                        department.setModifyTime(date);
                        department.setModifyBy(userId);
                        department.setParentId(parentId);
                        department.setParentName(parentName);
                        departmentMapper.updateById(department);
                    }else {
                        department = new Department();
                        BeanUtils.copyProperties(departmentDTO,department);
                        department.setDepartmentCode(UUID.randomUUID().toString().trim().replaceAll("-", "").toLowerCase());
                        department.setCreateBy(userId);
                        department.setCreateTime(date);
                        department.setParentId(parentId);
                        department.setTenantId(tenantId);
                        department.setParentName(parentName);
                        department.setSortOrder(BaseContent.DEFAULT_SORT);
                        department.setThirdCode(departmentDTO.getDepartmentCode());
                        departmentMapper.insert(department);
                    }
                    //查询parentId为null的数据，根据这些数据的parentCode去填充对应的parentId和parentName
                    List<Department> departmentList = departmentMapper.findByParentIdIsNull();
                    List<Department> needUpdateList = new ArrayList<>();
                    if (null != departmentList && departmentList.size()>0){
                        for (Department depart:departmentList){
                            Department byDepartmentCode = departmentMapper.findByThirdCode(depart.getParentCode());
                            if (null != byDepartmentCode){
                                depart.setModifyBy(userId);
                                depart.setModifyTime(date);
                                depart.setParentId(byDepartmentCode.getId());
                                depart.setParentName(byDepartmentCode.getDepartmentName());
                                depart.setTenantId(byDepartmentCode.getTenantId());
                                needUpdateList.add(depart);
                            }
                        }
                        departmentMapper.batchUpdateParentInfo(needUpdateList);
                    }

                }
                break;
            case DELETE://删除
                List<Department> departmentList = new ArrayList<>();
                for (EhrPushDepartmentDTO departmentDTO:data.getData()){
                    List<Long> idsByDepartmentCode = departmentMapper.findIdsByThirdCode(departmentDTO.getDepartmentCode());
                    for (Long id : idsByDepartmentCode){
                        Department department = new Department();
                        department.setId(id);
                        department.setModifyTime(date);
                        department.setModifyBy(userId);
                        department.setIsDel(DelFlag.DELELTED.getValue());
                        departmentList.add(department);
                    }
                }

                List<Department> needDel = new ArrayList<>();

                for (Department d : departmentList){
                    List<PostForStaffChooseVO> byDepartmentId = postMapper.findByDepartmentId(d.getId());
                    if (null != byDepartmentId && byDepartmentId.size()>0){
                        continue;
                    }

                    List<Staff> staffList = staffMapper.findByDepartmentId(d.getId());
                    if (null != staffList && staffList.size()>0){
                        continue;
                    }
                    needDel.add(d);
                }
                if (!CollectionUtils.isEmpty(needDel)) {
                    tenantIdList = departmentMapper.getTenantIdListByIds(needDel.stream().map(Department::getId).collect(Collectors.toList()));
                    departmentMapper.updateDelFlagById(needDel);
                }
                break;
            default:
                throw new ServiceException(SystemCode.PARAM_VALID_ERROR,OrganizationSystemCode.THIRD_BUSINESS_PARAMS_PARSE_ERROR.getMsg());
        }

        //清空树节点缓存
        if (!CollectionUtils.isEmpty(tenantIdList)){
            deleteTreeCache(tenantIdList);
        }

    }

    /**
     * 根据id获取父节点的名称，用逗号隔开
     *
     * @param id
     * @return
     */
    @Override
    public String getParentNamesById(Long id) {
        List<Department> parentNodes = departmentMapper.findParentNodesById(id);
        StringBuilder names = new StringBuilder();
        for(int i = parentNodes.size()-1; i>=0; i--) {
            names.append(parentNodes.get(i).getDepartmentName());
            if (i != 0){
                names.append("/");
            }
        }
        return names.toString();
    }

    /**
     * 根据id获取父节点的id，用逗号隔开
     *
     * @param id
     * @return
     */
    @Override
    public String getParentIdsById(Long id) {
        List<Department> parentNodes = departmentMapper.findParentNodesById(id);
        StringBuilder ids = new StringBuilder();
        for(int i = parentNodes.size()-1; i>=0; i--) {
            ids.append(parentNodes.get(i).getId());
            if (i != 0){
                ids.append(",");
            }
        }
        return ids.toString();
    }

    /**
     * 根据组织机构类型获取组织机构列表
     *
     * @param type
     * @return
     */
    @Override
    public List<DepartmentByTypeVO> getByType(Integer type) {
        //获取租户id
        Integer tenantId = TenantContext.getOne();
        return departmentMapper.getByType(type,tenantId);
    }

    /**
     * 根据租户id获取树形结构组织机构
     *
     * @param reqVO
     * @return
     */
    @Override
    public List<DepartmentWithPostTreeVO> getDepartmentTreeTenantIds(DepartmentTreeReqVO reqVO) {

        Integer tenantId = TenantContext.getOne();
        reqVO.setTenantId(tenantId);
        if (null == reqVO.getIsIncludeDept()){
            reqVO.setIsIncludeDept(Boolean.FALSE);
        }
        List<OrgParentIdWithDepartment> orgParentIdWithDepartment = departmentMapper.findSimpleDepartmentTree(reqVO);
        List<OrgDepartmentIdWithPost> orgDepartmentIdWithPost = null;
        List<OrgDepartmentIdWithStaff> orgDepartmentIdWithStaff = null;
        if (null != reqVO.getIsIncludePost() && reqVO.getIsIncludePost()){
            orgDepartmentIdWithPost = postMapper.findOrgDepartmentIdWithPost(tenantId);
        }
        if (null != reqVO.getIsIncludeStaff() &&reqVO.getIsIncludeStaff()){
            orgDepartmentIdWithStaff = staffMapper.findOrgDepartmentIdWithStaff(tenantId);
            List<String> userNames = getUserNames(orgDepartmentIdWithStaff);
            Map<String, Integer> userIdMap = getUserId(userNames);
            setUserId(orgDepartmentIdWithStaff,userIdMap);
        }
        List<Long> secondNodes = null;
        if ( null != reqVO.getDepartmentId()){
            secondNodes = Arrays.asList(reqVO.getDepartmentId());
        }else {
            //获取集团下的节点
            Department topNode = departmentMapper.findTopNode();
            secondNodes = departmentMapper.findByParentId(topNode.getId());
        }
        List<DepartmentWithPostTreeVO> departmentWithPostTreeVOS = buildDepartmentWithPostTree(orgParentIdWithDepartment, orgDepartmentIdWithPost,orgDepartmentIdWithStaff,secondNodes);
        if ( null != reqVO.getDepartmentId()){
            departmentWithPostTreeVOS = departmentWithPostTreeVOS.get(0).getChildren();
        }
        return departmentWithPostTreeVOS;
    }

    private void setUserId(List<OrgDepartmentIdWithStaff> departmentIdWithStaffs, Map<String, Integer> userIdMap) {
        if (!CollectionUtils.isEmpty(departmentIdWithStaffs) && !CollectionUtils.isEmpty(userIdMap)){
            for (OrgDepartmentIdWithStaff departmentIdWithStaff:departmentIdWithStaffs){
                if (!CollectionUtils.isEmpty(departmentIdWithStaff.getStaffList())){
                    for (StaffForDepartmentTreeVO staffForDepartmentTreeVO:departmentIdWithStaff.getStaffList()){
                        staffForDepartmentTreeVO.setUserId(userIdMap.get(staffForDepartmentTreeVO.getWorkNum()));
                    }
                }
            }
        }
    }

    public List<String> getUserNames(List<OrgDepartmentIdWithStaff> departmentIdWithStaffs){
        List<String> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(departmentIdWithStaffs)){
            for (OrgDepartmentIdWithStaff departmentIdWithStaff:departmentIdWithStaffs){
                if (!CollectionUtils.isEmpty(departmentIdWithStaff.getStaffList())){
                    for (StaffForDepartmentTreeVO staffForDepartmentTreeVO:departmentIdWithStaff.getStaffList()){
                        result.add(staffForDepartmentTreeVO.getWorkNum());
                    }
                }
            }
        }
        return result;
    }

    public Map<String,Integer> getUserId(List<String> workNums){
        Map<String,Integer> result = new HashMap<>();
        if (!CollectionUtils.isEmpty(workNums)) {
            Map<Integer, UserDto> userMapByUsernames = userRpcService.getUserMapByUsernames(workNums);
            if (!CollectionUtils.isEmpty(userMapByUsernames)) {
                for (Map.Entry<Integer, UserDto> entry : userMapByUsernames.entrySet()) {
                    UserDto value = entry.getValue();
                    result.put(value.getUsername(), value.getUserId());
                }
            }
        }
        return result;
    }

    /**
     * 根据当前id，获取所有父级
     *
     * @param id
     * @return
     */
    @Override
    public List<Department> findParentNodesById(Long id) {
        return departmentMapper.findParentNodesById(id);
    }

    /**
     * 获取指定节点的树形结构
     *
     * @param ids
     * @return
     */
    @Override
    public List<DepartmentTreeVO> getDepartmentTreeByIds(List<Long> ids) {
        List<DepartmentTreeVO> result = null;
        if (!CollectionUtils.isEmpty(ids)){
            List<Long> deptIds = new ArrayList<>();
            List<Long> idsByParentIds = departmentMapper.findIdsByParentIds(ids);
            List<Long> parentIdsByIds = departmentMapper.findParentIdsByIds(ids);
            if (!CollectionUtils.isEmpty(idsByParentIds) && !CollectionUtils.isEmpty(parentIdsByIds)){
                deptIds.addAll(idsByParentIds);
                deptIds.addAll(parentIdsByIds);
            }else if (CollectionUtils.isEmpty(idsByParentIds) && !CollectionUtils.isEmpty(parentIdsByIds)){
                deptIds = parentIdsByIds;
            }else if (!CollectionUtils.isEmpty(idsByParentIds) && CollectionUtils.isEmpty(parentIdsByIds)){
                deptIds = idsByParentIds;
            }

            if (!CollectionUtils.isEmpty(deptIds)){
                List<Long> deptIdList = deptIds.stream().distinct().collect(Collectors.toList());
                List<OrgParentIdWithDepartment> departmentTreeByIds = departmentMapper.findDepartmentTreeByIds(deptIdList);
                result = buildDepartmentTree(departmentTreeByIds);
            }
        }
        return result;
    }

    /**
     * 获取该组织机构下的所有子节点的id
     *
     * @param id 组织机构id
     * @return
     */
    @Override
    public List<Long> getChildNodeIdsById(Long id) {
        return departmentMapper.findIdsByParentId(id);
    }


    /**
     * 构建组织机构树形结构
     *
     * @param orgParentIdWithDepartment
     * @return
     */
    public List<DepartmentTreeVO> buildDepartmentTree(List<OrgParentIdWithDepartment> orgParentIdWithDepartment) {
        //最终结果集
        List<DepartmentTreeVO> topList = null;

        for (OrgParentIdWithDepartment parentIdWithDepartment : orgParentIdWithDepartment) {
            for (DepartmentTreeVO departmentTreeVO : parentIdWithDepartment.getDepartmentTreeVOList()) {
                for (OrgParentIdWithDepartment all : orgParentIdWithDepartment) {
                    if (departmentTreeVO.getId().equals(all.getParentId())){
                        departmentTreeVO.setChildren(all.getDepartmentTreeVOList());
                    }
                }
            }
            if (parentIdWithDepartment.getParentId()==0){
                topList = parentIdWithDepartment.getDepartmentTreeVOList();
            }

        }
        return topList;
    }

    /**
     * 构建组织机构树形结构，包含岗位
     *
     * @param orgParentIdWithDepartment
     * @return
     */
    private List<DepartmentWithPostTreeVO> buildDepartmentWithPostTree(List<OrgParentIdWithDepartment> orgParentIdWithDepartment,List<OrgDepartmentIdWithPost> postList,List<OrgDepartmentIdWithStaff> staffList,List<Long> topIds) {
        //最终结果集
        List<DepartmentWithPostTreeVO> topList = new ArrayList<>();

        //构建 k-v 组织机构id-岗位列表
        Map<Long,List<PostForDepartmentTreeVO>> postMap = new HashMap<>();
        if ( null != postList && postList.size()>0){
            for (OrgDepartmentIdWithPost post:postList){
                postMap.put(post.getDepartmentId(),post.getPostList());
            }
        }

        //构建 k-v 组织机构id-职员列表
        Map<Long,List<StaffForDepartmentTreeVO>> staffMap = new HashMap<>();
        if ( null != staffList && staffList.size()>0){
            for (OrgDepartmentIdWithStaff staff:staffList){
                staffMap.put(staff.getDepartmentId(),staff.getStaffList());
            }
        }

        for (OrgParentIdWithDepartment parentIdWithDepartment : orgParentIdWithDepartment) {
            for (DepartmentWithPostTreeVO departmentWithPostTreeVO : parentIdWithDepartment.getSimpleDepartmentList()) {
                for (OrgParentIdWithDepartment all : orgParentIdWithDepartment) {
                    if (departmentWithPostTreeVO.getDepartmentId().equals(all.getParentId())){
                        departmentWithPostTreeVO.setChildren(all.getSimpleDepartmentList());
                    }
                    if (postMap.size()>0){
                        departmentWithPostTreeVO.setPostList(postMap.get(departmentWithPostTreeVO.getDepartmentId()));
                    }
                    if (staffMap.size()>0){
                        departmentWithPostTreeVO.setStaffList(staffMap.get(departmentWithPostTreeVO.getDepartmentId()));
                    }
                }
                if (null != topIds){
                    for (Long topId:topIds){
                        if (topId.equals(departmentWithPostTreeVO.getDepartmentId())){
                            topList.add(departmentWithPostTreeVO);
                        }
                    }
                }
            }
            if (topIds == null){
                if (parentIdWithDepartment.getParentId()==0){
                    topList = parentIdWithDepartment.getSimpleDepartmentList();
                }
            }

        }
        return topList;
    }

    /**
     * 删除所有租户对应的组织机构缓存数据
     *
     * @param tenantIdList
     */
    private void deleteTreeCache(List<Integer> tenantIdList){
        for (Integer tenantId:tenantIdList){
            redisTemplate.delete(DepartmentContent.DEPARTMENT_TREE_KEY+tenantId);
            redisTemplate.delete(DepartmentContent.DEPARTMENT_POST_TREE_KEY+tenantId);
        }
    }

}
