<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huafon.dao.StaffRelationMapper">

    <insert id="batchSave">
        INSERT INTO
        hf_org_staff_relation(staff_id,linkman,linkman_mobile,relation,remark,confirm_status,create_by,create_time,is_del)
        VALUES
        <foreach collection="list" item="emp" separator=",">
            (#{emp.staffId},#{emp.linkman},#{emp.linkmanMobile},#{emp.relation},#{emp.remark},#{emp.confirmStatus},#{emp.createBy},#{emp.createTime},${emp.isDel})
        </foreach>
    </insert>

    <update id="updateDelFlag">
        <foreach collection="list" item="emp" separator=";">
            UPDATE
            hf_org_staff_relation
            <set>
                <if test="emp.isDel != null">
                    is_del = #{emp.isDel}
                </if>
                <if test="emp.modifyBy != null">
                    ,modify_by = #{emp.modifyBy}
                </if>
                <if test="emp.modifyTime != null">
                    ,modify_time = #{emp.modifyTime}
                </if>
            </set>
            WHERE
            <if test="emp.id != null">
                id = #{emp.id}
            </if>
            <if test="emp.staffId != null">
                staff_id = #{emp.staffId}
            </if>
        </foreach>
    </update>

    <update id="updateByStaffId">
        UPDATE
        hf_org_staff_relation
        <set>
            <if test="isDel != null">
                is_del = #{isDel}
            </if>
            <if test="modifyBy != null">
                ,modify_by = #{modifyBy}
            </if>
            <if test="modifyTime != null">
                ,modify_time = #{modifyTime}
            </if>
        </set>
        WHERE
        is_del = 0
        AND
        staff_id = #{staffId}
    </update>

    <update id="updateConfirmStatusByStaffId">
        UPDATE
        hf_org_staff_relation
        <set>
            <if test="confirmStatus != null">
                confirm_status = #{confirmStatus}
            </if>
            <if test="modifyBy != null">
                ,modify_by = #{modifyBy}
            </if>
            <if test="modifyTime != null">
                ,modify_time = #{modifyTime}
            </if>
        </set>
        WHERE
        is_del = 0
        AND
        staff_id = #{staffId}
    </update>

    <select id="findByStaffId" resultType="com.huafon.models.vo.StaffRelationVO">
        SELECT
            t.id,
            t.linkman,
            t.linkman_mobile,
            t.relation,
            t.remark
        FROM
            hf_org_staff_relation AS t
        WHERE
            t.is_del = 0
        AND
            confirm_status = 1
        AND
            t.staff_id = #{staffId}
    </select>

</mapper>