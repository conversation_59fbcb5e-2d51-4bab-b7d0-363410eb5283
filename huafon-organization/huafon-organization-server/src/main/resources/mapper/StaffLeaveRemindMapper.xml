<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huafon.dao.StaffLeaveRemindMapper">

    <update id="updateByWorkNum">
        UPDATE
            hf_org_staff_leave_remind
        SET is_del = #{isDel},
            modify_by = #{modifyBy},
            modify_time = #{modifyTime}
        WHERE work_num = #{workNum}
          AND is_del = 0
    </update>

    <update id="updateDelByStaffId">
        UPDATE
        hf_org_staff_leave_remind
        SET is_del = 1
        WHERE staff_id in
        <foreach close=")" collection="ids" index="index" item="staffId" open="(" separator=",">
            #{staffId}
        </foreach>
        AND is_del = 0
    </update>

    <update id="updateIsDelById">
        UPDATE
            hf_org_staff_leave_remind
        SET is_del = #{isDel},
            modify_by = #{modifyBy},
            modify_time = #{modifyTime},
            leave_date = #{leaveDate}
        WHERE id = #{id}
    </update>

    <select id="countTotal" resultType="java.lang.Integer">
        SELECT count(*)
        FROM hf_org_staff_leave_remind t1
        WHERE
        t1.is_del = 0
        <if test="postId != null">
            AND t1.leave_post_id = #{postId}
        </if>
        <if test="condition != null and condition != ''">
            AND(
            t1.staff_name like concat('%', #{condition}, '%') OR
            t1.work_num like concat('%', #{condition}, '%')
            )
        </if>
    </select>

    <select id="findLeaveRemindPage" resultType="com.huafon.models.vo.StaffLeaveRemindPageRespVO">
        SELECT
        t1.id,
        t1.staff_id staffId,
        t1.staff_name staffName,
        t1.work_num workNum,
        t1.leave_date leaveDate,
        t1.leave_department_id leaveDepartmentId,
        t1.leave_post_id leavePostId,
        t2.post_name leavePostName,
        t1.leave_department_ids leaveDepartmentIds
        FROM hf_org_staff_leave_remind t1
        LEFT JOIN hf_org_post t2 ON t1.leave_post_id = t2.id AND t2.is_del = 0
        WHERE
        t1.is_del = 0
        AND
        t1.tenant_id = #{reqVO.tenantId}
        <if test=" reqVO.postId != null">
            AND t1.leave_post_id = #{reqVO.postId}
        </if>
        <if test="reqVO.condition != null and reqVO.condition != ''">
            AND(
            t1.staff_name like concat('%', #{reqVO.condition}, '%') OR
            t1.work_num like concat('%', #{reqVO.condition}, '%')
            )
        </if>
        ORDER BY t1.modify_time DESC,t1.create_time DESC
    </select>

    <select id="findByWorkNum" resultType="com.huafon.models.entity.StaffLeaveRemind">
        SELECT
            t.id,
            t.staff_id staffId,
            t.staff_name staffName,
            t.work_num workNum,
            t.leave_date leaveDate,
            t.leave_department_id leaveDepartmentId,
            t.leave_post_id leavePostId,
            t.leave_department_ids leaveDepartmentIds
        FROM hf_org_staff_leave_remind t
        WHERE
            t.is_del = 0
        AND
            t.work_num = #{workNum}
        <if test="tenantId != null">
        AND t.tenant_id = #{tenantId}
        </if>
    </select>

</mapper>