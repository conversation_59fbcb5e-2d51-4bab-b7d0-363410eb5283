<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huafon.dao.StaffMapper">

    <!--    <insert id="batchInsert">-->
    <!--        <foreach collection="list" item="emp" separator=";">-->
    <!--            INSERT-->
    <!--            INTO-->
    <!--            hf_org_staff-->
    <!--            <trim prefix="(" suffix=")" suffixOverrides=",">-->
    <!--                <if test="emp.departmentId != null">-->
    <!--                    department_id,-->
    <!--                </if>-->
    <!--                <if test="emp.departmentName != null and emp.departmentName != ''">-->
    <!--                    department_name,-->
    <!--                </if>-->
    <!--                <if test="emp.postId != null">-->
    <!--                    post_id,-->
    <!--                </if>-->
    <!--                <if test="emp.postName != null and emp.postName != ''">-->
    <!--                    post_name,-->
    <!--                </if>-->
    <!--                <if test="emp.staffCode != null and emp.staffCode != ''">-->
    <!--                    staff_code,-->
    <!--                </if>-->
    <!--                <if test="emp.staffName != null and emp.staffName != ''">-->
    <!--                    staff_name,-->
    <!--                </if>-->
    <!--                <if test="emp.workerNum != null and emp.workerNum != ''">-->
    <!--                    worker_num,-->
    <!--                </if>-->
    <!--                <if test="emp.status != null">-->
    <!--                    status,-->
    <!--                </if>-->
    <!--                <if test="emp.staffType != null">-->
    <!--                    staff_type,-->
    <!--                </if>-->
    <!--                <if test="emp.email != null and emp.email != ''">-->
    <!--                    email,-->
    <!--                </if>-->
    <!--                <if test="emp.gender != null and emp.gender != ''">-->
    <!--                    gender,-->
    <!--                </if>-->
    <!--                <if test="emp.landline != null and emp.landline != ''">-->
    <!--                    landline,-->
    <!--                </if>-->
    <!--                <if test="emp.mobile != null and emp.mobile != ''">-->
    <!--                    mobile,-->
    <!--                </if>-->
    <!--                <if test="emp.createBy != null">-->
    <!--                    create_by,-->
    <!--                </if>-->
    <!--                <if test="emp.createTime != null">-->
    <!--                    create_time,-->
    <!--                </if>-->
    <!--                <if test="emp.isDel != null">-->
    <!--                    is_del,-->
    <!--                </if>-->
    <!--            </trim>-->
    <!--            <trim prefix="values (" suffix=")" suffixOverrides=",">-->
    <!--                <if test="emp.departmentId != null">-->
    <!--                    #{emp.departmentId,jdbcType=INTEGER},-->
    <!--                </if>-->
    <!--                <if test="emp.departmentName != null and emp.departmentName != ''">-->
    <!--                    #{emp.departmentName,jdbcType=VARCHAR},-->
    <!--                </if>-->
    <!--                <if test="emp.postId != null">-->
    <!--                    #{emp.postId,jdbcType=INTEGER},-->
    <!--                </if>-->
    <!--                <if test="emp.postName != null and emp.postName != ''">-->
    <!--                    #{emp.postName,jdbcType=VARCHAR},-->
    <!--                </if>-->
    <!--                <if test="emp.staffCode != null and emp.staffCode != ''">-->
    <!--                    #{emp.staffCode,jdbcType=VARCHAR},-->
    <!--                </if>-->
    <!--                <if test="emp.staffName != null and emp.staffName != ''">-->
    <!--                    #{emp.staffName,jdbcType=VARCHAR},-->
    <!--                </if>-->
    <!--                <if test="emp.workerNum != null and emp.workerNum != ''">-->
    <!--                    #{emp.workerNum,jdbcType=VARCHAR},-->
    <!--                </if>-->
    <!--                <if test="emp.status != null">-->
    <!--                    #{emp.status,jdbcType=INTEGER},-->
    <!--                </if>-->
    <!--                <if test="emp.staffType != null">-->
    <!--                    #{emp.staffType,jdbcType=INTEGER},-->
    <!--                </if>-->
    <!--                <if test="emp.email != null and emp.email != ''">-->
    <!--                    #{emp.email,jdbcType=VARCHAR},-->
    <!--                </if>-->
    <!--                <if test="emp.gender != null and emp.gender != ''">-->
    <!--                    #{emp.gender,jdbcType=VARCHAR},-->
    <!--                </if>-->
    <!--                <if test="emp.landline != null and emp.landline != ''">-->
    <!--                    #{emp.landline,jdbcType=VARCHAR},-->
    <!--                </if>-->
    <!--                <if test="emp.mobile != null and emp.mobile != ''">-->
    <!--                    #{emp.mobile,jdbcType=VARCHAR},-->
    <!--                </if>-->
    <!--                <if test="emp.createBy != null">-->
    <!--                    #{emp.createBy,jdbcType=INTEGER},-->
    <!--                </if>-->
    <!--                <if test="emp.createTime != null">-->
    <!--                    #{emp.createTime,jdbcType=TIMESTAMP},-->
    <!--                </if>-->
    <!--                <if test="emp.isDel != null">-->
    <!--                    #{emp.isDel,jdbcType=INTEGER},-->
    <!--                </if>-->
    <!--            </trim>-->
    <!--        </foreach>-->
    <!--    </insert>-->

    <update id="updateDelFlagById">
        <foreach collection="list" item="emp" separator=";">
            UPDATE
            hf_org_staff
            <set>
                <if test="emp.isDel != null">
                    is_del = #{emp.isDel}
                </if>
                <if test="emp.modifyBy != null">
                    ,modify_by = #{emp.modifyBy}
                </if>
                <if test="emp.modifyTime != null">
                    ,modify_time = #{emp.modifyTime}
                </if>
            </set>
            WHERE
            id = #{emp.id}
        </foreach>
    </update>

    <select id="findByWorkNum" resultType="com.huafon.models.entity.Staff">
        SELECT t.id,
               t.avatar_url     avatarUrl,
               t.staff_name     staffName,
               t.work_num       workNum,
               t.department_id  departmentId,
               t.department_ids departmentIds,
               t.post_id        postId,
               t.staff_status   staffStatus,
               t.tenant_id      tenantId
        FROM hf_org_staff t
        WHERE t.is_del = 0
          AND t.staff_status = 0
          AND t.work_num = #{workNum}
    </select>

    <select id="countTotal" resultType="java.lang.Integer">
        SELECT count(*)
        FROM hf_org_staff t1 LEFT JOIN hf_org_department t2 ON t1.department_id = t2.id AND t2.is_del = 0
        LEFT JOIN hf_org_post t3 ON t1.post_id = t3.id AND t3.is_del = 0
        WHERE
        t1.is_del = 0
        <if test="staffStatus != null">
            AND staff_status = #{staffStatus}
        </if>
        <if test="departmentIds != null and departmentIds.size>0">
            AND t1.department_id in
            <foreach collection="departmentIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="condition != null and condition != ''">
            AND(
            t1.staff_name like concat('%', #{condition}, '%') OR
            t1.mobile like concat('%', #{condition}, '%') OR
            t2.department_name like concat('%', #{condition}, '%')
            )
        </if>
    </select>

    <select id="findStaffPage" resultType="com.huafon.models.vo.StaffPageRespVO">
        SELECT
        t1.id,
        t1.avatar_url avatarUrl,
        t1.staff_name staffName,
        t1.work_num workNum,
        t2.department_code departmentCode,
        t2.department_name departmentName,
        t1.department_id departmentId,
        t1.department_ids departmentIds,
        t1.post_id postId,
        t3.post_name postName,
        t1.mobile,
        t1.gender,
        t1.staff_status staffStatus,
        t1.email,
        t1.entry_time entryTime,
        t1.leave_time leaveTime,
        t1.id_number idNumber,
        t1.leave_reason leaveReason
        FROM hf_org_staff t1 LEFT JOIN hf_org_department t2 ON t1.department_id = t2.id AND t2.is_del = 0
        LEFT JOIN hf_org_post t3 ON t1.post_id = t3.id AND t3.is_del = 0
        WHERE
        t1.is_del = 0
        AND t1.tenant_id = #{reqVO.tenantId}
        <if test=" reqVO.staffStatus != null">
            AND t1.staff_status = #{reqVO.staffStatus}
        </if>
        <if test="reqVO.departmentIds != null and reqVO.departmentIds.size>0">
            AND t1.department_id in
            <foreach collection="reqVO.departmentIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqVO.condition != null and reqVO.condition != ''">
            AND(
            t1.staff_name like concat('%', #{reqVO.condition}, '%') OR
            t1.work_num like concat('%', #{reqVO.condition}, '%') OR
            t1.mobile like concat('%', #{reqVO.condition}, '%')
            )
        </if>
        ORDER BY t1.modify_time DESC,t1.create_time DESC,t1.id ASC
    </select>

    <select id="getInfoById" resultType="com.huafon.models.vo.StaffVO">
        SELECT t1.id,
               t1.avatar_url              avatarUrl,
               t1.staff_name              staffName,
               t1.work_num                workNum,
               t1.id_number               idNumber,
               t1.gender,
               t1.post_id                 postId,
               t2.post_name               postName,
               t1.staff_status            staffStatus,
               t1.email,
               t1.mobile,
               t1.birthday,
               t1.entry_time              entryTime,
               t1.present_address         presentAddress,
               t1.present_detail_address  presentDetailAddress,
               t1.domicile_type           domicileType,
               t1.domicile_address        domicileAddress,
               t1.domicile_detail_address domicileDetailAddress,
               t1.politics_status         politicsStatus,
               t1.education,
               t1.education_date          educationDate,
               t1.graduation_unit         graduationUnit,
               t1.major,
               t1.department_ids          departmentIds,
               t1.department_id           departmentId,
               t1.leave_time              leaveTime,
               t1.leave_reason            leaveReason,
               t1.leader_work_num         leaderWorkNum,
               t1.is_exist_leader         isExistLeader
        FROM hf_org_staff t1
                 LEFT JOIN hf_org_post t2 ON t1.post_id = t2.id AND t2.is_del = 0
        WHERE t1.is_del = 0
          AND t1.id = #{id}
    </select>

    <select id="findByPostId" resultType="com.huafon.models.entity.Staff">
        SELECT t1.id,
               t1.avatar_url avatarUrl,
               t1.staff_name staffName,
               t1.work_num   workNum,
               t1.id_number  idNumber,
               t1.gender,
               t1.email,
               t1.mobile
        FROM hf_org_staff AS t1
        WHERE t1.is_del = 0
          AND t1.staff_status = 0
          AND t1.post_id = #{postId}
    </select>

    <select id="findByDepartmentId" resultType="com.huafon.models.entity.Staff">
        SELECT t.id
        FROM hf_org_staff t
        WHERE t.is_del = 0
        AND
        t.staff_status = 0
        <if test="departmentId != null">
            AND t.department_id = #{departmentId}
        </if>
    </select>

    <select id="findWorkNumsByStaffIds" resultType="java.lang.String">
        SELECT t.work_num
        FROM hf_org_staff t
        WHERE
        t.id in
        <foreach open="(" collection="ids" index="index" item="id" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="findWaitOnJobByWorkNum" resultType="com.huafon.models.entity.Staff">
        SELECT t.id,
               t.avatar_url     avatarUrl,
               t.staff_name     staffName,
               t.work_num       workNum,
               t.department_id  departmentId,
               t.department_ids departmentIds,
               t.post_id        postId,
               t.staff_status   staffStatus
        FROM hf_org_staff t
        WHERE t.is_del = 0
          AND t.staff_status = 2
          AND t.work_num = #{workNum}
          AND t.tenant_id = #{tenantId}
    </select>

    <select id="getStaffOrganizationInfoByWorkNum" resultType="com.huafon.api.dto.StaffOrganizationInfoRpcDTO">
        SELECT
        t2.department_name departmentName,
        t1.department_id departmentId,
        t1.post_id postId,
        t3.post_name postName,
        t1.staff_name staffName,
        t1.id staffId,
        t1.work_num workNum,
        t1.mobile,
        t1.id_number idNumber,
        t1.gender,
        t1.birthday
        FROM hf_org_staff t1 LEFT JOIN hf_org_department t2 ON t1.department_id = t2.id AND t2.is_del = 0
        LEFT JOIN hf_org_post t3 ON t1.post_id = t3.id AND t3.is_del = 0
        WHERE
        t1.is_del = 0
        AND t1.staff_status = 0
        AND t1.work_num = #{workNum}
        <if test="tenantId != null">
            AND t1.tenant_id = #{tenantId}
        </if>
        LIMIT 1
    </select>

    <resultMap id="orgPostIdWithStaff" type="com.huafon.api.dto.PostWithStaffsRpcDTO">
        <result column="postId" property="postId"/>
        <result column="postName" property="postName"/>
        <collection property="staffList" ofType="com.huafon.api.dto.StaffInfoByPostIdRpcDTO">
            <id column="staffId" property="staffId"/>
            <result column="staffName" property="staffName"/>
            <result column="workNum" property="workNum"/>
            <result column="departmentId" property="departmentId"/>
            <result column="mobile" property="mobile"/>
            <result column="avatar_url" property="avatarUrl"/>
        </collection>
    </resultMap>

    <select id="getStaffListByPostIds" resultMap="orgPostIdWithStaff">
        SELECT
        t1.id staffId,
        t1.staff_name staffName,
        t1.work_num workNum,
        t1.department_id departmentId,
        t1.post_id postId,
        t1.mobile,
        t1.avatar_url,
        t3.post_name postName
        FROM hf_org_staff t1
        LEFT JOIN hf_org_post t3 ON t1.post_id = t3.id AND t3.is_del = 0
        WHERE
        t1.is_del = 0
        AND t1.staff_status = 0
        AND t1.post_id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getByName" resultType="com.huafon.models.vo.StaffWithOrganizationVO">
        SELECT t1.id              staffId,
               t1.avatar_url      avatarUrl,
               t1.staff_name      staffName,
               t1.work_num        workNum,
               t1.mobile,
               t1.id_number       idNumber,
               t1.department_id   departmentId,
               t2.department_name departmentName,
               t2.department_code departmentCode,
               t1.tenant_id       tenantId
        FROM hf_org_staff t1
                 LEFT JOIN hf_org_department t2 ON t1.department_id = t2.id AND t2.is_del = 0
        WHERE t1.is_del = 0
          AND t1.staff_status = 0
          AND t1.tenant_id = #{tenantId}
          AND t1.staff_name like concat('%', #{staffName}, '%')
    </select>

    <select id="findByIdNumber" resultType="com.huafon.models.entity.Staff">
        SELECT t.id,
               t.avatar_url     avatarUrl,
               t.staff_name     staffName,
               t.work_num       workNum,
               t.department_id  departmentId,
               t.department_ids departmentIds,
               t.post_id        postId,
               t.staff_status   staffStatus
        FROM hf_org_staff t
        WHERE t.is_del = 0
          AND t.staff_status = 0
          AND upper(t.id_number) = #{idNumber}
          AND t.tenant_id = #{tenantId}
    </select>

    <select id="findByInId" resultType="com.huafon.api.dto.StaffByIdsRpcDTO">
        SELECT
        t.id staffId,
        t.avatar_url avatarUrl,
        t.staff_name staffName,
        t.mobile,
        t.work_num workNum
        FROM hf_org_staff t
        WHERE t.is_del = 0
        AND t.id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findById" resultType="com.huafon.api.dto.StaffByIdsRpcDTO">
        SELECT t.id               staffId,
               t.avatar_url       avatarUrl,
               t.staff_name       staffName,
               t.work_num         workNum,
               t.department_id    departmentId,
               t.mobile,
               t2.department_name departmentName
        FROM hf_org_staff t
                 LEFT JOIN hf_org_department t2 ON t.department_id = t2.id AND t2.is_del = 0
        WHERE t.is_del = 0
          AND t.id = #{id}
    </select>

    <select id="getStaffWithOrganizationById" resultType="com.huafon.models.vo.StaffWithOrganizationVO">
        SELECT t1.id              staffId,
               t1.avatar_url      avatarUrl,
               t1.staff_name      staffName,
               t1.work_num        workNum,
               t1.mobile,
               t1.id_number       idNumber,
               t1.department_id   departmentId,
               t2.department_name departmentName,
               t2.tenant_id       tenantId
        FROM hf_org_staff t1
                 INNER JOIN hf_org_department t2 ON t1.department_id = t2.id AND t2.is_del = 0
        WHERE t1.is_del = 0
          AND t1.staff_status = 0
          AND t1.id = #{staffId}
    </select>

    <select id="getStaffOrganizationInfoByWorkNums"
            resultType="com.huafon.api.dto.StaffOrganizationInfoRpcDTO">
        SELECT
        t2.department_name departmentName,
        t1.department_id departmentId,
        t1.post_id postId,
        t3.post_name postName,
        t1.staff_name staffName,
        t1.id staffId,
        t1.work_num workNum,
        t1.mobile,
        t1.id_number idNumber,
        t1.gender,
        t1.birthday
        FROM hf_org_staff t1 LEFT JOIN hf_org_department t2 ON t1.department_id = t2.id AND t2.is_del = 0
        LEFT JOIN hf_org_post t3 ON t1.post_id = t3.id AND t3.is_del = 0
        WHERE
        t1.is_del = 0
        AND t1.staff_status = 0
        <if test="tenantId != null">
            AND t1.tenant_id = #{tenantId}
        </if>
        AND t1.work_num IN
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findByWorkNumAndTenantId" resultType="com.huafon.models.entity.Staff">
        SELECT t.id,
               t.avatar_url     avatarUrl,
               t.staff_name     staffName,
               t.work_num       workNum,
               t.department_id  departmentId,
               t.department_ids departmentIds,
               t.post_id        postId,
               t.staff_status   staffStatus
        FROM hf_org_staff t
        WHERE t.is_del = 0
          AND t.staff_status = 0
          AND t.work_num = #{workNum}
          AND t.tenant_id = #{tenantId}
    </select>

    <select id="getInfoByWorkNum" resultType="com.huafon.models.vo.StaffVO">
        SELECT t1.id,
               t1.avatar_url              avatarUrl,
               t1.staff_name              staffName,
               t1.work_num                workNum,
               t1.id_number               idNumber,
               t1.gender,
               t1.post_id                 postId,
               t2.post_name               postName,
               t1.staff_status            staffStatus,
               t1.email,
               t1.mobile,
               t1.birthday,
               t1.entry_time              entryTime,
               t1.present_address         presentAddress,
               t1.present_detail_address  presentDetailAddress,
               t1.domicile_type           domicileType,
               t1.domicile_address        domicileAddress,
               t1.domicile_detail_address domicileDetailAddress,
               t1.politics_status         politicsStatus,
               t1.education,
               t1.education_date          educationDate,
               t1.graduation_unit         graduationUnit,
               t1.major,
               t1.department_ids          departmentIds,
               t1.department_id           departmentId,
               t1.leave_time              leaveTime,
               t1.leave_reason            leaveReason,
               t3.department_name         departmentName
        FROM hf_org_staff t1
                 LEFT JOIN hf_org_post t2 ON t1.post_id = t2.id AND t2.is_del = 0
                 LEFT JOIN hf_org_department t3 ON t1.department_id = t3.id AND t3.is_del = 0
        WHERE t1.is_del = 0
          AND t1.tenant_id = #{tenantId}
          AND t1.work_num = #{workNum} LIMIT 1
    </select>

    <resultMap id="findDepartmentWithStaff" type="com.huafon.models.query.OrgDepartmentIdWithStaff">
        <result column="department_id" property="departmentId"/>
        <collection property="staffList" ofType="com.huafon.models.vo.StaffForDepartmentTreeVO">
            <result column="id" property="staffId"/>
            <result column="staff_name" property="staffName"/>
            <result column="work_num" property="workNum"/>
            <result column="id_number" property="idNumber"/>
            <result column="mobile" property="mobile"/>
            <result column="email" property="email"/>
            <result column="gender" property="gender"/>
            <result column="avatar_url" property="avatarUrl"/>
            <result column="postId" property="postId"/>
            <result column="postName" property="postName"/>
        </collection>
    </resultMap>

    <select id="findOrgDepartmentIdWithStaff" resultMap="findDepartmentWithStaff">
        SELECT t.id,
               t.staff_name,
               t.work_num,
               t.department_id,
               t.id_number,
               t.mobile,
               t.email,
               t.gender,
               t.avatar_url,
               t2.id postId,
               t2.post_name postName
        FROM hf_org_staff AS t LEFT JOIN hf_org_post t2 ON t.post_id = t2.id AND t2.is_del = 0
        WHERE t.is_del = 0
          AND t.staff_status = 0
          AND t.tenant_id = #{tenantId}
    </select>

    <select id="statisticsStaffByDept" resultType="com.huafon.models.vo.StatisticsStaffByDeptRespVO">
        SELECT t2.id departmentId,
               t2.department_name departmentName,
               count(*) personNum
        FROM "hf_org_staff" t1 INNER JOIN hf_org_department t2 ON t1.department_id = t2.id
        WHERE t1.is_del = 0
          AND t2.is_del = 0
          AND t1.staff_status = 0
        <if test="reqVO.departmentTypes != null and reqVO.departmentTypes.size>0 ">
            AND t2.department_type IN
            <foreach collection="reqVO.departmentTypes" index="index" item="departmentType" open="(" separator="," close=")">
                #{departmentType}
            </foreach>
        </if>
        <if test="list != null and list.size>0">
            AND t1.tenant_id IN
            <foreach collection="list" index="index" item="tenantId" open="(" separator="," close=")">
                #{tenantId}
            </foreach>
        </if>
        GROUP BY t2.id, t2.department_name
    </select>

    <select id="findByWorkNumsAndTenantId" resultType="com.huafon.models.entity.Staff">
        SELECT t.id,
               t.avatar_url     avatarUrl,
               t.staff_name     staffName,
               t.work_num       workNum,
               t.department_id  departmentId,
               t.department_ids departmentIds,
               t.post_id        postId,
               t.staff_status   staffStatus,
               t.mobile
        FROM hf_org_staff t
        WHERE t.is_del = 0
          AND t.staff_status = 0
          AND t.work_num IN
        <foreach collection="list" index="index" item="workNum" open="(" separator="," close=")">
            #{workNum}
        </foreach>
          AND t.tenant_id = #{tenantId}
    </select>

    <select id="countStaff" resultType="java.lang.Integer">
        SELECT count(*)
        FROM hf_org_staff t
        WHERE t.is_del = 0
          AND t.staff_status = 0
    </select>

    <select id="findWorkNumListByDeptIds" resultType="com.huafon.models.entity.Staff">
        SELECT t.work_num workNum,
               t.department_id departmentId
        FROM hf_org_staff t
        WHERE t.is_del = 0
          AND t.staff_status = 0
          AND t.department_id IN
        <foreach collection="list" index="index" item="departmentId" open="(" separator="," close=")">
            #{departmentId}
        </foreach>
    </select>

    <select id="getByLevels" resultType="com.huafon.api.dto.StaffDTO">
        with RECURSIVE t as
                           (
                               select a.id,a.work_num,a.department_id,a.staff_name,a.mobile,a.email,a.leader_work_num, a.is_del,0 as level
                               from hf_org_staff a
                               where id = #{id}
                                 and is_del = 0
                                 and tenant_id = #{tenantId}
                                 and staff_status = 0
                               union all
                               select k.id,k.work_num,k.department_id,k.staff_name,k.mobile,k.email, k.leader_work_num, k.is_del,level+1 as level
                               from hf_org_staff k,
                                    t c
                               where k.is_del = 0
                                 and tenant_id = #{tenantId}
                                 and staff_status = 0
                                 and c.leader_work_num = k.work_num
                           )
        select t.id staffId,t.work_num,t.department_id,t.staff_name,t.mobile,t.email,g.department_name,t.level
        from t LEFT JOIN hf_org_department g ON t.department_id = g.id AND g.is_del = 0
    </select>

    <select id="getWorkNumList" resultType="java.lang.String">
        SELECT t.work_num
        FROM hf_org_staff t
        WHERE t.is_del = 0
        AND t.staff_status = 0
        AND t.tenant_id = #{tenantId}
        <if test="departmentIds != null and departmentIds.size>0">
            AND t.department_id IN
            <foreach collection="departmentIds" index="index" item="departmentId" open="(" separator="," close=")">
               #{departmentId}
            </foreach>
        </if>
        <if test="postId != null">
            AND t.post_id = #{postId}
        </if>
        <if test="gender != null">
            AND t.gender = #{gender}
        </if>
        <if test="condition != null and condition != ''">
            AND t.staff_name like concat('%', #{condition}, '%')
        </if>
    </select>

    <select id="getByWorkNum" resultType="com.huafon.models.entity.Staff">
        SELECT t1.id,
               t1.avatar_url              avatarUrl,
               t1.staff_name              staffName,
               t1.work_num                workNum,
               t1.id_number               idNumber,
               t1.gender,
               t1.post_id                 postId,
               t1.staff_status            staffStatus,
               t1.email,
               t1.mobile,
               t1.birthday,
               t1.department_id           departmentId,
               t1.leave_time              leaveTime,
               t1.leave_reason            leaveReason,
               t1.leader_work_num         leaderWorkNum
        FROM hf_org_staff t1
        WHERE t1.is_del = 0
          AND t1.tenant_id = #{tenantId}
          AND t1.work_num = #{workNum}
    </select>

    <select id="getWorkNumListByPostId" resultType="java.lang.String">
        SELECT t.work_num
        FROM hf_org_staff t
        WHERE t.is_del = 0
        AND t.staff_status = 0
        AND t.tenant_id = #{tenantId}
        <if test="postIdList != null and postIdList.size>0">
            AND t.post_id IN
            <foreach collection="postIdList" index="index" item="postId" open="(" separator="," close=")">
                #{postId}
            </foreach>
        </if>
    </select>

</mapper>