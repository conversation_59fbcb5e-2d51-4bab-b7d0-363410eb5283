import com.alibaba.fastjson.JSONObject;
import com.huafon.Application;
import com.huafon.api.dto.StaffDTO;
import com.huafon.api.service.impl.IStaffRpcService;
import com.huafon.dao.StaffMapper;
import com.huafon.models.enums.StaffLevelEnum;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: TODO
 * @Date: 2022/11/15 4:24 PM
 * @Author: zyf
 **/
@SpringBootTest(classes = Application.class)
public class Test1 {

    @Autowired
    private StaffMapper staffMapper;

    @Autowired
    private IStaffRpcService staffRpcService;

    @Test
    public void test(){
        List<String> levelList = Arrays.asList("LEVELUP2");
        Set<StaffDTO> result =  new HashSet<>();
        List<StaffDTO> byLevels = staffMapper.getByLevels(3884L,1);
        byLevels = byLevels.stream().sorted(Comparator.comparing(StaffDTO::getLevel)).collect(Collectors.toList());

        int size = byLevels.size();
        for (String type:levelList){
            StaffLevelEnum byType = StaffLevelEnum.getByType(type);
            if (!Objects.isNull(byType)){
                int index = byType.getIndex();
                if (index<size) {
                    StaffDTO staffDTO = byLevels.get(index);
                    result.add(staffDTO);
                }
            }
        }
        System.out.println(JSONObject.toJSONString(result));
    }

    @Test
    public void test1(){
        List<String> levelList = Arrays.asList("LEVELUP2","LEVELUP1","SELF","LEVELUP3","LEVELUP4","LEVELUP5");
        Set<StaffDTO> byLevels1 = staffRpcService.getByLevels(3884L, 1, levelList);
        System.out.println(byLevels1);
    }

}