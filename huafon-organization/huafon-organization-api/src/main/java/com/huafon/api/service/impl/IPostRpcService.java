package com.huafon.api.service.impl;

import com.huafon.api.dto.PostRpcDTO;

import java.util.List;

/**
 * @Description: 岗位rpc接口
 * @Date: 2022/8/25 18:39
 * @Author: zyf
 **/
public interface IPostRpcService {

    /**
     * 查询当前租户下的所有岗位id
     *
     * @param tenantId
     * @return
     */
    List<Integer> getPostIdsByTenantId(Integer tenantId);

    /**
     * 通过名称模糊查询岗位信息
     * @param nameLike 模糊查询
     */
    List<PostRpcDTO> queryPostInfoNameLike(String nameLike,List<Integer> tenantIds);

    /**
     * 通过岗位ID列表批量查询岗位信息
     * 如果tenant不为空，则会使用岗位ID列表和租户ID共同过滤
     * @param postIds 岗位ID列表
     * @param tenant 租户ID
     */
    List<PostRpcDTO> queryPostInfoByPostIds(List<Long> postIds, Integer tenant);

}
