drop table if exists hf_iot_device_property;
create table hf_iot_device_property
(
    id                  serial
        constraint hf_iot_device_property_pk
            primary key,
    device_id           integer,
    module              varchar(16),
    action              varchar(32),
    property_name       varchar(32),
    property_identifier varchar(32),
    read_write          varchar(16),
    data_type           varchar(16),
    data_spec           varchar(512),
    current_status      varchar(32),
    create_time         timestamp,
    modify_time         timestamp,
    is_del              integer default 0
);

comment on table hf_iot_device_property is '设备属性';

comment on column hf_iot_device_property.device_id is '设备ID，关联iot_device.id';

comment on column hf_iot_device_property.module is '用于前端展示组件';

comment on column hf_iot_device_property.action is '动作，描述接口调用';

comment on column hf_iot_device_property.property_name is '属性名称';

comment on column hf_iot_device_property.property_identifier is '属性标识';

comment on column hf_iot_device_property.read_write is '读写类型';

comment on column hf_iot_device_property.data_type is '数据类型';

comment on column hf_iot_device_property.data_spec is '数据规范';

comment on column hf_iot_device_property.current_status is '当前状态';

alter table hf_iot_device_property
    owner to postgres;
