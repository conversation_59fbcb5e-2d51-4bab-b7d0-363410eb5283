drop table if exists hf_iot_device_enum;
create table hf_iot_device_enum
(
    id   serial
        constraint hf_iot_device_enum_pk
            primary key,
    name varchar(32) not null,
    type varchar(32) not null
);

comment on table hf_iot_device_enum is '设备类型(内部定义)';

alter table hf_iot_device_enum
    owner to postgres;

INSERT INTO hf_iot_device_enum (id, name, type) VALUES (1, '灯控', 'LIGHT');
INSERT INTO hf_iot_device_enum (id, name, type) VALUES (2, '窗帘', 'CURTAIN');
INSERT INTO hf_iot_device_enum (id, name, type) VALUES (3, '传感器', 'SENSOR');
INSERT INTO hf_iot_device_enum (id, name, type) VALUES (4, '插座', 'OUTLET');
INSERT INTO hf_iot_device_enum (id, name, type) VALUES (5, '报警器', 'ALARM');
INSERT INTO hf_iot_device_enum (id, name, type) VALUES (6, '空调', 'AIRCONDITION');
INSERT INTO hf_iot_device_enum (id, name, type) VALUES (7, '新风', 'FRESHAIR');

select setval('hf_iot_device_enum_id_seq', coalesce((select max(id) + 1 from hf_iot_device_enum), 1), false)
