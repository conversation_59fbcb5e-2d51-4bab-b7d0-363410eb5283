drop table if exists hf_iot_device_service;
create table hf_iot_device_service
(
    id            serial
        constraint hf_iot_device_service_pk
            primary key,
    device_id     integer not null,
    service_name  varchar(64),
    action        varchar(64),
    call_type     varchar(16),
    identifier    varchar(64),
    input_params  varchar(1024),
    output_params varchar(1024),
    create_time   timestamp,
    modify_time   timestamp,
    is_del        integer default 0
);

comment on table hf_iot_device_service is '设备服务';

comment on column hf_iot_device_service.device_id is '设备ID';

comment on column hf_iot_device_service.service_name is '服务名称';

comment on column hf_iot_device_service.action is '动作';

comment on column hf_iot_device_service.call_type is '调用方式(ASYNC异步，SYNC同步)';

comment on column hf_iot_device_service.identifier is '服务唯一标识';

comment on column hf_iot_device_service.input_params is '服务调用入参描述';

comment on column hf_iot_device_service.output_params is '服务调用出参数描述';

alter table hf_iot_device_service
    owner to postgres;
