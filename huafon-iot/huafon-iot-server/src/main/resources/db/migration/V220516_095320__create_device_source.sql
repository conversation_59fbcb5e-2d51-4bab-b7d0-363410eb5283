drop table if exists hf_iot_device_source;
create table hf_iot_device_source
(
    id                        serial
        constraint hf_iot_device_source_pk
            primary key,
    source                    varchar(32) not null,
    device_enum_id            integer     not null,
    source_product_identifier varchar(64),
    source_product_type       varchar(64),
    source_product_name       varchar(64),
    source_device_type        varchar(64),
    source_device_name        varchar(64),
    create_time               timestamp,
    modify_time               timestamp,
    is_del                    integer default 0
);

comment on table hf_iot_device_source is '设备信息(厂商定义)';

comment on column hf_iot_device_source.source is '设备来源(设备的厂商)';

comment on column hf_iot_device_source.device_enum_id is '设备关联内部定义的类型';

comment on column hf_iot_device_source.source_product_identifier is '厂商产品唯一码(ProductKey)';

comment on column hf_iot_device_source.source_product_type is '厂商产品类型';

comment on column hf_iot_device_source.source_product_name is '厂商产品名称';

comment on column hf_iot_device_source.source_device_type is '厂商设备类型';

comment on column hf_iot_device_source.source_device_name is '厂商设备名称';

alter table hf_iot_device_source
    owner to postgres;

-- 1-500灯控
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_LIGHT', '普通灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_CHOPIN_LIGHT_PROJECT', '普通灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_CHOPIN_LIGHT_CUSTOM', '普通灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_STAR_LIGHT', '普通灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_HOTEL_DOOR_PLATE', '普通灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_CARD_SWITCH', '普通灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_HAYDN_LIGHT', '普通灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_VOICEPANEL_LIGHT', '普通灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'LAFITE_ZIGBEE_LIGHT', '普通灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_ZS3_CHOPIN_LIGHT', '普通灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_LIGHT_MODULE', '普通灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_MOORE_4_SWITCH', '普通灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_CARD_SWITCH_GUANGNIAN', '普通灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'ACG_ZIGBEE_3_SWITCH', '普通灯', now(), null, 0);

--501-1000 可调光
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_DIMMER_LIGHT', '可调光灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_RGB_LIGHTSTRIP', '可调光灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_ZEROFIREWIRE_DIMMER', '可调光灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_SINGLEFIREWIRE_DIMMER', '可调光灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_RGBW_LIGHTSTRIP', '可调光灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_ZEROFIREWIRE_DIMMER_2G', '可调光灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_CHOPIN_DIMMER_LIGHT', '可调光灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_FOUR_CHANNEL_DIMMER', '可调光灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_LIGHT_MODEL', '可调光灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_HAYDN_DIMMER_LIGHT', '可调光灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_HAYDN_DIMMER_PALETTE_LIGHT', '可调光灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_HAYDN_DIMMER_MODULE_PRO', '可调光灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_YAOYE_DIMMER', '可调光灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_YAOYE_DOUBLE_DIMMER', '可调光灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_YAOYE_RGBCW_DIMMER', '可调光灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_MEIGUI_LIGHT', '可调光灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'KONKE_ZIGBEE_Z3S_DIMMER_MODULE', '可调光灯', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 1, null, null, null, 'ACG_ZIGBEE_3_DIMMER', '可调光灯', now(), null, 0);

-- 1001-1500 窗帘
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 2, null, null, null, 'KONKE_ZIGBEE_CURTAIN', '窗帘', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 2, null, null, null, 'KONKE_ZIGBEE_RollingGate', '窗帘', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 2, null, null, null, 'KONKE_ZIGBEE_DooYa', '窗帘', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 2, null, null, null, 'KONKE_ZIGBEE_WindowPusher', '窗帘', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 2, null, null, null, 'KONKE_ZIGBEE_WISTARMOTOR', '窗帘', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 2, null, null, null, 'KONKE_ZIGBEE_CHOPIN_CURTAIN_PROJECT', '窗帘', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 2, null, null, null, 'KONKE_ZIGBEE_CHOPIN_CURTAIN_CUSTOM', '窗帘', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 2, null, null, null, 'KONKE_ZIGBEE_STAR_CURTAIN', '窗帘', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 2, null, null, null, 'KONKE_ZIGBEE_CHOPIN_DRY_CONTACT_CURTAIN_PROJECT', '窗帘', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 2, null, null, null, 'KONKE_ZIGBEE_CHOPIN_DRY_CONTACT_CURTAIN_CUSTOM', '窗帘', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 2, null, null, null, 'KONKE_ZIGBEE_STAR_DRY_CONTACT_CURTAIN', '窗帘', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 2, null, null, null, 'KONKE_ZIGBEE_3_WISTARMOTOR', '窗帘', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 2, null, null, null, 'KONKE_ZIGBEE_HAYDN_CURTAIN', '窗帘', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 2, null, null, null, 'LAFITE_ZIGBEE_CURTAIN', '窗帘', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 2, null, null, null, 'KONKE_ZIGBEE_3_ROLLING_SHUTTER_MOTOR', '窗帘', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 2, null, null, null, 'ACG_ZIGBEE_3_CURTAIN', '窗帘', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 2, null, null, null, 'ACG_ZIGBEE_3_MOTOR', '窗帘', now(), null, 0);

-- 2001-2500 插座类
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 4, null, null, null, 'KONKE_ZIGBEE_SOCKET', '插座', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 4, null, null, null, 'KONKE_ZIGBEE_K2PRO_SOCKET', '插座', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 4, null, null, null, 'KONKE_ZIGBEE_10A_SOCKET', '插座', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 4, null, null, null, 'KONKE_ZIGBEE_16A_SOCKET', '插座', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 4, null, null, null, 'KONKE_ZIGBEE_Extension_SOCKET', '插座', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 4, null, null, null, 'KONKE_ZIGBEE_CHOPIN_10A_SOCKET', '插座', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 4, null, null, null, 'KONKE_ZIGBEE_CHOPIN_16A_SOCKET', '插座', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 4, null, null, null, 'KONKE_ZIGBEE_HAYDN_SOCKET', '插座', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 4, null, null, null, 'KONKE_ZIGBEE_MEIGUI_SOCKET', '插座', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 4, null, null, null, 'ACG_ZIGBEE_3_SOCKET', '插座', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 4, null, null, null, 'ACG_ZIGBEE_3_MEASURE_SOCKET', '插座', now(), null, 0);

-- 3001-3500 布尔传感器
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 3, null, null, null, 'KONKE_ZIGBEE_HUMANBODYINFRAREDSENSOR', '传感器', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 3, null, null, null, 'KONKE_ZIGBEE_SMOKESENSOR', '传感器', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 3, null, null, null, 'KONKE_ZIGBEE_GASSENSOR', '传感器', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 3, null, null, null, 'KONKE_ZIGBEE_WATERSENSOR', '传感器', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 3, null, null, null, 'KONKE_ZIGBEE_UNIVERSALSENSOR', '传感器', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 3, null, null, null, 'KONKE_ZIGBEE_SMARTBODYSENSOR', '传感器', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 3, null, null, null, 'KONKE_ZIGBEE_KIT_SMARTBODYSENSOR', '传感器', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 3, null, null, null, 'KONKE_ZIGBEE_INFRAREDCURTAIL', '传感器', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 3, null, null, null, 'HORN_ZIGBEE_SMOKESENSOR', '传感器', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 3, null, null, null, 'HORN_ZIGBEE_GASSENSOR', '传感器', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 3, null, null, null, 'ACG_ZIGBEE_3_SENSOR_BODY_INFRARED', '传感器', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 3, null, null, null, 'ACG_ZIGBEE_3_SENSOR_CURTAIN_INFRARED', '传感器', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 3, null, null, null, 'ACG_ZIGBEE_3_SENSOR_INFRARED_RADIATION', '传感器', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 3, null, null, null, 'ACG_ZIGBEE_3_SENSOR_SMOKE', '传感器', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 3, null, null, null, 'ACG_ZIGBEE_3_SENSOR_GAS', '传感器', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 3, null, null, null, 'ACG_ZIGBEE_3_SENSOR_CO', '传感器', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 3, null, null, null, 'ACG_ZIGBEE_3_TVOC', '传感器', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 3, null, null, null, 'ACG_ZIGBEE_3_SENSOR_WATER', '传感器', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 3, null, null, null, 'ACG_ZIGBEE_3_DOOR_CONTACT', '传感器', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 3, null, null, null, 'KONKE_ZIGBEE_KIT_DoorContact', '传感器', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 3, null, null, null, 'KONKE_ZIGBEE_DOORCONTACT', '传感器', now(), null, 0);

-- 11001-11002 报警器
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 5, null, null, null, 'KONKE_ZIGBEE_ALERTOR', '报警器', now(), null, 0);
INSERT INTO hf_iot_device_source (source, device_enum_id, source_product_identifier, source_product_type, source_product_name, source_device_type, source_device_name, create_time, modify_time, is_del) VALUES ('IKONKE', 5, null, null, null, 'KONKE_ZIGBEE_BANGDE_ALERTOR', '邦德报警器', now(), null, 0);


select setval('hf_iot_device_source_id_seq', coalesce((select max(id) + 1 from hf_iot_device_source), 1), false)
