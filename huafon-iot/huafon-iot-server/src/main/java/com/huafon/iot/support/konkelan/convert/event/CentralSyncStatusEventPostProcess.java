package com.huafon.iot.support.konkelan.convert.event;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.huafon.iot.config.support.WebSocketCommonHandler;
import com.huafon.iot.dao.mapper.DeviceInfoMapper;
import com.huafon.iot.models.dto.WSDeviceStatus;
import com.huafon.iot.models.entity.DeviceInfo;
import com.huafon.iot.support.konkelan.entity.EventPushMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.huafon.iot.support.konkelan.convert.event.DeviceHwInfoEventPostProcess.processDeviceStatus;

/**
 * 同步中控下的设备状态
 *
 * <AUTHOR>
 * @since 2022-06-02 14:57
 **/
@Slf4j
@Component
public class CentralSyncStatusEventPostProcess implements LanEventPostProcess {

	private final DeviceInfoMapper deviceInfoMapper;
	private final WebSocketCommonHandler webSocketCommonHandler;

	@Autowired
	public CentralSyncStatusEventPostProcess(DeviceInfoMapper deviceInfoMapper, WebSocketCommonHandler webSocketCommonHandler) {
		this.deviceInfoMapper = deviceInfoMapper;
		this.webSocketCommonHandler = webSocketCommonHandler;
	}

	@Override
	public boolean support(String eventType) {
		return "INTERNAL_SYNC_DEVICE_STATUS".equals(eventType);
	}

	@Override
	public void postProcess(EventPushMessage message) {
		JSONArray array = (JSONArray) message.getArg();
		for (int i = 0; i < array.size(); i++) {
			JSONObject current = array.getJSONObject(i);
			if (current.containsKey("mac") && current.containsKey("onlineStatus")) {
				String mac = current.getString("mac");
				String online = processDeviceStatus(current.getInteger("onlineStatus"));
				List<DeviceInfo> deviceInfos = deviceInfoMapper.searchDeviceInfoByMac(SOURCE, message.getCcuID(), mac);
				for (DeviceInfo deviceInfo : deviceInfos) {
					deviceInfoMapper.updateDeviceStatus(deviceInfo.getDeviceId(), online);
					webSocketCommonHandler.sendMessage(new WSDeviceStatus(deviceInfo.getDeviceId(), online));
				}
			}
		}
	}
}
