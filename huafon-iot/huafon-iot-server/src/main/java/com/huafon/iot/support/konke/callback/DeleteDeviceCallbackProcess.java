package com.huafon.iot.support.konke.callback;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.huafon.iot.dao.mapper.DeviceInfoMapper;
import com.huafon.iot.models.dto.KonKeEventPushDTO;
import com.huafon.iot.models.entity.DeviceInfo;
import com.huafon.iot.service.DeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-05-10 17:59
 **/
@Component
@Slf4j
public class DeleteDeviceCallbackProcess implements KonKeCallbackProcess {

	@Autowired
	DeviceInfoMapper deviceInfoMapper;
	@Autowired
	DeviceService deviceService;

	@PostConstruct
	public void init() {
		KonKeCallbackStrategyFactory.register("DevicesDeletePushEvent", this);
	}


	@Override
	public void process(KonKeEventPushDTO eventPushDTO) {
		log.info("【中控删除设备推送】{}", eventPushDTO.getPushCcuId());
		String ccuID = eventPushDTO.getPushCcuId();
		JSONObject pushMsg = eventPushDTO.getPushMsg();
		JSONArray deleteDevices = pushMsg.getJSONArray("deleteDevices");
		List<Integer> willDelete = new ArrayList<>();
		for (int i = 0; i < deleteDevices.size(); i++) {
			JSONObject currentDevice = deleteDevices.getJSONObject(i);
			if (currentDevice == null) {
				continue;
			}
			String identify = currentDevice.getString("id");
			DeviceInfo deviceInfo = deviceInfoMapper.findDeviceBySourceAndIdentifyAndCentral(SOURCE, identify, ccuID);
			if (deviceInfo != null && deviceInfo.getDeviceId() != null) {
				willDelete.add(deviceInfo.getDeviceId());
			}
		}

		deviceService.deleteDevices(willDelete);
	}
}
