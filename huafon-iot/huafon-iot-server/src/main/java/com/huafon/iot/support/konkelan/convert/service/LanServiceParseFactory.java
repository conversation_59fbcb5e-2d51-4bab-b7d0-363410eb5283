package com.huafon.iot.support.konkelan.convert.service;

import com.huafon.iot.models.entity.DeviceInvokeService;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-05-20 16:00
 **/
public class LanServiceParseFactory {

	static List<LanServiceParse> parses = new ArrayList<>();

	static {

	}

	public static List<DeviceInvokeService> parseService(Integer operateType) {
		if (operateType == null) {
			return new ArrayList<>();
		}

		for (LanServiceParse parse : parses) {
			if (parse.support(operateType)) {
				return parse.parse(operateType);
			}
		}
		return new ArrayList<>();
	}

}
