package com.huafon.iot.support.konkelan.convert.event;

import com.alibaba.fastjson.JSONObject;
import com.huafon.iot.config.support.WebSocketCommonHandler;
import com.huafon.iot.dao.mapper.DeviceInfoMapper;
import com.huafon.iot.dao.mapper.DevicePropertyMapper;
import com.huafon.iot.models.dto.WSPropertyStatus;
import com.huafon.iot.models.entity.DeviceInfo;
import com.huafon.iot.support.konkelan.convert.status.AlarmStatusParse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static com.huafon.iot.support.konkelan.convert.event.PowerNotifyEventPostProcess.LOW_BATTERY_THRESHOLD;

/**
 * <AUTHOR>
 * @see AlarmStatusParse
 * @since 2022-05-25 10:06
 **/
@Slf4j
@Component
public class AlarmEventPostProcess extends AbstractLanEventPostProcess {


	private final DevicePropertyMapper devicePropertyMapper;
	private final WebSocketCommonHandler webSocketCommonHandler;

	@Autowired
	public AlarmEventPostProcess(DeviceInfoMapper deviceInfoMapper, DevicePropertyMapper devicePropertyMapper, WebSocketCommonHandler webSocketCommonHandler) {
		super(deviceInfoMapper);
		this.devicePropertyMapper = devicePropertyMapper;
		this.webSocketCommonHandler = webSocketCommonHandler;
	}

	@Override
	public boolean support(String eventType) {
		return "ZIGBEE_ALERTOR_STATUS".equals(eventType);
	}

	/**
	 * @see AlarmStatusParse
	 */
	@Override
	protected void doPostProcess(DeviceInfo deviceInfo, String eventType, Object arg) {
		JSONObject argument = (JSONObject) arg;
		Integer deviceId = deviceInfo.getDeviceId();
		if (argument.containsKey("alarm")) {
			boolean alarm = Objects.equals(1, argument.getInteger("alarm"));
			int result = devicePropertyMapper.updateDevicePropertyStatus(deviceId, "ALARM_STATUS_TRANSFER",
					"ALARM_STATUS_TRANSFER", Boolean.toString(alarm));
			if (result > 0) {
				webSocketCommonHandler.sendMessage(new WSPropertyStatus(deviceId, "ALARM_STATUS_TRANSFER", alarm));
			}
		}
		if (argument.containsKey("sound")) {
			Integer sound = argument.getInteger("sound");
			if (!Objects.equals(255, sound)) {
				int result = devicePropertyMapper.updateDevicePropertyStatus(deviceId, GENERAL_ACTION,
						"ADJUST_VOLUME", Integer.toString(sound));
				if (result > 0) {
					webSocketCommonHandler.sendMessage(new WSPropertyStatus(deviceId, "ADJUST_VOLUME", sound));
				}
			}
		}
		if (argument.containsKey("power")) {
			Integer power = argument.getInteger("power");
			String type = Objects.equals(0, power) ? "市电" : Objects.equals(1, power) ? "电池" : "其他";
			int result = devicePropertyMapper.updateDevicePropertyStatus(deviceId, "power",
					"power", type);
			if (result > 0) {
				webSocketCommonHandler.sendMessage(new WSPropertyStatus(deviceId, "power", type));
			}
		}
		if (argument.containsKey("battery")) {
			Integer battery = argument.getInteger("battery");
			if (!Objects.equals(255, battery)) {
				int result = devicePropertyMapper.updateDevicePropertyStatus(deviceId, "battery", "battery", battery.toString());
				if (result > 0 && battery <= LOW_BATTERY_THRESHOLD) {
					webSocketCommonHandler.sendMessage(new WSPropertyStatus(deviceId, "battery", result));
				}
			}
		}

	}
}
