package com.huafon.iot.models.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-05-05 19:40
 **/
@Data
public class CommonResult implements Serializable {
	private String info;
	private Boolean showInfo = false;
	private Boolean success = true;
	private Object data;


	public CommonResult() {
	}

	public CommonResult(Object data) {
		this.data = data;
	}


	public CommonResult(Boolean success, Boolean showInfo, String info) {
		this.info = info;
		this.showInfo = showInfo;
		this.success = success;
	}
}
