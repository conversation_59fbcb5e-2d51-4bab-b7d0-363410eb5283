package com.huafon.iot.models.entity;

import lombok.Data;

/**
 * 厂商设备(已接入的设备)
 *
 * <AUTHOR>
 * @since 2022-05-05 11:01
 **/
@Data
public class DeviceSource {
	private String source;//来源:设备的厂商
	private String sourceProductIdentifier;//厂商产品唯一码(ProductKey)
	private String sourceProductType;//厂商产品类型
	private String sourceProductName;//厂商产品名称
	private String sourceDeviceType;//厂商设备类型
	private String sourceDeviceName;//厂商设备名称
	private DeviceEnum deviceEnum;
}
