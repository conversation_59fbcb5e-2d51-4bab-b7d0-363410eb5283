package com.huafon.iot.support.konkelan.convert.status;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.huafon.iot.models.entity.DeviceProperty;
import org.apache.commons.lang3.Range;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2022-05-20 14:59
 **/
public class LanStatusParseFactory {

	static Map<String, LanStatusParse> parses = new HashMap<>();

	static {
		parses.put("SWITCH", new SwitchStatusParse());//开关
		parses.put("DOOYA_STATUS", new DooYaStatusParse());//杜亚电机
		parses.put("SENSOR_BOOL_STATUS", new BooleSensorStatusParse());//布尔传感器
		parses.put("DOOR_CONTACT_STATUS", new DoorContactStatusParse());//门磁
		parses.put("ZIGBEE_ALERTOR_STATUS", new AlarmStatusParse());//报警器
		parses.put("DIMMABLE_LIGHT_STATUS", new DimmerLightStatusParse());//调光灯
		parses.put("STATUS", new OutletStatusParse());//插座类
		parses.put("FAN_COIL_STATUS", new FanCoilStatusParse());//风机盘管(空调)
	}

	public static List<DeviceProperty> parseStatus(Integer operateType, JSON argument) {
		if (argument == null) {
			//如果STATUS为null，则通过operateType获取默认json
			LanStatusParse parseWhenStatusIsNull = findParseWhenStatusIsNull(operateType);
			if(parseWhenStatusIsNull == null){
				return new ArrayList<>();
			}
			parseWhenStatusIsNull.parse(operateType, parseWhenStatusIsNull.defaultStatus(operateType));
		}

		List<DeviceProperty> properties = new ArrayList<>();
		if (argument instanceof JSONObject) {
			return doParse(operateType, (JSONObject) argument);
		} else {
			JSONArray argJson = (JSONArray) argument;
			for (Object o : argJson) {
				List<DeviceProperty> prop = doParse(operateType, (JSONObject) o);
				if (prop.isEmpty()) continue;
				properties.addAll(prop);
			}
		}

		return properties;
	}

	private static List<DeviceProperty> doParse(Integer operateType, JSONObject argument) {
		String opcode = argument.getString("opcode");
		if (opcode == null || !parses.containsKey(opcode)) {
			return new ArrayList<>();
		}
		LanStatusParse lanStatusParse = parses.get(opcode);


		return lanStatusParse.parse(operateType, argument);
	}

	private static LanStatusParse findParseWhenStatusIsNull(Integer operateType) {
		if(operateType != null) {
			if(Range.between(1, 500).contains(operateType) || Objects.equals(operateType, 510)){//开关类：1-500，510
				return parses.get("SWITCH");
			} else if(Range.between(501, 1000).contains(operateType)){//可调光类：501-1000,不包括510
				return parses.get("DIMMABLE_LIGHT_STATUS");
			} else if(Range.between(1001, 1500).contains(operateType)){//电机类：1001-1500
				return parses.get("SWITCH");
			} else if(Range.between(2001, 2500).contains(operateType)){//插座类：2001-2500
				return parses.get("STATUS");
			} else if(Range.between(3001, 3500).contains(operateType)){//布尔传感器：3001-3500
				return parses.get("SENSOR_BOOL_STATUS");
			} else if(Range.between(3497, 3499).contains(operateType)){ //门磁：3497-3499
				return parses.get("DOOR_CONTACT_STATUS");
			} else if(Objects.equals(11001, operateType) || Objects.equals(11002, operateType)) {//报警器：11001：zigbee报警器/11002邦德报警器
				return parses.get("ZIGBEE_ALERTOR_STATUS");
			} else if(Range.between(15001, 15003).contains(operateType)){//风机盘管：15001-15003
				return parses.get("FAN_COIL_STATUS");
			}
		}

		return null;
	}

}
