package com.huafon.iot.support.konke.status;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.huafon.iot.models.entity.DeviceProperty;
import com.huafon.iot.support.konke.status.factory.KonKePropertyFactory;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> 普通灯
 * {"on" :  true}
 * @since 2022-04-29 15:11
 **/
@Slf4j
public class NormalLightStatusConvert implements StatusInfo2PropertyConvert {
	private static final Set<String> supportDevices = Sets
			.newHashSet("KONKE_ZIGBEE_LIGHT", "KONKE_KK_LIGHT", "KONKE_KK_SINGLEFIREWIRE_LIGHT",
					"KONKE_KK_MINI_KSOCKET", "KONKE_ZIGBEE_CHOPIN_LIGHT_PROJECT", "KONKE_ZIGBEE_CHOPIN_LIGHT_CUSTOM");


	@Override
	public boolean support(String deviceType) {
		return supportDevices.contains(deviceType);
	}

	@Override
	public List<DeviceProperty> convert(JSONObject jsonObject) {
		DeviceProperty deviceProperty = KonKePropertyFactory.generateSwitch();
		if(jsonObject.containsKey("status")){
			Boolean on = (Boolean)(jsonObject.getJSONObject("status").get("on"));
			deviceProperty.setCurrentStatus(on.toString());
		} else {
			log.warn("device status unknown {}", jsonObject);
			deviceProperty.setCurrentStatus("false");
		}
		return Collections.singletonList(deviceProperty);
	}




}
