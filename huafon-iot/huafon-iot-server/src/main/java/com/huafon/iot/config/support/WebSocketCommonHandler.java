package com.huafon.iot.config.support;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.*;

import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.util.Collection;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @since 2022-05-23 16:13
 **/
@Slf4j
public class WebSocketCommonHandler implements WebSocketHandler {

	private ConcurrentHashMap<String, WebSocketSessionHolder> sessions = new ConcurrentHashMap<>();
	private final AtomicInteger count = new AtomicInteger();


	@Override
	public void afterConnectionEstablished(WebSocketSession session) throws Exception {
		if (!session.isOpen()) {
			return;
		}
		count.getAndIncrement();
		this.sessions.put(session.getId(), new WebSocketSessionHolder(session));
		log.info("WebSocket session [{}] Established ", session.getId());
	}

	@Override
	public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
		log.info("WebSocket session [{}] receiveMessage: {}", session.getId(), message.getPayload());
		if ("PING".equals(message.getPayload())) {
			if (sessions.containsKey(session.getId())) {
				sessions.get(session.getId()).updateBeatTime();
			}
		}
	}

	@Override
	public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
		log.error("WebSocket session [{}] transportError: {}", session.getId(), exception.getMessage());
		handlerDisconnect(session);
	}

	@Override
	public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
		handlerDisconnect(session);
	}


	private void handlerDisconnect(WebSocketSession session) {
		if (this.sessions.remove(session.getId()) != null) {
			this.count.decrementAndGet();
			log.info("WebSocket session [{}] Closed ", session.getId());
		}
	}

	@Override
	public boolean supportsPartialMessages() {
		return false;
	}


	public void sendMessage(Object message) {
		Collection<WebSocketSessionHolder> sessionHolders = sessions.values();
		for (WebSocketSessionHolder sessionHolder : sessionHolders) {
			try {
				if (sessionHolder.isExpires()) {
					log.info("【WebSocketSession过期】: {}", sessionHolder);
					handlerDisconnect(sessionHolder.getSession());
				} else {
					log.info("【发送数据到前台】: {}", System.currentTimeMillis());
					sessionHolder.getSession().sendMessage(new TextMessage(JSON.toJSONString(message)));
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	private static class WebSocketSessionHolder {
		private static final Integer EXPIRE_TIME = 120;//TIME_UNIT:second
		private final WebSocketSession session;
		private volatile long lastHeartBeatTime;

		public WebSocketSessionHolder(WebSocketSession session) {
			this.session = session;
			this.lastHeartBeatTime = System.currentTimeMillis();
		}

		public WebSocketSession getSession() {
			return this.session;
		}

		public void updateBeatTime() {
			this.lastHeartBeatTime = System.currentTimeMillis();
		}

		public boolean isExpires() {
			return Duration.between(Instant.ofEpochMilli(lastHeartBeatTime), Instant.ofEpochMilli(System.currentTimeMillis())).getSeconds() > EXPIRE_TIME;
		}

		@Override
		public String toString() {
			return "WebSocketSessionHolder{" +
					"session=" + session +
					", lastHeartBeatTime=" + lastHeartBeatTime +
					'}';
		}
	}
}
