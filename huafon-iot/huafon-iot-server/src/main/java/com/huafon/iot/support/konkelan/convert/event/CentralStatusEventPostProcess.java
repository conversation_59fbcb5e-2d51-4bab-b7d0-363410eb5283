package com.huafon.iot.support.konkelan.convert.event;

import com.huafon.iot.config.support.WebSocketCommonHandler;
import com.huafon.iot.dao.mapper.DeviceInfoMapper;
import com.huafon.iot.models.dto.WSDeviceStatus;
import com.huafon.iot.models.entity.DeviceInfo;
import com.huafon.iot.models.enums.ImportType;
import com.huafon.iot.support.konkelan.entity.EventPushMessage;
import com.huafon.iot.support.konkelan.support.CentralServerContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 中控状态消息推送处理
 *
 * <AUTHOR>
 * @since 2022-05-23 15:51
 **/
@Slf4j
@Component
public class CentralStatusEventPostProcess implements LanEventPostProcess {

	private final DeviceInfoMapper deviceInfoMapper;
	private final WebSocketCommonHandler webSocketCommonHandler;
	private CentralServerContext centralServerContext;

	@Autowired
	public CentralStatusEventPostProcess(DeviceInfoMapper deviceInfoMapper, WebSocketCommonHandler webSocketCommonHandler) {
		this.deviceInfoMapper = deviceInfoMapper;
		this.webSocketCommonHandler = webSocketCommonHandler;
	}

	@Override
	public boolean support(String eventType) {
		return "INTERNAL_CENTRAL_STATUS".equals(eventType);
	}

	@Override
	public void postProcess(EventPushMessage message) {
		if (Objects.equals("ONLINE", message.getArg())) {
			centralServerContext.syncDeviceStatus(message.getCcuID());
		} else if (Objects.equals("OFFLINE", message.getArg())) {
			List<DeviceInfo> deviceInfos = deviceInfoMapper.searchDevicesBySourceAndCentral(SOURCE, ImportType.LAN, message.getCcuID());
			if (deviceInfos.size() == 0) return;
			deviceInfoMapper.updateDeviceStatusByCentral(SOURCE, message.getCcuID(), "OFFLINE");
			for (DeviceInfo deviceInfo : deviceInfos) {
				webSocketCommonHandler.sendMessage(new WSDeviceStatus(deviceInfo.getDeviceId(), (String) message.getArg()));
			}
		}
	}

	@Autowired
	public void setClientContext(@Lazy CentralServerContext centralServerContext) {
		this.centralServerContext = centralServerContext;
	}
}
