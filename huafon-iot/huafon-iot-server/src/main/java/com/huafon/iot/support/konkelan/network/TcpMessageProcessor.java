package com.huafon.iot.support.konkelan.network;

import com.huafon.iot.support.konkelan.entity.CCUInfo;
import com.huafon.iot.support.konkelan.entity.EventPushMessage;
import com.huafon.iot.support.konkelan.entity.MessageFingerprint;
import com.huafon.iot.support.konkelan.support.BaseRequest;
import com.huafon.iot.support.konkelan.support.BaseResponse;
import com.huafon.iot.support.konkelan.support.CentralServerContext;
import com.huafon.iot.support.konkelan.support.ClientResponseCallback;
import com.huafon.iot.support.konkelan.util.CommonUtil;
import io.netty.channel.ChannelFuture;
import io.netty.channel.EventLoopGroup;
import io.netty.util.concurrent.DefaultPromise;
import io.netty.util.concurrent.Future;
import io.netty.util.concurrent.GenericFutureListener;
import io.netty.util.concurrent.Promise;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;


/**
 * 中控交互消息处理器
 *
 * <AUTHOR>
 * @since 2022-05-18 13:38
 **/
@Slf4j
public class TcpMessageProcessor {

	private final EventLoopGroup eventExecutors;
	private final TcpClientConnector clientConnector;
	private final ConcurrentHashMap<MessageFingerprint, RequestTrackerTask> requestTasks;
	private final ClientResponseCallback callback;


	public TcpMessageProcessor(EventLoopGroup eventExecutors, TcpClientConnector clientConnector,
							   ClientResponseCallback callback) {
		this.eventExecutors = eventExecutors;
		this.clientConnector = clientConnector;
		this.requestTasks = new ConcurrentHashMap<>();
		this.callback = callback;
	}

	Future<BaseResponse> doRequest(BaseRequest request) {
		DefaultPromise<BaseResponse> promise = new DefaultPromise<>(eventExecutors.next());

		if (requestTasks.containsKey(request.messageFingerprint())) {
			promise.setFailure(new RuntimeException("Repeated Request"));
			return promise;
		}
		RequestTrackerTask requestTrackerTask = new RequestTrackerTask(request, promise);
		eventExecutors.submit(requestTrackerTask);
		registerRequestTrackerTask(request.messageFingerprint(), requestTrackerTask);
		return promise;
	}

	void onMessageReceived(CCUInfo ccuInfo, BaseResponse response) {
		MessageFingerprint fingerprint = response.messageFingerprint();
		if (requestTasks.containsKey(fingerprint)) {
			RequestTrackerTask task = requestTasks.get(fingerprint);
			removeRequestTrackerTask(fingerprint);
			task.cancel();
			task.processResponseMessage(response);

		} else {
			boolean interceptor = clientConnector.responseInterceptor(response);
			if (interceptor) {
				return;
			}
			log.info("RESPONSE: {}", response);
			EventPushMessage message = new EventPushMessage();
			message.setCcuID(CentralServerContext.CCU_PREFIX + ccuInfo.getZkId());
			message.setNodeId(response.getNodeId());
			message.setEventType(response.getOpcode());
			message.setArg(CommonUtil.transfer(response.getArg()));
			callback.eventPushCallback(message);
		}
	}


	private void registerRequestTrackerTask(MessageFingerprint fingerprint, RequestTrackerTask task) {
		requestTasks.put(fingerprint, task);
	}

	private void removeRequestTrackerTask(MessageFingerprint fingerprint) {
		requestTasks.remove(fingerprint);
	}

	private class RequestTrackerTask implements Runnable, GenericFutureListener<Future<Void>> {

		private final BaseRequest request;
		private final Promise<BaseResponse> promise;
		private ScheduledFuture<?> checkRequestTimeoutTimer;
		private final int requestTimeout;
		private volatile boolean canceled;

		public RequestTrackerTask(BaseRequest request, Promise<BaseResponse> promise) {
			this.request = request;
			this.promise = promise;
			this.requestTimeout = request.getRequestTimeout();
		}

		@Override
		public void run() {
			try {
				if (canceled) {
					return;
				}
				setRequestTimeoutTimer();
				ChannelFuture send = clientConnector.send(request);
				send.addListener(this);
			} catch (Exception e) {
				removeRequestTrackerTask(request.messageFingerprint());
				promise.setFailure(e);
				log.error("【发送消息失败】{}", e.getMessage());
			}
		}

		private void processResponseMessage(BaseResponse response) {
			cancelRequestTimeoutTimer();
			promise.setSuccess(response);
		}

		private void setRequestTimeoutTimer() {
			checkRequestTimeoutTimer = eventExecutors.schedule(() -> {
				if (canceled) {
					return;
				}
				removeRequestTrackerTask(request.messageFingerprint());
				promise.setFailure(new RuntimeException("【TCP请求等待超时】" + request));

			}, requestTimeout, TimeUnit.MILLISECONDS);
		}

		private void cancelRequestTimeoutTimer() {
			if (checkRequestTimeoutTimer != null) {
				checkRequestTimeoutTimer.cancel(true);
			}
		}

		public void cancel() {
			this.canceled = true;
		}

		@Override
		public void operationComplete(Future<Void> future) throws Exception {
			if (!future.isSuccess()) {
				cancelRequestTimeoutTimer();
				removeRequestTrackerTask(request.messageFingerprint());
				promise.setFailure(new RuntimeException("【TCP发送消息失败】"));
			}
		}
	}


}
