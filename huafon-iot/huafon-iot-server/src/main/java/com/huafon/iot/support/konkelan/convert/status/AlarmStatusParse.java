package com.huafon.iot.support.konkelan.convert.status;

import com.alibaba.fastjson.JSONObject;
import com.huafon.iot.models.dto.KonKeLanOperation;
import com.huafon.iot.models.dto.OperationProperty;
import com.huafon.iot.models.entity.DeviceProperty;
import com.huafon.iot.models.enums.DeviceDataType;
import com.huafon.iot.models.enums.PropertyModule;
import com.huafon.iot.models.enums.READ_WRITE;
import com.huafon.iot.support.DevicePropertyTransfer;
import com.huafon.iot.support.konke.status.factory.KonKePropertyFactory;
import com.huafon.iot.support.konkelan.convert.event.AlarmEventPostProcess;
import com.huafon.iot.support.konkelan.convert.event.PowerNotifyEventPostProcess;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 报警器
 *
 * <AUTHOR>
 * @see PowerNotifyEventPostProcess
 * @see AlarmEventPostProcess
 * @since 2022-05-25 09:09
 **/
@Slf4j
public class AlarmStatusParse implements LanStatusParse, DevicePropertyTransfer {

	@Override
	public List<DeviceProperty> parse(Integer operateType, JSONObject status) {
		List<DeviceProperty> properties = new ArrayList<>();
		if (status.containsKey("arg")) {
			JSONObject arg = status.getJSONObject("arg");
			//告警状态：控制时需要转换
			if (arg.containsKey("alarm")) {
				DeviceProperty property = KonKePropertyFactory.generateBooleProperty(PropertyModule.POWER_SWITCH, "ALARM_STATUS_TRANSFER", "开/关报警器",
						"ALARM_STATUS_TRANSFER", READ_WRITE.READ_WRITE, "CLOSE", "OPEN");
				property.setCurrentStatus(Boolean.toString(Objects.equals(1, arg.getInteger("alarm"))));
				properties.add(property);
			}
			//报警器音量
			if (arg.containsKey("sound")) {
				Integer sound = arg.getInteger("sound");
				if (!Objects.equals(255, sound)) {
					if (Objects.equals(11001, operateType)) {//合法值:0-100。255 为无效值
						DeviceProperty property = KonKePropertyFactory.generateProgress("HJ_Server", "报警音量", "ADJUST_VOLUME",
								0, 100);
						property.setCurrentStatus(sound.toString());
						properties.add(property);
					} else if (Objects.equals(11002, operateType)) {//合法值:0-3。255 为无效值
						DeviceProperty property = KonKePropertyFactory.generateProgress("HJ_Server", "报警音量", "ADJUST_VOLUME",
								0, 3);
						property.setCurrentStatus(sound.toString());
						properties.add(property);
					}
				}
			}
			//供电方式：0市电1电池
			if (arg.containsKey("power")) {
				DeviceProperty powerProperty = KonKePropertyFactory.generateTextRead("power", "供电方式", "power", 5);
				Integer power = arg.getInteger("power");
				powerProperty.setCurrentStatus(Objects.equals(0, power) ? "市电" : (Objects.equals(1, power) ? "电池" : "其他"));
				properties.add(powerProperty);
			}
			//电池电量
			if (arg.containsKey("battery")) {
				DeviceProperty batteryProperty =
						KonKePropertyFactory.generateNumberRead("battery", "电量",
								"battery", DeviceDataType.INT, 0, 100, 1);
				batteryProperty.setCurrentStatus(arg.getString("battery"));
				properties.add(batteryProperty);
			}

		}
		return properties;
	}


	@Override
	public JSONObject defaultStatus(Integer operateType) {
		JSONObject element = new JSONObject();
		element.put("nodeid", "*");
		element.put("opcode", "SWITCH");

		JSONObject arg = new JSONObject();
		arg.put("alarm", "0");
		arg.put("sound", "0");
		arg.put("power", "1");
		arg.put("battery", "100");
		element.put("arg", arg);

		return element;
	}

	/**
	 * 开启报警器：
	 * {"nodeid": 1#,"opcode": "OPEN_ALERTOR", "arg": "*","requester":"HJ_Server"}
	 * 关闭报警器：
	 * {"nodeid": 1#,"opcode": "CLOSE_ALERTOR", "arg": "*","requester":"HJ_Server"}
	 * <p/>
	 * konKeLanOperation:
	 * {
	 * action: ALARM_STATUS_TRANSFER,
	 * propertyIdentify: ALARM_STATUS_TRANSFER
	 * arg: true/false
	 * }
	 * transferTo
	 * {
	 * action: HJ_Server,
	 * propertyIdentify: OPEN_ALERTOR/CLOSE_ALERTOR
	 * arg: *
	 * }
	 */
	@Override
	public void transfer(OperationProperty operationProperty) {
		if (operationProperty instanceof KonKeLanOperation) {
			KonKeLanOperation konKeLanOperation = (KonKeLanOperation) operationProperty;
			String action = konKeLanOperation.getAction();
			if ("ALARM_STATUS_TRANSFER".equals(action)) {
				Object value = konKeLanOperation.getArg();
				konKeLanOperation.setAction("HJ_Server");
				konKeLanOperation.setPropertyIdentifier(Objects.equals(true, value) ? "OPEN_ALERTOR" : "CLOSE_ALERTOR");
				konKeLanOperation.setArg("*");
			}
		}
	}
}
