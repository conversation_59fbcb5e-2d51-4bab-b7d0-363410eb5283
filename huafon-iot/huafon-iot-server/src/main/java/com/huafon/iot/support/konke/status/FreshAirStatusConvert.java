package com.huafon.iot.support.konke.status;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.huafon.iot.models.entity.DeviceProperty;
import com.huafon.iot.models.enums.PropertyModule;
import com.huafon.iot.models.enums.READ_WRITE;
import com.huafon.iot.support.konke.status.factory.KonKePropertyFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * 新风面板
 *
 * <AUTHOR>
 * @since 2022-05-10 14:00
 **/
public class FreshAirStatusConvert implements StatusInfo2PropertyConvert {
	private static final Set<String> supportDevices =
			Sets.newHashSet("KONKE_ZIGBEE_KELIN_FreshAir");

	@Override
	public boolean support(String deviceType) {
		return supportDevices.contains(deviceType);
	}

	@Override
	public List<DeviceProperty> convert(JSONObject jsonObject) {
		boolean existStatus = jsonObject.containsKey("status");
		List<DeviceProperty> properties = new ArrayList<>();
		if (existStatus) {
			JSONObject status = jsonObject.getJSONObject("status");
			//开关
			if (status.containsKey("on")) {
				DeviceProperty switchProperty = KonKePropertyFactory.generateSwitch("FreshAirSwitch", "新风开关");
				switchProperty.setCurrentStatus(status.getBoolean("on").toString());
				properties.add(switchProperty);
			}
			//运行模式：AUTO(自动), MANUAL(手动)
			if (status.containsKey("runModel")) {
				List<String> enumValues = Arrays.asList("AUTO", "MANUAL");//自动/手动
				DeviceProperty runModelProperty = KonKePropertyFactory.generateEnumProperty(PropertyModule.MODULE_TYPE,"FreshAirSetRunModel", "运行模式", "runModel", READ_WRITE.READ_WRITE, enumValues);
				runModelProperty.setCurrentStatus(status.getString("runModel"));
				properties.add(runModelProperty);
			}
			//工作模式：TOTAL_HEAT_EXCHANGE(热交换), AIR_PURGE(空净)
			if (status.containsKey("workModel")) {
				List<String> enumValues = Arrays.asList("TOTAL_HEAT_EXCHANGE", "AIR_PURGE");//自动/手动
				DeviceProperty runModelProperty = KonKePropertyFactory.generateEnumProperty(PropertyModule.MODULE_TYPE,"FreshAirSetRunModel", "运行模式", "workModel", READ_WRITE.READ_WRITE, enumValues);
				runModelProperty.setCurrentStatus(status.getString("workModel"));
				properties.add(runModelProperty);
			}
			//风速：STOP(停止)，LOW(低风)，MID(中风)，HIGH(高风)
			if (status.containsKey("speed")) {
				List<String> enumValues = Arrays.asList("STOP", "LOW", "MID", "HIGH");
				DeviceProperty runModelProperty = KonKePropertyFactory.generateEnumProperty(PropertyModule.MODULE_TYPE, "FreshAirSetSpeed", "风速", "speed", READ_WRITE.READ_WRITE, enumValues);
				runModelProperty.setCurrentStatus(status.getString("speed"));
				properties.add(runModelProperty);
			}
			//pm2.5/co2值不确定设备云会主动推送
			/*
			if (status.containsKey("pm25")) {
				DeviceProperty pmProperty = KonKePropertyFactory.generateNumberRead("PM25", "PM2.5", "pm25", DeviceDataType.INT, 0, 999, 1);
				pmProperty.setCurrentStatus(status.getString("pm25"));
				properties.add(pmProperty);
			}
			if (status.containsKey("co2")) {
				DeviceProperty co2Property = KonKePropertyFactory.generateNumberRead("CO2", "二氧化碳", "co2", DeviceDataType.INT, 0, 999, 1);
				co2Property.setCurrentStatus(status.getString("co2"));
				properties.add(co2Property);
			}
			 */
			//滤网状态：NORMAL(正常)，CLEAN(清洗)， REPLACE(更换)
			if (status.containsKey("filterScreenStatus")) {
				List<String> enumValues = Arrays.asList("NORMAL", "CLEAN", "REPLACE");
				DeviceProperty filterStatusProperty = KonKePropertyFactory.generateEnumProperty(PropertyModule.MODULE_TYPE,"FILTER_SCREEN_STATUS", "滤网状态", "filterScreenStatus", READ_WRITE.READ_ONLY, enumValues);
				filterStatusProperty.setCurrentStatus(status.getString("filterScreenStatus"));
				properties.add(filterStatusProperty);
			}
		}
		return properties;
	}
}
