package com.huafon.area.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description: 区域责任配置DTO
 * @Date: 2022/8/5 10:20
 * @Author: zyf
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AreaDutyConfigDTO implements Serializable {
    private static final long serialVersionUID = 4160436534815578024L;

    private Integer id;

    /**
     * 区域id
     */
    private Integer areaId;

    /**
     * 部门负责人id
     */
    private Integer deptPrincipalPersonId;

    /**
     * 部门负责人名称
     */
    private String deptPrincipalPersonName;

    /**
     * 部门负责人手机号
     */
    private String deptPrincipalPersonMobile;

    /**
     * 责任部门id
     */
    private Integer dutyDeptId;

    /**
     * 级联责任部门id
     */
    private String dutyDeptIds;

    /**
     * 责任部门名称
     */
    private String dutyDeptName;

    /**
     * 级联责任部门名称
     */
    private String dutyDeptNames;

    /**
     * 区域负责人id
     */
    private Integer areaPrincipalPersonId;

    /**
     * 区域负责人名称
     */
    private String areaPrincipalPersonName;

    /**
     * 区域负责人手机号
     */
    private String areaPrincipalPersonMobile;

    /**
     * 责任区域部门id
     */
    private Integer dutyAreaDeptId;

    /**
     * 级联责任区域部门id
     */
    private String dutyAreaDeptIds;

    /**
     * 责任区域部门名称
     */
    private String dutyAreaDeptName;

    /**
     * 级联责任区域部门名称
     */
    private String dutyAreaDeptNames;

    /**
     * 区域负责人工号
     */
    private String areaPrincipalPersonWorkNum;

    /**
     * 部门负责人工号
     */
    private String deptPrincipalPersonWorkNum;
}
