package com.haufon.contractor.service;

import com.huafon.area.AreaApplication;
import com.huafon.area.service.AreaService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * <AUTHOR>
 * @since 2022-08-22 13:26
 **/
@Slf4j
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = AreaApplication.class)
public class CommonTest {

    private final AreaService service;

    public CommonTest(AreaService service) {
        this.service = service;
    }

    @BeforeEach
    public void before() {
    }

    @Test
    //@Transactional
    public void testJson() {
//        Long newId = null;
//        Long id = null;
//        Long aboveId = null;
//        service.move(id, newId, aboveId);
    }

    @AfterEach
    public void after() {
    }
}