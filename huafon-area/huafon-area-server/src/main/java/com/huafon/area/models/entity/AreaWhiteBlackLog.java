package com.huafon.area.models.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.huafon.area.enums.AreaPassableType;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Table: sdb_permission
 * demo类，可删除
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hf_area_white_black_log")
public class AreaWhiteBlackLog extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField
    private Long areaId;

    @TableField
    private String description;

    @TableField
    private String source;
    @TableField
    private Integer sourceId;

    @TableField
    private String operatorName;
    @TableField
    private String type;
    @TableField
    private Integer operator;
    @TableField
    private AreaPassableType afterPassable;

}