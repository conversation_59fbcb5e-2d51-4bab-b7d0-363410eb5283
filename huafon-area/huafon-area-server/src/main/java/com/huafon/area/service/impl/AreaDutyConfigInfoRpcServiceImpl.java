package com.huafon.area.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.huafon.area.api.dto.AreaDutyConfigDTO;
import com.huafon.area.api.dto.AreaDutyConfigInfoDTO;
import com.huafon.area.api.dto.AreaPrincipalPersonDTO;
import com.huafon.area.api.dto.DeptPrincipalPersonDTO;
import com.huafon.area.api.service.AreaDutyConfigInfoRpcService;
import com.huafon.area.dao.AreaDutyConfigInfoMapper;
import com.huafon.area.models.entity.AreaDutyConfigInfo;
import com.huafon.common.config.TenantContext;
import com.huafon.common.utils.StreamUtils;
import com.huafon.portal.api.dto.UserDto;
import com.huafon.portal.api.dto.dept.DeptDto;
import com.huafon.portal.api.service.DeptRpcService;
import com.huafon.portal.api.service.UserRpcV2Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 区域责任配置RPC业务层
 * @Date: 2022/8/5 10:23
 * @Author: zyf
 **/
@Slf4j
@Service
@DubboService
public class AreaDutyConfigInfoRpcServiceImpl implements AreaDutyConfigInfoRpcService {

    private final AreaDutyConfigInfoMapper areaDutyConfigInfoMapper;

    @DubboReference
    private DeptRpcService deptRpcService;

    @DubboReference
    private UserRpcV2Service userRpcV2Service;

    public AreaDutyConfigInfoRpcServiceImpl(AreaDutyConfigInfoMapper areaDutyConfigInfoMapper) {
        this.areaDutyConfigInfoMapper = areaDutyConfigInfoMapper;
    }


    /**
     * 根据区域id查询区域配置责任信息
     *
     * @param areaId
     * @return
     */
    @Override
    public AreaDutyConfigInfoDTO findByAreaId(Integer areaId) {
        AreaDutyConfigInfo areaDutyConfigInfo = areaDutyConfigInfoMapper.queryByAreaId(areaId);
        AreaDutyConfigInfoDTO configInfoDTO = afterDetailProcess(areaDutyConfigInfo);
        return configInfoDTO;
    }

    @Override
    public List<AreaDutyConfigInfoDTO> findByAreaIds(List<Integer> areaIds) {
        List<AreaDutyConfigInfoDTO> byAreaIdsForRpc = null;
        if (!CollectionUtils.isEmpty(areaIds)){
            List<AreaDutyConfigInfo> areaDutyConfigInfos = areaDutyConfigInfoMapper.queryByAreaIds(areaIds);
            if (!CollectionUtils.isEmpty(areaDutyConfigInfos)){
                byAreaIdsForRpc = setUserAndDeptInfo(areaDutyConfigInfos);
//                byAreaIdsForRpc = new ArrayList<>();
//                for (AreaDutyConfigInfo areaDutyConfigInfo : areaDutyConfigInfos) {
//                    AreaDutyConfigInfoDTO areaDutyConfigInfoDTO = afterDetailProcess(areaDutyConfigInfo);
//                    byAreaIdsForRpc.add(areaDutyConfigInfoDTO);
//                }
            }
        }
        return byAreaIdsForRpc;
    }

    /**
     * 根据责任部门名称查询关联的有效区域列表
     * @param dutyDeptNames
     * @param tenantId
     * @return
     */
    @Override
    public List<AreaDutyConfigInfoDTO> findByDutyDeptNames(List<String> dutyDeptNames, Integer tenantId) {
        List<AreaDutyConfigInfoDTO> areaDutyConfigInfoDTOList = null;
        if (!CollectionUtils.isEmpty(dutyDeptNames) && Objects.nonNull(tenantId)){
            List<DeptDto> byNameList = deptRpcService.getByNameList(tenantId, dutyDeptNames);
            log.info("=======根据部门名称查询返回：=======：{}",JSONObject.toJSONString(byNameList));
            if (!CollectionUtils.isEmpty(byNameList)){
                List<Integer> deptIdList = byNameList.stream().map(x -> x.getId()).collect(Collectors.toList());
                List<AreaDutyConfigInfo> areaDutyConfigInfos = areaDutyConfigInfoMapper.queryByDutyDeptIds(deptIdList, tenantId);
                log.info("=======根据部门id查询返回：=======：{}",JSONObject.toJSONString(areaDutyConfigInfos));
                areaDutyConfigInfoDTOList = buildAreaDutyConfigInfoDTO(areaDutyConfigInfos,byNameList);
            }
        }
        return areaDutyConfigInfoDTOList;
    }

    /**
     * 根据租户id查询区域配置责任信息
     *
     * @param tenantId
     * @return
     */
    @Override
    public List<AreaDutyConfigInfoDTO> findByTenantId(Integer tenantId) {
        List<AreaDutyConfigInfoDTO> list = null;
        List<AreaDutyConfigInfo> areaDutyConfigInfos = areaDutyConfigInfoMapper.queryByTenantId(tenantId);
        if (!CollectionUtils.isEmpty(areaDutyConfigInfos)){
            list = setUserAndDeptInfo(areaDutyConfigInfos);
        }
        return list;
    }

    private List<AreaDutyConfigInfoDTO> setUserAndDeptInfo(List<AreaDutyConfigInfo> areaDutyConfigInfos){
        List<AreaDutyConfigInfoDTO> list = new ArrayList<>();
        List<Integer> userIdList = new ArrayList<>();
        List<Integer> deptIdList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(areaDutyConfigInfos)){
            for (AreaDutyConfigInfo configInfo : areaDutyConfigInfos) {
                String areaPrincipalPerson = configInfo.getAreaPrincipalPerson();
                if (StringUtils.isNotBlank(areaPrincipalPerson)){
                    List<Integer> userIds = JSONObject.parseArray(areaPrincipalPerson, Integer.class);
                    if (!CollectionUtils.isEmpty(userIds)){
                        userIdList.addAll(userIds);
                    }
                }
                String deptPrincipalPerson = configInfo.getDeptPrincipalPerson();
                if (StringUtils.isNotBlank(deptPrincipalPerson)){
                    List<Integer> userIds = JSONObject.parseArray(deptPrincipalPerson, Integer.class);
                    if (!CollectionUtils.isEmpty(userIds)){
                        userIdList.addAll(userIds);
                    }
                }

                if (!Objects.isNull(configInfo) && !Objects.isNull(configInfo.getDutyAreaDeptId())){
                    deptIdList.add(configInfo.getDutyAreaDeptId());
                }
                if (!Objects.isNull(configInfo) && !Objects.isNull(configInfo.getDutyDeptId())){
                    deptIdList.add(configInfo.getDutyDeptId());
                }

            }

            Map<Integer, UserDto> userMap = getUserMap(TenantContext.getOne(), userIdList);
            Map<Integer, String> deptMap = getDeptMap(TenantContext.getOne(),deptIdList);

            for (AreaDutyConfigInfo configInfo : areaDutyConfigInfos) {
                AreaDutyConfigInfoDTO configInfoDTO = new AreaDutyConfigInfoDTO();
                BeanUtils.copyProperties(configInfo,configInfoDTO);
                configInfoDTO.setDutyAreaDeptName(deptMap.get(configInfoDTO.getDutyAreaDeptId()));
                configInfoDTO.setDutyDeptName(deptMap.get(configInfoDTO.getDutyDeptId()));

                String areaPrincipalPerson = configInfoDTO.getAreaPrincipalPerson();
                if (StringUtils.isNotBlank(areaPrincipalPerson)){
                    List<Integer> userIds = JSONObject.parseArray(areaPrincipalPerson, Integer.class);
                    if (!CollectionUtils.isEmpty(userIds)){
                        List<AreaPrincipalPersonDTO> areaPrincipalPersonList = new ArrayList<>();
                        for (Integer userId : userIds) {
                            UserDto userDto = userMap.get(userId);
                            if (!Objects.isNull(userDto)) {
                                AreaPrincipalPersonDTO areaPrincipalPersonDTO = new AreaPrincipalPersonDTO();
                                areaPrincipalPersonDTO.setAreaPrincipalPersonId(userId);
                                areaPrincipalPersonDTO.setAreaPrincipalPersonName(userDto.getName());
                                areaPrincipalPersonDTO.setAreaPrincipalPersonMobile(userDto.getMobile());
                                areaPrincipalPersonDTO.setAreaPrincipalPersonWorkNum(userDto.getWorkNum());
                                areaPrincipalPersonList.add(areaPrincipalPersonDTO);
                            }
                        }

                        configInfoDTO.setAreaPrincipalPersonList(areaPrincipalPersonList);
                    }
                }

                String deptPrincipalPerson = configInfoDTO.getDeptPrincipalPerson();
                if (StringUtils.isNotBlank(deptPrincipalPerson)){
                    List<Integer> userIds = JSONObject.parseArray(deptPrincipalPerson, Integer.class);
                    if (!CollectionUtils.isEmpty(userIds)){
                        List<DeptPrincipalPersonDTO> deptPrincipalPersonList = new ArrayList<>();
                        for (Integer userId : userIds) {
                            UserDto userDto = userMap.get(userId);
                            if (!Objects.isNull(userDto)) {
                                DeptPrincipalPersonDTO deptPrincipalPersonDTO = new DeptPrincipalPersonDTO();
                                deptPrincipalPersonDTO.setDeptPrincipalPersonId(userId);
                                deptPrincipalPersonDTO.setDeptPrincipalPersonName(userDto.getName());
                                deptPrincipalPersonDTO.setDeptPrincipalPersonMobile(userDto.getMobile());
                                deptPrincipalPersonDTO.setDeptPrincipalPersonWorkNum(userDto.getWorkNum());
                                deptPrincipalPersonList.add(deptPrincipalPersonDTO);
                            }
                        }
                        configInfoDTO.setDeptPrincipalPersonList(deptPrincipalPersonList);
                    }
                }

                list.add(configInfoDTO);

            }
        }
        return list;

    }

    @Override
    public void batchUpdate(List<AreaDutyConfigInfoDTO> list) {
        if (!CollectionUtils.isEmpty(list)){
            List<AreaDutyConfigInfo> collect = list.stream().map(x -> {
                AreaDutyConfigInfo areaDutyConfigInfo = new AreaDutyConfigInfo();
                areaDutyConfigInfo.setId(x.getId());
                areaDutyConfigInfo.setAreaPrincipalPerson(x.getAreaPrincipalPerson());
                areaDutyConfigInfo.setDeptPrincipalPerson(x.getDeptPrincipalPerson());
                return areaDutyConfigInfo;
            }).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(collect)){
                areaDutyConfigInfoMapper.batchUpdateDutyPerson(collect);
            }
        }
    }

    /**
     * 根据责任部门查询区域列表
     * @param dutyDeptId
     * @return
     */
    @Override
    public List<AreaDutyConfigInfoDTO> findByDutyDeptId(Integer dutyDeptId) {
        return areaDutyConfigInfoMapper.findByDutyDeptId(dutyDeptId);
    }

    /**
     * 根据责任部门查询区域列表
     * @param dutyDeptIds
     * @return
     */
    @Override
    public List<AreaDutyConfigInfoDTO> findByDutyDeptIds(List<Integer> dutyDeptIds) {
        List<AreaDutyConfigInfoDTO> list = null;
        if (!CollectionUtils.isEmpty(dutyDeptIds)){
            list = areaDutyConfigInfoMapper.findByDutyDeptIds(dutyDeptIds);
        }
        return list;
    }

    private List<AreaDutyConfigInfoDTO> buildAreaDutyConfigInfoDTO(List<AreaDutyConfigInfo> areaDutyConfigInfos, List<DeptDto> byNameList) {
        List<AreaDutyConfigInfoDTO> list = null;
        if (!CollectionUtils.isEmpty(areaDutyConfigInfos)){
            Map<Integer, String> map = StreamUtils.propMap(byNameList, DeptDto::getId, DeptDto::getName);
            list = areaDutyConfigInfos.stream().map(x->{
                AreaDutyConfigInfoDTO areaDutyConfigInfoDTO = new AreaDutyConfigInfoDTO();
                areaDutyConfigInfoDTO.setAreaId(x.getAreaId());
                areaDutyConfigInfoDTO.setAreaName(x.getAreaName());
                areaDutyConfigInfoDTO.setDutyDeptName(map.get(x.getDutyDeptId()));
                return areaDutyConfigInfoDTO;
            }).collect(Collectors.toList());
        }
        return list;
    }

    public AreaDutyConfigInfoDTO afterDetailProcess(AreaDutyConfigInfo areaDutyConfigInfo) {
        AreaDutyConfigInfoDTO areaDutyConfigInfoDTO = null;
        if (Objects.nonNull(areaDutyConfigInfo)){
            areaDutyConfigInfoDTO = new AreaDutyConfigInfoDTO();
            BeanUtils.copyProperties(areaDutyConfigInfo,areaDutyConfigInfoDTO);
            setPersonInfo(areaDutyConfigInfoDTO);
            setDeptInfo(areaDutyConfigInfoDTO);
        }
        return areaDutyConfigInfoDTO;
    }

    public void setDeptInfo(AreaDutyConfigInfoDTO areaDutyConfigInfoDTO) {
        List<Integer> deptIdList = new ArrayList<>();
        if (!Objects.isNull(areaDutyConfigInfoDTO) && !Objects.isNull(areaDutyConfigInfoDTO.getDutyAreaDeptId())){
            deptIdList.add(areaDutyConfigInfoDTO.getDutyAreaDeptId());
        }
        if (!Objects.isNull(areaDutyConfigInfoDTO) && !Objects.isNull(areaDutyConfigInfoDTO.getDutyDeptId())){
            deptIdList.add(areaDutyConfigInfoDTO.getDutyDeptId());
        }
        if (!CollectionUtils.isEmpty(deptIdList)){
            Map<Integer, String> deptMap = getDeptMap(TenantContext.getOne(),deptIdList);
            areaDutyConfigInfoDTO.setDutyAreaDeptName(deptMap.get(areaDutyConfigInfoDTO.getDutyAreaDeptId()));
            areaDutyConfigInfoDTO.setDutyDeptName(deptMap.get(areaDutyConfigInfoDTO.getDutyDeptId()));
        }
    }

    public Map<Integer,String> getDeptMap(Integer tenantId, List<Integer> deptIdList){
        //获取部门信息
        List<DeptDto> nodeByIds = deptRpcService.getByIds(deptIdList, tenantId);
        Map<Integer, String> deptMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(nodeByIds)){
            deptMap = nodeByIds.stream().collect(Collectors.toMap(DeptDto::getId, DeptDto::getName, (key1, key2) -> key2));
        }
        return deptMap;
    }

    public void setPersonInfo(AreaDutyConfigInfoDTO areaDutyConfigInfoDTO) {
        List<Integer> userIdList = new ArrayList<>();
        if(Objects.nonNull(areaDutyConfigInfoDTO)){
            userIdList = mergeUserId(areaDutyConfigInfoDTO);
        }

        if (!CollectionUtils.isEmpty(userIdList)){
            Map<Integer, UserDto> userMap = getUserMap(TenantContext.getOne(), userIdList);

            String areaPrincipalPerson = areaDutyConfigInfoDTO.getAreaPrincipalPerson();
            if (StringUtils.isNotBlank(areaPrincipalPerson)){
                List<Integer> userIds = JSONObject.parseArray(areaPrincipalPerson, Integer.class);
                if (!CollectionUtils.isEmpty(userIds)){
                    List<AreaPrincipalPersonDTO> areaPrincipalPersonList = new ArrayList<>();
                    for (Integer userId : userIds) {
                        UserDto userDto = userMap.get(userId);
                        if (!Objects.isNull(userDto)) {
                            AreaPrincipalPersonDTO areaPrincipalPersonDTO = new AreaPrincipalPersonDTO();
                            areaPrincipalPersonDTO.setAreaPrincipalPersonId(userId);
                            areaPrincipalPersonDTO.setAreaPrincipalPersonName(userDto.getName());
                            areaPrincipalPersonDTO.setAreaPrincipalPersonMobile(userDto.getMobile());
                            areaPrincipalPersonDTO.setAreaPrincipalPersonWorkNum(userDto.getWorkNum());
                            areaPrincipalPersonList.add(areaPrincipalPersonDTO);
                        }
                    }

                    areaDutyConfigInfoDTO.setAreaPrincipalPersonList(areaPrincipalPersonList);
                }
            }

            String deptPrincipalPerson = areaDutyConfigInfoDTO.getDeptPrincipalPerson();
            if (StringUtils.isNotBlank(deptPrincipalPerson)){
                List<Integer> userIds = JSONObject.parseArray(deptPrincipalPerson, Integer.class);
                if (!CollectionUtils.isEmpty(userIds)){
                    List<DeptPrincipalPersonDTO> deptPrincipalPersonList = new ArrayList<>();
                    for (Integer userId : userIds) {
                        UserDto userDto = userMap.get(userId);
                        if (!Objects.isNull(userDto)) {
                            DeptPrincipalPersonDTO deptPrincipalPersonDTO = new DeptPrincipalPersonDTO();
                            deptPrincipalPersonDTO.setDeptPrincipalPersonId(userId);
                            deptPrincipalPersonDTO.setDeptPrincipalPersonName(userDto.getName());
                            deptPrincipalPersonDTO.setDeptPrincipalPersonMobile(userDto.getMobile());
                            deptPrincipalPersonDTO.setDeptPrincipalPersonWorkNum(userDto.getWorkNum());
                            deptPrincipalPersonList.add(deptPrincipalPersonDTO);
                        }
                    }
                    areaDutyConfigInfoDTO.setDeptPrincipalPersonList(deptPrincipalPersonList);
                }
            }

        }
    }

    private List<Integer> mergeUserId(AreaDutyConfigInfoDTO areaDutyConfigInfoDTO) {
        List<Integer> userId = new ArrayList<>();
        String areaPrincipalPerson = areaDutyConfigInfoDTO.getAreaPrincipalPerson();
        if (StringUtils.isNotBlank(areaPrincipalPerson)){
            List<Integer> userIds = JSONObject.parseArray(areaPrincipalPerson, Integer.class);
            if (!CollectionUtils.isEmpty(userIds)){
                userId.addAll(userIds);
            }
        }
        String deptPrincipalPerson = areaDutyConfigInfoDTO.getDeptPrincipalPerson();
        if (StringUtils.isNotBlank(deptPrincipalPerson)){
            List<Integer> userIds = JSONObject.parseArray(deptPrincipalPerson, Integer.class);
            if (!CollectionUtils.isEmpty(userIds)){
                userId.addAll(userIds);
            }
        }
        return userId;
    }

    public Map<Integer, UserDto> getUserMap(Integer tenantId, List<Integer> userIdList) {
        //获取用户信息
        List<UserDto> nodeByIds = userRpcV2Service.getByIds(userIdList, tenantId);
        Map<Integer, UserDto> userMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(nodeByIds)) {
            userMap = nodeByIds.stream().collect(Collectors.toMap(UserDto::getUserId, Function.identity(), (key1, key2) -> key2));
        }
        return userMap;
    }
}
