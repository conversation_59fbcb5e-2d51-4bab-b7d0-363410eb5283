package com.huafon.area.models.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * @Description: TODO
 * @Date: 2024/3/26 10:12
 * @Author: zyf
 **/
public interface AreaDutyConfigResponse {

    @Data
    @ApiModel(description = "区域基础信息VO")
    @EqualsAndHashCode(callSuper = true)
    class InfoVo extends AreaDutyConfigRequest.AreaDutyConfigVO {


    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel(value = "AreaDutyConfigCountVO", description = "区域责任部门统计VO")
    class AreaDutyConfigCountVO {

        @ApiModelProperty(value = "全部数据")
        private Integer total;

        @ApiModelProperty(value = "待分类")
        private Integer noClassify;

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel(value = "AreaDutyConfigPageRespVO", description = "区域责任部门分页返回VO")
    class AreaDutyConfigPageRespVO {

        @ApiModelProperty(value = "区域id")
        private Integer areaId;

        @ApiModelProperty(value = "区域名称")
        private String areaName;

        @ApiModelProperty(value = "责任区域id")
        private Integer dutyAreaDeptId;

        @ApiModelProperty(value = "级联责任区域")
        private String dutyAreaDeptNames;

        @ApiModelProperty(value = "区域负责人")
        private String areaPrincipalPersonName;

        @ApiModelProperty(value = "区域负责人id")
        private Integer areaPrincipalPersonId;

        @ApiModelProperty(value = "责任部门id")
        private Integer dutyDeptId;

        @ApiModelProperty(value = "级联责任部门")
        private String dutyDeptNames;

        @ApiModelProperty(value = "部门负责人")
        private String deptPrincipalPersonName;

        @ApiModelProperty(value = "部门负责人id")
        private Integer deptPrincipalPersonId;

    }


}
