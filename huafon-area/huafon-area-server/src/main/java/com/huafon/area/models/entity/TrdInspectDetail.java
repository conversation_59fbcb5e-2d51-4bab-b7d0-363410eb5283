package com.huafon.area.models.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Table: sdb_permission
 * demo类，可删除
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "inspect_detail")
public class TrdInspectDetail {

    private static final long serialVersionUID = 1L;


    @TableId(type = IdType.AUTO)
    private Long inspectId;
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String inspectNo;
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String inspectName;
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long inspectPlanId;
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String subsidiaryName;
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String inspectedDepartment;
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String disciplineCate;
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String checkLevel;
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String inspectCate;
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String inspectMode;
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String inspector;
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String inspectState;
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime inspectDate;
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime createTime;
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime updateTime;


}