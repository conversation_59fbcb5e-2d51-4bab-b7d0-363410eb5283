package com.huafon.area.utils;

import com.alibaba.fastjson.JSONArray;

import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * @Description: 经纬度转坐标x, y
 * @Date: 2022/1/6 9:06
 * @Author: zyf
 **/
public class TransformToCoordUtil {
    private static volatile double X1;
    private static volatile double Y1;

    private static volatile double LON1;
    private static volatile double LAT1;

    private static volatile double X2;
    private static volatile double Y2;

    private static volatile double LON2;
    private static volatile double LAT2;

    private static final DecimalFormat formater = new DecimalFormat();

    public static void init(JSONArray parseArray) {
        JSONArray array1 = parseArray.getJSONArray(0);
        JSONArray array2 = parseArray.getJSONArray(1);
        X1 = array1.getDouble(0);
        Y1 = array1.getDouble(1);
        LON1 = array1.getDouble(2);
        LAT1 = array1.getDouble(3);
        X2 = array2.getDouble(0);
        Y2 = array2.getDouble(1);
        LON2 = array2.getDouble(2);
        LAT2 = array2.getDouble(3);

    }

    public static void init(double x1, double y1, double lon1, double lat1, double x2, double y2, double lon2, double lat2) {
        X1 = x1;
        Y1 = y1;
        LON1 = lon1;
        LAT1 = lat1;
        X2 = x2;
        Y2 = y2;
        LON2 = lon2;
        LAT2 = lat2;
    }

    public static double[] transformToCoordinateSystemPosition(double lon, double lat, int scale) {

        double lon_cos = Math.cos(MathUtil.div(MathUtil.mul(LAT2, Math.PI), 180, scale));
//        double m = (lon2 - lon1) * lon_cos;
        double m = MathUtil.mul(MathUtil.sub(LON2, LON1), lon_cos);

//        double n = lat2 - lat1;
        double n = MathUtil.sub(LAT2, LAT1);

//        double p = (lon - lon1) * lon_cos;
        double p = MathUtil.mul(MathUtil.sub(lon, LON1), lon_cos);
//        double q = lat - lat1;
        double q = MathUtil.sub(lat, LAT1);

//        double M = x2 - x1;
        double M = MathUtil.sub(X2, X1);
//        double N = y2 - y1;
        double N = MathUtil.sub(Y2, Y1);

//        double a = ((p * p + q * q) * (M * M + N * N)) / (m * m + n * n);
        double a = MathUtil.div(MathUtil.mul(MathUtil.add(MathUtil.mul(p, p), MathUtil.mul(q, q)), MathUtil.add(MathUtil.mul(M, M), MathUtil.mul(N, N))), MathUtil.add(MathUtil.mul(m, m), MathUtil.mul(n, n)), scale);


        //Math.sqrt(x * x + y * y)
//        double b = ((m * p + q * n) * norm(M, N) * Math.sqrt(a)) / (norm(m, n) * norm(p, q));

//        double b = ((m * p + q * n) * Math.sqrt(M * M + N * N) * Math.sqrt(a)) / (Math.sqrt(m * m + n * n) * Math.sqrt(p * p + q * q));
        double b = MathUtil.div((MathUtil.mul(MathUtil.mul(MathUtil.add(MathUtil.mul(m, p), MathUtil.mul(q, n)), Math.sqrt(MathUtil.add(MathUtil.mul(M, M), MathUtil.mul(N, N)))), Math.sqrt(a))), (MathUtil.mul(Math.sqrt(MathUtil.add(MathUtil.mul(m, m), MathUtil.mul(n, n))), Math.sqrt(MathUtil.add(MathUtil.mul(p, p), MathUtil.mul(q, q))))), scale);

//        double c = Math.sqrt(b * b * N * N - (M * M + N * N) * (b * b - a * M * M));
        double c = Math.sqrt(MathUtil.sub(MathUtil.mul(MathUtil.mul(b, b), MathUtil.mul(N, N)), MathUtil.mul(MathUtil.add(MathUtil.mul(M, M), MathUtil.mul(N, N)), MathUtil.sub(MathUtil.mul(b, b), MathUtil.mul(MathUtil.mul(a, M), M)))));

//        double Q1 = (b * N + c) / (M * M + N * N);
        double Q1 = MathUtil.div(MathUtil.add(MathUtil.mul(b, N), c), MathUtil.add(MathUtil.mul(M, M), MathUtil.mul(N, N)), scale);

//        double Q2 = (b * N - c) / (M * M + N * N);
        double Q2 = MathUtil.div(MathUtil.sub(MathUtil.mul(b, N), c), MathUtil.add(MathUtil.mul(M, M), MathUtil.mul(N, N)), scale);

//        double P1 = (b - Q1 * N) / M;
        double P1 = MathUtil.div(MathUtil.sub(b, MathUtil.mul(Q1, N)), M, scale);

//        double P2 = (b - Q2 * N) / M;
        double P2 = MathUtil.div(MathUtil.sub(b, MathUtil.mul(Q2, N)), M, scale);

//        double x_1 = P1 + x1;
        double x_1 = MathUtil.add(P1, X1);
//        double y_1 = Q1 + y1;
        double y_1 = MathUtil.add(Q1, Y1);
//        double x_2 = P2 + x1;
        double x_2 = MathUtil.add(P2, X1);
//        double y_2 = Q2 + y1;
        double y_2 = MathUtil.add(Q2, Y1);

//        double judge1 = (x_1 - x1) * (y2 - y1) - (y_1 - y1) * (x2 - x1);
        double judge1 = MathUtil.sub(MathUtil.mul(MathUtil.sub(x_1, X1), MathUtil.sub(Y2, Y1)), MathUtil.mul(MathUtil.sub(y_1, Y1), MathUtil.sub(X2, X1)));

//        double judge2 = (x_2 - x1) * (y2 - y1) - (y_2 - y1) * (x2 - x1);
//        double judge = (lon - lon1) * (LAT2 - lat1) - (lat - lat1) * (lon2 - lon1);
        double judge = MathUtil.sub(MathUtil.mul(MathUtil.sub(lon, LON1), MathUtil.sub(LAT2, LAT1)), MathUtil.mul(MathUtil.sub(lat, LAT1), MathUtil.sub(LON2, LON1)));

        double x = 0;
        double y = 0;

        if (MathUtil.mul(judge, judge1) < 0) {
            x = x_1;
            y = y_1;
        } else {
            x = x_2;
            y = y_2;
        }

        double[] doubles = new double[2];

        formater.setMaximumFractionDigits(8);
        formater.setGroupingSize(0);
        formater.setRoundingMode(RoundingMode.FLOOR);

        doubles[0] = Double.valueOf(formater.format(x));
        doubles[1] = Double.valueOf(formater.format(y));
        return doubles;
    }

}
