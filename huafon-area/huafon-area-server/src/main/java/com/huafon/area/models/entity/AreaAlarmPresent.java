package com.huafon.area.models.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.huafon.area.dao.handler.IntegerArrayTypeHandler;
import com.huafon.area.enums.AreaPersonPassableTag;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import com.huafon.support.exceptions.ServiceException;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
* 区域：告警配置-在场人员
* <AUTHOR>
* @since 2024-01-10 16:49
*/
@Data
@TableName(value = "hf_area_alarm_present", autoResultMap = true)
public class AreaAlarmPresent extends BaseEntity{

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
    * 区域ID
    */
    @TableField(value = "area_id")
    private Long areaId;

    /**
    * 配置分组Code
    */
    @TableField(value = "group_code")
    private String groupCode;

    /**
     * 日期类型：1.工作日;2公休日;3节假日
     */
    @TableField(value = "time_type", typeHandler = IntegerArrayTypeHandler.class)
    private List<Integer> timeType;

    /**
    * 类型：INSIDER(内部人员)、CONTRACTOR(承包商)、CARD(绑卡人员)、VISITOR(访客)、DEPT(部门)、ALL_INSIDER(全部内部人员)、ALL_CONTRACTOR(全部承包商)、ALL_VISITOR(全部访客)
    */
    @TableField(value = "target_type")
    private AreaPersonPassableTag targetType;

    /**
    * 类型ID:如果是DEPT则是部门ID
    */
    @TableField(value = "target_id")
    private Integer targetId;

    /**
    * 人员的UserId
    */
    @TableField(value = "target_user_id")
    private Integer targetUserId;

    /**
    * 访客、承包商、绑卡人员存储身份证信息
    */
    @TableField(value = "target_code")
    private String targetCode;

    /**
    * 如果是人员存储人员姓名，如果是部门存储部门名称
    */
    @TableField(value = "name")
    private String name;

    /**
    * 头像
    */
    @TableField(value = "avatar")
    private String avatar;

    /**
    * 联系方式
    */
    @TableField(value = "contact")
    private String contact;

    /**
    * 工作单位
    */
    @TableField(value = "work_dept")
    private String workDept;

    /**
    * 开始时间
    */
    @TableField(value = "start_time")
    private LocalTime startTime;

    /**
    * 结束时间
    */
    @TableField(value = "end_time")
    private LocalTime endTime;

    /**
    * 操作时间
    */
    @TableField(value = "operation_time")
    private LocalDateTime operationTime;

    public String getKey() {
        switch (targetType) {
            case DEPT:
                return this.getTargetType().getValue() + "|" + this.getTargetId();
            case INSIDER:
            case CONTRACTOR:
            case CARD:
            case VISITOR:
            case OTHERS:
                return this.getTargetType().getValue() + "|" + this.getTargetUserId();
            case ALL_INSIDER:
            case ALL_CONTRACTOR:
            case ALL_VISITOR:
                return this.getTargetType().getValue();
            default:
                throw new ServiceException("人员类型不符合约定。");
        }
    }

    public String getUniqueId() {
        return "AreaAlarmPresent{" +
                ", targetType=" + targetType +
                ", targetUserId=" + targetUserId +
                ", targetId=" + targetId +
                ", groupCode='" + groupCode + '\'' +
                '}';
    }


}
