package com.huafon.area.models.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.huafon.area.api.enums.AreaLogPersonType;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Table: sdb_permission
 * demo类，可删除
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hf_area_white_black_log_person")
public class AreaWhiteBlackLogPerson extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField
    private Integer logId;
    @TableField
    private Long areaId;

    @TableField
    private Integer userId;
    @TableField
    private String username;
    @TableField
    private AreaLogPersonType type;

}