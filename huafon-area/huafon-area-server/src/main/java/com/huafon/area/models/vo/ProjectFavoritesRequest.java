package com.huafon.area.models.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface ProjectFavoritesRequest {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "项目收藏夹-基础信息")
    class BaseVo {

        @ApiModelProperty("projectId")
        private Long projectId;

        @ApiModelProperty("project名称")
        private String projectName;

        @ApiModelProperty("排序值")
        private Integer orderNum;

    }


}
