package com.huafon.area.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.LinkedListMultimap;
import com.google.common.collect.Multimap;
import com.huafon.area.dao.TrdInspectDetailMapper;
import com.huafon.area.models.entity.Area;
import com.huafon.area.models.entity.TrdInspectDetail;
import com.huafon.area.models.vo.TrdInspectDetailRequest;
import com.huafon.area.models.vo.TrdInspectDetailResponse;
import com.huafon.area.service.TrdInspectDetailService;
import com.huafon.common.utils.bean.BeanUtils;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.framework.mybatis.service.MybatisServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
@Transactional
public class TrdInspectDetailServiceImpl extends MybatisServiceImpl<TrdInspectDetailMapper, TrdInspectDetail> implements TrdInspectDetailService {

    @Override
    @DS("slave_1")
    public CommonPage<TrdInspectDetailResponse.InfoVo> pageQuery(TrdInspectDetailRequest.PageQuery pageQuery) {
        LambdaQueryWrapper<TrdInspectDetail> wrapper = this.getLambdaQuery();

        if (!StringUtils.isEmpty(pageQuery.getInspectState())) {
            wrapper = wrapper.eq(TrdInspectDetail::getInspectState, pageQuery.getInspectState());
        }
        wrapper = wrapper.eq(TrdInspectDetail::getInspectedDepartment, pageQuery.getInspectedDepartment());
        wrapper = wrapper.orderByDesc(TrdInspectDetail::getUpdateTime);

        CommonPage<TrdInspectDetail> page = this.commonPage(pageQuery.getPageInfo(), wrapper);
        CommonPage<TrdInspectDetailResponse.InfoVo> result = CommonPage.copy(page);
        result.setList(page.getList().stream().map(x -> BeanUtils.convert(x, TrdInspectDetailResponse.InfoVo.class)).collect(Collectors.toList()));
        return result;
    }

    @Override
    @DS("slave_1")
    public Map<Long, Long> mapData(List<Area> areas) {
        HashMap<Long, Long> result = new HashMap<>();
        if (areas.isEmpty()) {
            return result;
        }
        Multimap<String, Long> areaMap = LinkedListMultimap.create();
        areas.forEach(area -> areaMap.put(area.getDepartmentName(), area.getId()));
        Set<String> departmentNames = areas.stream().map(Area::getDepartmentName).collect(Collectors.toSet());

        List<TrdInspectDetail> datas = this.lambdaQuery().in(TrdInspectDetail::getInspectedDepartment, departmentNames).list();
        for (TrdInspectDetail data : datas) {
            if (areaMap.containsKey(data.getInspectedDepartment())) {
                Collection<Long> areaIds = areaMap.get(data.getInspectedDepartment());
                for (Long areaId : areaIds) {
                    Long count = result.getOrDefault(areaId, 0L);
                    result.put(areaId, ++count);
                }
            }
        }
        return result;
    }

    @Override
    @DS("slave_1")
    public Map<String, String> unspecified(List<Area> areas) {
        HashMap<String, String> result = new HashMap<>();
        if (areas.isEmpty()) {
            return result;
        }

        LambdaQueryWrapper<TrdInspectDetail> lambdaQuery = this.getLambdaQuery();
        if (!areas.isEmpty()) {
            Set<String> areaNames = areas.stream().map(Area::getName).collect(Collectors.toSet());
            lambdaQuery.notIn(TrdInspectDetail::getInspectedDepartment, areaNames);
        }
        List<TrdInspectDetail> datas = this.list(lambdaQuery);
        datas.forEach(data -> result.put(data.getInspectNo(), data.getInspectedDepartment()));
        return result;

    }

    @Override
    @DS("slave_1")
    public Map<String, Integer> tabCount(String inspectedDepartment) {
        Integer notStarted = this.lambdaQuery().eq(TrdInspectDetail::getInspectedDepartment, inspectedDepartment).eq(TrdInspectDetail::getInspectState, "未开始").count();
        Integer inProgress = this.lambdaQuery().eq(TrdInspectDetail::getInspectedDepartment, inspectedDepartment).eq(TrdInspectDetail::getInspectState, "进行中").count();
        Integer finished = this.lambdaQuery().eq(TrdInspectDetail::getInspectedDepartment, inspectedDepartment).eq(TrdInspectDetail::getInspectState, "已完成").count();
        return new HashMap<String, Integer>() {{
            put("notStarted", notStarted);
            put("inProgress", inProgress);
            put("finished", finished);
        }};
    }
}
