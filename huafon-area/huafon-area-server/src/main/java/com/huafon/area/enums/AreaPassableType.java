package com.huafon.area.enums;

import com.huafon.framework.mybatis.enums.IDictEnum;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * date  : 2022-03-18 14:01
 * description:
 */
@Getter
public enum AreaPassableType implements IDictEnum<String> {
    CUSTOM("CUSTOM", "自定义"),
    WHITE("WHITE", "白名单"),
    BLACK("BLACK", "黑名单"),
    NO_LIMIT("NO_LIMIT", "不限制"),
    ;

    private final String value;

    private final String name;

    AreaPassableType(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public AreaPersonPassableType getPersonPassableType() {
        if (Objects.equals(this, WHITE)) {
            return AreaPersonPassableType.ABLE;
        } else {
            return AreaPersonPassableType.UNABLE;
        }
    }
}