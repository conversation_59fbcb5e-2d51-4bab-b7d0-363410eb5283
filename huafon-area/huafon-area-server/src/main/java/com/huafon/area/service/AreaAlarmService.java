package com.huafon.area.service;

import com.huafon.area.models.entity.AreaAlarm;
import com.huafon.area.models.vo.AreaRequest;
import com.huafon.area.models.vo.AreaResponse;
import com.huafon.framework.mybatis.service.MybatisIService;

import java.util.Collection;
import java.util.List;

/**
* 区域：告警配置
* <AUTHOR>
* @since 2024-01-10 16:41
*/
public interface AreaAlarmService extends MybatisIService<AreaAlarm> {

    AreaResponse.AlarmVO getAreaAlarm(Long areaId);

    List<AreaResponse.ThrdAlarmVO> getAreaAlarm();

    List<AreaResponse.AreaAlarmInfoVO> getAreaAlarm(Collection<Long> areaIds);

    void modifyAreaAlarm(AreaRequest.AlarmAddVO alarmInfo);

    void deleteByAreaIds(Collection<Long> areaIds);
}
