package com.huafon.area.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.huafon.area.api.dto.AreaRpcDto;
import com.huafon.area.api.dto.CountCardInfoDto;
import com.huafon.area.api.dto.NodeStructureDto;
import com.huafon.area.api.enums.AreaLogPersonType;
import com.huafon.area.api.service.AreaRpcService;
import com.huafon.area.dao.AreaMapper;
import com.huafon.area.models.entity.Area;
import com.huafon.area.models.entity.AreaWhiteBlackLogPerson;
import com.huafon.area.models.vo.AreaRequest;
import com.huafon.area.models.vo.AreaResponse;
import com.huafon.area.service.AreaService;
import com.huafon.area.service.AreaWhiteBlackLogService;
import com.huafon.common.utils.BaseUtils;
import com.huafon.common.utils.StreamUtils;
import com.huafon.common.utils.bean.BeanUtils;
import com.huafon.framework.mybatis.enums.DelFlag;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.portal.api.dto.dept.DeptDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 区域登记
 */
@Slf4j
@DubboService
public class AreaRpcServiceImpl implements AreaRpcService {

    private final AreaService service;
    private final AreaWhiteBlackLogService logService;
    private final AreaMapper mapper;

    public AreaRpcServiceImpl(AreaService service, AreaWhiteBlackLogService logService, AreaMapper mapper) {
        this.service = service;
        this.logService = logService;
        this.mapper = mapper;
    }

    @Override
    public List<CountCardInfoDto> countCardById(List<Long> ids, List<Integer> tenantIds, String type) {
        return BeanUtils.convert(CountCardInfoDto.class, service.countCardById(AreaRequest.CountCardByIdVo.builder().ids(ids).build(), tenantIds, type));
    }

    @Override
    public Map<Long, String> getNodeNameByIds(List<Long> ids) {
        Map<Long, String> result = new HashMap<>(ids.size());
        List<AreaResponse.NodeStructureVo> structures = service.getNodeStructure(ids);

        for (AreaResponse.NodeStructureVo node : structures) {
            result.put(node.getId(), node.getStructureName());
        }
        return result;
    }

    @Override
    public Map<Long, Long> getBuildNodeByIds(List<Long> ids) {
        Map<Long, Long> result = new HashMap<>();
        List<AreaResponse.NodeStructureVo> structures = service.getNodeStructure(ids);
        for (AreaResponse.NodeStructureVo node : structures) {
            if (Objects.nonNull(node.getIds()) && !node.getIds().isEmpty()) {
                List<Area> areaTmps = service.listByIds(node.getIds());
                Map<Long, Area> areaMap = new HashMap<>(areaTmps.size());
                areaTmps.forEach(areaTmp -> areaMap.put(areaTmp.getId(), areaTmp));
                for (Long nodeId : node.getIds()) {
                    Area areaTmp = areaMap.get(nodeId);
                    if (Objects.equals(areaTmp.getType(), "10")) {
                        result.put(node.getId(), nodeId);
                        break;
                    }
                }

            }
        }
        structures.clear();
        return result;
    }

    @Override
    public AreaRpcDto getByParentAndName(Long pid, String name, Integer tenantId) {
        Area area = service.lambdaQuery().eq(Area::getParentId, pid).eq(Area::getName, name).eq(Area::getTenantId, tenantId).one();
        if (Objects.nonNull(area)) {
            return new AreaRpcDto(area.getId(), area.getParentId(), area.getTenantId(), area.getName(), null, area.getRemark(), null, area.getLatitude(), area.getLongitude(),area.getFenceId(),area.getX(),area.getY(),null);
        }
        return null;
    }

//    @Override
//    public AreaRpcDto getByFenceId(Long fenceId) {
//        Area area = service.lambdaQuery().eq(Area::getFenceId, fenceId).last("limit 1").one();
//        if (Objects.nonNull(area)) {
//            return new AreaRpcDto(area.getId(), area.getParentId(), area.getTenantId(), area.getName(), null, area.getRemark(), null, area.getLatitude(), area.getLongitude());
//        }
//        return null;
//    }

    @Override
    public AreaRpcDto getById(Long id) {
        return BeanUtils.convert(service.getById(id), AreaRpcDto.class);
    }

    @Override
    public List<AreaRpcDto> listById(Collection<Long> ids) {
        return BeanUtils.convert(AreaRpcDto.class, service.listByIds(ids));
    }

    @Override
    public NodeStructureDto getNodeById(Long id) {
        List<AreaResponse.NodeStructureVo> structures = service.getNodeStructure(Collections.singletonList(id));
        if (!structures.isEmpty()) {
            return BeanUtils.convert(structures.get(0), NodeStructureDto.class);
        }
        return null;
    }

    @Override
    public List<NodeStructureDto> getNodeByIds(List<Long> ids) {
        return BeanUtils.convert(NodeStructureDto.class, service.getNodeStructure(ids));
    }

    @Override
    public CommonPage<AreaRpcDto> getNodeNameByType(Integer pageNo, Integer pageSize, String type, Integer tenantId) {
        LambdaQueryWrapper<Area> wrapper = service.getLambdaQuery();
        if (!StringUtils.isEmpty(type)) {
            wrapper = wrapper.eq(Area::getType, type);
        }
        if (Objects.nonNull(tenantId)) {
            wrapper = wrapper.eq(Area::getTenantId, tenantId);
        }
        wrapper = wrapper.orderByDesc(Area::getOrderNum, Area::getId);
        CommonPage<Area> page = service.commonPage(pageNo, pageSize, wrapper);
        CommonPage<AreaRpcDto> result = CommonPage.copy(page);
        result.setList(page.getList().stream().map(x -> AreaRpcDto.builder().id(x.getId()).tenantId(x.getTenantId()).name(x.getName()).remark(x.getRemark()).build()).collect(Collectors.toList()));
        return result;
    }

    @Override
    public List<AreaRpcDto> getNodeNameByType(String type, String searchKey) {
        LambdaQueryWrapper<Area> wrapper = service.getLambdaQuery();
        if (!StringUtils.isEmpty(type)) {
            wrapper = wrapper.eq(Area::getType, type);
        }
        if (!StringUtils.isEmpty(searchKey)) {
            wrapper = wrapper.like(Area::getName, searchKey);
        }
        wrapper = wrapper.orderByDesc(Area::getOrderNum, Area::getId);
        List<Area> areas = service.list(wrapper);
        return areas.stream().map(x -> AreaRpcDto.builder().id(x.getId()).tenantId(x.getTenantId()).name(x.getName()).remark(x.getRemark()).build()).collect(Collectors.toList());
    }


    @Override
    public List<Long> getSelfAndChildId(Long id) {
        List<Long> result = new ArrayList<>();
        Area area = service.getById(id);
        if (Objects.isNull(area)) {
            return null;
        }
        List<AreaResponse.TreeItemVo> areaList = mapper.getChildByParentId(id, area.getTenantId());
        if (Objects.nonNull(areaList) && !areaList.isEmpty()) {
            result.addAll(areaList.stream().map(AreaResponse.TreeItemVo::getId).collect(Collectors.toList()));
        }
        result.add(id);
        areaList.clear();
        return result;
    }

    @Override
    public List<Long> getSelfAndChildIds(List<Long> ids) {
        List<Long> result = new ArrayList<>();
        List<AreaResponse.TreeItemVo> areaList = mapper.getChildByParentIds(ids, null);
        result.addAll(areaList.stream().map(AreaResponse.TreeItemVo::getId).collect(Collectors.toList()));
        result.addAll(ids);
        return result;
    }

    @Override
    public AreaRpcDto findAreaByNodeName(String areaStr, String regex, Integer tenantId) {
        String[] areas = areaStr.split(regex);
        Long pid = 0L;
        for (int i = 0; i < areas.length; i++) {
            String child = areas[i];
            AreaRpcDto tmp = this.getByParentAndName(pid, child, tenantId);
            if (i == areas.length - 1 && Objects.nonNull(tmp)) {
                return tmp;
            } else if (Objects.nonNull(tmp)) {
                pid = tmp.getId();
            } else {
                return null;
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addLog(Long areaId, String source, String description, Integer userId) {
        logService.add(areaId, source, description, userId, null, Collections.singleton(AreaWhiteBlackLogPerson.builder().userId(userId).type(AreaLogPersonType.INTRUDE).build()));
    }


    @Override
    public Map<Long, Long> findIdCodeMap(List<Long> fenceCodes) {
        LambdaQueryWrapper<Area> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BaseUtils.isNotEmpty(fenceCodes), Area::getFenceId, fenceCodes);
        queryWrapper.eq(BaseUtils.isNotEmpty(fenceCodes) && fenceCodes.size() == 1, Area::getFenceId, fenceCodes.get(0));
        List<Area> list = mapper.selectList(queryWrapper);
        return StreamUtils.propMap(list, Area::getFenceId, Area::getId);
    }

    @Override
    public Map<Long, AreaRpcDto> getBuildNodeInfoMap(List<Long> ids) {
        Map<Long, AreaRpcDto> map = new HashMap<>();
        if (!CollectionUtils.isEmpty(ids)) {
            List<AreaResponse.NodeStructureVo> structures = service.getNodeStructure(ids);
            for (AreaResponse.NodeStructureVo node : structures) {
                if (Objects.nonNull(node.getIds()) && !node.getIds().isEmpty()) {
                    List<Area> areaTmps = service.listByIds(node.getIds());
                    Map<Long, Area> areaMap = new HashMap<>(areaTmps.size());
                    areaTmps.forEach(areaTmp -> areaMap.put(areaTmp.getId(), areaTmp));
                    for (Long nodeId : node.getIds()) {
                        Area areaTmp = areaMap.get(nodeId);
                        if (Objects.equals(areaTmp.getType(), "10")) {
                            AreaRpcDto areaRpcDto = new AreaRpcDto();
                            BeanUtils.copyProperties(areaTmp,areaRpcDto);
                            map.put(node.getId(), areaRpcDto);
                            break;
                        }
                    }

                }
            }
            structures.clear();

        }
        return map;
    }

    @Override
    public List<AreaRpcDto> getNextNodeId(Integer id) {
        List<AreaRpcDto> result = new ArrayList<>();
        List<Area> childIdsById = service.findChildIdsById(id);
        Area byId = service.getById(id);
        if (!CollectionUtils.isEmpty(childIdsById)){
            for (Area area:childIdsById){
                AreaRpcDto areaRpcDto = initDto(area);
                if (Objects.nonNull(areaRpcDto)) {
                    result.add(areaRpcDto);
                }
            }
        }
        if (!Objects.isNull(byId)){
            AreaRpcDto areaRpcDto = initDto(byId);
            if (Objects.nonNull(areaRpcDto)) {
                result.add(areaRpcDto);
            }
        }
        return result;
    }

    private AreaRpcDto initDto(Area area) {
        AreaRpcDto areaRpcDto = null;
        if (Objects.nonNull(area)){
            areaRpcDto = new AreaRpcDto();
            areaRpcDto.setId(area.getId());
            areaRpcDto.setName(area.getName());
            areaRpcDto.setParentId(area.getParentId());
            areaRpcDto.setFenceId(area.getFenceId());
            areaRpcDto.setFencePoint(area.getFencePoint());
            areaRpcDto.setTenantId(area.getTenantId());
        }
        return areaRpcDto;
    }

    @Override
    public List<AreaRpcDto> getSecondNodes(Integer tenantId) {
        List<AreaRpcDto> result = new ArrayList<>();
        List<Area> secondNodes = service.findSecondNodes(tenantId);
        if (!CollectionUtils.isEmpty(secondNodes)){
            for (Area area:secondNodes){
                AreaRpcDto areaRpcDto = initDto(area);
                result.add(areaRpcDto);
            }
        }
        return result;
    }

    @Override
    public Map<Long, List<Long>> getSelfAndChildIdsMap(List<Long> ids) {
        Map<Long, List<Long>> result = new HashMap<>();
        if (!CollectionUtils.isEmpty(ids)){
            for (Long id:ids) {
                List<Long> idsByParentId = service.findIdsByParentId(id);
                if (!CollectionUtils.isEmpty(idsByParentId)) {
                    result.put(id, idsByParentId);
                }
            }
        }
        return result;
    }

    @Override
    public List<AreaRpcDto> getBuildAndChildNodesWithFence() {
        List<AreaRpcDto> result = new ArrayList<>();
        LambdaQueryWrapper<Area> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in( Area::getType, Arrays.asList("10","15"));
        queryWrapper.isNotNull( Area::getFenceId);
        List<Area> list = mapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(list)){
            for (Area area:list) {
                AreaRpcDto areaRpcDto = initDto(area);
                result.add(areaRpcDto);
            }
        }
        return result;
    }

    @Override
    public Map<String, String> getCustomStructureMap(List<String> areaIdArrList) {
        Map<String, String> customStructureMap = new HashMap<>();
        List<Long> areaIdList = getAreaIdList(areaIdArrList);
        if (!CollectionUtils.isEmpty(areaIdList)) {
            LambdaQueryWrapper<Area> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(Area::getId, areaIdList);
            queryWrapper.eq(Area::getIsDel, DelFlag.SAVE.getValue());
            List<Area> list = mapper.selectList(queryWrapper);
            if (!CollectionUtils.isEmpty(list)){
                Map<Long, String> idNameMap = StreamUtils.propMap(list, Area::getId, Area::getName);
                for (String areaIdArr:areaIdArrList) {
                    String[] split = areaIdArr.split(",");
                    StringJoiner stringJoiner = new StringJoiner("/");
                    for (String areaId:split) {
                        String s = idNameMap.get(Long.valueOf(areaId));
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(s)){
                            stringJoiner.add(s);
                        }
                    }
                    customStructureMap.put(areaIdArr, stringJoiner.toString());
                }
            }
        }
        return customStructureMap;
    }

    private List<Long> getAreaIdList(List<String> areaIdArrList) {
        List<Long> areaIdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(areaIdArrList)){
            for (String areaIdArr:areaIdArrList) {
                String[] split = areaIdArr.split(",");
                for (String areaId:split) {
                    areaIdList.add(Long.valueOf(areaId));
                }
            }
        }
        return areaIdList;
    }
}