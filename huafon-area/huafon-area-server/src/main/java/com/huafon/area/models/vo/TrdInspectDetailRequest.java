package com.huafon.area.models.vo;

import com.huafon.framework.mybatis.pojo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotNull;

public interface TrdInspectDetailRequest {


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @ApiModel(description = "第三方(田间云)巡检数据")
    class PageQuery extends PageRequest {

        @NotNull
        @ApiModelProperty("区域id")
        private Long areaId;

        @ApiModelProperty("部门名称")
        private String inspectedDepartment;

        @ApiModelProperty("状态")
        private String inspectState;
    }


}
