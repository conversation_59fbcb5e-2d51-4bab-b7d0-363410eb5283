package com.huafon.form.service.impl;

import com.huafon.form.mapper.CustomComponentRepositoryMapper;
import com.huafon.form.models.CustomComponentRepository;
import com.huafon.form.service.CustomComponentRepositoryService;
import com.huafon.framework.mybatis.service.MybatisServiceImpl;
import com.huafon.support.core.pojo.SystemCode;
import com.huafon.support.exceptions.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CustomComponentRepositoryServiceImpl extends MybatisServiceImpl<CustomComponentRepositoryMapper, CustomComponentRepository> implements CustomComponentRepositoryService {

    @Override
    public CustomComponentRepository findById(Integer id) {
        return Optional.ofNullable(this.getById(id)).orElseThrow(() -> new ServiceException(SystemCode.DATA_NOT_EXIST));
    }

}