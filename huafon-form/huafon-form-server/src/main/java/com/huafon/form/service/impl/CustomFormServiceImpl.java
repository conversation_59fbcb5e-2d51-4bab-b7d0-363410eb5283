package com.huafon.form.service.impl;

import com.huafon.common.config.TenantContext;
import com.huafon.common.utils.bean.BeanUtils;
import com.huafon.form.enums.ApplicationType;
import com.huafon.form.mapper.CustomFormMapper;
import com.huafon.form.models.CustomApplicationModule;
import com.huafon.form.models.CustomForm;
import com.huafon.form.models.CustomFormField;
import com.huafon.form.models.CustomTable;
import com.huafon.form.parser.pojo.FormField;
import com.huafon.form.service.*;
import com.huafon.form.vo.CustomFormRequest;
import com.huafon.form.vo.CustomFormResponse;
import com.huafon.framework.mybatis.enums.DelFlag;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import com.huafon.framework.mybatis.service.MybatisServiceImpl;
import com.huafon.support.config.UserContext;
import com.huafon.support.core.pojo.SystemCode;
import com.huafon.support.exceptions.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CustomFormServiceImpl extends MybatisServiceImpl<CustomFormMapper, CustomForm> implements CustomFormService {

    private final CustomFormOriginalService originalService;
    private final CustomFormParser parser;
    private final CustomApplicationModuleService moduleService;
    private final CustomFormFieldService fieldService;
    private final CustomFormFieldValidService validService;
    private final CustomTableService tableService;

    public CustomFormServiceImpl(CustomFormOriginalService originalService, CustomFormParser parser, CustomApplicationModuleService moduleService, CustomFormFieldService fieldService, CustomFormFieldValidService validService, CustomTableService tableService) {
        this.originalService = originalService;
        this.parser = parser;
        this.moduleService = moduleService;
        this.fieldService = fieldService;
        this.validService = validService;
        this.tableService = tableService;
    }


    @Override
    public CustomForm findById(Integer id) {
        return Optional.ofNullable(this.getById(id)).orElseThrow(() -> new ServiceException(SystemCode.DATA_NOT_EXIST));
    }

    @Override
    public CustomFormResponse.InfoVo selectById(Integer id) {
        CustomFormResponse.InfoVo info = new CustomFormResponse.InfoVo();
        CustomForm form = this.findById(id);
        BeanUtils.convert(form, info);
        info.setOriginal(originalService.getOriginal(id));
        info.setFields(fieldService.getFields(id));
        return info;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submit(CustomFormRequest.SubmitVo submitVo) {
        Integer id = this.init(submitVo);

        originalService.save(id, submitVo.getOriginal());

        //解析JSON
        List<FormField> formFields = parser.parseOriginal(submitVo.getOriginal(), submitVo.getTableName());

        //初始化数据库实体表
        Map<String, CustomTable> tableMap = tableService.init(formFields.stream().map(FormField::getTableName).distinct().collect(Collectors.toList()));
//        维护表单字段
        List<CustomFormField> customFormFields = fieldService.saveBatch(formFields, id, tableMap);
//        维护数据库表字段
//        tableService.maintainTable(customFormFields);


// todo     维护表单字段校验 这期先放着好了 validService
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void clear(Integer id) {

        this.lambdaUpdate().set(BaseEntity::getModifyBy, UserContext.getId()).set(BaseEntity::getModifyTime, LocalDateTime.now()).set(BaseEntity::getIsDel, DelFlag.DELELTED.getValue()).in(CustomForm::getId, id).update();
        this.fieldService.lambdaUpdate().set(BaseEntity::getModifyBy, UserContext.getId()).set(BaseEntity::getModifyTime, LocalDateTime.now()).set(BaseEntity::getIsDel, DelFlag.DELELTED.getValue()).in(CustomFormField::getFormId, id).update();


    }


    public Integer init(CustomFormRequest.SubmitVo submitVo) {
        Integer id = submitVo.getId();

        CustomForm form;
        if (Objects.isNull(id)) {
            form = new CustomForm(submitVo.getName(), tableService.init(submitVo.getTableName()).getId(), TenantContext.getOne());
            form.setCreate(UserContext.getId());
            form.setTenantId(TenantContext.getOne());
            this.save(form);
        } else {
            form = findById(submitVo.getId());
            form.setName(submitVo.getName());
            form.setTableId(tableService.init(submitVo.getTableName()).getId());
            form.setModify(UserContext.getId());
            form.setTenantId(TenantContext.getOne());
            this.updateById(form);
        }
        id = form.getId();
        CustomApplicationModule module = new CustomApplicationModule(id, ApplicationType.FORM, submitVo.getApplicationId());
        module.setCreate(UserContext.getId());
        moduleService.save(module);
        return id;
    }
}