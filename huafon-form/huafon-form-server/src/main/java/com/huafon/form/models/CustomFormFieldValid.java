package com.huafon.form.models;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import lombok.*;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value = "hf_custom_form_field_valid")
public class CustomFormFieldValid extends BaseEntity {
    private static final long serialVersionUID = 8245930190290767700L;
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField(value = "field_id")
    private Integer fieldId;

    @TableField(value = "not_null")
    private String notNull;


    @TableField(value = "check_size_max")
    private Boolean checkSizeMax;

    @TableField(value = "size_max")
    private Integer sizeMax;

    @TableField(value = "check_size_min")
    private Boolean checkSizeMin;

    @TableField(value = "size_min")
    private Integer sizeMin;

    @TableField(value = "check_date_pattern")
    private Boolean checkDatePattern;

    @TableField(value = "date_pattern")
    private String datePattern;

    @TableField(value = "check_regular")
    private Boolean checkRegular;

    @TableField(value = "regular")
    private String regular;

}