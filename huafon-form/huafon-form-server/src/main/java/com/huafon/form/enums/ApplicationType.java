package com.huafon.form.enums;

import com.huafon.framework.mybatis.enums.IDictEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * date  : 2022-03-18 14:01
 * description:
 */
@Getter
public enum ApplicationType implements IDictEnum<String> {
    FORM("FORM", "表单"),
    FLOW_FORM("FLOW_FORM", "流程表单"),
    PLAN("PLAN", "计划任务"),
    ;

    private final String value;

    private final String name;

    ApplicationType(String value, String name) {
        this.value = value;
        this.name = name;
    }
}