package com.huafon.form.sql;

import cn.hutool.core.builder.Builder;
import com.huafon.support.exceptions.ServiceException;
import com.huafon.support.utils.StringUtils;

import java.util.Objects;

public class SqlColumnBuilder implements Builder<String> {

    private String table;
    private String column;

    private String type;

    private String operation;
    private final StringBuilder sql = new StringBuilder();

    public static SqlColumnBuilder create() {
        return new SqlColumnBuilder();
    }

    public SqlColumnBuilder alterTable(String tableName) {
        this.table = tableName;
        sql.append("alter table ");
        sql.append(tableName);
        sql.append(" ");
        return this;
    }

    public SqlColumnBuilder drop(String column) {
        sql.append("drop column ");
        sql.append(column);
        sql.append(";");
        this.column = column;
        this.operation = "DROP";
        return this;
    }

    public SqlColumnBuilder type(String type) {
        if (Objects.equals(this.operation, "DROP")) {
            throw new ServiceException("sql拼接异常.");
        }
        sql.append(" ");
        sql.append(type);
        this.type = type;
        return this;
    }

    public SqlColumnBuilder defaultValue(String defaultValue) {
        if (Objects.equals(this.operation, "DROP")) {
            throw new ServiceException("sql拼接异常.");
        }

        if (StringUtils.isEmpty(this.type)) {
            throw new ServiceException("sql拼接异常.");
        }
        this.sql.append("default '" + defaultValue + "'");
        return this;
    }

    public SqlColumnBuilder notNull(Boolean notNull) {
        if (Objects.equals(this.operation, "DROP")) {
            throw new ServiceException("sql拼接异常.");
        }

        if (StringUtils.isEmpty(this.type)) {
            throw new ServiceException("sql拼接异常.");
        }
        this.sql.append(notNull ? " not null " : " null ");
        return this;
    }

    public SqlColumnBuilder add(String column) {
        sql.append("add ");
        sql.append(column);
        this.column = column;
        this.operation = "ADD";
        /**
         * alter table security_environment.hf_application_module1
         * add column_8 varchar default 'as' not null;
         * <p>
         * comment on column security_environment.hf_application_module1.column_8 is '1231111';
         */
        return this;
    }


    @Override
    public String build() {
        this.sql.append(";");
        return this.sql.toString();
    }


}