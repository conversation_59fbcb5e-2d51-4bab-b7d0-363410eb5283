package com.huafon.form.service.impl;

import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSON;
import com.huafon.common.utils.bean.BeanUtils;
import com.huafon.form.mapper.CustomFormFileMapper;
import com.huafon.form.models.CustomFormFile;
import com.huafon.form.service.CustomFormFileService;
import com.huafon.form.vo.CustomApiResponse;
import com.huafon.framework.mybatis.enums.DelFlag;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import com.huafon.framework.mybatis.service.MybatisServiceImpl;
import com.huafon.support.exceptions.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CustomFormFileServiceImpl extends MybatisServiceImpl<CustomFormFileMapper, CustomFormFile> implements CustomFormFileService {

    @Override
    public void save(Integer formId, long snowflakeId, Map<String, Object> fileMap) {
        for (Map.Entry<String, Object> entry : fileMap.entrySet()) {
            String fieldName = entry.getKey();
            Object params = entry.getValue();
            if (params instanceof List) {
                List files = (List) params;
                for (int i = 0; i < files.size(); i++) {
                    Map<String, Object> json = (Map<String, Object>) files.get(i);
                    this.saveOrUpdateByCode(new CustomFormFile(formId, snowflakeId, fieldName, json));
                }
            } else {
                throw new ServiceException("文件格式不符合规范 " + JSON.toJSONString(params));
            }
        }
    }

    @Override
    public Object getByFieldName(Integer formId, Long dataId, String fieldName) {
        List<CustomFormFile> files = this.lambdaQuery().eq(CustomFormFile::getFormId, formId).eq(CustomFormFile::getDataId, dataId).eq(CustomFormFile::getFieldName, fieldName).eq(BaseEntity::getIsDel, DelFlag.SAVE.getValue()).list();
        return files.stream().map(x -> new CustomApiResponse.FileVo(x.getCode(), x.getName(), x.getUrl(), x.getType())).collect(Collectors.toList());
    }

    private void saveOrUpdateByCode(CustomFormFile file) {
        CustomFormFile dbData = this.lambdaQuery().eq(CustomFormFile::getCode, file.getCode()).eq(BaseEntity::getIsDel, DelFlag.SAVE.getValue()).one();
        if (Objects.isNull(dbData)) {
            file.setCode(UUID.fastUUID().toString());
            file.setCreate(0L);
            this.save(file);
        } else {
            BeanUtils.convert(file, dbData);
            dbData.setModify(0L);
            this.updateById(file);
        }
    }

}