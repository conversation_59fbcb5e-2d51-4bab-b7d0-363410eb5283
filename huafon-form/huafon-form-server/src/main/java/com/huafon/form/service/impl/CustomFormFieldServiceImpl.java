package com.huafon.form.service.impl;

import com.huafon.common.utils.bean.BeanUtils;
import com.huafon.form.mapper.CustomFormFieldMapper;
import com.huafon.form.models.CustomFormField;
import com.huafon.form.models.CustomTable;
import com.huafon.form.parser.pojo.FormField;
import com.huafon.form.service.CustomComponentRepositoryService;
import com.huafon.form.service.CustomFormFieldService;
import com.huafon.form.service.CustomTableService;
import com.huafon.form.vo.CustomFormResponse;
import com.huafon.framework.mybatis.enums.DelFlag;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import com.huafon.framework.mybatis.service.MybatisServiceImpl;
import com.huafon.support.config.UserContext;
import com.huafon.support.core.pojo.SystemCode;
import com.huafon.support.exceptions.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CustomFormFieldServiceImpl extends MybatisServiceImpl<CustomFormFieldMapper, CustomFormField> implements CustomFormFieldService {

    private final CustomComponentRepositoryService repositoryService;
    private final CustomTableService tableService;

    public CustomFormFieldServiceImpl(CustomComponentRepositoryService repositoryService, CustomTableService tableService) {
        this.repositoryService = repositoryService;
        this.tableService = tableService;
    }

    @Override
    public CustomFormField findById(Integer id) {
        return Optional.ofNullable(this.getById(id)).orElseThrow(() -> new ServiceException(SystemCode.DATA_NOT_EXIST));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<CustomFormField> saveBatch(List<FormField> formFields, Integer formId, Map<String, CustomTable> tableMap) {
        List<CustomFormField> fields = new ArrayList<>();
        List<FormField> updates = new ArrayList<>();
        List<FormField> adds = new ArrayList<>();
        List<String> drops = new ArrayList<>();

        List<String> fieldNames = formFields.stream().map(FormField::getFieldName).collect(Collectors.toList());
        List<CustomFormField> fieldDbs = this.lambdaQuery().eq(CustomFormField::getFormId, formId).list();

        Map<String, CustomFormField> fieldDbMap = fieldDbs.stream().collect(Collectors.toMap(CustomFormField::getFieldName, x -> x));
        Map<String, FormField> fieldMap = formFields.stream().collect(Collectors.toMap(FormField::getFieldName, x -> x));

        for (String fieldName : fieldNames) {
            FormField formField = fieldMap.get(fieldName);
            if (fieldDbMap.containsKey(fieldName)) {
                //更新字段
                updates.add(formField);
            } else {
//                新增字段
                adds.add(formField);
            }
        }

        for (CustomFormField field : fieldDbs) {
            if (!fieldMap.containsKey(field.getFieldName())) {
                drops.add(field.getFieldName());
            }
        }
        tableService.maintainTable(adds, updates, drops, tableMap);
        this.maintainFromField(formId, adds, updates, drops, tableMap, fieldDbMap);

        return fields;
    }

    private void maintainFromField(Integer formId, List<FormField> adds, List<FormField> updates, List<String> drops, Map<String, CustomTable> tableMap, Map<String, CustomFormField> fieldDbMap) {

        for (int i = 0; i < adds.size(); i++) {
            FormField field = adds.get(i);
            CustomFormField tmp = BeanUtils.convert(field, CustomFormField.class);
            tmp.setTableId(tableMap.get(field.getTableName()).getId());
            tmp.setFormId(formId);
            tmp.setCreate(UserContext.getId());
            this.save(tmp);
        }
        for (int i = 0; i < updates.size(); i++) {
            FormField field = updates.get(i);
            CustomFormField tmp = fieldDbMap.get(field.getFieldName());
            BeanUtils.convert(field, tmp, new String[]{"componentId", "fieldType"});
            tmp.setTableId(tableMap.get(field.getTableName()).getId());
            tmp.setFormId(formId);
            tmp.setModify(UserContext.getId());
            this.updateById(tmp);
        }
        for (int i = 0; i < drops.size(); i++) {
            String field = drops.get(i);
            CustomFormField tmp = fieldDbMap.get(field);
            this.lambdaUpdate().set(BaseEntity::getModifyBy, UserContext.getId())
                    .set(BaseEntity::getModifyTime, LocalDateTime.now())
                    .set(BaseEntity::getIsDel, DelFlag.DELELTED.getValue())
                    .eq(CustomFormField::getId, tmp.getId()).update();
        }
    }

    @Override
    public List<CustomFormResponse.FieldVo> getFields(Integer id) {
        List<CustomFormField> fields = this.lambdaQuery().eq(CustomFormField::getFormId, id).eq(BaseEntity::getIsDel, DelFlag.SAVE.getValue()).list();
        return fields.stream().map(x -> BeanUtils.convert(x, CustomFormResponse.FieldVo.class)).collect(Collectors.toList());
    }

}