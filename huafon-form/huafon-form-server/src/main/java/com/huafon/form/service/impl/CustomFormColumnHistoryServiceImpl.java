package com.huafon.form.service.impl;

import com.huafon.form.mapper.CustomFormColumnHistoryMapper;
import com.huafon.form.models.CustomFormColumnHistory;
import com.huafon.form.models.CustomFormField;
import com.huafon.form.service.CustomFormColumnHistoryService;
import com.huafon.form.service.CustomFormFieldService;
import com.huafon.form.vo.CustomFormColumnHistoryRequest;
import com.huafon.form.vo.CustomFormColumnHistoryResponse;
import com.huafon.framework.mybatis.enums.DelFlag;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import com.huafon.framework.mybatis.service.MybatisServiceImpl;
import com.huafon.support.config.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CustomFormColumnHistoryServiceImpl extends MybatisServiceImpl<CustomFormColumnHistoryMapper, CustomFormColumnHistory> implements CustomFormColumnHistoryService {

    private final CustomFormFieldService fieldService;

    public CustomFormColumnHistoryServiceImpl(CustomFormFieldService fieldService) {
        this.fieldService = fieldService;
    }

    @Override
    public List<CustomFormColumnHistory> findByFormId(Integer formId) {
        return this.lambdaQuery().eq(CustomFormColumnHistory::getFormId, formId).eq(CustomFormColumnHistory::getUserId, UserContext.getId()).eq(BaseEntity::getIsDel, DelFlag.SAVE.getValue()).list();
    }

    @Override
    public CustomFormColumnHistoryResponse.InfoVo selectByFormId(Integer formId) {
        return this.build(formId, findByFormId(formId));
    }

    private CustomFormColumnHistoryResponse.InfoVo build(Integer formId, List<CustomFormColumnHistory> histories) {
        CustomFormColumnHistoryResponse.InfoVo result = new CustomFormColumnHistoryResponse.InfoVo(formId);
        histories.forEach(history -> result.getFieldName().add(history.getFieldName()));
        return result;
    }

    @Override
    public void submit(Integer formId, CustomFormColumnHistoryRequest.SubmitVo submitVo) {
        Long userId = UserContext.getOrElseThrow().getUserId();
        this.clear(formId);
        List<CustomFormField> fields = fieldService.lambdaQuery().in(CustomFormField::getFieldName, submitVo.getFieldName()).eq(BaseEntity::getIsDel, DelFlag.SAVE.getValue()).eq(CustomFormField::getFormId, formId).list();
        this.saveBatch(fields.stream().map(field -> new CustomFormColumnHistory(userId, field.getId(), field.getFieldName(), formId)).collect(Collectors.toList()));
    }

    public void clear(Integer formId) {
        this.removeByColumn(CustomFormColumnHistory::getFormId, formId, CustomFormColumnHistory::getUserId, UserContext.getOrElseThrow().getUserId());
    }

}