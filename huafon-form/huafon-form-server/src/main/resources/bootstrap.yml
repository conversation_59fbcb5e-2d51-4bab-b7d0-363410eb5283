spring:
  application:
    name: form
  cloud:
    nacos:
      server-addr: ${NACOS_SERVER_ADDR:http://*********:8848}
      config:
        refreshable-dataids: application.yml,redis.yml,xxl-job.yml,form,datasource_security.yml
        file-extension: yml
        namespace: ${NACOS_NAMESPACE:huafon}
      discovery:
        enabled: true
        namespace: ${NACOS_NAMESPACE:huafon}
  data:
    mongodb:
      host: *********
      port: 32117
      database: custom_form
      username: huafon
      password: huafon123

log:
  enable: false