package com.huafon.visitor.api.dto.push;

import lombok.Data;

/**
 * 违规处罚
 */
@Data
public class ContractorViolateStipulateDTO extends PushBaseDTO{

    /**
     * 承包商 id
     */
    private String contractorId;
    /**
     * 关联承包商人员
     */
    private String contractorPersonId;
    /**
     * 违规人员
     */
    private String contractorPerson;
    /**
     * 发生日期
     */
    private String occurrenceDate;
    /**
     * 违规地点
     */
    private String address;

    /**
     * 违规描述
     */
    private String content;
    /**
     * 违规照片
     */
    private String pictureFile;
    /**
     * 处置过程及结果
     */
    private String processContent;
    /**
     * 处置结果附件
     */
    private String resultFile;

    private Integer deleted;
    /**
     * 业务id 数据的唯一标识
     */
    private Long bizId;

}
