package com.huafon.visitor.api.dto.push;

import lombok.Data;

/**
 * 数据推送
 * 承包商基本信息
 */
@Data
public class ContractorInfoPushDTO extends PushBaseDTO {

    /**
     * 单位名称
     */
    private String contractorName;
    /**
     * 承包商信用代码
     */
    private String creditCode;
    /**
     * 营业执照
     */
    private String businessLicense;
    /**
     * 负责人
     */
    private String principalName;
//    /**
//     * 专业
//     */
//    private String specContent;
    /**
     * 负责人联系电话
     */
    private String principalPhone;
    /**
     * 合同
     */
    private String contractFile;

    /**
     * 安全协议
     */
    private String securityProtocol;

    /**
     * 服务期限起
     */
    private String startDate;


    /**
     * 服务期限止
     */
    private String endDate;

    /**
     * 第三方单位类型
     * 1 建筑工程
     * 2 设备安装
     * 3 检维修
     * 4 监测
     * 5 设计
     * 6 监理
     * 7 技术服务
     * 8 其他
     */
    private String contractorType;
    /**
     * 单位性质
     * 1.民营 2.国营 3.事业单位 4.其它
     */
    private String contractorNature;

    /**
     * 单位地址
     */
    private String contractorAddress;

    /**
     * 承包范围
     */
    private String contractorScope;

    private Integer deleted;
    /**
     * 业务id 数据的唯一标识
     */
    private Long bizId;


    private Long contractorProjectId;

}
