package com.huafon.visitor.api.service;

import com.huafon.visitor.api.dto.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024-08-06 10:51
 **/
public interface ContractorRpcService {

	/**
	 * 计算技术交底今天入场人员的未完成的承包商人员ID
	 * @param source
	 * @return
	 */
	Set<Integer> calculateSafetyTechnology(ContractorStaffTechnologyCalculateDTO source);

	/**
	 * 查询承包商人员批量审批待办信息
	 * @param userId
	 * @param tenantId
	 * @return
	 */
	TaskInfoDTO queryTaskInfo(Long userId, Integer tenantId);

	/**
	 * 查询项目对接人为自己的项目信息
	 * @param userId
	 * @param tenantId
	 * @return
	 */
	TaskInfoDTO queryProject(Long userId, Integer tenantId);

	/**
	 * 通过承包商id列表查询信息
	 * @param contractorIds
	 * @return
	 */
	List<ContractorInfoDTO> queryByContractorIds(List<Long> contractorIds);

	/**
	 * 给培训考试用
	 * @param source
	 * @return
	 */
	Map<Long, Map<String, ExamStaffResultDTO>> examTrainStaffQuery(List<ExamStaffQuery> source);
}
