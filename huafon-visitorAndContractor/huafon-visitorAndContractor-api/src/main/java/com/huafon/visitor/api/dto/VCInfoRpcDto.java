package com.huafon.visitor.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> zhang
 * @program: huafon-base
 * @description: 获取访客和承包商详情
 * @date 2022-06-09 13:18:25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VCInfoRpcDto implements Serializable {

    private static final long serialVersionUID = -1471034429855076128L;
    /**
     * <AUTHOR>
     * @describe: 访客和承包商的照片
     * @date 2022/6/9 13:21
     */
    private String photo;

    /**
     * <AUTHOR>
     * @describe: 访客与承包商的姓名
     * @date 2022/6/10 10:12
     */
    private String name;

    /**
     * <AUTHOR>
     * @describe: 访客承包商的身份证号码
     * @date 2022/6/9 13:21
     */
    private String idCard;

    /**
     * <AUTHOR>
     * @describe: 类型 1|承包商人员 2|访客
     * @date 2022/6/9 13:24
     */
    private Integer type;


}