<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huafon.contractor.dao.mapper.ContractorStaffLicenseAccMapper">
  <resultMap id="BaseResultMap" type="com.huafon.contractor.models.entity.ContractorStaffLicenseAcc">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="con_sta_lic_acc_id" jdbcType="BIGINT" property="conStaLicAccId" />
    <result column="con_sta_lic_id" jdbcType="BIGINT" property="conStaLicId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    con_sta_lic_acc_id, con_sta_lic_id, name, url, create_time, modify_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    select 
    <include refid="Base_Column_List" />
    from hf_contractor_staff_license_acc
    where con_sta_lic_acc_id = #{conStaLicAccId,jdbcType=BIGINT}
  </select>

  <insert id="insertBatch" parameterType="java.util.List">
    insert into hf_contractor_staff_license_acc
    (con_sta_lic_id, name, url, create_time,modify_time)
    values
    <foreach collection="list" index="index" item="acc" separator=",">
      (
      #{acc.conStaLicId},
      #{acc.name},
      #{acc.url},
      #{acc.createTime},
      #{acc.modifyTime}
      )
    </foreach>
  </insert>

  <update id="deleteContractorStaffLicenseByLicenseId">
    UPDATE
      hf_contractor_staff_license_acc
    SET
      is_del = 1,
      modify_time = #{operationTime}
    WHERE is_del = 0
    AND con_sta_lic_id IN
    <foreach collection="staffLicenseId" open="(" close=")" item="item" separator=",">
      #{item}
    </foreach>
  </update>
</mapper>
