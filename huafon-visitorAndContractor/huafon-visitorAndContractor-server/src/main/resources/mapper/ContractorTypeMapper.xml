<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huafon.contractor.dao.mapper.ContractorTypeMapper">
    <resultMap id="BaseResultMap" type="com.huafon.contractor.models.entity.ContractorType">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <id column="contractor_type_id" jdbcType="BIGINT" property="contractorTypeId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="is_del" jdbcType="SMALLINT" property="isDel"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="category_id" jdbcType="INTEGER" property="categoryId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        contractor_type_id, name, remark, is_del, tenant_id, create_time, modify_time, category_id, create_by, modify_by, modify_name
    </sql>
    <insert id="insertSelective" parameterType="com.huafon.contractor.models.entity.ContractorType"  useGeneratedKeys="true"
            keyColumn="contractor_type_id" keyProperty="contractorTypeId">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        insert into hf_contractor_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contractorTypeId != null">
                contractor_type_id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="isDel != null">
                is_del,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="categoryId != null">
                category_id,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="modifyBy != null">
                modify_by,
            </if>
            <if test="modifyName != null">
                modify_name,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contractorTypeId != null">
                #{contractorTypeId,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="isDel != null">
                #{isDel,jdbcType=SMALLINT},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="categoryId != null">
                #{categoryId,jdbcType=INTEGER},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=INTEGER},
            </if>
            <if test="modifyBy != null">
                #{modifyBy,jdbcType=INTEGER},
            </if>
            <if test="modifyName != null">
                #{modifyName,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.huafon.contractor.models.entity.ContractorType">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        update hf_contractor_type
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="isDel != null">
                is_del = #{isDel,jdbcType=SMALLINT},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="categoryId != null">
                category_id = #{categoryId,jdbcType=INTEGER},
            </if>
            <if test="modifyBy != null">
                modify_by = #{modifyBy,jdbcType=INTEGER},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
        </set>
        where contractor_type_id = #{contractorTypeId,jdbcType=BIGINT}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hf_contractor_type
        where contractor_type_id = #{contractorTypeId,jdbcType=BIGINT} and is_del = 0
    </select>

    <select id="getCount" resultType="java.lang.Integer">
        select count(*)
        from hf_contractor_type
        where is_del = 0
        <if test="req.name != '' and req.name != null">
            and name like '%' || #{req.name,jdbcType=VARCHAR} || '%'
        </if>
        <if test="req.tenantIds != '' and req.tenantIds != null">
            and tenant_id in
            <foreach collection="req.tenantIds" item="id" index="index"
                     open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="req.categoryId != null">
           AND category_id = #{req.categoryId}
        </if>
    </select>

    <select id="selectListByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hf_contractor_type
        where is_del = 0
        <if test="req.name != '' and req.name != null">
            and name like '%' || #{req.name,jdbcType=VARCHAR} || '%'
        </if>
        <if test="req.tenantIds != '' and req.tenantIds != null">
            and tenant_id in
            <foreach collection="req.tenantIds" item="id" index="index"
                     open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="req.categoryId != null">
            AND category_id = #{req.categoryId}
        </if>
        order by create_time desc
        limit #{limit} offset #{offset}
    </select>


    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hf_contractor_type
        where contractor_type_id = #{contractorTypeId,jdbcType=BIGINT}
    </select>


    <select id="selectByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hf_contractor_type
        where contractor_type_id in
        <foreach collection="ids" item="id" index="index"
                 open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="selectListAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hf_contractor_type
        where is_del = 0
        <if test="req.tenantIds != '' and req.tenantIds != null">
            and tenant_id in
            <foreach collection="req.tenantIds" item="id" index="index"
                     open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        order by modify_time desc
    </select>

    <select id="selectByName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hf_contractor_type
        where name = #{name,jdbcType=VARCHAR}
        and tenant_id = #{tenantId,jdbcType=INTEGER}
        and is_del = 0
        limit 1
    </select>

    <select id="queryByCategoryIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hf_contractor_type
        where is_del = 0
        and tenant_id = #{tenantId,jdbcType=INTEGER}
        and category_id IN
        <foreach collection="categoryIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>
