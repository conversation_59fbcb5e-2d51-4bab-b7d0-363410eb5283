<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huafon.contractor.dao.mapper.ContractorDingTalkMapper">

    <update id="batchUpdate">
        <foreach collection="list" item="entity" separator=";">
            UPDATE
            hf_contractor_ding_talk
            <set>
                <if test="entity.status != null">
                    status = #{entity.status,jdbcType=VARCHAR}
                </if>
            </set>
            WHERE
            id = #{entity.id,jdbcType=INTEGER}
        </foreach>
    </update>

    <select id="queryByContractorStaffId" resultType="com.huafon.contractor.models.entity.ContractorDingTalk">
        SELECT
            t1."id",
            t1.tenant_id,
            t1.contractor_staff_id,
            t1.content,
            t1.modify_time,
            t1.modify_name,
            t1.instance_id,
            t1.status
        FROM
            hf_contractor_ding_talk AS t1
        WHERE
            t1.is_del = 0
        AND
            t1.contractor_staff_id = #{contractorStaffId}
        ORDER BY t1.id DESC LIMIT 1
    </select>

    <select id="queryByContractorStaffIds" resultType="com.huafon.contractor.models.entity.ContractorDingTalk">
        SELECT
            t1."id",
            t1.tenant_id,
            t1.contractor_staff_id,
            t1.content,
            t1.modify_time,
            t1.modify_name,
            t1.instance_id,
            t1.status
        FROM
            hf_contractor_ding_talk AS t1
        WHERE
            t1.is_del = 0
          AND
            t1.contractor_staff_id IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>
