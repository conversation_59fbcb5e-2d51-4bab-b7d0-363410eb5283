<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huafon.contractor.dao.mapper.ContractorTypeCategoryMapper">

    <resultMap id = "contractorTypeCategoryMap" type ="com.huafon.contractor.models.entity.ContractorTypeCategory">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="name" property="name"/>
        <result column="icon" property="icon"/>
        <result column="remark" property="remark"/>
        <result column="sort" property="sort"/>
        <result column="is_default" property="isDefault"/>
        <result column="type" property="type"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <sql id = "commonSql">
        id,
        parent_id,
        name,
        icon,
        remark,
        sort,
        is_default,
        type,
        tenant_id
    </sql>

    <select id="queryTree" resultType="com.huafon.contractor.models.entity.ContractorTypeCategory">
        WITH RECURSIVE tmp AS (
            SELECT a.id,
            a.parent_id,
            a.name,
            a.remark,
            a.sort,
            a.is_default,
            a.type,
            a.icon
            FROM hf_contractor_type_category AS a
            WHERE a.is_del = 0
            AND a.tenant_id IN
            <foreach collection="tenantIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            <if test="parentId != null">
                AND a.id = #{parentId}
            </if>
        UNION
            SELECT r.id,
            r.parent_id,
            r.name,
            r.remark,
            r.sort,
            r.is_default,
            r.type,
            r.icon
            FROM tmp
            JOIN hf_contractor_type_category r ON tmp.id = r.parent_id
            WHERE r.tenant_id IN
            <foreach collection="tenantIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            AND r.is_del = 0
        )
        SELECT tmp.*
        FROM tmp;
    </select>

</mapper>
