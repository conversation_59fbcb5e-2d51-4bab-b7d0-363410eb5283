<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huafon.contractor.dao.mapper.ContractorSecurityDepositDetailMapper">

    <resultMap id = "contractorSecurityDepositDetailMap" type ="com.huafon.contractor.models.entity.ContractorSecurityDepositDetail">
        <id column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="reference_id" property="referenceId"/>
        <result column="reference_type" property="referenceType"/>
        <result column="contractor_id" property="contractorId"/>
        <result column="opt_payment" property="optPayment"/>
        <result column="remaining" property="remaining"/>
        <result column="remark" property="remark"/>
        <result column="opt_user_id" property="optUserId"/>
        <result column="opt_user_name" property="optUserName"/>
        <result column="opt_time" property="optTime"/>
        <result column="state" property="state"/>
        <result column="old_id" property="oldId"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_by_name" property="createByName"/>
        <result column="modify_by_name" property="modifyByName"/>
    </resultMap>


    <sql id = "commonSql">
        id,
        type,
        reference_id,
        reference_type,
        contractor_id,
        opt_payment,
        remaining,
        remark,
        opt_user_id,
        opt_user_name,
        opt_time,
        state,
        old_id,
        tenant_id,
        create_by_name,
        modify_by_name
    </sql>

    <select id="queryByPage" resultType="com.huafon.contractor.models.vo.deposit.ContractorSecurityDepositDetailCommonVo">
        SELECT
        <include refid="commonSql"/>
        FROM
            hf_contractor_security_deposit_detail
        WHERE is_del = 0 AND state = 1
        <if test="query.tenantId != null">
             AND tenant_id = #{query.tenantId}
        </if>
        <if test="query.searchKey != null and query.searchKey != ''">
             AND (opt_user_name LIKE CONCAT('%',#{query.searchKey},'%'))
        </if>
        <if test="query.contractorId != null">
           AND contractor_id = #{query.contractorId}
        </if>
        <choose>
            <when test="query.orders != null and query.orders.size() > 0">
                ORDER BY
                <foreach collection="query.orders" separator="," item="item">
                    ${item.column} <choose><when test="item.asc">ASC</when><otherwise>DESC</otherwise></choose>
                </foreach>
            </when>
            <otherwise>
                 ORDER BY opt_time DESC
            </otherwise>
        </choose>
    </select>


</mapper>
