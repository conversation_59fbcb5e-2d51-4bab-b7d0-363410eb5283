<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huafon.safety.dao.mapper.TechnologyInformTemplateSignatureMapper">

    <resultMap id = "technologyInformTemplateSignatureMap" type ="com.huafon.safety.models.entity.TechnologyInformTemplateSignature">
        <id column="id" property="id"/>
        <result column="template_id" property="templateId"/>
        <result column="person_type" property="personType"/>
        <result column="audit_opinion" property="auditOpinion"/>
        <result column="sort" property="sort"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <sql id = "commonSql">
        id,
        template_id,
        person_type,
        audit_opinion,
        sort,
        tenant_id
    </sql>

    <select id="batchInsert">
        INSERT INTO hf_safety_technology_inform_template_signature
            (
                 template_id,
                 person_type,
                 audit_opinion,
                 sort,
                 tenant_id,
                 is_del,
                 create_by,
                 create_time,
                 modify_by,
                 modify_time
            )
        VALUES
        <foreach collection="source" item="item" separator=",">
            (
                #{item.templateId},
                #{item.personType},
                #{item.auditOpinion},
                #{item.sort},
                #{item.tenantId},
                0,
                #{item.createBy},
                #{item.createTime},
                #{item.modifyBy},
                #{item.modifyTime}
            )
        </foreach>
    </select>

</mapper>
