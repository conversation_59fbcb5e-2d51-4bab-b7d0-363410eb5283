package com.huafon.contractor.service;

import com.huafon.contractor.models.dto.BatchDeleteDTO;
import com.huafon.contractor.models.dto.ProcessRefuseDTO;
import com.huafon.contractor.models.entity.ContractorStaff;
import com.huafon.contractor.models.reqo.staff.*;
import com.huafon.contractor.models.vo.StaffOuterCountVo;
import com.huafon.contractor.models.vo.staff.StaffOuterCommonVo;
import com.huafon.framework.mybatis.pojo.CommonPage;

/**
 * <AUTHOR>
 * @since 2024-05-21 09:35
 **/
public interface StaffOuterService {

	/**
	 * 新增
	 * @param reqStaffAdd
	 * @return
	 */
	Long add(ReqStaffAdd reqStaffAdd);

	/**
	 * 更新承包商人员信息
	 */
	ContractorStaff update(ReqStaffUpdate reqStaffUpdate);

	/**
	 * 删除
	 * @param reqStaff
	 */
	void del(BatchDeleteDTO reqStaff);

	/**
	 * 审批留
	 * @param check
	 */
	void check(ReqStaffCheck check);

	/**
	 * 拒绝
	 * @param refuse
	 */
	void refuse(ProcessRefuseDTO refuse);

	/**
	 * 分页查询
	 * @param query
	 * @return
	 */
	CommonPage<StaffOuterCommonVo> commonPage(StaffOuterPageQuery query);

	/**
	 * 人员统计
	 * @return
	 */
	StaffOuterCountVo processListCount(StaffQuery query);
}
