package com.huafon.contractor.models.reqo.contractor;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.huafon.contractor.models.reqo.ListPage;
import com.huafon.safety.service.support.json.EndOfDayDeserializer;
import com.huafon.safety.service.support.json.StartOfDayDeserializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/4/12 15:54
 */
@Data
public class ReqContractorListPage extends ListPage {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "是否加入黑名单 0|否 1|是")
    private Integer isBlacklist;

    @ApiModelProperty(value = "类型id")
    private Long contractorTypeId;

    @ApiModelProperty(value = "名字不为空，true")
    private Boolean NameNotNull;

    @ApiModelProperty(value = "加入黑名单开始时间")
    @JsonDeserialize(using = StartOfDayDeserializer.class)
    private Date blacklistTimeStart;

    @ApiModelProperty(value = "加入黑名单结束时间")
    @JsonDeserialize(using = EndOfDayDeserializer.class)
    private Date blacklistTimeEnd;
}
