package com.huafon.contractor.models.reqo.project;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2022/6/18 9:53
 */
@Data
public class ReqProjectEntrust {

    @ApiModelProperty(value = "项目id", required = true)
    @NotNull(message = "项目id不能为空")
    private Long contractorProjectId;

    @ApiModelProperty(value = "委托人userId", required = true)
    @NotNull(message = "委托人不能为空")
    private Integer entrustUserId;

    @ApiModelProperty(value = "审核意见")
    private String content;
}
