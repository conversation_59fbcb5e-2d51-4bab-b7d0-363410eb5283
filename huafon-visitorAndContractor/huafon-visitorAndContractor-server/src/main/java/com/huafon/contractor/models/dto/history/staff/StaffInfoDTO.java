package com.huafon.contractor.models.dto.history.staff;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.huafon.contractor.models.entity.ContractorStaff;
import com.huafon.contractor.models.reqo.staff.ReqStaffUpdate;
import com.huafon.contractor.models.vo.staff.StaffVo;
import lombok.Data;
import org.apache.logging.log4j.util.Strings;

import java.io.Serializable;

@Data
public class StaffInfoDTO implements Serializable {

    public static final String VERSION = "1.0";


    private String version;

    /**
     * Column: contractor_staff_id
     * Type: bigserial
     * Default value: nextval('security_environment.hf_contractor_staff_id_seq'::regclass)
     * Remark: 主键
     */
    private Long contractorStaffId;

    /**
     * Column: tenant_id
     * Type: int8
     * Remark: 公司id
     */
    private Integer tenantId;

    /**
     * Column: name
     * Type: varchar(255)
     * Remark: 姓名
     */
    private String name;

    /**
     * Column: id_card
     * Type: varchar(255)
     * Remark: 身份证
     */
    private String idCard;

    /**
     * Column: mobile
     * Type: varchar(255)
     * Remark: 号码
     */
    private String mobile;

    /**
     * Column: photo
     * Type: varchar(255)
     * Remark: 照片-正面
     */
    private String photo;

    /**
     * Column: is_special_work
     * Type: int2
     * Remark: 是否特殊工种 1|是 0|否
     */
    private Integer isSpecialWork;

    /**
     * Column: health_code
     * Type: varchar(255)
     * Remark: 健康码
     */
    private String healthCode;

    /**
     * Column: travel_history_card
     * Type: varchar(255)
     * Remark: 行程卡
     */
    private String travelHistoryCard;

    /**
     * Column: nucleic_acid_report
     * Type: varchar(255)
     * Remark: 核酸报告
     */
    private String nucleicAcidReport;

    /**
     * Column: medical_examination_report
     * Type: varchar(255)
     * Remark: 体检报告
     */
    private String medicalExaminationReport;

    /**
     * Column: service_contract
     * Type: varchar(255)
     * Remark: 劳务合同
     */
    private String serviceContract;

    private String idCardPhotoFront;

    private String idCardPhotoReverse;

    /**
     * Column: task_id
     * Type: varchar(**********)
     * Remark: 工作流id
     */
    private String taskId;

    private Integer UserId;

    /**
     * 证件照类型字典code
     */
    private String idTypeCode;

    /**
     * 证件照类型字典Name
     */
    private String idTypeName;


    public JSONObject toSnapshot() {
        return JSONObject.parseObject(JSONObject.toJSONString(this));
    }


    public static StaffInfoDTO fromSnapshot(JSONObject snapshot) {
        if (snapshot == null) {
            return null;
        }
        StaffInfoDTO target = new StaffInfoDTO();
        // 设置承包商员工ID
        target.setContractorStaffId(snapshot.getLong("contractorStaffId"));
        // 设置租户ID
        target.setTenantId(snapshot.getInteger("tenantId"));
        // 设置姓名
        target.setName(snapshot.getString("name"));
        // 设置身份证号
        target.setIdCard(snapshot.getString("idCard"));
        // 设置手机号
        target.setMobile(snapshot.getString("mobile")); 
        // 设置照片
        target.setPhoto(snapshot.getString("photo"));
        // 设置身份证正面照片
        target.setIdCardPhotoFront(snapshot.getString("idCardPhotoFront"));
        // 设置身份证反面照片
        target.setIdCardPhotoReverse(snapshot.getString("idCardPhotoReverse"));
        // 设置是否特殊工种
        target.setIsSpecialWork(snapshot.getInteger("isSpecialWork"));
        // 设置健康码
        target.setHealthCode(snapshot.getString("healthCode"));
        // 设置行程卡
        target.setTravelHistoryCard(snapshot.getString("travelHistoryCard"));
        // 设置核酸检测报告
        target.setNucleicAcidReport(snapshot.getString("nucleicAcidReport"));
        // 设置体检报告
        target.setMedicalExaminationReport(snapshot.getString("medicalExaminationReport"));
        // 设置服务合同
        target.setServiceContract(snapshot.getString("serviceContract"));
        // 设置工作流ID
        target.setTaskId(snapshot.getString("taskId"));
        // 设置用户ID
        target.setUserId(snapshot.getInteger("userId"));
        // 设置证件类型代码
        target.setIdTypeCode(snapshot.getString("idTypeCode"));
        // 设置证件类型名称
        target.setIdTypeName(snapshot.getString("idTypeName"));

        String version = snapshot.getString("version");
        //版本的更新特殊处理
        return target;

    }

    public void copyReview(StaffVo target) {
        if (target == null) {
            return;
        }

        // 设置承包商员工ID
        target.setContractorStaffId(this.getContractorStaffId());
        // 设置租户ID
        target.setTenantId(this.getTenantId());
        // 设置姓名
        target.setName(this.getName());
        // 设置身份证号
        target.setIdCard(this.getIdCard());
        // 设置手机号
        target.setMobile(this.getMobile());
        // 设置照片
        target.setPhoto(this.getPhoto());
        // 设置身份证正面照片
        target.setIdCardPhotoFront(this.getIdCardPhotoFront());
        // 设置身份证反面照片
        target.setIdCardPhotoReverse(this.getIdCardPhotoReverse());
        // 设置是否特殊工种
        target.setIsSpecialWork(this.getIsSpecialWork());
        // 设置健康码
        target.setHealthCode(this.getHealthCode());
        // 设置行程卡
        target.setTravelHistoryCard(this.getTravelHistoryCard());
        // 设置核酸检测报告
        target.setNucleicAcidReport(this.getNucleicAcidReport());
        // 设置体检报告
        String medicalExaminationReport1 = this.getMedicalExaminationReport();
        if (Strings.isNotBlank(medicalExaminationReport1)) {
            target.setMedicalExaminationReport(JSONArray.parseArray(medicalExaminationReport1, String.class));
        }
        // 设置服务合同
        String serviceContract1 = this.getServiceContract();
        if (Strings.isNotBlank(serviceContract1)) {
            target.setServiceContract(JSONArray.parseArray(serviceContract1, String.class));
        }
        // 设置任务ID
        target.setTaskId(this.getTaskId());
        // 设置用户ID
        target.setUserId(this.getUserId());
        // 设置证件类型代码
        target.setIdTypeCode(this.getIdTypeCode());
        // 设置证件类型名称
        target.setIdTypeName(this.getIdTypeName());
    }

    public static ContractorStaff toEntity(ContractorStaff target, StaffInfoDTO source) {
        if (source == null) {
            return target;
        }
        if (target == null) {
            target = new ContractorStaff();
        }
        // 设置承包商员工ID
        target.setContractorStaffId(source.getContractorStaffId());
        // 设置租户ID
        target.setTenantId(source.getTenantId());
        // 设置姓名
        target.setName(source.getName());
        // 设置身份证号
        target.setIdCard(source.getIdCard());
        // 设置手机号
        target.setMobile(source.getMobile());
        // 设置照片
        target.setPhoto(source.getPhoto());
        // 设置身份证正面照片
        target.setIdCardPhotoFront(source.getIdCardPhotoFront());
        // 设置身份证反面照片
        target.setIdCardPhotoReverse(source.getIdCardPhotoReverse());
        // 设置是否特殊工种
        target.setIsSpecialWork(source.getIsSpecialWork());
        // 设置健康码
        target.setHealthCode(source.getHealthCode());
        // 设置行程卡
        target.setTravelHistoryCard(source.getTravelHistoryCard());
        // 设置核酸检测报告
        target.setNucleicAcidReport(source.getNucleicAcidReport());
        // 设置体检报告
        target.setMedicalExaminationReport(source.getMedicalExaminationReport());
        // 设置服务合同
        target.setServiceContract(source.getServiceContract());
        // 设置任务ID
        target.setTaskId(source.getTaskId());
        // 设置用户ID
        target.setUserId(source.getUserId());
        // 设置证件类型代码
        target.setIdTypeCode(source.getIdTypeCode());
        // 设置证件类型名称
        target.setIdTypeName(source.getIdTypeName());
        return target;
    }


    public static StaffInfoDTO fromEntity(ReqStaffUpdate source, Integer userId) {
        if (source == null) {
            return null;
        }
        //将ContractorStaff 转换为 StaffInfoDTO
        StaffInfoDTO target = new StaffInfoDTO();

        // 设置承包商员工ID
        target.setContractorStaffId(source.getContractorStaffId());
        // 设置租户ID
        target.setTenantId(source.getTenantId());
        // 设置姓名
        target.setName(source.getName());
        // 设置身份证号
        target.setIdCard(source.getIdCard());
        // 设置手机号
        target.setMobile(source.getMobile());
        // 设置照片
        target.setPhoto(source.getPhoto());
        // 设置身份证正面照片
        target.setIdCardPhotoFront(source.getIdCardPhotoFront());
        // 设置身份证反面照片
        target.setIdCardPhotoReverse(source.getIdCardPhotoReverse());
        // 设置是否特殊工种
        target.setIsSpecialWork(source.getIsSpecialWork());
        // 设置健康码
        target.setHealthCode(source.getHealthCode());
        // 设置行程卡
        target.setTravelHistoryCard(source.getTravelHistoryCard());
        // 设置核酸检测报告
        target.setNucleicAcidReport(source.getNucleicAcidReport());
        // 设置体检报告
        target.setMedicalExaminationReport(JSONArray.toJSONString(source.getMedicalExaminationReport()));
        // 设置服务合同
        target.setServiceContract(JSONArray.toJSONString(source.getServiceContract()));
        // 设置任务ID
        // 设置用户ID
        target.setUserId(userId);
        // 设置证件类型代码
        target.setIdTypeCode(source.getIdTypeCode());
        // 设置证件类型名称
        target.setIdTypeName(source.getIdTypeName());
        // 设置版本号
        target.setVersion(VERSION);

        return target;
    }

}
