package com.huafon.contractor.models.vo.staff;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huafon.contractor.models.enums.CardBindStatusEnum;
import com.huafon.contractor.models.enums.TechnologyStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2022/4/19 19:11
 */
@Data
public class StaffListVo {

    @ApiModelProperty(value = "人员id")
    private Long contractorStaffId;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "身份证")
    private String idCard;

    @ApiModelProperty(value = "号码")
    private String mobile;

    @ApiModelProperty(value = "照片")
    private String photo;

//    @ApiModelProperty(value = "所属公司id")
//    private Long departmentId;
//
//    @ApiModelProperty(value = "所属公司名称(甲方)")
//    private String departmentName;

    @ApiModelProperty(value = "承包商id")
    private Long contractorId;

    @ApiModelProperty(value = "承包商名称")
    private String contractorName;

    @ApiModelProperty(value = "承包商项目id")
    private Long contractorProjectId;

    @ApiModelProperty(value = "承包商项目名称")
    private String contractorProjectName;

    @ApiModelProperty(value = "申请时间")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "加入黑名单原因")
    private String blacklistReason;

    @ApiModelProperty(value = "培训情况")
    private Integer trainState;

    @ApiModelProperty(value = "绑卡情况: PERSON_CARD|定位卡绑定; ACCESS_CARD|门禁卡绑定; UNTIE_PERSON_CARD|绑定过定位卡; UNTIE_ACCESS_CARD|绑定过门禁卡")
    private Set<CardBindStatusEnum> cardBindStatus;

    @ApiModelProperty(value = "状态 1|未入厂 2|已入厂 3|已离厂 4|黑名单 5|待审批 6｜审核未通过")
    private Integer state;

    @ApiModelProperty(value = "是否特殊工种 1|是 0|否")
    private Integer isSpecialWork;

    @ApiModelProperty(value = "证书列表")
    private List<StaffLicenseVo> licenseList;

    @ApiModelProperty(value = "更新时间")
    private Date modifyTime;

    @ApiModelProperty(value = "查看状态 1|未读 2|已读")
    private Integer readState;

    @ApiModelProperty(value = "入厂时间")
    private Date theFactoryTime;

    @ApiModelProperty(value = "离厂时间")
    private Date leaveFactoryTime;

    @ApiModelProperty(value = "加入黑名单时间")
    private Date blacklistTime;

    @ApiModelProperty(value = "用户id")
    private Integer userId;

    @ApiModelProperty(value = "交底状态：NOT_INFORM:未交底; HAD_INFORM:已交底")
    private TechnologyStatusEnum technologyStatus;

    @ApiModelProperty(value = "审批流ID")
    private String taskId;

    @ApiModelProperty(value = "钉钉审批状态 进行中:RUNNING | 撤销:TERMINATED | 完成:COMPLETED")
    private String dingTalkStatus;

    @ApiModelProperty(value = "项目状态：2|审核中 4|施工中 5|已完结 6|已取消 7|已暂停")
    private Integer projectState;


    @ApiModelProperty(value = "证件照类型字典code")
    private String idTypeCode;
    @ApiModelProperty(value = "证件照类型字典Name")
    private String idTypeName;

    @ApiModelProperty(value = "重新发起审核状态：0未发起；1已发起")
    private Integer reviewState;
}
