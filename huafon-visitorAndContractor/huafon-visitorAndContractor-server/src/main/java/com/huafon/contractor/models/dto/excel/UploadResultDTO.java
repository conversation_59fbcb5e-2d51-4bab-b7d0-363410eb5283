package com.huafon.contractor.models.dto.excel;

import com.huafon.contractor.service.support.ExcelValidationError;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 上传结果列表
 *
 * <AUTHOR>
 * @since 2022-10-21 10:26
 **/
@Data
@ApiModel(value = "Excel上传")
public class UploadResultDTO<T> {
	@ApiModelProperty(value = "成功数量")
	private Integer success;

	@ApiModelProperty(value = "成功列表")
	private List<T> successList;

	@ApiModelProperty(value = "错误数量")
	private Integer error;

	@ApiModelProperty(value = "错误列表")
	private List<ExcelValidationError<T>> errorList;

	public UploadResultDTO() {
		this(new ArrayList<>(), new ArrayList<>());
	}

	public UploadResultDTO(List<T> successList) {
		this(successList, new ArrayList<>());
	}

	public UploadResultDTO(Integer success, List<ExcelValidationError<T>> errorList) {
		this(new ArrayList<>(), errorList);
		this.setSuccess(success);
	}

	public UploadResultDTO(List<T> successList, List<ExcelValidationError<T>> errorList) {
		Objects.requireNonNull(successList, "成功列表必须不为NULL");
		Objects.requireNonNull(errorList, "失败列表必须不为NULL");

		this.success = successList.size();
		this.successList = successList;
		this.error = errorList.size();
		this.errorList = errorList;
	}
}
