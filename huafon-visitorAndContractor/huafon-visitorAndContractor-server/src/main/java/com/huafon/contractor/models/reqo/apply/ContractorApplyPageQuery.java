package com.huafon.contractor.models.reqo.apply;

import com.huafon.framework.mybatis.pojo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "承包商申请列表查询")
public class ContractorApplyPageQuery extends PageRequest {

    @ApiModelProperty(value = "模糊搜索")
    private String searchKey;

    @ApiModelProperty(value = "租户ID", hidden = true)
    private Integer tenantId;

    @ApiModelProperty(value = "1流程中;2完成;3取消")
    private Integer status;

}
