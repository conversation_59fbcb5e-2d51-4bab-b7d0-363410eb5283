package com.huafon.contractor.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.huafon.common.config.TenantContext;
import com.huafon.common.utils.bean.BeanUtils;
import com.huafon.contractor.dao.mapper.*;
import com.huafon.contractor.models.constants.ProcessConstants;
import com.huafon.contractor.models.constants.ProjectConstants;
import com.huafon.contractor.models.dto.*;
import com.huafon.contractor.models.dto.excel.ContractorDownloadDTO;
import com.huafon.contractor.models.dto.excel.ContractorUploadDTO;
import com.huafon.contractor.models.entity.*;
import com.huafon.contractor.models.reqo.contractor.*;
import com.huafon.contractor.models.reqo.project.ReqProjectList;
import com.huafon.contractor.models.reqo.project.ReqProjectQuery;
import com.huafon.contractor.models.vo.IdAndNameVo;
import com.huafon.contractor.models.vo.PageListResultVo;
import com.huafon.contractor.models.vo.contractor.ContractorLicenseVo;
import com.huafon.contractor.models.vo.contractor.ContractorListVo;
import com.huafon.contractor.models.vo.contractor.ContractorStatusCountVo;
import com.huafon.contractor.models.vo.contractor.ContractorVo;
import com.huafon.contractor.models.vo.project.ContractorProjectListVo;
import com.huafon.contractor.models.vo.type.ContractorTypeInfoVo;
import com.huafon.contractor.models.vo.type.ContractorTypeQualityVo;
import com.huafon.contractor.service.ContractorSecurityDepositService;
import com.huafon.contractor.service.ContractorService;
import com.huafon.contractor.service.ProjectService;
import com.huafon.contractor.service.TypeService;
import com.huafon.contractor.service.support.ExcelValidationError;
import com.huafon.contractor.service.support.ExcelValidationResult;
import com.huafon.contractor.utils.DataMapUtil;
import com.huafon.contractor.utils.JwtToken;
import com.huafon.safety.utils.TransferUtils;
import com.huafon.support.config.UserContext;
import com.huafon.support.core.pojo.R;
import com.huafon.support.dto.UserInfoDto;
import com.huafon.support.exceptions.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ContractorServiceImpl implements ContractorService {

    @Resource
    private ContractorMapper contractorMapper;

    @Resource
    private ContractorLicenseMapper contractorLicenseMapper;

    @Resource
    private ContractorOtherPropertyMapper otherPropertyMapper;

    @Resource
    private ContractorTypeMapper contractorTypeMapper;

    @Resource
    private ContractorProjectMapper contractorProjectMapper;

    @Resource
    private JwtToken jwtToken;

    @Resource
    private DataMapUtil dataMapUtil;

    @Autowired
    private TypeService typeService;

    @Autowired
    private ContractorProjectMapper projectMapper;

    @Autowired
    private ContractorViolationMapper violationMapper;

    @Autowired
    private ContractorSecurityDepositService securityDepositService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ContractorTypeInfoMapper contractorTypeInfoMapper;

    @Override
    @Transactional
    public Long add(ReqContractorAdd reqContractorAdd) {
        return insertData(reqContractorAdd);
    }

    @Override
    @Transactional
    public R edit(ReqContractorEdit reqContractorEdit) {
        Contractor contractor = getRow(reqContractorEdit.getContractorId());
        String socialCreditCode = reqContractorEdit.getSocialCreditCode();
        Contractor contractorBySocialCreditCode = contractorMapper.selectBySocialCreditCode(socialCreditCode, TenantContext.getOne());
        if (null != contractorBySocialCreditCode && !contractorBySocialCreditCode.getContractorId().equals(contractor.getContractorId())) {
            throw new ServiceException("该社会信用代码已存在，请检查输入后再试。");
        }
        if (contractor.getIsBlacklist() == ProjectConstants.CONTRACTOR_IS_BLACKLIST_YES) {
            throw new ServiceException("编辑失败，该承包商信息不可编辑。");
        }
        updateData(reqContractorEdit);
        return R.ok(contractor.getContractorId());
    }

    @Override
    public R del(ReqContractor req) {
        Long contractorId = req.getContractorId();
        getRow(contractorId);
        contractorMapper.deleteByContractorId(contractorId);
        contractorTypeInfoMapper.deleteByContractorIdAndTypeIds(ContractorTypeDTO.builder().contractorId(contractorId).user(UserContext.getId()).time(new Date()).build());
        return R.ok();
    }

    @Override
    public PageListResultVo<ContractorListVo> getListByPage(ReqContractorListPage reqPage) {
        //获取租户id
        reqPage.setTenantIds((List<Integer>) TenantContext.get());
        int page = reqPage.getPageNo();
        int pageSize = reqPage.getPageSize();

        Integer offset = (page - 1) * pageSize;
        List<ContractorListVo> contractorVoList = new ArrayList<>();

        int allCount = contractorMapper.getCount(reqPage);
        if (allCount > 0) {
            List<Contractor> contractorList = contractorMapper.selectListByPage(reqPage, offset, pageSize);
            convertList(contractorList, contractorVoList);
        }
        return new PageListResultVo<>(allCount, page, pageSize, contractorVoList);
    }

    @Override
    public R getAll(ReqProjectList reqProjectList) {
        //获取租户id
        reqProjectList.setTenantIds((List<Integer>) TenantContext.get());
        List<Contractor> contractorList = contractorMapper.selectList(reqProjectList);
        List<ContractorVo> list = new ArrayList<>();
        if (contractorList.size() > 0) {
            List<Long> contractorIds = contractorList.stream().filter(Objects::nonNull).map(Contractor::getContractorId).distinct().collect(Collectors.toList());
            Map<Long, List<ContractorType>> contractorTypeMappings = contractorTypeInfoMapper.queryContractorTypeByContractorIds(contractorIds);
            //查类型map
            Set<Long> contractorTypeIds = contractorTypeMappings.values().stream().flatMap(Collection::stream).map(ContractorType::getContractorTypeId).collect(Collectors.toSet());
            Map<Long, String> contractorTypeMap = dataMapUtil.getTypeMap(new ArrayList<>(contractorTypeIds));
            Map<Long, List<ContractorTypeQualityVo>> qualityMap = typeService.queryContractorTypeQuality(contractorTypeIds);

            //保证金退还
            Map<Long, List<ContractorSecurityDeposit>> securityDepositMappings = securityDepositService.queryByContractorIds(contractorIds, Collections.singletonList(ProcessConstants.IN_PROCESS));

            for (Contractor contractor : contractorList) {
                ContractorVo contractorVo = new ContractorVo();
                contractorVo.setContractorId(contractor.getContractorId());
                contractorVo.setName(contractor.getName());
                contractorVo.setNature(contractor.getNature());
//                contractorVo.setContractorTypeId(contractor.getContractorTypeId());
//                contractorVo.setContractorTypeName(contractorTypeMap.get(contractor.getContractorTypeId()));
//                contractorVo.setQualityList(qualityMap.getOrDefault(contractor.getContractorTypeId(), Collections.emptyList()));

                List<ContractorType> originContractorTypeInfos = contractorTypeMappings.getOrDefault(contractor.getContractorId(), Collections.emptyList());
                List<ContractorTypeInfoVo> targetContractorTypeInfos = originContractorTypeInfos.stream().map(item -> {
                    ContractorTypeInfoVo targetItem = new ContractorTypeInfoVo();
                    targetItem.setContractorTypeId(item.getContractorTypeId());
                    targetItem.setName(item.getName());
                    targetItem.setQualityList(qualityMap.getOrDefault(item.getContractorTypeId(), Collections.emptyList()));
                    return targetItem;
                }).collect(Collectors.toList());
                contractorVo.setContractorTypeInfos(targetContractorTypeInfos);

                contractorVo.setSocialCreditCode(contractor.getSocialCreditCode());
                contractorVo.setLicenseVoList(JSONObject.parseArray(JSONObject.toJSONString(contractor.getLicenseList()), ContractorLicenseVo.class));
                List<ContractorOtherProperty> contractorOtherProperties = Optional.ofNullable(contractor.getOtherPropertyFiles()).orElse(Collections.emptyList());
                contractorOtherProperties = contractorOtherProperties.stream().filter(e -> Strings.isNotBlank(e.getUrl())).collect(Collectors.toList());
                contractorVo.setOtherPropertyFiles(BeanUtils.convert(AttachmentDTO.class, contractorOtherProperties));

                //保证金
                BigDecimal totalPayment = Optional.ofNullable(contractor.getTotalPayment()).orElse(BigDecimal.ZERO);
                BigDecimal deduct = Optional.ofNullable(contractor.getDeduct()).orElse(BigDecimal.ZERO);
                contractorVo.setTotalPayment(totalPayment);
                contractorVo.setDeduct(deduct);
                contractorVo.setRemaining(totalPayment.subtract(deduct).max(BigDecimal.ZERO));

                List<ContractorSecurityDeposit> securityDeposit = securityDepositMappings.getOrDefault(contractor.getContractorId(), Collections.emptyList());
                contractorVo.setHasPaymentSendBackTask(!CollectionUtils.isEmpty(securityDeposit));

                list.add(contractorVo);
            }
        }
        return R.ok(list);
    }

    @Override
    public ContractorVo getRow(ReqContractor reqContractor) {
        Long contractorId = reqContractor.getContractorId();
        Contractor contractor = getRow(contractorId);
        ContractorVo contractorVo = JSONObject.parseObject(JSONObject.toJSONString(contractor), ContractorVo.class);

        //加入营业执照
        List<ContractorLicense> licenseList = contractorLicenseMapper.selectListByContractorId(contractorId);
        List<ContractorLicenseVo> LicenseVoList = JSONObject.parseArray(JSONObject.toJSONString(licenseList), ContractorLicenseVo.class);
        contractorVo.setLicenseVoList(LicenseVoList);

        //其他附件材料
        List<ContractorOtherProperty> otherProperties = otherPropertyMapper.queryByContractorId(contractorId);
        contractorVo.setOtherPropertyFiles(BeanUtils.convert(AttachmentDTO.class, otherProperties));
        //保证金
        BigDecimal totalPayment = Optional.ofNullable(contractor.getTotalPayment()).orElse(BigDecimal.ZERO);
        BigDecimal deduct = Optional.ofNullable(contractor.getDeduct()).orElse(BigDecimal.ZERO);
        contractorVo.setTotalPayment(totalPayment);
        contractorVo.setDeduct(deduct);
        contractorVo.setRemaining(totalPayment.subtract(deduct).max(BigDecimal.ZERO));

        contractorVo.setValidFinished(validFinished(contractorId));
        List<String> tradeVouchers = projectMapper.queryTradeVoucher(contractorId);
        List<PaymentSerialNumberDTO> serialNumbers = tradeVouchers.stream().map(e -> {
            PaymentSerialNumberDTO target = new PaymentSerialNumberDTO();
            target.setSerialNumber(e);
            return target;
        }).collect(Collectors.toList());
        contractorVo.setPaymentSerialNumbers(serialNumbers);

//        Long contractorTypeId = contractor.getContractorTypeId();
//        if (null != contractorTypeId && contractorTypeId > 0) {
//            //查询类型名称
//            ContractorType contractorType = contractorTypeMapper.selectById(contractorTypeId);
//            if (null != contractorType) {
//                contractorVo.setContractorTypeName(contractorType.getName());
//            }
//        }
        List<ContractorType> originContractorTypes = contractorTypeInfoMapper.queryContractorTypeByContractorId(contractor.getContractorId());
        if (!CollectionUtils.isEmpty(originContractorTypes)) {
            List<Long> contractorTypeIds = originContractorTypes.stream().map(ContractorType::getContractorTypeId).distinct().collect(Collectors.toList());
            Map<Long, List<ContractorTypeQualityVo>> qualityMap = typeService.queryContractorTypeQuality(contractorTypeIds);

            List<ContractorTypeInfoVo> targetContractorTypeInfos = originContractorTypes.stream().map(origin -> {
                ContractorTypeInfoVo target = new ContractorTypeInfoVo();
                target.setContractorTypeId(origin.getContractorTypeId());
                target.setName(origin.getName());
                target.setQualityList(qualityMap.getOrDefault(origin.getContractorTypeId(), Collections.emptyList()));
                return target;
            }).collect(Collectors.toList());
            contractorVo.setContractorTypeInfos(targetContractorTypeInfos);
        }

        return contractorVo;
    }

    @Override
    public ContractorVo getContractorInfoAndProject(ContractorInfoDto info) {
        ReqContractor reqContractor = new ReqContractor();
        reqContractor.setContractorId(info.getContractorId());
        ContractorVo contractorVo = this.getRow(reqContractor);
        List<ContractorProjectListVo> projectList = projectService.getMAll(info);
        contractorVo.setProjectList(projectList);
        return contractorVo;
    }

    @Override
    public R updateBlacklist(ReqContractorBlacklist reqContractorBlacklist) {
        Long contractorId = reqContractorBlacklist.getContractorId();
        getRow(contractorId);
        Contractor contractorDto = new Contractor();
        contractorDto.setContractorId(contractorId);
        contractorDto.setIsBlacklist(reqContractorBlacklist.getIsBlacklist());
        contractorDto.setBlacklistReason(reqContractorBlacklist.getBlacklistReason());
        contractorDto.setModifyTime(new Date());
        contractorDto.setBlacklistTime(new Date());
        if (Objects.equals(reqContractorBlacklist.getIsBlacklist(), 0)) {
            contractorDto.setBlacklistReason("");
        }
        contractorMapper.updateByPrimaryKeySelective(contractorDto);
        return R.ok(contractorId);
    }

    @Override
    public R login(ReqContractorLogin reqContractorLogin) {
        String socialCreditCode = reqContractorLogin.getSocialCreditCode();
        String secretKey = reqContractorLogin.getSecretKey();
        Integer tenantId = TenantContext.getOne();
        if (null == tenantId) {
            throw new ServiceException("租户参数错误。");
        }
        Contractor contractor = contractorMapper.selectBySocialCreditCode(socialCreditCode, tenantId);
        if (null == contractor) {
            throw new ServiceException("未找到该承包商，请检查输入信息后再试。");
        } else if (null == contractor.getSecretKey() || !contractor.getSecretKey().equals(secretKey)) {
            throw new ServiceException("秘钥错误，请检查输入信息后再试。");
        } else if (contractor.getIsBlacklist() == ProjectConstants.CONTRACTOR_IS_BLACKLIST_YES) {
            throw new ServiceException("登录失败，该承包商在黑名单中，请联系管理员获取支持。");
        }

        ContractorProject project = contractorProjectMapper.selectByContractorId(contractor.getContractorId());
        if (null == project) {
            throw new ServiceException("登录失败，秘钥已过期，请联系管理员获取支持。");
        }
        ContractorInfoDto contractorInfoDto = new ContractorInfoDto();
        contractorInfoDto.setContractorId(contractor.getContractorId());
        contractorInfoDto.setTenantId(contractor.getTenantId());
        String contractorToken = jwtToken.encodeContractorInfo(contractorInfoDto);
        return R.ok(contractorToken);
    }

    private Contractor getRow(Long contractorId) {
        Contractor contractor = contractorMapper.selectByPrimaryKey(contractorId);
        if (null == contractor) {
            throw new ServiceException("未找到该承包商，请检查输入信息后再试。");
        }
        return contractor;
    }

    public Long insertData(ReqContractorAdd reqContractorAdd) {
        String socialCreditCode = reqContractorAdd.getSocialCreditCode();
        Contractor contractorInfo = contractorMapper.selectBySocialCreditCode(socialCreditCode, TenantContext.getOne());
        if (null != contractorInfo) {
            throw new ServiceException("该社会信用代码已存在，请检查输入后再试。");
        }

        UserInfoDto userInfo = UserContext.get();

        Date currentTime = new Date();
        Contractor contractor = new Contractor();
        contractor.setTenantId(TenantContext.getOne());

        contractor.setName(reqContractorAdd.getName());
        contractor.setSocialCreditCode(reqContractorAdd.getSocialCreditCode());
        contractor.setNature(reqContractorAdd.getNature());
//        contractor.setContractorTypeId(reqContractorAdd.getFirstContractorTypeId());

        contractor.setCreateTime(currentTime);
        contractor.setCreateBy(UserContext.getId());
        contractor.setModifyTime(currentTime);
        if (null != userInfo) {
            contractor.setCreateBy(userInfo.getUserId());
        }
        contractorMapper.insertSelective(contractor);
        Long contractorId = contractor.getContractorId();

        List<ReqContractorLicense> reqLicenseList = reqContractorAdd.getLicenseVoList();
        insertLicense(reqLicenseList, contractorId, currentTime);

        List<AttachmentDTO> otherPropertyFiles = reqContractorAdd.getOtherPropertyFiles();
        processOtherPropertyFiles(otherPropertyFiles, contractorId);

        //类型
        processContractorTypeInfo(contractorId, reqContractorAdd.getContractorTypeIds());
        return contractorId;
    }

    private void processContractorTypeInfo(Long contractorId, Collection<Long> contractorTypeIds) {
        if (Objects.isNull(contractorId)) {
            return;
        }

        contractorTypeIds = Optional.ofNullable(contractorTypeIds).orElse(Collections.emptyList()).stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());

        List<ContractorType> oldContractorTypes = contractorTypeInfoMapper.queryContractorTypeByContractorId(contractorId);
        List<Long> oldTypeIds = oldContractorTypes.stream().map(ContractorType::getContractorTypeId).filter(Objects::nonNull).distinct().collect(Collectors.toList());

        Collection<Long> deleteTypeIds = org.apache.commons.collections4.CollectionUtils.subtract(oldTypeIds, contractorTypeIds);
        Collection<Long> createTypeIds = org.apache.commons.collections4.CollectionUtils.subtract(contractorTypeIds, oldTypeIds);

        if (!CollectionUtils.isEmpty(deleteTypeIds)) {
            ContractorTypeDTO opt = ContractorTypeDTO.builder().contractorId(contractorId).contractorTypeIds(deleteTypeIds).user(UserContext.getId()).time(new Date()).build();
            contractorTypeInfoMapper.deleteByContractorIdAndTypeIds(opt);
        }

        if (!CollectionUtils.isEmpty(createTypeIds)) {
            List<ContractorTypeInfo> targets = createTypeIds.stream().filter(Objects::nonNull).map(e -> {
                ContractorTypeInfo target = new ContractorTypeInfo();
                target.setContractorId(contractorId);
                target.setContractorTypeId(e);
                target.setTenantId(TenantContext.getOne());
                target.setCreate(UserContext.getId());
                return target;
            }).collect(Collectors.toList());

            contractorTypeInfoMapper.batchInsert(targets);
        }

    }

    private void updateData(ReqContractorEdit reqContractorEdit) {
        Date currentTime = new Date();
        Contractor contractor = new Contractor();

        Long contractorId = reqContractorEdit.getContractorId();

        contractor.setContractorId(contractorId);
        contractor.setName(reqContractorEdit.getName());
        contractor.setSocialCreditCode(reqContractorEdit.getSocialCreditCode());
        contractor.setNature(reqContractorEdit.getNature());
//        contractor.setContractorTypeId(reqContractorEdit.getFirstContractorTypeId());

        contractor.setModifyTime(currentTime);
        contractorMapper.updateByPrimaryKeySelective(contractor);
        List<ReqContractorLicense> reqLicenseList = reqContractorEdit.getLicenseVoList();
        contractorLicenseMapper.deleteByContractorId(contractorId);
        insertLicense(reqLicenseList, contractorId, currentTime);
        List<AttachmentDTO> otherPropertyFiles = reqContractorEdit.getOtherPropertyFiles();
        processOtherPropertyFiles(otherPropertyFiles, contractorId);
        processContractorTypeInfo(contractorId, reqContractorEdit.getContractorTypeIds());
    }

    /**
     * 插入营业执照数据
     *
     * @param reqLicenseList
     * @param contractorId
     * @param currentTime
     */
    private void insertLicense(List<ReqContractorLicense> reqLicenseList, Long contractorId, Date currentTime) {
        if (null != reqLicenseList && reqLicenseList.size() > 0) {
            List<ContractorLicense> licenseList = new ArrayList<>();
            for (ReqContractorLicense license : reqLicenseList) {
                ContractorLicense contractorLicense = new ContractorLicense();
                contractorLicense.setContractorId(contractorId);
                contractorLicense.setName(license.getName());
                contractorLicense.setUrl(license.getUrl());
                contractorLicense.setCreateTime(currentTime);
                contractorLicense.setModifyTime(currentTime);
                licenseList.add(contractorLicense);
            }
            contractorLicenseMapper.insertBatch(licenseList);
        }
    }

    private void processOtherPropertyFiles(List<AttachmentDTO> sources, Long contractorId) {
        sources = Optional.ofNullable(sources).orElse(Collections.emptyList());
        otherPropertyMapper.deleteByContractorId(contractorId);

        if (!CollectionUtils.isEmpty(sources)) {
            List<ContractorOtherProperty> list = sources.stream()
                    .map(origin -> {
                        ContractorOtherProperty target = BeanUtils.convert(origin, ContractorOtherProperty.class);
                        target.setContractorId(contractorId);
                        target.setTenantId(TenantContext.getOne());
                        target.setCreate(UserContext.getId());
                        return target;
                    }).collect(Collectors.toList());
            otherPropertyMapper.batchInsert(list);
        }
    }

    /**
     * 分页列表数据处理
     *
     * @param contractorList
     * @param contractorVoList
     */
    private void convertList(List<Contractor> contractorList, List<ContractorListVo> contractorVoList) {
        if (contractorList.size() > 0) {
            List<Long> contractorIds = contractorList.stream().filter(Objects::nonNull).map(Contractor::getContractorId).filter(Objects::nonNull).collect(Collectors.toList());
            //查类型map
            Map<Long, List<ContractorType>> contractorTypeMap = contractorTypeInfoMapper.queryContractorTypeByContractorIds(contractorIds);

            ReqProjectQuery projectQuery = new ReqProjectQuery();
            projectQuery.setContractorIds(contractorIds);
            projectQuery.setStates(Arrays.asList(ProjectConstants.STATE_TWO, ProjectConstants.STATE_FOUR, ProjectConstants.STATE_FIVE));
            List<ContractorProject> contractorProjects = projectMapper.selectProjectList(projectQuery);
            Map<Long, List<ContractorProject>> contractorProjectMappings = contractorProjects.stream().filter(e -> Objects.nonNull(e.getContractorId()))
                    .collect(Collectors.groupingBy(ContractorProject::getContractorId));

            Map<Long, Boolean> finishedValidMap = this.validFinished(contractorIds);

            List<ContractorLicense> contractorLicenses = contractorLicenseMapper.selectListByContractorIds(contractorIds);
            Map<Long, List<ContractorLicense>> licenseMappings = contractorLicenses.stream()
                    .filter(e -> Objects.nonNull(e.getContractorId()))
                    .collect(Collectors.groupingBy(ContractorLicense::getContractorId));

            for (Contractor origin : contractorList) {
                ContractorListVo target = new ContractorListVo();
                target.setContractorId(origin.getContractorId());
                target.setName(origin.getName());
                target.setNature(origin.getNature());
                //类型
                List<ContractorType> typeList = contractorTypeMap.getOrDefault(origin.getContractorId(), Collections.emptyList());
                List<IdAndNameVo> targetTypes = typeList.stream().map(e -> new IdAndNameVo(e.getContractorTypeId(), e.getName())).collect(Collectors.toList());
                target.setContractorTypes(targetTypes);

                target.setSocialCreditCode(origin.getSocialCreditCode());
                target.setBlacklistReason(origin.getBlacklistReason());
                target.setBlacklistTime(origin.getBlacklistTime());
                //保证金
                BigDecimal totalPayment = Optional.ofNullable(origin.getTotalPayment()).orElse(BigDecimal.ZERO);
                BigDecimal deduct = Optional.ofNullable(origin.getDeduct()).orElse(BigDecimal.ZERO);
                target.setTotalPayment(totalPayment);
                target.setDeduct(deduct);
                target.setRemaining(totalPayment.subtract(deduct).max(BigDecimal.ZERO));

                List<ContractorLicense> licenseList = licenseMappings.getOrDefault(origin.getContractorId(), Collections.emptyList());
                List<ContractorLicenseVo> licenseVos = licenseList.stream().filter(Objects::nonNull).map(e -> {
                    ContractorLicenseVo license = new ContractorLicenseVo();
                    license.setUrl(e.getUrl());
                    license.setName(e.getName());
                    return license;
                }).collect(Collectors.toList());
                target.setLicenseVoList(licenseVos);

                List<ContractorProject> projectList = contractorProjectMappings.getOrDefault(origin.getContractorId(), Collections.emptyList());
                List<IdAndNameVo> unFinishedProjectList = projectList.stream()
                        .filter(e -> Objects.equals(e.getState(), ProjectConstants.STATE_TWO) || Objects.equals(e.getState(), ProjectConstants.STATE_FOUR))
                        .map(e -> new IdAndNameVo(e.getContractorProjectId(), e.getName())).collect(Collectors.toList());
                target.setUnFinishedProjectList(unFinishedProjectList);

                List<IdAndNameVo> finishedProjectList = projectList.stream()
                        .filter(e -> Objects.equals(e.getState(), ProjectConstants.STATE_FIVE))
                        .map(e -> new IdAndNameVo(e.getContractorProjectId(), e.getName())).collect(Collectors.toList());
                target.setFinishedProjectList(finishedProjectList);

                target.setValidFinished(finishedValidMap.getOrDefault(origin.getContractorId(), false));
                contractorVoList.add(target);
            }
        }
    }


    @Override
    public ExcelValidationResult<ContractorUploadDTO> validationImportExcel(List<ContractorUploadDTO> source) {
        if (CollectionUtils.isEmpty(source)) {
            log.warn("[承包商导入检查数据为空]");
            return new ExcelValidationResult<>(new ArrayList<>());
        }
        List<ContractorUploadDTO> success = new ArrayList<>();
        List<ExcelValidationError<ContractorUploadDTO>> error = new ArrayList<>();
        Set<String> duplicateSocialCreditCode = new HashSet<>();//重复的社会信用代码
        for (ContractorUploadDTO dto : source) {
            Contractor contractorInfo = contractorMapper.selectBySocialCreditCode(dto.getSocialCreditCode(), TenantContext.getOne());

            String[] contractorTypes = Optional.ofNullable(dto.getContractorType()).orElse(Strings.EMPTY).split("、");
            if (null != contractorInfo || duplicateSocialCreditCode.contains(dto.getSocialCreditCode())) {
                error.add(new ExcelValidationError<>(dto, "社会信用代码已存在"));
                continue;
            }

            List<String> inexistence = new ArrayList<>();
            Set<Long> contractorTypeIds = new HashSet<>();
            for (String type : contractorTypes) {
                ContractorType contractorType = contractorTypeMapper.selectByName(type, TenantContext.getOne());
                if (Objects.isNull(contractorType)) {
                    inexistence.add(type);
                } else {
                    contractorTypeIds.add(contractorType.getContractorTypeId());
                }
            }
            if (!CollectionUtils.isEmpty(inexistence)) {
                error.add(new ExcelValidationError<>(dto, "承包商类型不存在: " + Joiner.on("、").skipNulls().join(inexistence)));
                continue;
            }

            duplicateSocialCreditCode.add(dto.getSocialCreditCode());
            dto.setContractorTypeIds(new ArrayList<>(contractorTypeIds));
            success.add(dto);
        }
        return new ExcelValidationResult<>(success, error);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importContractorIntoDatabase(List<ContractorUploadDTO> source) {
        if (CollectionUtils.isEmpty(source)) {
            log.warn("[承包商导入数据库：数据为空]");
            return;
        }
        UserInfoDto userInfo = UserContext.getOrElseThrow();
        Date operationTime = new Date();
        for (ContractorUploadDTO item : source) {
            Contractor contractor = new Contractor();
            contractor.setTenantId(TenantContext.getOne());
            contractor.setName(item.getName());
            contractor.setSocialCreditCode(item.getSocialCreditCode());
            contractor.setNature(item.getNature());
//            contractor.setContractorTypeId(CollectionUtils.isEmpty(item.getContractorTypeIds()) ? null : item.getContractorTypeIds().get(0));
            contractor.setCreateTime(operationTime);
            contractor.setModifyTime(operationTime);
            contractor.setCreateBy(userInfo.getUserId());
            contractorMapper.insertSelective(contractor);

            processContractorTypeInfo(contractor.getContractorId(), item.getContractorTypeIds());
        }
    }

    @Override
    public ContractorStatusCountVo statusCount() {
        ContractorStatusCountVo result = new ContractorStatusCountVo();
        List<Contractor> contractorList = contractorMapper.queryByTenant(Collections.singletonList(TenantContext.getOne()));

        result.setBlacklist(contractorList.stream().filter(e -> Objects.equals(e.getIsBlacklist(), 1)).count());
        result.setWhiteList(contractorList.stream().filter(e -> Objects.equals(e.getIsBlacklist(), 0)).count());

        return result;
    }

    @Override
    public List<ContractorDownloadDTO> download(ReqContractorListPage reqContractorListPage) {
        PageListResultVo<ContractorListVo> pageListResultVo = this.getListByPage(reqContractorListPage);
        List<ContractorListVo> list = pageListResultVo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<ContractorDownloadDTO> result = new ArrayList<>();
        for (ContractorListVo origin : list) {
            ContractorDownloadDTO target = new ContractorDownloadDTO();
            target.setName(origin.getName());
            String contractorTypeName = Optional.ofNullable(origin.getContractorTypes()).orElse(Collections.emptyList())
                    .stream().map(IdAndNameVo::getName).collect(Collectors.joining("、"));
            target.setContractorType(contractorTypeName);
            target.setSocialCreditCode(origin.getSocialCreditCode());
            target.setNature(origin.getNature());
            target.setBlacklistReason(origin.getBlacklistReason());
            target.setBlacklistTime(origin.getBlacklistTime());
            target.setRemaining(origin.getRemaining());
            List<IdAndNameVo> unFinishedProjects = Optional.ofNullable(origin.getUnFinishedProjectList()).orElse(Collections.emptyList());
            List<String> projectNames = unFinishedProjects.stream().filter(Objects::nonNull).map(IdAndNameVo::getName).collect(Collectors.toList());
            target.setUnFinishedProjectList(Joiner.on("、").skipNulls().join(projectNames));
            result.add(target);
        }
        return result;
    }

    @Override
    public boolean validFinished(Long contractorId) {
        if (Objects.isNull(contractorId)) {
            return false;
        }
        ReqProjectQuery projectQuery = new ReqProjectQuery();
        projectQuery.setContractorIds(Collections.singletonList(contractorId));
        projectQuery.setStates(Collections.singletonList(ProjectConstants.STATE_FIVE));
        List<ContractorProject> finishedProjectList = projectMapper.selectProjectList(projectQuery);
//        finishedProjectList = finishedProjectList.stream().filter(e -> {
//            if (Strings.isBlank(e.getTradeVoucher())) {
//                return false;
//            }
//
//            BigDecimal supplementPayment = e.getSupplementPayment();
//            if (Objects.isNull(supplementPayment)) {
//                return false;
//            }
//
//            return supplementPayment.compareTo(BigDecimal.ZERO) > 0;
//        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(finishedProjectList)) {
            return false;
        }

        List<Long> finishedProjectIds = finishedProjectList.stream().map(ContractorProject::getContractorProjectId).filter(Objects::nonNull).collect(Collectors.toList());

        Map<Long, List<ContractorSecurityDeposit>> depositMap = securityDepositService.hasSendBackTask(finishedProjectIds);

        return finishedProjectIds.stream().anyMatch(e -> {
            List<ContractorSecurityDeposit> depositList = depositMap.getOrDefault(e, Collections.emptyList());
            return depositList.isEmpty();
        });
    }

    @Override
    public Map<Long, Boolean> validFinished(Collection<Long> contractorIds) {
        if (CollectionUtils.isEmpty(contractorIds)) {
            return new HashMap<>();
        }
        ReqProjectQuery projectQuery = new ReqProjectQuery();
        projectQuery.setContractorIds(contractorIds);
        projectQuery.setStates(Collections.singletonList(ProjectConstants.STATE_FIVE));
        List<ContractorProject> finishedProjectList = projectMapper.selectProjectList(projectQuery);
//        finishedProjectList = finishedProjectList.stream().filter(e -> {
//            if (Strings.isBlank(e.getTradeVoucher())) {
//                return false;
//            }
//
//            BigDecimal supplementPayment = e.getSupplementPayment();
//            if (Objects.isNull(supplementPayment)) {
//                return false;
//            }
//
//            return supplementPayment.compareTo(BigDecimal.ZERO) > 0;
//        }).collect(Collectors.toList());

        List<Long> allProjectIds = finishedProjectList.stream().map(ContractorProject::getContractorProjectId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, List<ContractorSecurityDeposit>> depositMap = securityDepositService.hasSendBackTask(allProjectIds);
        Map<Long, List<ContractorProject>> projectMap = finishedProjectList.stream().collect(Collectors.groupingBy(ContractorProject::getContractorId));

        Map<Long, Boolean> result = new HashMap<>();
        for (Long contractorId : contractorIds) {
            List<ContractorProject> projectList = projectMap.getOrDefault(contractorId, Collections.emptyList());
            if (CollectionUtils.isEmpty(projectList)) {
                result.put(contractorId, false);
                continue;
            }
            List<Long> projectIds = TransferUtils.transfer(projectList, ContractorProject::getContractorProjectId);
            result.put(contractorId, false);

            for (Long projectId : projectIds) {
                List<ContractorSecurityDeposit> depositList = depositMap.getOrDefault(projectId, Collections.emptyList());
                if (CollectionUtils.isEmpty(depositList)) {
                    result.put(contractorId, true);
                    break;
                }
            }
        }

        return result;
    }


    public static final String LOCK_PREFIX = "SECURITY_DEPOSIT_OPT_LOCK_";

    @Override
    public BigDecimal optPayment(ContractorSecurityDepositDetailPostDTO source, Integer type) {

        Long contractorId = source.getContractorId();
        BigDecimal value = source.getOptPayment();
        RLock lock = redissonClient.getLock(LOCK_PREFIX + contractorId);
        try {
            boolean locked = lock.tryLock(5, TimeUnit.SECONDS);
            if (locked) {
                Contractor contractor = contractorMapper.selectByPrimaryKey(contractorId);
                if (contractor != null) {
                    BigDecimal totalPayment = Optional.ofNullable(contractor.getTotalPayment()).orElse(BigDecimal.ZERO);
                    BigDecimal deduct = Optional.ofNullable(contractor.getDeduct()).orElse(BigDecimal.ZERO);
                    //1.缴纳;2.处罚;3.退还
                    if (Objects.equals(type, 1)) {
                        totalPayment = totalPayment.add(value);
                    } else if (Objects.equals(type, 2)) {
                        deduct = deduct.add(value.abs());
                    } else if (Objects.equals(type, 3)) {
                        totalPayment = totalPayment.subtract(source.getTotalPayment()).max(BigDecimal.ZERO);
                        deduct = deduct.subtract(source.getDeductPayment().abs()).max(BigDecimal.ZERO);
                    } else if (Objects.equals(type ,4)) {
                        deduct = deduct.subtract(value.abs());
                    }
                    BigDecimal remaining = totalPayment.subtract(deduct);
                    if (remaining.compareTo(BigDecimal.ZERO) < 0) {
                        throw new ServiceException("操作失败，该承包商剩余保证金不足。");
                    }
                    contractor.setTotalPayment(totalPayment);
                    contractor.setDeduct(deduct);
                    contractorMapper.updateByPrimaryKeySelective(contractor);
                    return remaining;
                } else {
                    throw new ServiceException("操作失败，承包商信息不存在。");
                }
            } else {
                throw new ServiceException("变更承包商保证金信息失败，请稍后重试");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        } finally {
            if (lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    @Override
    public BigDecimal getRemaining(Long contractorId) {
        Contractor contractor = contractorMapper.selectByPrimaryKey(contractorId);
        if (contractor != null) {
            BigDecimal totalPayment = Optional.ofNullable(contractor.getTotalPayment()).orElse(BigDecimal.ZERO);
            BigDecimal deduct = Optional.ofNullable(contractor.getDeduct()).orElse(BigDecimal.ZERO);

            return totalPayment.subtract(deduct).max(BigDecimal.ZERO);
        }
        return BigDecimal.ZERO;
    }
}
