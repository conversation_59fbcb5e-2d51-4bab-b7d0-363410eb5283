package com.huafon.contractor.models.vo.evaluate;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huafon.api.dto.v2.CheckUserDto;
import com.huafon.contractor.models.dto.AttachmentDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @since 2024-06-24 17:33
*/
@Data
@ApiModel(value = "承包商项目评价记录列表")
public class ContractorProjectEvaluateCommonVo implements Serializable {

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "评价记录编码")
    private String code;

    @ApiModelProperty(value = "评价时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date evaluateTime;

    @ApiModelProperty(value = "评价人UserId")
    private Integer evaluateUserId;

    @ApiModelProperty(value = "评价人")
    private String evaluateUserName;

    @ApiModelProperty(value = "评价部门Id")
    private Integer evaluateDeptId;

    @ApiModelProperty(value = "评价部门")
    private String evaluateDeptName;

    @ApiModelProperty(value = "承包商项目ID")
    private Integer contractorProjectId;

    @ApiModelProperty(value = "承包商项目")
    private String contractorProjectName;

    @ApiModelProperty(value = "承包商ID")
    private Integer contractorId;

    @ApiModelProperty(value = "承包商")
    private String contractorName;

    @ApiModelProperty(value = "项目开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date projectStartTime;

    @ApiModelProperty(value = "项目结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date projectEndTime;

    @ApiModelProperty(value = "实际结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date projectFinishedTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "附件")
    private List<AttachmentDTO> attachments;

    @ApiModelProperty(value = "流程ID")
    private String taskId;

    @ApiModelProperty(value = "流程状态：1流程中，2已完成；3已取消")
    private Integer status;

    @ApiModelProperty(value = "评价最终匹配到的规则ID")
    private Integer evaluateRuleId;

    @ApiModelProperty(value = "评价结果")
    private String evaluateResult;

    @ApiModelProperty(value = "最终得分")
    private Double finalScore;

//    @ApiModelProperty(value = "评价规则")
//    private Object rules;

    @ApiModelProperty(value = "选择的评价表ID")
    private Integer sheetId;

    @ApiModelProperty(value = "选择的评价表")
    private String sheetName;


    @ApiModelProperty(value = "是否允许多选：0不允许；1允许")
    private Integer multipleStandard;

    @ApiModelProperty(value = "租户ID")
    private Integer tenantId;

    @ApiModelProperty(value = "创建人名称")
    private String createByName;

    @ApiModelProperty(value = "编辑人名称")
    private String modifyByName;

    @ApiModelProperty(value = "流程名称")
    private String workflowName;

    @ApiModelProperty(value = "未操作人")
    private List<CheckUserDto> notOperating;


}
