package com.huafon.contractor.dao.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.huafon.contractor.models.entity.ContractorProjectCar;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
* <AUTHOR>
* @since 2024-08-23 10:05
*/
@Mapper
public interface ContractorProjectCarMapper extends BaseMapper<ContractorProjectCar> {


    /**
    * 批量生成
    */
    void batchInsert(@Param("source") List<ContractorProjectCar> source);


    default LambdaQueryWrapper<ContractorProjectCar> lambdaQuery(Long contractorProjectId) {
        return new LambdaQueryWrapper<ContractorProjectCar>()
                .eq(ContractorProjectCar::getContractorProjectId, contractorProjectId);
    }

    default List<ContractorProjectCar> queryByProjectId(Long contractorProjectId) {
        return this.selectList(this.lambdaQuery(contractorProjectId));
    }

    default void removeByProjectId(Long contractorProjectId) {
        this.delete(this.lambdaQuery(contractorProjectId));
    }

}
