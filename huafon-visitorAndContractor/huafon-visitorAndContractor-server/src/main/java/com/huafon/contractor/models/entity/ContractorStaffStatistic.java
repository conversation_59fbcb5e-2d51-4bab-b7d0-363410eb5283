package com.huafon.contractor.models.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.huafon.common.handler.LongArrayTypeHandler;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
* 承包商每日人员统计
* <AUTHOR>
* @since 2024-05-11 20:26
*/
@Data
@TableName(value = "hf_contractor_staff_statistic", autoResultMap = true)
public class ContractorStaffStatistic extends BaseEntity{

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
    * 统计日期
    */
    @TableField(value = "statistic_date")
    private LocalDate statisticDate;

    /**
    * 承包商人员统计UserId列表
    */
    @TableField(value = "staff_user_ids", typeHandler = LongArrayTypeHandler.class)
    private List<Long> staffUserIds;

    /**
    * 租户ID
    */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER)
    private Integer tenantId;


}
