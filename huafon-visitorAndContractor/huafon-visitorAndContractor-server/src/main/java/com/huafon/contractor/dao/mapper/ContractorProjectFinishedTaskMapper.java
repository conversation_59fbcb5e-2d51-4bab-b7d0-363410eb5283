package com.huafon.contractor.dao.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huafon.contractor.models.entity.ContractorProject;
import com.huafon.contractor.models.entity.ContractorProjectFinishedTask;
import com.huafon.contractor.models.reqo.project.ReqContractorProjectFinishedTaskPage;
import com.huafon.contractor.models.vo.project.ContractorProjectFinishedTaskCommonVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @since 2023-10-23 10:35
*/
@Mapper
public interface ContractorProjectFinishedTaskMapper extends BaseMapper<ContractorProjectFinishedTask> {

    /**
    * 分页
    */
    IPage<ContractorProjectFinishedTaskCommonVo> queryByPage(@Param("page") IPage<ContractorProjectFinishedTaskCommonVo> page, @Param("query") ReqContractorProjectFinishedTaskPage query);

    /**
    * 批量生成
    */
    void batchInsert(@Param("source") List<ContractorProjectFinishedTask> source);

    /**
     * 查询自己是对接人的项目列表
     * @param userId
     * @param tenantId
     * @return
     */
    List<ContractorProject> queryByUserAndTenantId(@Param("userId") Long userId, @Param("tenantId") Integer tenantId);


    default LambdaQueryWrapper<ContractorProjectFinishedTask> lambdaQuery() {
        return new LambdaQueryWrapper<ContractorProjectFinishedTask>();
    }

}
