package com.huafon.contractor.models.dto.history;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.core.type.TypeReference;
import com.huafon.common.handler.JsonbTypeHandler;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025-03-07 11:11
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "承包商历史操作DTO")
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "category")
@JsonSubTypes({
		@JsonSubTypes.Type(value = DelayDTO.class, name = "DELAY"),
		@JsonSubTypes.Type(value = PrincipalChangeDTO.class, name = "CHANGE"),
})
public abstract class ProjectHistoryDTO implements Serializable {

	private String category;

	public ProjectHistoryDTO(String category) {
		this.category = category;
	}


	public static class ProjectHistoryDTOHandler extends JsonbTypeHandler<ProjectHistoryDTO> {
		@Override
		protected TypeReference<ProjectHistoryDTO> typeReference() {
			return new TypeReference<ProjectHistoryDTO>() {
			};
		}
	}
}
