package com.huafon.contractor.models.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.huafon.contractor.models.dto.AttachmentDTO;
import com.huafon.contractor.models.enums.EvaluateOperateEnum;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import lombok.Data;

import java.util.List;

/**
* 承包商项目评价标准
* <AUTHOR>
* @since 2024-06-21 17:14
*/
@Data
@TableName(value = "hf_contractor_project_evaluate_standard", autoResultMap = true)
public class ContractorProjectEvaluateStandard extends BaseEntity{

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
    * 评价标准编码
    */
    @TableField(value = "code")
    private String code;

    /**
    * 承包商项目评价标准-分类ID
    */
    @TableField(value = "type_id")
    private Integer typeId;

    /**
    * 排序值
    */
    @TableField(value = "sort")
    private Integer sort;

    /**
    * 评价标准
    */
    @TableField(value = "evaluate_standard")
    private String evaluateStandard;

    /**
    * PLUS(加分),MINUS(减分)
    */
    @TableField(value = "operator")
    private EvaluateOperateEnum operator;

    /**
    * 分值
    */
    @TableField(value = "score")
    private Double score;

    /**
    * 附件
    */
    @TableField(value = "attachments", typeHandler = AttachmentDTO.AttachmentDTOHandler.class)
    private List<AttachmentDTO> attachments;

    /**
    * 备注
    */
    @TableField(value = "remark")
    private String remark;

    /**
    * 租户ID
    */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER)
    private Integer tenantId;

    /**
    * 创建人名称
    */
    @TableField(value = "create_by_name")
    private String createByName;

    /**
    * 编辑人名称
    */
    @TableField(value = "modify_by_name")
    private String modifyByName;


}
