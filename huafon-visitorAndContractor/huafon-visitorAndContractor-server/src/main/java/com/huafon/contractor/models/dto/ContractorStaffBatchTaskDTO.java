package com.huafon.contractor.models.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collection;

/**
 * <AUTHOR>
 * @since 2024-09-26 16:57
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ContractorStaffBatchTaskDTO implements Serializable {

	private Collection<Long> contractorStaffIds;

	private Integer tenantId;

	private Long userId;
}
