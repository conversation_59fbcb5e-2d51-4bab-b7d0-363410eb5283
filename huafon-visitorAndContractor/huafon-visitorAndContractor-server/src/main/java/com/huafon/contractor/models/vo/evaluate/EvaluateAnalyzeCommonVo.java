package com.huafon.contractor.models.vo.evaluate;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-06-25 17:27
 **/
@Data
@ApiModel(value = "承包商项目评价分析列表")
public class EvaluateAnalyzeCommonVo implements Serializable {

	@ApiModelProperty(value = "承包商ID")
	private Integer contractorId;

	@ApiModelProperty(value = "承包商名称")
	private String contractorName;

	@ApiModelProperty(value = "明细条目")
	private List<EvaluateAnalyzeCommonItemVo> items = new ArrayList<>();

	@JsonIgnore
	private List<Integer> referenceEvaluateIds;

}
