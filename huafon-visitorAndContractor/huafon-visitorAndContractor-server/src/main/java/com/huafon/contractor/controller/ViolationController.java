package com.huafon.contractor.controller;

import com.huafon.admin.api.annotation.OperationLog;
import com.huafon.admin.api.enums.OperationLogType;
import com.huafon.contractor.annotation.ContractorLoginRequired;
import com.huafon.contractor.models.dto.ContractorInfoDto;
import com.huafon.contractor.models.dto.TaskCountDTO;
import com.huafon.contractor.models.reqo.violation.*;
import com.huafon.contractor.models.vo.PageListResultVo;
import com.huafon.contractor.models.vo.project.ProcessCountVo;
import com.huafon.contractor.models.vo.violation.ViolationListVo;
import com.huafon.contractor.models.vo.violation.ViolationVo;
import com.huafon.contractor.service.ViolationService;
import com.huafon.support.config.UserContext;
import com.huafon.support.core.pojo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/4/12 13:22
 * 承包商违规处罚单管理
 */
@Api(tags = "违规处罚单管理")
@RestController
@RequestMapping("/violation")
public class ViolationController {

    @Resource
    private ViolationService violationService;

    @PostMapping("/add")
    @ApiOperation(value = "违规处罚单新增")
    @OperationLog(notes = "违规处罚单新增", type = OperationLogType.ADD)
    public Long add(@RequestBody @Valid ReqViolationAdd reqViolationAdd) {
        return violationService.add(reqViolationAdd);
    }

    @PostMapping("/edit")
    @ApiOperation(value = "违规处罚单编辑")
    @OperationLog(notes = "违规处罚单编辑", type = OperationLogType.MODITY)
    public Long edit(@RequestBody @Valid ReqViolationEdit reqViolationEdit) {
        return violationService.edit(reqViolationEdit);
    }

    @PostMapping("/getlistbypage")
    @ApiOperation(value = "违规处罚单列表-分页")
    public R<PageListResultVo<ViolationListVo>> getListByPage(@RequestBody @Valid ReqViolationListPage reqViolationListPage) {
        return violationService.getListByPage(reqViolationListPage);
    }

    @PostMapping("/process/list/count")
    @ApiOperation(value = "违规处罚流程统计")
    public R<ProcessCountVo> processListCount() {
        return R.ok(violationService.processListCount());
    }

    @PostMapping("/self/getlistbypage")
    @ApiOperation(value = "违规处罚单列表-分页(仅查询自己创建的)")
    public R<PageListResultVo<ViolationListVo>> getListByPageH5(@RequestBody @Valid ReqViolationListPage reqViolationListPage) {
        reqViolationListPage.setUserId(UserContext.getId());
        return violationService.getListByPage(reqViolationListPage);
    }

    @PostMapping("/self/list/count")
    @ApiOperation(value = "列表统计(仅查询自己创建的)")
    public R<TaskCountDTO> countH5() {
        return R.ok(violationService.countH5());
    }

    @PostMapping("/getall")
    @ApiOperation(value = "违规处罚单列表-全部")
    public R<List<ViolationListVo>> getAll(@RequestBody @Valid ReqViolationAll reqViolationAll) {
        return violationService.getAll(reqViolationAll);
    }

    @PostMapping("/getrow")
    @ApiOperation(value = "违规处罚单详情")
    public R<ViolationVo> getRow(@RequestBody @Valid ReqViolation reqViolation) {
        return violationService.getRow(reqViolation);
    }

    @PostMapping("/del")
    @ApiOperation(value = "违规处罚单删除")
    @OperationLog(notes = "违规处罚单删除", type = OperationLogType.DELETE)
    public R del(@RequestBody @Valid ReqViolation reqViolation) {
        return violationService.del(reqViolation);
    }

    @PostMapping("/batch/del")
    @ApiOperation(value = "违规处罚单批量删除")
    @OperationLog(notes = "违规处罚单批量删除", type = OperationLogType.DELETE)
    public void batchDel(@RequestBody @Valid ReqBatchViolation query) {
        violationService.batchDel(query);
    }

    @PostMapping("/h5/getrow")
    @ApiOperation(value = "违规处罚单详情-移动端")
    @ContractorLoginRequired
    public R<ViolationVo> getMRow(@RequestBody @Valid ReqViolation reqViolation, @RequestAttribute("contractorInfo") ContractorInfoDto info) {
        //reqViolation.setContractorId(info.getContractorId());
        return violationService.getRow(reqViolation);
    }

    @PostMapping("/h5/getall")
    @ApiOperation(value = "违规处罚单列表-全部-移动端")
    @ContractorLoginRequired
    public R<List<ViolationListVo>> getMAll(@RequestBody @Valid ReqViolationAll reqViolationAll, @RequestAttribute("contractorInfo") ContractorInfoDto info) {
        reqViolationAll.setContractorId(info.getContractorId());
        return violationService.getAll(reqViolationAll);
    }

    @PostMapping("/process/check")
    @ApiOperation(value = "流程节点审核")
    public R<Void> check(@RequestBody @Valid ReqViolationCheck query) {
        violationService.check(query);
        return R.ok();
    }

    @PostMapping("/process/staging")
    @ApiOperation(value = "流程节点暂存")
    public R<Void> staging(@RequestBody @Valid ReqViolationEdit query) {
        violationService.staging(query);
         return R.ok();
    }

    @PostMapping("/process/refuse")
    @ApiOperation(value = "流程节点撤销/拒绝")
    public R<Void> refuse(@RequestBody @Valid ReqViolationRefuse refuse) {
         violationService.refuse(refuse);
         return R.ok();
    }
}
