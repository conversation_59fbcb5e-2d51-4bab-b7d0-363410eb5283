package com.huafon.contractor.models.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.huafon.contractor.models.dto.AttachmentDTO;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import lombok.Data;

import java.util.List;

/**
* 承包商项目-入场设备设施
* <AUTHOR>
* @since 2024-08-23 10:00
*/
@Data
@TableName(value = "hf_contractor_project_equipment", autoResultMap = true)
public class ContractorProjectEquipment extends BaseEntity{

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
    * 项目ID
    */
    @TableField(value = "contractor_project_id")
    private Long contractorProjectId;

    /**
    * 名称
    */
    @TableField(value = "name")
    private String name;

    /**
    * 照片
    */
    @TableField(value = "picture", typeHandler = AttachmentDTO.AttachmentDTOHandler.class)
    private List<AttachmentDTO> picture;

    /**
    * 是否特种设备：0 不是；1是
    */
    @TableField(value = "is_special")
    private Integer isSpecial;

    /**
    * 租户ID
    */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER)
    private Integer tenantId;


}
