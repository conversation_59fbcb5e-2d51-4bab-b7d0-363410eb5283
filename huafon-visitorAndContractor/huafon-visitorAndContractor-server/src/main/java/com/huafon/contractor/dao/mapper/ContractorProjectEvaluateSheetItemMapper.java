package com.huafon.contractor.dao.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.huafon.common.config.TenantContext;
import com.huafon.contractor.models.entity.ContractorProjectEvaluateSheetItem;
import org.apache.ibatis.annotations.Mapper;


/**
* <AUTHOR>
* @since 2024-06-24 11:13
*/
@Mapper
public interface ContractorProjectEvaluateSheetItemMapper extends BaseMapper<ContractorProjectEvaluateSheetItem> {


    default LambdaQueryWrapper<ContractorProjectEvaluateSheetItem> lambdaQuery() {
        return new LambdaQueryWrapper<ContractorProjectEvaluateSheetItem>().eq(ContractorProjectEvaluateSheetItem::getTenantId, TenantContext.getOne());
    }

}
