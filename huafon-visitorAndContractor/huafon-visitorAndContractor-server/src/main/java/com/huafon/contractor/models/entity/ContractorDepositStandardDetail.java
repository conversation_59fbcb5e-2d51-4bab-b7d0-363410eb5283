package com.huafon.contractor.models.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 承包商押金标准明细
 */
@Data
@TableName("hf_contractor_deposit_standard_detail")
public class ContractorDepositStandardDetail {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 标准ID
     */
    @TableField("standard_id")
    private Long standardId;
    
    /**
     * 金额范围（LT_3W-小于3万, GE_3W-大于等于3万, GTE_50W_LT_100W-大于等于50万小于100万, GTE_100W-大于等于100万, GT_100W-大于100万）
     */
    @TableField("amount_range")
    private String amountRange;
    
    /**
     * 押金值
     */
    @TableField("deposit_value")
    private BigDecimal depositValue;
    
    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;
    
    /**
     * 创建人ID
     */
    @TableField("create_by")
    private Long createBy;
    
    /**
     * 创建人姓名
     */
    @TableField("create_by_name")
    private String createByName;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
    
    /**
     * 修改人ID
     */
    @TableField("modify_by")
    private Long modifyBy;
    
    /**
     * 修改人姓名
     */
    @TableField("modify_by_name")
    private String modifyByName;
    
    /**
     * 修改时间
     */
    @TableField("modify_time")
    private LocalDateTime modifyTime;
} 