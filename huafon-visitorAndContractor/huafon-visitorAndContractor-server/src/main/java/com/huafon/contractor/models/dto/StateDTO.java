package com.huafon.contractor.models.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024-03-21 10:35
 **/
@Data
@ApiModel(value = "变更状态DTO")
public class StateDTO implements Serializable {
	@ApiModelProperty(value = "记录ID")
	@NotNull(message = "记录ID不能为空")
	private Integer id;

	@ApiModelProperty(value = "要变更到的状态: 2|审核中 4|施工中 5|已完结 6|已取消 7|已暂停")
	@NotNull(message = "记录ID不能为空")
	private Integer state;
}
