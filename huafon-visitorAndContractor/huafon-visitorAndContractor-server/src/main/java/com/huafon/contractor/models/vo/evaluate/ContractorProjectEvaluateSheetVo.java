package com.huafon.contractor.models.vo.evaluate;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @since 2024-06-24 10:47
*/
@Data
@ApiModel(value = "承包商项目评价表")
public class ContractorProjectEvaluateSheetVo implements Serializable {

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "评价表-分类ID")
    private Integer typeId;

    @ApiModelProperty(value = "评价表-分类名称")
    private String typeName;

    @ApiModelProperty(value = "评价表编码")
    private String code;

    @ApiModelProperty(value = "评价表名称")
    private String name;

    @ApiModelProperty(value = "基础分")
    private Double basicScore;


    @ApiModelProperty(value = "是否允许多选：0不允许；1允许")
    private Integer multipleStandard;

    @ApiModelProperty(value = "评价时分值是否允许修改:0不允许；1允许")
    private Integer scoreEditable;

    @ApiModelProperty(value = "评价标注信息")
    List<ContractorProjectEvaluateStandardVo> standardInfos;

    @ApiModelProperty(value = "租户ID")
    private Integer tenantId;

    @ApiModelProperty(value = "创建人名称")
    private String createByName;

    @ApiModelProperty(value = "编辑人名称")
    private String modifyByName;


    @ApiModelProperty(value = "编辑人UserId")
    private Integer modifyBy;

    @ApiModelProperty(value = "编辑时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

}
