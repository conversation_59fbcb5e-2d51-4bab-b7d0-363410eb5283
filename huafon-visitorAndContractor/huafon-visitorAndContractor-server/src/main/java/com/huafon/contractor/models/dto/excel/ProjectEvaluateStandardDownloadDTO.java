package com.huafon.contractor.models.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.huafon.contractor.models.enums.EvaluateOperateEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024-06-21 20:49
 **/
@Data
@ContentRowHeight(18)
@ContentStyle(wrapped = BooleanEnum.TRUE)
public class ProjectEvaluateStandardDownloadDTO implements Serializable {

	@ExcelProperty(index = 0, value = "评价标准分类")
	@NotBlank(message = "评价标准分类不能为空")
	private String evaluateStandardType;

	@ExcelProperty(index = 1, value = "评价标准")
	private String evaluateStandard;

	@ExcelProperty(index = 2, value = "分值类型")
	private String operate;

	public void evaluateOperate(EvaluateOperateEnum operateEnum) {
		if (Objects.nonNull(operateEnum)) {
			switch (operateEnum){
				case PLUS:
					this.operate = "加分";
					break;
				case MINUS:
					this.operate = "扣分";
					break;
				default:
			}
		}
	}

	@ExcelProperty(index = 3, value = "分值")
	private String score;

	@ExcelProperty(index = 4, value = "排序值")
	private String sort;

	@ExcelProperty(index = 5, value = "备注")
	private String remark;
}
