package com.huafon.contractor.models.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.huafon.contractor.models.dto.AttachmentDTO;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
* 承包商项目资质审核任务-条目
* <AUTHOR>
* @since 2024-05-15 16:59
*/
@Data
@TableName(value = "hf_contractor_project_quality_examine_task_item", autoResultMap = true)
public class ContractorProjectQualityExamineTaskItem extends BaseEntity{

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
    * 项目资质审核任务ID
    */
    @TableField(value = "quality_examine_task_id")
    private Integer qualityExamineTaskId;

    /**
     * 承包商项目下的资质记录ID
     */
    @TableField(value = "project_quality_item_id")
    private Long projectQualityItemId;

    /**
     * 审核内容
     */
    @TableField(value = "examine_content")
    private String examineContent;

    /**
    * 资质名称
    */
    @TableField(value = "name")
    private String name;

    /**
    * 过期日期
    */
    @TableField(value = "expire_date")
    private Date expireDate;

    /**
     * 1需要上传附件;0不需要
     */
    @TableField(value = "is_acc")
    private Integer isAcc;

    /**
    * 附件
    */
    @TableField(value = "attachments", typeHandler = AttachmentDTO.AttachmentDTOHandler.class)
    private List<AttachmentDTO> attachments;

    /**
    * 审核人ID
    */
    @TableField(value = "examine_user_id")
    private Integer examineUserId;

    /**
    * 审核人名称
    */
    @TableField(value = "examine_user_name")
    private String examineUserName;

    /**
    * 审核部门ID
    */
    @TableField(value = "examine_department_id")
    private Integer examineDepartmentId;

    /**
    * 审核部门名称
    */
    @TableField(value = "examine_department_name")
    private String examineDepartmentName;

    /**
     * 结果
     */
    @TableField(value = "examine_result", updateStrategy = FieldStrategy.IGNORED)
    private String examineResult;

    /**
     * 租户ID
     */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER)
    private Integer tenantId;


}
