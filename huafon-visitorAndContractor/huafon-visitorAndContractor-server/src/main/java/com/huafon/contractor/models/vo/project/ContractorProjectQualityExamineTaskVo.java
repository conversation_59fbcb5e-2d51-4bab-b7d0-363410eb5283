package com.huafon.contractor.models.vo.project;

import com.huafon.contractor.models.dto.SignatureInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
* <AUTHOR>
* @since 2024-05-15 16:59
*/
@Data
@ApiModel(value = "承包商项目资质审核任务")
public class ContractorProjectQualityExamineTaskVo extends ContractorProjectVo implements Serializable {

	@ApiModelProperty(value = "任务ID")
	private Integer taskId;

	@ApiModelProperty(value = "任务备注")
	private String taskRemark;

	@ApiModelProperty(value = "签字信息")
	private SignatureInfoDTO signatureInfo;

	@ApiModelProperty(value = "审核人ID")
	private Integer examineUserId;

	@ApiModelProperty(value = "审核人名称")
	private String examineUserName;

}
