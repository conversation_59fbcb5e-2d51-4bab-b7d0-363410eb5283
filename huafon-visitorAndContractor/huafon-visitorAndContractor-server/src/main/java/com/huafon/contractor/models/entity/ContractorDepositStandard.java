package com.huafon.contractor.models.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 承包商押金标准
 */
@Data
@TableName("hf_contractor_deposit_standard")
public class ContractorDepositStandard {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 类型（PRODUCTION-生产时期, CONSTRUCTION_LE_100-建设时期(M<=100万), CONSTRUCTION_GT_100-建设时期(M>100万)）
     */
    @TableField("type")
    private String type;
    
    /**
     * 周期最小值（天）
     */
    @TableField("period_min")
    private Integer periodMin;
    
    /**
     * 周期最大值（天）
     */
    @TableField("period_max")
    private Integer periodMax;
    
    /**
     * 金额最小值
     */
    @TableField("amount_min")
    private BigDecimal amountMin;
    
    /**
     * 金额最大值
     */
    @TableField("amount_max")
    private BigDecimal amountMax;
    
    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
    
    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;
    
    /**
     * 创建人ID
     */
    @TableField("create_by")
    private Long createBy;
    
    /**
     * 创建人姓名
     */
    @TableField("create_by_name")
    private String createByName;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
    
    /**
     * 修改人ID
     */
    @TableField("modify_by")
    private Long modifyBy;
    
    /**
     * 修改人姓名
     */
    @TableField("modify_by_name")
    private String modifyByName;
    
    /**
     * 修改时间
     */
    @TableField("modify_time")
    private LocalDateTime modifyTime;
} 