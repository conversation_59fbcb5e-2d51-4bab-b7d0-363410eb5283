package com.huafon.contractor.models.vo.staff;

import com.huafon.contractor.models.enums.TechnologyStatusEnum;
import com.huafon.contractor.models.vo.CardBindStatusVo;
import com.huafon.contractor.models.vo.project.ContractorStaffProjectVo;
import com.huafon.safety.models.vo.TechnologyInformStaffSimpleVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/4/20 10:34
 */
@Data
public class StaffVo {

    @ApiModelProperty(value = "人员id")
    private Long contractorStaffId;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "身份证")
    private String idCard;

    @ApiModelProperty(value = "号码")
    private String mobile;

//    @ApiModelProperty(value = "所属公司名称(甲方)")
//    private String departmentName;

    @ApiModelProperty(value = "是否特殊工种 1|是 0|否")
    private Integer isSpecialWork;

    @ApiModelProperty(value = "健康码")
    private String healthCode;

    @ApiModelProperty(value = "行程卡")
    private String travelHistoryCard;

    @ApiModelProperty(value = "核酸报告")
    private String nucleicAcidReport;

    @ApiModelProperty(value = "体检报告")
    private List<String> medicalExaminationReport;

    @ApiModelProperty(value = "劳务合同")
    private List<String> serviceContract;

    @ApiModelProperty(value = "状态 1|未入厂 2|已入厂 3|已离厂 4|黑名单 5|待审批 6｜审核未通过")
    private Integer state;

    @ApiModelProperty(value = "照片")
    private String photo;

    @ApiModelProperty(value = "身份证-正面")
    private String idCardPhotoFront;

    @ApiModelProperty(value = "身份证-反面")
    private String idCardPhotoReverse;

    @ApiModelProperty(value = "证书列表")
    private List<StaffLicenseVo> licenseList;

    @ApiModelProperty(value = "所属公司id")
    private String departmentId;

    @ApiModelProperty(value = "违规罚款单列表")
    private List<StaffViolationVo> violationList;

    @ApiModelProperty(value = "入厂时间")
    private Date theFactoryTime;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "承包商名称")
    private String contractorName;

    @ApiModelProperty(value = "项目信息(该人员在全部租户下施工中的项目信息)")
    private List<ContractorStaffProjectVo> projectList;

    @ApiModelProperty(value = "审批流ID")
    private String taskId;

    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    @ApiModelProperty(value = "承包商项目ID")
    private Long contractorProjectId;

    @ApiModelProperty(value = "租户ID")
    private Integer tenantId;

    @ApiModelProperty(value = "出入权限(绑卡信息)")
    private List<CardBindStatusVo> cardBindStatus;

    @ApiModelProperty(value = "安全技术交底")
    private List<TechnologyInformStaffSimpleVo> technologyInforms;

    @ApiModelProperty(value = "安全技术交底状态")
    private TechnologyStatusEnum technologyStatus;

    @ApiModelProperty(value = "保险信息列表(扫一扫UserId查询返回)")
    private Collection<ContractorStaffInsuranceVo> insuranceList;

    @ApiModelProperty(value = "保险信息")
    private ContractorStaffInsuranceVo insurance;

    @ApiModelProperty(value = "培训情况：1已培训")
    private Integer trainState;


    @ApiModelProperty(value = "证件照类型字典code")
    private String idTypeCode;
    @ApiModelProperty(value = "证件照类型字典Name")
    private String idTypeName;

    @ApiModelProperty(value = "重新发起审核状态：0未发起；1已发起")
    private Integer reviewState;
}
