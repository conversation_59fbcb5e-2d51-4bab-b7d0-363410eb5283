package com.huafon.contractor.models.vo.project;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huafon.contractor.models.enums.ProjectHistoryTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* @since 2025-02-20 10:24
*/
@Data
@ApiModel(value = "承包商项目延期历史记录列表")
public class ContractorProjectHistoryCommonVo implements Serializable {

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "项目ID")
    private Integer contractorProjectId;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "操作时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date optTime;

    @ApiModelProperty(value = "操作人")
    private Integer optUserId;

    @ApiModelProperty(value = "操作人名称")
    private String optUserName;

    @ApiModelProperty(value = "操作类型")
    private ProjectHistoryTypeEnum type;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "租户ID")
    private Integer tenantId;


}
