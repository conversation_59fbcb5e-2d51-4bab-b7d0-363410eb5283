package com.huafon.contractor.service.impl;

import com.huafon.common.config.TenantContext;
import com.huafon.common.util.NacosServiceChecker;
import com.huafon.common.utils.bean.BeanUtils;
import com.huafon.contractor.dao.mapper.ContractorProjectConfigMapper;
import com.huafon.contractor.models.entity.ContractorProjectConfig;
import com.huafon.contractor.models.vo.project.ContractorProjectConfigVo;
import com.huafon.contractor.service.ContractorProjectConfigService;
import com.huafon.framework.mybatis.service.MybatisServiceImpl;
import com.huafon.portal.api.dto.UserDto;
import com.huafon.safety.service.remote.UserRemoteService;
import com.huafon.support.config.UserContext;
import com.huafon.support.exceptions.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;


/**
* 承包商项目-资质审核
* <AUTHOR>
* @since 2024-05-15 15:56
*/
@Service
@Slf4j
@Transactional
public class ContractorProjectConfigServiceImpl extends MybatisServiceImpl<ContractorProjectConfigMapper, ContractorProjectConfig> implements ContractorProjectConfigService{

    @Autowired
    private UserRemoteService userRemoteService;
    @Autowired
    private NacosServiceChecker serviceChecker;



    @Override
    public void saveOrUpdate(ContractorProjectConfigVo update) {
        ContractorProjectConfig source = null;
        if (Objects.isNull(update.getId())) {
            source = getByTenantId(TenantContext.getOne());
        } else {
            source = this.getById(update.getId());
            if (Objects.isNull(source)) {
                throw new ServiceException("更新失败，承包商项目-资质审核不存在");
            }
        }
        if (source == null) {
            source = getDefaultValue(TenantContext.getOne());
            source.setCreate(UserContext.getId());
        }

        source.setQualityExamine(update.getQualityExamine());
        source.setSecurityDeposit(update.getSecurityDeposit());
        source.setEquipmentCar(update.getEquipmentCar());
        source.setAutoStartEvaluate(update.getAutoStartEvaluate());
        source.setModify(UserContext.getId());

        this.saveOrUpdate(source);
    }

    private ContractorProjectConfig getDefaultValue(Integer tenantId) {
        ContractorProjectConfig result = new ContractorProjectConfig();
        result.setTenantId(tenantId);
        result.setQualityExamine(false);
        result.setSecurityDeposit(false);
        result.setEquipmentCar(false);
        result.setAutoStartEvaluate(false);
        return result;
    }

    private ContractorProjectConfig getByTenantId(Integer tenantId) {
        return this.lambdaQuery().eq(ContractorProjectConfig::getTenantId, tenantId).orderByDesc(ContractorProjectConfig::getId).last(" limit 1").one();
    }


    @Override
    public ContractorProjectConfigVo queryConfigByTenantId(Integer tenantId) {
        ContractorProjectConfig contractorProjectConfig = this.getByTenantId(tenantId);
        if (Objects.isNull(contractorProjectConfig)) {
            contractorProjectConfig = getDefaultValue(tenantId);
        }
        ContractorProjectConfigVo target = BeanUtils.convert(contractorProjectConfig, ContractorProjectConfigVo.class);

        //判断东山定制服务
        boolean serviceOnline = serviceChecker.isServiceOnline(SERVICE_NAME);
        if (!serviceOnline) {
            log.warn("【东山定制服务未在线】tenantId {}", tenantId);
            target.setDsPersonalise(false);
            target.setSecurityDeposit(false);
        }

        Integer userId = Optional.ofNullable(contractorProjectConfig.getModifyBy()).map(Long::intValue).orElse(-1);
        UserDto userDto = userRemoteService.queryByUserId(userId);
        if (Objects.nonNull(userDto)) {
            target.setModifyBy(userId);
            target.setModifyName(userDto.getName());
        }
        return target;
    }

    @Override
    public boolean isOpen(Integer tenantId, Function<ContractorProjectConfig, Boolean> transfer) {
        ContractorProjectConfig configInfo = this.getByTenantId(tenantId);
        if (Objects.isNull(configInfo)) {
            configInfo = getDefaultValue(tenantId);
        }

        Boolean open = transfer.apply(configInfo);
        log.info("是否开启配置：tenantId {}, {}, value {}", tenantId, transfer, open);
        return open;
    }
}
