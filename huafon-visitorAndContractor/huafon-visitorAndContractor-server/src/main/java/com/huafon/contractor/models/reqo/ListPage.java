package com.huafon.contractor.models.reqo;

import com.huafon.framework.mybatis.pojo.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/4/20 13:25
 */
@Data
public class ListPage extends PageRequest {

    @ApiModelProperty(value = "租户id", hidden = true)
    private List<Integer> tenantIds;

//    @ApiModelProperty(value = "组织机构id")
//    private Long departmentId;
}
