package com.huafon.contractor.dao.mapper;

import com.huafon.contractor.models.entity.ContractorProjectQualityAcc;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface ContractorProjectQualityAccMapper {

    /**
     * 批量插入
     * @param list
     * @return
     */
    int insertBatch(@Param("list") List<ContractorProjectQualityAcc> list);

    /**
     * 根据资质id查询列表
     * @param conProQuaId
     * @return
     */
    List<ContractorProjectQualityAcc> selectListByConProQuaIds(Long conProQuaId);

    void deleteQualityAcc(@Param("conProQuaIds") Collection<Long> conProQuaIds);
}
