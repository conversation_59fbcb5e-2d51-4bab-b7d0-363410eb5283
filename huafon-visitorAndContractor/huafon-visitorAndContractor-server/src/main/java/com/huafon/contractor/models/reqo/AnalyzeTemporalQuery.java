package com.huafon.contractor.models.reqo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Sets;
import com.huafon.contractor.models.dto.QueryHistoryDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.logging.log4j.util.Strings;

import javax.validation.constraints.AssertTrue;
import java.io.Serializable;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.Temporal;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024-05-11 14:58
 **/
@Getter
@Setter
public class AnalyzeTemporalQuery extends QueryHistoryDTO implements Serializable {

	private static final Set<ChronoUnit> SUPPORT_UNIT = Sets.newHashSet(ChronoUnit.YEARS, ChronoUnit.MONTHS, ChronoUnit.DAYS);

	@ApiModelProperty(value = "筛选类型")
	private Integer type = 1;

	@ApiModelProperty(value = "时间类型：YEARS,MONTHS,DAYS")
	private ChronoUnit unit = ChronoUnit.MONTHS;

	@AssertTrue(message = "不支持的时间类型")
	public boolean assertUnitType() {
		return SUPPORT_UNIT.contains(unit);
	}

	@ApiModelProperty(value = "统计时段开始")
	private String startYearAndMonth;

	@ApiModelProperty(value = "统计时段结束")
	private String endYearAndMonth ;

	@ApiModelProperty(value = "部门ID")
	private Integer deptId;

	@ApiModelProperty(value = "过滤部门ID", hidden = true)
	private List<Integer> filterDeptIds;

	@ApiModelProperty(value = "项目类型：字典")
	private String projectType;

	@ApiModelProperty(value = "租户ID", hidden = true)
	private Integer tenantId;

	@ApiModelProperty(value = "过滤的states", hidden = true)
	private List<Integer> states;

	@JsonIgnore
	public Date getStartYearAndMonthDateValue() {
		Temporal temporal = getStartYearAndMonthTemporal();
		if (temporal instanceof Year) {
			temporal = ((Year) temporal).atMonth(1);
		}
		if (temporal instanceof YearMonth) {
			temporal = ((YearMonth) temporal).atDay(1);
		}
		if (temporal instanceof LocalDate) {
			temporal = ((LocalDate) temporal).atStartOfDay();
		}
		return Date.from(((LocalDateTime)temporal).atZone(ZoneId.systemDefault()).toInstant());
	}

	@JsonIgnore
	public Date getEndYearAndMonthDateValue() {
		Temporal temporal = getEndYearAndMonthTemporal();
		if (temporal instanceof Year) {
			temporal = ((Year) temporal).atMonth(12);
		}
		if (temporal instanceof YearMonth) {
			temporal = ((YearMonth) temporal).atEndOfMonth();
		}
		if (temporal instanceof LocalDate) {
			temporal = ((LocalDate) temporal).atTime(LocalTime.MAX);
		}
		return Date.from(((LocalDateTime)temporal).atZone(ZoneId.systemDefault()).toInstant());
	}

	@JsonIgnore
	public Temporal getStartYearAndMonthTemporal() {
		return Optional.ofNullable(parseTemporalStr(this.getStartYearAndMonth())).orElse(YearMonth.now().withMonth(1));
	}

	@JsonIgnore
	public Temporal getEndYearAndMonthTemporal() {
		return Optional.ofNullable(parseTemporalStr(this.getEndYearAndMonth())).orElse(YearMonth.now().withMonth(12));
	}

	private Temporal parseTemporalStr(String value) {
		if (Strings.isBlank(value)) {
			value = DateTimeFormatter.ISO_LOCAL_DATE.format(LocalDateTime.now());
		}
		if (ChronoUnit.YEARS.equals(unit)) {
			return Year.parse(value.substring(0, 4));
		} else if (ChronoUnit.MONTHS.equals(unit)) {
			return YearMonth.parse(value.substring(0, 7));
		} else if (ChronoUnit.DAYS.equals(unit)) {
			return LocalDate.parse(value.substring(0, 10));
		}
		return null;
	}
}
