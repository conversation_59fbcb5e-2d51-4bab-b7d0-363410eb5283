package com.huafon.contractor.models.reqo.project;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.huafon.framework.mybatis.pojo.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ReqProjectChangeHistoryProcessPage extends PageRequest {

    @ApiModelProperty(value = "模糊搜索")
    @JSONField(name = "name")
    @JsonProperty(value = "name")
    private String searchKey;

    @ApiModelProperty(value = "项目对接人部门")
    private Integer declareDepartmentId;

    @ApiModelProperty(value = "项目对接人部门-子部门列表", hidden = true)
    private List<Integer> declareSubDepartmentId;

    @ApiModelProperty(value = "项目开始时间：筛选开始,格式：yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    private Date startTimeStart;

    @ApiModelProperty(value = "项目开始时间：筛选结束，格式：yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    private Date startTimeEnd;

    @ApiModelProperty(value = "项目结束时间：筛选开始,格式：yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    private Date endTimeStart;

    @ApiModelProperty(value = "项目结束时间：筛选结束，格式：yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    private Date endTimeEnd;

    @ApiModelProperty(value = "项目类型")
    private String projectType;


    @ApiModelProperty(value = "状态,1 流程中；2已完成；3取消")
    private Integer status;

    @ApiModelProperty(hidden = true)
    private Integer tenantId;

}
