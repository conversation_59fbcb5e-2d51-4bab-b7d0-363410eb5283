package com.huafon.contractor.models.reqo.staff;

import com.huafon.framework.mybatis.pojo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-09-26 10:27
 **/
@Data
@ApiModel(value = "承包商人员批量审批任务H5列表查询")
public class ContractorStaffBatchTaskH5PageQuery extends PageRequest {

	@ApiModelProperty(value = "模糊搜索")
	private String searchKey;

	@ApiModelProperty(value = "租户ID", hidden = true)
	private Integer tenantId;

	@ApiModelProperty(hidden = true)
	private Long userId;
}
