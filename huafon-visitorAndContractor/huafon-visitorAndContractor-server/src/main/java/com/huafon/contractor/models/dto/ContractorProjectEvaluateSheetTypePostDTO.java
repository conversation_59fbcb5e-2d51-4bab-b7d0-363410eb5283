package com.huafon.contractor.models.dto;

import com.huafon.contractor.models.entity.ContractorProjectEvaluateSheetType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
* <AUTHOR>
* @since 2024-06-24 09:59
*/
@Data
@ApiModel(value = "承包商项目评价表-分类提交")
public class ContractorProjectEvaluateSheetTypePostDTO implements Serializable {

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "父节点")
    private Integer parentId;

    @ApiModelProperty(value = "类型名称")
    @NotEmpty(message = "类型名称不能为空")
    private String name;

    @ApiModelProperty(value = "图标")
    private String icon;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "排序")
    private Integer sort;

//    @ApiModelProperty(value = "是否默认：true默认，不可删除")
//    private Boolean isDefault;
//
//    @ApiModelProperty(value = "节点类型：TOP(顶级节点)、NO_CLASSIFY(待分类)")
//    private String type;

//    @ApiModelProperty(value = "租户ID")
//    private Integer tenantId;

//    @ApiModelProperty(value = "创建人名称")
//    private String createByName;
//
//    @ApiModelProperty(value = "编辑人名称")
//    private String modifyByName;


    public ContractorProjectEvaluateSheetType transferToEntity(ContractorProjectEvaluateSheetType target) {
        if (target == null) {
            target = new ContractorProjectEvaluateSheetType();
        }
        target.setId(this.getId());
        target.setParentId(this.getParentId());//父节点
        target.setName(this.getName());//类型名称
        target.setIcon(this.getIcon());//图标
        target.setRemark(this.getRemark());//备注
        target.setSort(this.getSort());//排序
//        target.setIsDefault(this.getIsDefault());//是否默认：true默认，不可删除
//        target.setType(this.getType());//节点类型：TOP(顶级节点)、NO_CLASSIFY(待分类)
//        target.setTenantId(this.getTenantId());//租户ID
//        target.setCreateByName(this.getCreateByName());//创建人名称
//        target.setModifyByName(this.getModifyByName());//编辑人名称
        return target;
    }
}
