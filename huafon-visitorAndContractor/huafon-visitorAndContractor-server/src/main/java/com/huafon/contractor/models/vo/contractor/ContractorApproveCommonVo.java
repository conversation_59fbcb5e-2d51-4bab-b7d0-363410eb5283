package com.huafon.contractor.models.vo.contractor;

import com.huafon.api.dto.v2.CheckUserDto;
import com.huafon.contractor.models.dto.AttachmentDTO;
import com.huafon.contractor.models.vo.type.ContractorTypeInfoVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
* <AUTHOR>
* @since 2024-11-26 10:09
*/
@Data
@ApiModel(value = "承包商审批任务列表")
public class ContractorApproveCommonVo implements Serializable {

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "承包商名称")
    private String name;

    @ApiModelProperty(value = "社会信用编码")
    private String socialCreditCode;

    @ApiModelProperty(value = "性质")
    private String nature;

    @ApiModelProperty(value = "承包商类型")
    private List<Long> contractorTypeId;

    @ApiModelProperty(value = "承包商类型信息")
    private List<ContractorTypeInfoVo> contractorTypeInfos;

    @ApiModelProperty(value = "营业执照信息")
    private List<AttachmentDTO> licenseFile;

    @ApiModelProperty(value = "其他资质信息")
    private List<AttachmentDTO> otherPropertyFile;

    @ApiModelProperty(value = "审批流ID")
    private String taskId;

    @ApiModelProperty(value = "状态：1流程中;2已完成;3已取消")
    private Integer status;

    @ApiModelProperty(value = "租户ID")
    private Integer tenantId;

    @ApiModelProperty(value = "创建人信息")
    private Long createBy;

    @ApiModelProperty(value = "创建人")
    private String createByName;

    @ApiModelProperty(value = "最近编辑人信息")
    private Long modifyBy;

    @ApiModelProperty(value = "编辑人名称")
    private String modifyByName;

    @ApiModelProperty(value = "流程名称")
    private String workflowName;

    @ApiModelProperty(value = "未操作人")
    private List<CheckUserDto> notOperating;
}
