package com.huafon.contractor.controller;

import com.huafon.admin.api.annotation.OperationLog;
import com.huafon.admin.api.enums.OperationLogType;
import com.huafon.common.utils.bean.BeanUtils;
import com.huafon.contractor.models.dto.MoveTreeDTO;
import com.huafon.contractor.models.dto.ViolationTermsCategoryPostDTO;
import com.huafon.contractor.models.entity.ContractorViolationTermsCategory;
import com.huafon.contractor.models.reqo.violationTerms.ReqViolationTerms;
import com.huafon.contractor.models.reqo.violationTerms.ReqViolationTermsAdd;
import com.huafon.contractor.models.reqo.violationTerms.ReqViolationTermsEdit;
import com.huafon.contractor.models.reqo.violationTerms.ReqViolationTermsListPage;
import com.huafon.contractor.models.vo.PageListResultVo;
import com.huafon.contractor.models.vo.TreeStatisticalVo;
import com.huafon.contractor.models.vo.violationTerms.ViolationTermsCategoryTreeVo;
import com.huafon.contractor.models.vo.violationTerms.ViolationTermsCategoryVo;
import com.huafon.contractor.models.vo.violationTerms.ViolationTermsVo;
import com.huafon.contractor.service.ContractorViolationTermsCategoryService;
import com.huafon.contractor.service.ViolationTermsService;
import com.huafon.support.core.pojo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Date 2022/4/12 13:28
 * 承包商违规条款配置
 */
@Api(tags = "违规条款配置")
@RestController
@RequestMapping("/violationterms")
public class ViolationTermsController {

    @Resource
    private ViolationTermsService violationTermsService;

    @Autowired
    private ContractorViolationTermsCategoryService termsCategoryService;

    @PostMapping("/add")
    @ApiOperation(value = "违规条款配置新增")
    @OperationLog(notes = "违规条款配置新增", type = OperationLogType.ADD)
    public Long add(@RequestBody @Valid ReqViolationTermsAdd reqViolationTermsAdd) {
        return violationTermsService.add(reqViolationTermsAdd);
    }

    @PostMapping("/edit")
    @ApiOperation(value = "违规条款配置编辑")
    @OperationLog(notes = "违规条款配置编辑", type = OperationLogType.MODITY)
    public Long edit(@RequestBody @Valid ReqViolationTermsEdit reqViolationTermsEdit) {
        return violationTermsService.edit(reqViolationTermsEdit);
    }

    @PostMapping("/del")
    @ApiOperation(value = "违规条款配置删除")
    @OperationLog(notes = "违规条款配置删除", type = OperationLogType.DELETE)
    public R del(@RequestBody @Valid ReqViolationTerms reqViolationTerms) {
        return violationTermsService.del(reqViolationTerms);
    }

    @PostMapping("/getlistbypage")
    @ApiOperation(value = "违规条款配置列表-分页")
    public R<PageListResultVo<ViolationTermsVo>> getListByPage(@RequestBody @Valid ReqViolationTermsListPage reqViolationTermsListPage) {
        return R.ok(violationTermsService.getListByPage(reqViolationTermsListPage));
    }

    @PostMapping("/getrow")
    @ApiOperation(value = "违规条款配置详情")
    public R<ViolationTermsVo> getRow(@RequestBody @Valid ReqViolationTerms reqViolationTerms) {
        return violationTermsService.getRow(reqViolationTerms);
    }

    @PostMapping("/category/create")
    @ApiOperation(value = "创建违规条款(类型)")
    @OperationLog(notes = "创建违规条款(类型)", type = OperationLogType.ADD)
    public R<Integer> createMarkType(@RequestBody @Validated ViolationTermsCategoryPostDTO create) {
        return R.ok(termsCategoryService.createContractorViolationTermsCategory(create));
    }

    @PostMapping("/category/edit")
    @ApiOperation(value = "编辑违规条款(类型)")
    @OperationLog(notes = "编辑违规条款(类型)", type = OperationLogType.MODITY)
    public R<Integer> updateMarkType(@RequestBody @Validated ViolationTermsCategoryPostDTO update) {
        return R.ok(        termsCategoryService.updateContractorViolationTermsCategory(update));
    }

    @DeleteMapping("/category/{id}")
    @ApiOperation(value = "删除违规条款(类型)")
    @OperationLog(notes = "删除违规条款(类型)", type = OperationLogType.MODITY)
    public R<Void> deleteMarkType(@PathVariable(value = "id") Integer id) {
        termsCategoryService.deleteContractorViolationTermsCategoryById(id);
        return R.ok();
    }

    @GetMapping("/category/{id}")
    @ApiOperation(value = "查询违规条款(类型)")
    public R<ViolationTermsCategoryVo> findMarkType(@PathVariable(value = "id") Integer id) {
        return R.ok(termsCategoryService.findContractorViolationTermsCategoryById(id));
    }

    @GetMapping("/category/tree")
    @ApiOperation(value = "违规条款树形(类型)")
    public R<ViolationTermsCategoryTreeVo> queryTree() {
        return R.ok(termsCategoryService.queryTree(null));
    }

    @GetMapping("/category/noClassify")
    @ApiOperation(value = "违规条款待分类(类型)")
    public R<ViolationTermsCategoryVo> queryNoClassify() {
        ContractorViolationTermsCategory source = termsCategoryService.getCategoryByType(ContractorViolationTermsCategoryService.NO_CLASSIFY);
        return R.ok(BeanUtils.convert(source, ViolationTermsCategoryVo.class));
    }

    @GetMapping("/category/statistics")
    @ApiOperation(value = "违规条款统计信息(类型)")
    public R<TreeStatisticalVo> statistical() {
        return R.ok(termsCategoryService.statistic());
    }


    @PostMapping("/category/move")
    @ApiOperation(value = "类型树移动")
    public R<Void> moveType(@RequestBody @Validated MoveTreeDTO moveTree) {
        termsCategoryService.moveTree(moveTree);
        return R.ok();
    }
}
