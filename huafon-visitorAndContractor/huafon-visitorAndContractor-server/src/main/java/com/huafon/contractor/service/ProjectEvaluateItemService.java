package com.huafon.contractor.service;


import com.huafon.contractor.models.entity.ContractorProjectEvaluateItem;
import com.huafon.contractor.models.vo.evaluate.ContractorProjectEvaluateItemVo;
import com.huafon.framework.mybatis.service.MybatisIService;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
* 承包商项目评价记录-明细
* <AUTHOR>
* @since 2024-06-24 17:33
*/
public interface ProjectEvaluateItemService extends MybatisIService<ContractorProjectEvaluateItem> {

    /**
    * 创建承包商项目评价记录-明细
    * @param create
    */
    void createContractorProjectEvaluateItem(Integer evaluateId, List<ContractorProjectEvaluateItemVo> create);

    /**
    * 更新承包商项目评价记录-明细
    * @param update
    */
    void updateContractorProjectEvaluateItem(Integer evaluateId, List<ContractorProjectEvaluateItemVo> update);

    /**
    * 删除承包商项目评价记录-明细
    * @param evaluateId
    */
    void deleteContractorProjectEvaluateItemByEvaluateId(Integer evaluateId);

    /**
    * 查询详情
    * @param evaluateId
    * @return
    */
    List<ContractorProjectEvaluateItemVo> queryContractorProjectEvaluateItemByEvaluateId(Integer evaluateId);

    /**
    * 查询映射：key:id value: entity
    * @param evaluateIds
    * @return
    */
    Map<Integer, List<ContractorProjectEvaluateItem>> queryContractorProjectEvaluateItemByEvaluateId(Collection<Integer> evaluateIds);

}
