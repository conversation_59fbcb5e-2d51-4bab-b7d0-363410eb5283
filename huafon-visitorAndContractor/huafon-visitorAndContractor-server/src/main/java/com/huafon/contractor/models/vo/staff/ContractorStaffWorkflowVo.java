package com.huafon.contractor.models.vo.staff;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* @since 2022-08-20 15:41
**/
@Data
@ApiModel("承包商人员工作流")
public class ContractorStaffWorkflowVo implements Serializable {

	private static final long serialVersionUID = 1138512636064211596L;

	@ApiModelProperty(value = "审批意见", dataType = "String")
	private String content;

	@ApiModelProperty(value = "审批人", dataType = "Integer")
	private Integer userId;

	@ApiModelProperty(value = "审批人姓名", dataType = "String")
	private String userName;

	@ApiModelProperty(value = "审批人联系方式", dataType = "String")
	private String mobile;

	@ApiModelProperty(value = "节点名称", dataType = "String")
	private String taskName;

	@ApiModelProperty(value = "按钮名称", dataType = "String")
	private String buttonName;

	@ApiModelProperty(value = "创建时间")
	@JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;

	@ApiModelProperty(value = "头像")
	private String avatar;
}
