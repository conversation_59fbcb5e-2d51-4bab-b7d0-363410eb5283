package com.huafon.contractor.models.reqo.type;

import com.huafon.contractor.models.entity.ContractorTypeCategory;
import com.huafon.contractor.models.vo.type.ContractorCategoryQualityVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Collection;

/**
* <AUTHOR>
* @since 2024-05-08 13:25
*/
@Data
@ApiModel(value = "承包商类型(分类)提交")
public class ReqTypeCategory implements Serializable {

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "父节点")
    private Integer parentId;

    @ApiModelProperty(value = "类型名称")
    private String name;

    @ApiModelProperty(value = "图标")
    private String icon;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "是否默认：true默认，不可删除")
    private Boolean isDefault;

    @ApiModelProperty(value = "节点类型：TOP(顶级节点)、NO_CLASSIFY(待分类)")
    private String type;

    @ApiModelProperty(value = "租户ID")
    private Integer tenantId;

    @ApiModelProperty(value = "资质信息")
    private Collection<ContractorCategoryQualityVo> qualityList;


    public ContractorTypeCategory transferToEntity(ContractorTypeCategory target) {
        if (target == null) {
            target = new ContractorTypeCategory();
        }
        target.setId(this.getId());
        target.setParentId(this.getParentId());//父节点
        target.setName(this.getName());//类型名称
        target.setIcon(this.getIcon());//图标
        target.setRemark(this.getRemark());//备注
        target.setSort(this.getSort());//排序
//        target.setIsDefault(this.getIsDefault());//是否默认：true默认，不可删除
//        target.setType(this.getType());//节点类型：TOP(顶级节点)、NO_CLASSIFY(待分类)
//        target.setTenantId(this.getTenantId());//租户ID
        return target;
    }
}
