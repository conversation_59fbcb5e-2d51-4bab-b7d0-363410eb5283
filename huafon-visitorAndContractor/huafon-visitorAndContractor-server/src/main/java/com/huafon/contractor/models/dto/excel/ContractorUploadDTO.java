package com.huafon.contractor.models.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.huafon.contractor.service.support.CustomRow;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/2/8 9:12
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class ContractorUploadDTO extends CustomRow {

    @ExcelProperty(index = 0, value = "承包商名称")
    @NotBlank(message = "名称不能为空")
    @Length(max = 150, message = "名称长度过长，阈值为150个文字")
    private String name;

    @ExcelProperty(index = 1, value = "社会信用代码")
    @NotBlank(message = "社会信用代码不能为空")
    @Pattern(regexp = "^([0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}|[1-9]\\d{14})$", message = "信用代码格式错误")
    private String socialCreditCode;

    @ExcelProperty(index = 2, value = "性质")
    @NotBlank(message = "性质不能为空")
    @Length(max = 150, message = "性质长度过长，阈值为150个文字")
    private String nature;

    @ExcelProperty(index = 3, value = "类型")
    @NotNull(message = "类型不能为空")
    @Length(max = 150, message = "类型长度过长，阈值为150个文字")
    private String contractorType;

    private List<Long> contractorTypeIds;
}
