package com.huafon.contractor.models.vo.staff;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/4/20 10:35
 */
@Data
public class StaffListAllVo {

    @ApiModelProperty(value = "人员id")
    private Long contractorStaffId;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "身份证")
    private String idCard;

    @ApiModelProperty(value = "号码")
    private String mobile;

    @ApiModelProperty(value = "是否特殊工种 1|是 0|否")
    private Integer isSpecialWork;

    @ApiModelProperty(value = "状态 1|未入厂 2|已入厂 3|已离厂 4|黑名单 5|待审批 6｜审核未通过")
    private Integer state;

    @ApiModelProperty(value = "承包商名称")
    private String contractorName;

    @ApiModelProperty(value = "承包商id")
    private Long contractorId;

    @ApiModelProperty(value = "照片")
    private String photo;

    @ApiModelProperty(value = "身份证-正面")
    private String idCardPhotoFront;

    @ApiModelProperty(value = "身份证-反面")
    private String idCardPhotoReverse;

    @ApiModelProperty(value = "证书列表")
    private List<StaffLicenseVo> licenseList;

    @ApiModelProperty(value = "审批意见")
    private String approveContent;

    @ApiModelProperty(value = "用户id")
    private Integer userId;


    @ApiModelProperty(value = "证件照类型字典code")
    private String idTypeCode;
    @ApiModelProperty(value = "证件照类型字典Name")
    private String idTypeName;

    @ApiModelProperty(value = "重新发起审核状态：0未发起；1已发起")
    private Integer reviewState;
}
