package com.huafon.contractor.models.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import lombok.Data;

/**
* 承包商项目评价表
* <AUTHOR>
* @since 2024-06-24 10:47
*/
@Data
@TableName("hf_contractor_project_evaluate_sheet")
public class ContractorProjectEvaluateSheet extends BaseEntity{

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
    * 评价表-分类ID
    */
    @TableField(value = "type_id")
    private Integer typeId;

    /**
    * 评价表编码
    */
    @TableField(value = "code")
    private String code;

    /**
    * 评价表名称
    */
    @TableField(value = "name")
    private String name;

    /**
    * 基础分
    */
    @TableField(value = "basic_score")
    private Double basicScore;

    /**
     * 是否允许多选：0不允许；1允许
     */
    @TableField(value = "multiple_standard")
    private Integer multipleStandard;

    /**
     * 评价时分值是否允许修改:0不允许；1允许
     */
    @TableField(value = "score_editable")
    private Integer scoreEditable;

    /**
    * 租户ID
    */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER)
    private Integer tenantId;

    /**
    * 创建人名称
    */
    @TableField(value = "create_by_name")
    private String createByName;

    /**
    * 编辑人名称
    */
    @TableField(value = "modify_by_name")
    private String modifyByName;


}
