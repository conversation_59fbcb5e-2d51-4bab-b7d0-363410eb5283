package com.huafon.contractor.models.vo.deposit;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 承包商押金标准VO
 */
@Data
@ApiModel(value = "ContractorDepositStandardVO", description = "承包商押金标准")
public class ContractorDepositStandardVO {
    
    @ApiModelProperty(value = "主键ID")
    private Long id;
    
    @ApiModelProperty(value = "类型（PRODUCTION-生产时期, CONSTRUCTION_LE_100-建设时期(M<=100万), CONSTRUCTION_GT_100-建设时期(M>100万)）")
    private String type;
    
    @ApiModelProperty(value = "类型名称")
    private String typeName;
    
    @ApiModelProperty(value = "周期最小值（天）")
    private Integer periodMin;
    
    @ApiModelProperty(value = "周期最大值（天）")
    private Integer periodMax;
    
    @ApiModelProperty(value = "金额最小值")
    private BigDecimal amountMin;
    
    @ApiModelProperty(value = "金额最大值")
    private BigDecimal amountMax;
    
    @ApiModelProperty(value = "备注")
    private String remark;
    
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime modifyTime;
    
    @ApiModelProperty(value = "明细列表")
    private List<ContractorDepositStandardDetailVO> detailList;
} 