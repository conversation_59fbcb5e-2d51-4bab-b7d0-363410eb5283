package com.huafon.contractor.models.vo.project;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* @since 2023-10-23 10:35
*/
@Data
@ApiModel(value = "承包商项目到期结束待办列表")
public class ContractorProjectFinishedTaskCommonVo implements Serializable {

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "承包商项目ID")
    private Long contractorProjectId;

    @ApiModelProperty(value = "承包商项目名称")
    private String contractorProjectName;

    @ApiModelProperty(value = "项目开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @ApiModelProperty(value = "项目结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    @ApiModelProperty(value = "项目申报时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;

    @ApiModelProperty(value = "承包商ID")
    private Integer contractorId;

    @ApiModelProperty(value = "承包商名称")
    private String contractorName;

    @ApiModelProperty(value = "承包商项目负责人")
    private String principal;

    @ApiModelProperty(value = "承包商项目负责人联系方式")
    private String principalMobile;

    @ApiModelProperty(value = "项目状态：2|审核中 4|施工中 5|已完结 6|已取消 7|已暂停")
    private Integer projectState;

}
