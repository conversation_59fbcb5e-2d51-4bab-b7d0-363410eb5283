package com.huafon.contractor.models.vo.project;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024-07-29 15:06
 **/
@Data
@ApiModel(value = "ContractorProjectSimpleVo", description = "承包商项目基础信息展示的VO")
public class ContractorProjectSimpleVo implements Serializable {

	@ApiModelProperty(value = "ID")
	private Long id;

	@ApiModelProperty(value = "承包商项目名称")
	private String name;

	@ApiModelProperty(value = "项目状态：2|审核中 4|施工中 5|已完结 6|已取消 7|已暂停")
	private Integer state;

	@ApiModelProperty(value = "项目对接人UserId")
	private Integer declareUserId;

	@ApiModelProperty(value = "对接人姓名")
	private String declareName;
}
