package com.huafon.contractor.dao.mapper;

import com.huafon.contractor.models.entity.ContractorViolationPhoto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ContractorViolationPhotoMapper {
    /**
     * 批量插入
     * @param list
     * @return
     */
    int insertBatch(@Param("list") List<ContractorViolationPhoto> list);

    /**
     * 根据id查询列表
     * @param contractorViolationId
     * @return
     */
    List<ContractorViolationPhoto> selectListByContractorViolationId(Long contractorViolationId);

    int deleteByContractorViolationId(Long contractorViolationId);
}