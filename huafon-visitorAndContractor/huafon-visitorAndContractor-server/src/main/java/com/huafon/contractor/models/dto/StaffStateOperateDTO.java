package com.huafon.contractor.models.dto;

import com.huafon.support.config.UserContext;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.Date;
import java.util.Objects;

import static com.huafon.contractor.models.constants.ProjectConstants.STAFF_STATE_THREE;
import static com.huafon.contractor.models.constants.ProjectConstants.STAFF_STATE_TWO;

/**
 * <AUTHOR>
 * @since 2023-10-10 16:13
 **/
@Data
@ApiModel(value = "操作承包商人员状态")
public class StaffStateOperateDTO {

	@ApiModelProperty(value = "承包商人员ID列表", required = true)
	@NotEmpty(message = "承包商人员ID列表不能为空")
	private Collection<Long> contractorStaffIds;

	@ApiModelProperty(value = "变更的状态: 1|未入场; 2|已入场; 3|已离场; 4|黑名单", required = true)
	@NotNull(message = "变更状态不能为空")
	@Range(min = 1, max = 4, message = "仅支持未入场; 已入场; 已离场; 黑名单")
	private Integer state;

	public void setState(Integer state) {
		this.modifyTime = new Date();
		this.modifyBy = UserContext.getId();
		this.state = state;
		if (Objects.nonNull(state)) {
			switch (state) {
				case STAFF_STATE_TWO:
					this.theFactoryTime = modifyTime;
					break;
				case STAFF_STATE_THREE:
					this.leaveFactoryTime = modifyTime;
					break;
				default:
			}
		}
	}

	@ApiModelProperty(value = "入场时间", hidden = true)
	private Date theFactoryTime;

	@ApiModelProperty(value = "离场时间", hidden = true)
	private Date leaveFactoryTime;

	@ApiModelProperty(value = "操作人", hidden = true)
	private Long modifyBy;

	@ApiModelProperty(value = "操作时间", hidden = true)
	private Date modifyTime;
}
