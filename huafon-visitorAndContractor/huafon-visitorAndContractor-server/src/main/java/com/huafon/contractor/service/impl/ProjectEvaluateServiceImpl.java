package com.huafon.contractor.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONValidator;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import com.huafon.api.dto.v2.DynamicDto;
import com.huafon.api.dto.v2.StartProcessResponseDTO;
import com.huafon.api.dto.v2.TaskDto;
import com.huafon.common.config.TenantContext;
import com.huafon.common.utils.bean.BeanUtils;
import com.huafon.contractor.dao.mapper.ContractorProjectEvaluateMapper;
import com.huafon.contractor.dao.mapper.ContractorProjectMapper;
import com.huafon.contractor.models.constants.ProcessConstants;
import com.huafon.contractor.models.constants.ProjectConstants;
import com.huafon.contractor.models.dto.*;
import com.huafon.contractor.models.entity.ContractorProject;
import com.huafon.contractor.models.entity.ContractorProjectEvaluate;
import com.huafon.contractor.models.entity.ContractorProjectEvaluateSheet;
import com.huafon.contractor.models.enums.CodeEnum;
import com.huafon.contractor.models.vo.evaluate.*;
import com.huafon.contractor.models.vo.project.ProcessCountVo;
import com.huafon.contractor.service.ProjectEvaluateItemService;
import com.huafon.contractor.service.ProjectEvaluateRuleService;
import com.huafon.contractor.service.ProjectEvaluateService;
import com.huafon.contractor.service.ProjectEvaluateSheetService;
import com.huafon.contractor.utils.CodeGenerateUtils;
import com.huafon.contractor.utils.WorkflowRpcUtil;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.framework.mybatis.service.MybatisServiceImpl;
import com.huafon.portal.api.dto.UserDto;
import com.huafon.safety.service.remote.DeptRemoteService;
import com.huafon.safety.service.remote.UserRemoteService;
import com.huafon.support.config.UserContext;
import com.huafon.support.dto.UserDeptPostDto;
import com.huafon.support.dto.UserInfoDto;
import com.huafon.support.dto.UserSimpleDto;
import com.huafon.support.exceptions.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DecimalFormat;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* 承包商项目评价记录
* <AUTHOR>
* @since 2024-06-24 17:33
*/
@Service
@Slf4j
@Transactional
public class ProjectEvaluateServiceImpl extends MybatisServiceImpl<ContractorProjectEvaluateMapper, ContractorProjectEvaluate>
        implements ProjectEvaluateService {
    @Autowired
    private UserRemoteService userRemoteService;
    @Autowired
    private CodeGenerateUtils codeGenerateUtils;
    @Autowired
    private ProjectEvaluateItemService evaluateItemService;
    @Autowired
    private ProjectEvaluateRuleService evaluateRuleService;
    @Autowired
    private ProjectEvaluateSheetService evaluateSheetService;

    @Autowired
    private DeptRemoteService deptRemoteService;

    @Autowired
    private WorkflowRpcUtil workflowRpcUtil;

    @Autowired
    private ContractorProjectMapper contractorProjectMapper;

    @Override
    public Integer createContractorProjectEvaluate(ContractorProjectEvaluatePostDTO create, boolean submit) {
        processFinalScore(create);
        ContractorProjectEvaluate target = create.transferToEntity(null);
        target.setStatus(ProcessConstants.IN_PROCESS);
        target.setCreate(UserContext.getId());
        target.setCreateByName(Optional.ofNullable(UserContext.get()).map(UserInfoDto::getName).orElse(""));
        target.setModifyByName(Optional.ofNullable(UserContext.get()).map(UserInfoDto::getName).orElse(""));
        target.setTenantId(TenantContext.getOne());
        target.setCode(codeGenerateUtils.createCodeFormat2(CodeEnum.CONTRACTOR_PROJECT_EVALUATE));
        this.save(target);
        evaluateItemService.createContractorProjectEvaluateItem(target.getId(), create.getContractorProjectEvaluateItems());
        startProcess(target, submit);

        return target.getId();
    }

    private void startProcess(ContractorProjectEvaluate source, boolean completeFirst) {
        if (Objects.isNull(source)) {
            return;
        }

        try{
            List<DynamicDto> dynamic = dynamic(source);
            Integer businessId = source.getId();
            String businessName = businessName(source);
            StartProcessResponseDTO startResult = workflowRpcUtil.startProcessV2(ProjectConstants.CONTRACTOR_PROJECT_EVALUATE, new HashMap<>(),
                    businessId, businessName, dynamic, completeFirst);
            if (startResult != null && Strings.isNotBlank(startResult.getWorkflowId())) {
                String workflowId = startResult.getWorkflowId();
                source.setStatus(ProcessConstants.IN_PROCESS);
                if (startResult.getIsFinish()) {
                    source.setStatus(ProcessConstants.FINISH_PROCESS);
                }
                source.setTaskId(workflowId);
                this.updateById(source);
            }
        } catch(Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    private String businessName(ContractorProjectEvaluate source) {
        String contractorProjectName = source.getContractorProjectName();

        String projectName = Strings.isEmpty(source.getContractorProjectName()) ? "-" : contractorProjectName;
        Double finalScore = source.getFinalScore();
        String finalScoreStr = "-";
        if (finalScore != null) {
            if ((finalScore % 1) == 0) {
                finalScoreStr = new DecimalFormat("#").format(finalScore);
            } else {
                finalScoreStr = new DecimalFormat("#.##").format(finalScore);
            }
        }

        return String.format("%s【%s分】", projectName , finalScoreStr);
    }

    private List<DynamicDto> dynamic(ContractorProjectEvaluate source) {
        List<DynamicDto> dynamic = new ArrayList<>();
        Integer contractorProjectId = source.getContractorProjectId();
        if (Objects.nonNull(contractorProjectId)) {
            ContractorProject contractorProject = contractorProjectMapper.selectByProjectId(contractorProjectId.longValue(), true);
            if (Objects.isNull(contractorProject)) {
                throw new ServiceException("操作失败，选择的承包商项目不存在");
            }

            //项目对接人
            Integer declareUserId = contractorProject.getDeclareUserId();
            if (declareUserId != null) {
                DynamicDto dynamicDto = new DynamicDto();
                dynamicDto.setField("declareUserId");
                dynamicDto.setUserId(declareUserId);
                dynamicDto.setDepartmentId(contractorProject.getDeclareDepartmentId());
                dynamic.add(dynamicDto);
            }

            //项目抄送相关部门/区域负责人
            String carbonCopy = contractorProject.getCarbonCopy();
            if (Strings.isNotBlank(carbonCopy) && JSONValidator.from(carbonCopy).validate()) {
                List<UserSimpleDto> carbonCopyUserInfos = JSONArray.parseArray(carbonCopy, UserSimpleDto.class);
                List<Integer> carbonCopyUserIds = carbonCopyUserInfos.stream().map(UserSimpleDto::getUserId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(carbonCopyUserIds)) {
                    List<UserDto> userInfos = userRemoteService.getByIds(carbonCopyUserIds);
                    Map<Integer, UserDto> userMappings = userInfos.stream().collect(Collectors.toMap(UserDto::getUserId, Function.identity(), (a, b) -> b));
                    for (Integer carbonCopyUserId : carbonCopyUserIds) {
                        UserDto userDto = userMappings.get(carbonCopyUserId);
                        if (Objects.nonNull(userDto) && !org.springframework.util.CollectionUtils.isEmpty(userDto.getDepartmentIds())) {
                            for (Integer departmentId : userDto.getDepartmentIds()) {
                                DynamicDto dynamicDto = new DynamicDto();
                                dynamicDto.setField("carbonUserId");
                                dynamicDto.setUserId(carbonCopyUserId);
                                dynamicDto.setDepartmentId(departmentId);
                                dynamic.add(dynamicDto);
                            }
                        } else {
                            DynamicDto dynamicDto = new DynamicDto();
                            dynamicDto.setField("carbonUserId");
                            dynamicDto.setUserId(carbonCopyUserId);
                            dynamic.add(dynamicDto);
                        }
                    }
                }
            }

            //项目对接人部门
            Integer declareDepartmentId = contractorProject.getDeclareDepartmentId();
            if (declareDepartmentId != null) {
                DynamicDto dynamicDto = new DynamicDto();
                dynamicDto.setField("declareDepartmentId");
                dynamicDto.setDepartmentId(declareDepartmentId);
                dynamic.add(dynamicDto);
            }

            //施工所在部门
            Integer constructDepartmentId = contractorProject.getConstructDepartmentId();
            if (constructDepartmentId != null) {
                DynamicDto dynamicDto = new DynamicDto();
                dynamicDto.setField("constructDepartmentId");
                dynamicDto.setDepartmentId(constructDepartmentId);
                dynamic.add(dynamicDto);
            }

            //申报部门
            Integer createByDepartmentId = contractorProject.getCreateByDepartmentId();
            if (createByDepartmentId != null) {
                DynamicDto dynamicDto = new DynamicDto();
                dynamicDto.setField("createByDepartmentId");
                dynamicDto.setDepartmentId(createByDepartmentId);
                dynamic.add(dynamicDto);
            }
        }

        //评价人
        Integer evaluateUserId = source.getEvaluateUserId();
        if (evaluateUserId != null) {
            DynamicDto evaluateUser = new DynamicDto();
            evaluateUser.setField("evaluateUserId");
            evaluateUser.setUserId(evaluateUserId);
            evaluateUser.setDepartmentId(source.getEvaluateDeptId());
            dynamic.add(evaluateUser);
        }

        //评价部门
        Integer evaluateDeptId = source.getEvaluateDeptId();
        if (evaluateDeptId != null) {
            DynamicDto evaluateDept = new DynamicDto();
            evaluateDept.setField("evaluateDeptId");
            evaluateDept.setDepartmentId(evaluateDeptId);
            dynamic.add(evaluateDept);
        }

        return dynamic;
    }

    private void processFinalScore(ContractorProjectEvaluatePostDTO source) {
        if (source == null) {
            return;
        }
        ContractorProjectEvaluateSheet evaluateSheet = evaluateSheetService.getById(source.getSheetId());
        if (evaluateSheet == null) {
            log.info("相关评价表信息不存在");
            return;
        }

        source.setSheetName(evaluateSheet.getName());
        source.setBaseScore(evaluateSheet.getBasicScore());
        source.setMultipleStandard(evaluateSheet.getMultipleStandard());
        source.setScoreEditable(evaluateSheet.getScoreEditable());
        if (Objects.equals(source.getMultipleStandard(), 0)) {
            Map<Integer, List<ContractorProjectEvaluateItemVo>> mappings = Optional.ofNullable(source.getContractorProjectEvaluateItems()).orElse(Collections.emptyList())
                    .stream()
                    .collect(Collectors.groupingBy(ContractorProjectEvaluateItemVo::getEvaluateStandardTypeId));

            Set<String> typeName = new HashSet<>();
            for (Map.Entry<Integer, List<ContractorProjectEvaluateItemVo>> mapping : mappings.entrySet()) {
                List<ContractorProjectEvaluateItemVo> selectedList = mapping.getValue().stream().filter(ContractorProjectEvaluateItemVo::getIsReference).collect(Collectors.toList());
                if (selectedList.size() > 1) {
                    ContractorProjectEvaluateItemVo item = selectedList.get(0);
                    typeName.add(item.getEvaluateStandardTypeName());
                }
            }
            if (!CollectionUtils.isEmpty(typeName)) {
                throw new ServiceException(Joiner.on("、").join(typeName) + " 存在多次选择");
            }
        }
        EvaluateResultDTO compute = evaluateRuleService.compute(source.getContractorProjectEvaluateItems(), evaluateSheet.getBasicScore());
        if (compute != null) {
            source.setEvaluateRuleId(compute.getRuleId());
            source.setEvaluateResult(compute.getResult());
            source.setFinalScore(compute.getScore());
            source.setRules(compute.getRules());
        }
    }

    @Override
    public Integer temporaryContractorProjectEvaluate(ContractorProjectEvaluatePostDTO source) {
        if (Objects.isNull(source.getId())) {
            return this.createContractorProjectEvaluate(source, false);
        } else {
            ContractorProjectEvaluate target = this.updateContractorProjectEvaluate(source);
            workflowRpcUtil.updateDynamicField(target.getTaskId(), dynamic(target), businessName(target), new HashMap<>());
            return target.getId();
        }
    }

    @Override
    public Integer syncContractorProjectEvaluate(ContractorProjectEvaluatePostDTO create, boolean submit, UserInfoDto userInfo) {
        processFinalScore(create);
        ContractorProjectEvaluate target = create.transferToEntity(null);
        target.setStatus(ProcessConstants.IN_PROCESS);
        target.setCreate(UserContext.getId());
        target.setCreateByName(Optional.ofNullable(UserContext.get()).map(UserInfoDto::getName).orElse(""));
        target.setModifyByName(Optional.ofNullable(UserContext.get()).map(UserInfoDto::getName).orElse(""));
        target.setTenantId(TenantContext.getOne());
        target.setCode(codeGenerateUtils.createCodeFormat2(CodeEnum.CONTRACTOR_PROJECT_EVALUATE));
        this.save(target);
        evaluateItemService.createContractorProjectEvaluateItem(target.getId(), create.getContractorProjectEvaluateItems());

        try{
            List<DynamicDto> dynamic = dynamic(target);
            Integer businessId = target.getId();
            String businessName = businessName(target);
            StartProcessResponseDTO startResult = workflowRpcUtil.startProcessV2TargetUser(ProjectConstants.CONTRACTOR_PROJECT_EVALUATE, new HashMap<>(),
                    businessId, businessName, dynamic, submit, userInfo);
            if (startResult != null && Strings.isNotBlank(startResult.getWorkflowId())) {
                String workflowId = startResult.getWorkflowId();
                target.setStatus(ProcessConstants.IN_PROCESS);
                if (startResult.getIsFinish()) {
                    target.setStatus(ProcessConstants.FINISH_PROCESS);
                }
                target.setTaskId(workflowId);
                this.updateById(target);
            }
        } catch(Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }

        return target.getId();
    }

    @Override
    public void syncEvaluate(ContractorProject project) {
        if (Objects.isNull(project)) {
            return;
        }
        UserInfoDto createUserInfo = new UserInfoDto();
        createUserInfo.setUserId(Optional.ofNullable(project.getDeclareUserId()).orElse(0).longValue());
        createUserInfo.setName(project.getDeclareName());
        List<UserDeptPostDto> deptPostList = new ArrayList<>();
        UserDeptPostDto item = new UserDeptPostDto();
        item.setDeptId(project.getDeclareDepartmentId());
        item.setDeptName(project.getDeclareDepartment());
        deptPostList.add(item);
        createUserInfo.setDeptPostList(deptPostList);

        ContractorProjectEvaluatePostDTO origin = ContractorProjectEvaluatePostDTO.builder()
                .evaluateUserId(project.getDeclareUserId())
                .evaluateUserName(project.getDeclareName())
                .evaluateDeptId(project.getDeclareDepartmentId())
                .evaluateDeptName(project.getDeclareDepartment())
                .contractorProjectId(project.getContractorProjectId().intValue())
                .contractorProjectName(project.getName())
                .contractorId(Optional.ofNullable(project.getContractorId()).orElse(0L).intValue())
                .contractorName(project.getContractorName())
                .projectStartTime(project.getStartTime())
                .projectEndTime(project.getEndTime())
                .projectFinishedTime(project.getFinishedTime())
                .build();

        List<ContractorProjectEvaluateSheetVo> sheetInfos = evaluateSheetService.queryTenantAllEvaluateSheet(null);
        if (!CollectionUtils.isEmpty(sheetInfos) && sheetInfos.size() == 1) {
            origin.setSheetId(sheetInfos.get(0).getId());
            origin.setSheetName(sheetInfos.get(0).getName());
            origin.setMultipleStandard(sheetInfos.get(0).getMultipleStandard());
            origin.setScoreEditable(sheetInfos.get(0).getScoreEditable());
            List<ContractorProjectEvaluateItemVo> items = Optional.ofNullable(sheetInfos.get(0).getStandardInfos()).orElse(Collections.emptyList()).stream()
                    .map(ContractorProjectEvaluateItemVo::convert)
                    .collect(Collectors.toList());
            origin.setContractorProjectEvaluateItems(items);
        }
        this.syncContractorProjectEvaluate(origin, false, createUserInfo);
    }

    @Override
    public ContractorProjectEvaluate updateContractorProjectEvaluate(ContractorProjectEvaluatePostDTO update) {
        processFinalScore(update);
        ContractorProjectEvaluate contractorProjectEvaluate = this.getById(update.getId());
        if (Objects.isNull(contractorProjectEvaluate)) {
            throw new ServiceException("更新失败，承包商项目评价记录不存在");
        }
        ContractorProjectEvaluate target = update.transferToEntity(contractorProjectEvaluate);
        target.setModify(UserContext.getId());
        target.setModifyByName(Optional.ofNullable(UserContext.get()).map(UserInfoDto::getName).orElse(""));
        this.updateById(target);
        evaluateItemService.updateContractorProjectEvaluateItem(target.getId(), update.getContractorProjectEvaluateItems());

        return target;
    }

    @Override
    public void check(ProcessCheckDTO<ContractorProjectEvaluatePostDTO> check) {
        ContractorProjectEvaluate source = this.updateContractorProjectEvaluate(check.getData());
        if (Objects.isNull(source)) {
            throw new ServiceException("操作失败，数据不存在");
        }
        try{
            List<DynamicDto> dynamic = dynamic(source);
            String businessName = businessName(source);
            List<String> complete = workflowRpcUtil.complete(source.getTaskId(), new HashMap<>(), check.getContent(), dynamic,
                    businessName, check.getCheck(), check.getSign(), check.getWorkflowNodeId(), check.getRetreatWorkflowNodeId());
            if (CollectionUtils.isEmpty(complete)) {
                source.setStatus(ProcessConstants.FINISH_PROCESS);
                this.updateById(source);
            }
        } catch(Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public void refuse(ProcessRefuseDTO refuse) {
        ContractorProjectEvaluate source = this.getById(refuse.getId());
        if (Objects.isNull(source)) {
            throw new ServiceException("相关记录不存在，请刷新后重试");
        }
        source.setStatus(ProcessConstants.CANCEL_PROCESS);
        this.updateById(source);
        workflowRpcUtil.finishProcess(source.getTaskId(), refuse.getContent(), refuse.getSign());
    }

    @Override
    public void deleteContractorProjectEvaluateById(Integer id) {
        ContractorProjectEvaluate target = this.getById(id);
        if (Objects.isNull(target)) {
            throw new ServiceException("删除失败，承包商项目评价记录不存在");
        }
        target.setModify(UserContext.getId());
        target.setModifyByName(Optional.ofNullable(UserContext.get()).map(UserInfoDto::getName).orElse(""));
        this.updateById(target);
        this.removeById(id);
        evaluateItemService.deleteContractorProjectEvaluateItemByEvaluateId(target.getId());

        if (Objects.nonNull(target.getTaskId()) && Objects.equals(target.getStatus(), ProcessConstants.IN_PROCESS)) {
            workflowRpcUtil.finishMultipleProcess(Collections.singletonList(target.getTaskId()), "删除评价记录", null);
        }
    }

    @Override
    public void batchDeleteContractorProjectEvaluateByIds(BatchDeleteDTO source) {
        if (CollectionUtils.isEmpty(source.getIds())) {
            return;
        }
        List<Integer> deleteIds = source.getIds().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deleteIds)) {
            return ;
        }
        this.lambdaUpdate().in(ContractorProjectEvaluate::getId, deleteIds)
                .set(ContractorProjectEvaluate::getModifyBy, UserContext.getId())
                .set(ContractorProjectEvaluate::getModifyByName, Optional.ofNullable(UserContext.get()).map(UserInfoDto::getName).orElse(""))
                .update();
        this.removeByIds(deleteIds);
    }

    @Override
    public ContractorProjectEvaluateVo findContractorProjectEvaluateById(Integer id) {
        ContractorProjectEvaluate contractorProjectEvaluate = this.getById(id);
        if (Objects.isNull(contractorProjectEvaluate)) {
            throw new ServiceException("承包商项目评价记录不存在");
        }
        ContractorProjectEvaluateVo target = BeanUtils.convert(contractorProjectEvaluate, ContractorProjectEvaluateVo.class);

        target.setContractorProjectEvaluateItems(evaluateItemService.queryContractorProjectEvaluateItemByEvaluateId(target.getId()));

        Integer userId = Optional.ofNullable(contractorProjectEvaluate.getModifyBy()).map(Long::intValue).orElse(-1);
        UserDto userDto = userRemoteService.queryByUserId(userId);
        if (Objects.nonNull(userDto)) {
            target.setModifyBy(userId);
            target.setModifyByName(userDto.getName());
        }
        return target;
    }

    @Override
    public CommonPage<ContractorProjectEvaluateCommonVo> commonPage(ContractorProjectEvaluatePageQuery query) {
        query.setTenantId(TenantContext.getOne());
        if (Objects.nonNull(query.getEvaluateDeptId())) {
            List<Integer> allDeptIds = deptRemoteService.getChildNodeIdsById(query.getEvaluateDeptId());
            query.setFilterDeptIds(allDeptIds);
        }

        Page<ContractorProjectEvaluateCommonVo> page = new Page<>(query.getPageNo(), query.getPageSize());
        IPage<ContractorProjectEvaluateCommonVo> pageResult = this.baseMapper.queryByPage(page, query);
        List<ContractorProjectEvaluateCommonVo> items = pageResult.getRecords();

        if (!CollectionUtils.isEmpty(items)) {
            List<String> allTaskIds = items.stream().map(ContractorProjectEvaluateCommonVo::getTaskId).filter(Strings::isNotBlank).distinct().collect(Collectors.toList());
            Map<String, TaskDto> processInfoMap = workflowRpcUtil.getCheckProcessListByIds(allTaskIds);

            for (ContractorProjectEvaluateCommonVo item : items) {
                TaskDto taskDto = processInfoMap.get(item.getTaskId());
                if (taskDto != null) {
                    item.setWorkflowName(taskDto.getName());
                    item.setNotOperating(taskDto.getCheckUserList());
                } else {
                    if (Objects.equals(item.getStatus(), 2)) {
                        item.setWorkflowName("已完成");
                    } else if (Objects.equals(item.getStatus(), 3)) {
                        item.setWorkflowName("已取消");
                    }
                }
            }
        }
        return new CommonPage<>(pageResult);
    }

    @Override
    public ProcessCountVo processListCount() {
        ProcessCountVo result = new ProcessCountVo();
        List<ContractorProjectEvaluate> list = this.lambdaQuery().eq(ContractorProjectEvaluate::getTenantId, TenantContext.getOne()).list();
        if (!CollectionUtils.isEmpty(list)) {
            long inCount = list.stream().filter(e -> Objects.equals(e.getStatus(), ProcessConstants.IN_PROCESS)).count();
            long finishCount = list.stream().filter(e -> Objects.equals(e.getStatus(), ProcessConstants.FINISH_PROCESS)).count();
            long cancelCount = list.stream().filter(e -> Objects.equals(e.getStatus(), ProcessConstants.CANCEL_PROCESS)).count();

            result.setAll(list.size());
            result.setProcess((int) inCount);
            result.setFinished((int) finishCount);
            result.setCancel((int) cancelCount);
        }

        return result;
    }

    @Override
    public CommonPage<EvaluateAnalyzeCommonVo> analyzeCommonPage(EvaluateAnalyzePageQuery query) {
        query.setTenantId(TenantContext.getOne());
        query.setStatus(ProcessConstants.FINISH_PROCESS);
        Page<EvaluateAnalyzeCommonVo> page = new Page<>(query.getPageNo(), query.getPageSize());
        List<EvaluateAnalyzeCommonVo> items = this.baseMapper.queryAnalyzeByPage(query);
        if (!CollectionUtils.isEmpty(items)) {
            List<Integer> allEvaluateIds = items.stream().flatMap(e -> e.getReferenceEvaluateIds().stream()).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            List<ContractorProjectEvaluate> contractorProjectEvaluates = this.listByIds(allEvaluateIds);
            Map<Integer, ContractorProjectEvaluate> evaluateMappings = contractorProjectEvaluates.stream().collect(Collectors.toMap(ContractorProjectEvaluate::getId, Function.identity(), (a, b) -> b));

            DecimalFormat df = new DecimalFormat("#.##");
            for (EvaluateAnalyzeCommonVo item : items) {
                List<Integer> referenceEvaluateIds = Optional.ofNullable(item.getReferenceEvaluateIds()).orElse(Collections.emptyList());
                List<ContractorProjectEvaluate> evaluates = evaluateMappings.entrySet().stream().filter(e -> referenceEvaluateIds.contains(e.getKey())).map(Map.Entry::getValue).collect(Collectors.toList());
                List<EvaluateAnalyzeCommonItemVo> analyzeItems = new ArrayList<>();
                if (!CollectionUtils.isEmpty(evaluates)) {
                    Optional<ContractorProjectEvaluate> contractorMax = evaluates.stream().max(Comparator.comparing(ContractorProjectEvaluate::getCreateTime));
                    ContractorProjectEvaluate contractorEvaluate = contractorMax.orElse(evaluates.get(evaluates.size() - 1));
                    item.setContractorName(contractorEvaluate.getContractorName());//承包商名称
                    Map<Integer, List<ContractorProjectEvaluate>> projectEvaluateMappings = evaluates.stream().collect(Collectors.groupingBy(ContractorProjectEvaluate::getContractorProjectId));

                    Map<YearMonth, double[]> contractorYearTotalMappings = new HashMap<>();
                    for (Map.Entry<Integer, List<ContractorProjectEvaluate>> projectEvaluateMapping : projectEvaluateMappings.entrySet()) {
                        List<ContractorProjectEvaluate> values = projectEvaluateMapping.getValue();
                        Optional<ContractorProjectEvaluate> max = values.stream().max(Comparator.comparing(ContractorProjectEvaluate::getCreateTime));

                        if (!CollectionUtils.isEmpty(values)) {
                            ContractorProjectEvaluate contractorProjectEvaluate = max.orElse(values.get(values.size() - 1));
                            EvaluateAnalyzeCommonItemVo analyzeCommonItem = new EvaluateAnalyzeCommonItemVo();
                            analyzeCommonItem.setContractorProjectId(projectEvaluateMapping.getKey());
                            analyzeCommonItem.setContractorProjectName(contractorProjectEvaluate.getContractorProjectName());//承包商项目名称

                            Map<YearMonth, List<ContractorProjectEvaluate>> yearMonthMappings = values.stream()
                                    .collect(Collectors.groupingBy(e -> YearMonth.from(e.getEvaluateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())));

                            YearMonth startYearMonth = query.getStartYearAndMonth();
                            YearMonth endYearAndMonth = query.getEndYearAndMonth();
                            double totalScore = 0D; int size = 0;//用于项目的合计
                            while(startYearMonth.compareTo(endYearAndMonth) <= 0) {
                                List<ContractorProjectEvaluate> evaluateList = yearMonthMappings.getOrDefault(startYearMonth, Collections.emptyList());
                                if (!CollectionUtils.isEmpty(evaluateList)) {
                                    Double allFinalScore = evaluateList.stream().map(ContractorProjectEvaluate::getFinalScore).filter(Objects::nonNull).reduce(0D, Double::sum);
                                    Double finalScore = allFinalScore / evaluateList.size();
                                    totalScore += finalScore;
                                    size++;
                                    analyzeCommonItem.setAdditionalProperty(startYearMonth.toString(), df.format(finalScore));

                                    double[] yearTotalMap = contractorYearTotalMappings.get(startYearMonth);
                                    if (yearTotalMap == null) {
                                        yearTotalMap = new double[2];
                                    }
                                    yearTotalMap[0] = yearTotalMap[0] + finalScore;
                                    yearTotalMap[1] = yearTotalMap[1] + 1;
                                    contractorYearTotalMappings.put(startYearMonth, yearTotalMap);
                                } else {
                                    analyzeCommonItem.setAdditionalProperty(startYearMonth.toString(), "-");
                                }
                                startYearMonth = startYearMonth.plusMonths(1L);
                            }
                            double total = size == 0 ? 0D : totalScore / size;
                            analyzeCommonItem.setTotal(total == 0D ? "0" : df.format(total));
                            EvaluateResultDTO evaluateResult = evaluateRuleService.compute(total);
                            if (Objects.nonNull(evaluateResult)) {
                                analyzeCommonItem.setEvaluateResult(evaluateResult.getResult());
                            }
                            analyzeItems.add(analyzeCommonItem);
                        }
                    }

                    //承包商的合计
                    EvaluateAnalyzeCommonItemVo analyzeCommonItem = new EvaluateAnalyzeCommonItemVo();
                    analyzeCommonItem.setContractorProjectName("合计");
                    YearMonth startYearMonth = query.getStartYearAndMonth();
                    YearMonth endYearAndMonth = query.getEndYearAndMonth();
                    double totalScore = 0D; double size = 0D;//用于项目的合计
                    while(startYearMonth.compareTo(endYearAndMonth) <= 0) {
                        double[] yearTotalMap = contractorYearTotalMappings.get(startYearMonth);
                        if (yearTotalMap != null) {
                            double finalScore = yearTotalMap[0];
                            double yearSize = yearTotalMap[1];
                            double score = finalScore / yearSize;
                            totalScore = totalScore + score;
                            size++;
                            analyzeCommonItem.setAdditionalProperty(startYearMonth.toString(), df.format(score));
                        } else {
                            analyzeCommonItem.setAdditionalProperty(startYearMonth.toString(), "-");
                        }
                        startYearMonth = startYearMonth.plusMonths(1L);
                    }
                    Double total = size == 0 ? 0D : totalScore / size;
                    analyzeCommonItem.setTotal(total == 0D ? "0" : df.format(total));
                    EvaluateResultDTO evaluateResult = evaluateRuleService.compute(total);
                    if (Objects.nonNull(evaluateResult)) {
                        analyzeCommonItem.setEvaluateResult(evaluateResult.getResult());
                    }
                    analyzeItems.add(analyzeCommonItem);
                }
                item.setItems(analyzeItems);
            }
        }

        if (Strings.isNotBlank(query.getEvaluateResult())) {
            items = items.stream().filter(x -> {
                Optional<EvaluateAnalyzeCommonItemVo> totalInfo = x.getItems().stream().filter(e -> Objects.equals(e.getContractorProjectName(), "合计")).findFirst();

                return totalInfo.isPresent() && Objects.equals(totalInfo.get().getEvaluateResult(), query.getEvaluateResult());
            }).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(query.getOrders())) {
            items.sort(Comparator.comparing(EvaluateAnalyzeCommonVo::getContractorId));
        } else {
            for (OrderItem order : query.getOrders()) {
                if (Objects.equals(order.getColumn(), "total")) {
                    items.sort((a, b) -> {
                        Optional<EvaluateAnalyzeCommonItemVo> aTotal = a.getItems().stream().filter(x -> Objects.equals(x.getContractorProjectName(), "合计")).findFirst();
                        Optional<EvaluateAnalyzeCommonItemVo> bTotal = b.getItems().stream().filter(x -> Objects.equals(x.getContractorProjectName(), "合计")).findFirst();

                        double aTotalVal = aTotal.map(x -> Optional.ofNullable(x.getTotal()).map(Double::valueOf).orElse(0D)).orElse(0D);
                        double bTotalVal = bTotal.map(x -> Optional.ofNullable(x.getTotal()).map(Double::valueOf).orElse(0D)).orElse(0D);

                        if (order.isAsc()) {
                            return Double.compare(aTotalVal, bTotalVal);
                        } else {
                            return Double.compare(bTotalVal, aTotalVal);
                        }
                    });
                }
            }
        }
        CommonPage<EvaluateAnalyzeCommonVo> result = new CommonPage<>(new Page<>(query.getPageNo(), query.getPageSize(), items.size()));
        result.setList(items.stream().skip((long) (query.getPageNo() - 1) * query.getPageSize()).limit(query.getPageSize()).collect(Collectors.toList()));
        return result;
    }

}
