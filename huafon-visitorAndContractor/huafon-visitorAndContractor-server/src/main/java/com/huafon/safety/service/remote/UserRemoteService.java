package com.huafon.safety.service.remote;

import com.huafon.portal.api.dto.UserDto;
import com.huafon.portal.api.dto.query.UserQuery;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023-08-02 10:28
 **/
public interface UserRemoteService {

	/**
	 * 包含租户信息
	 * @param userId
	 * @return
	 */
	UserDto queryByUserId(Integer userId);

	UserDto queryByUserId(Integer userId, Integer tenantId);

	/**
	 * 包含租户信息
	 * @param userIds
	 * @return
	 */
	List<UserDto> getByUserIds(Collection<Integer> userIds, Integer tenantId);

	/**
	 * 通过用户ID查询用户信息（租户ID）
	 * @param userIds 用户ID列表
	 * @return 用户信息
	 */
	List<UserDto> getByIds(List<Integer> userIds);

	/**
	 * 查基础信息
	 * @param userIds
	 * @return
	 */
	List<UserDto> getByUserIds(Collection<Integer> userIds);

	/**
	 * 获取映射
	 * @param userIds
	 * @return
	 */
	Map<Integer, String> getMappings(List<Integer> userIds);

	/**
	 * 通过部门、岗位、名称模糊搜索userId
	 * @return userId列表
	 */
	List<Integer> queryByDeptAndPostAndNameLike(UserQuery query);

	/**
	 * 获取映射
	 * @param userIds
	 * @return
	 */
	Map<Integer, UserDto> getUserMapForUserId(List<Integer> userIds);

	/**
	 * 查询领导
	 * @param userId
	 * @param tenantId
	 * @param levels
	 * @return
	 */
	Collection<UserDto> getByLevels(Integer userId, Integer tenantId, Collection<String> levels);

	/**
	 * 通过UserID获取账户基础信息
	 * @param userId
	 * @return 只返回基础信息：不包含和租户关联的信息
	 */
	UserDto getById(Integer userId);
}
