package com.huafon.safety.models.dto.message;

import com.alibaba.fastjson.annotation.JSONType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-04-20 17:01
 **/
@Data
@JSONType(typeName = "USER")
@ApiModel(value = "用户类型")
public class ReminderUser implements ReminderType {
	@ApiModelProperty(value = "用户ID")
	private Integer userId;
}
