package com.huafon.safety.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huafon.common.config.TenantContext;
import com.huafon.common.utils.bean.BeanUtils;
import com.huafon.contractor.dao.mapper.ContractorProjectMapper;
import com.huafon.contractor.models.constants.ProjectConstants;
import com.huafon.contractor.models.entity.ContractorProject;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.portal.api.dto.UserDto;
import com.huafon.safety.dao.mapper.TechnologyInformMapper;
import com.huafon.safety.dao.mapper.TechnologyInformTemplateMapper;
import com.huafon.safety.models.dto.IdAndNameDTO;
import com.huafon.safety.models.dto.SignatureInfoUserDTO;
import com.huafon.safety.models.dto.TechnologyInformDownloadDTO;
import com.huafon.safety.models.dto.TechnologyInformPostDTO;
import com.huafon.safety.models.entity.TechnologyInform;
import com.huafon.safety.models.entity.TechnologyInformTemplate;
import com.huafon.safety.models.entity.TechnologyInformUser;
import com.huafon.safety.models.enums.TechnologyInformStateEnum;
import com.huafon.safety.models.query.TechnologyInformPageQuery;
import com.huafon.safety.models.vo.TechnologyCountVo;
import com.huafon.safety.models.vo.TechnologyInformCommonVo;
import com.huafon.safety.models.vo.TechnologyInformStaffVo;
import com.huafon.safety.models.vo.TechnologyInformVo;
import com.huafon.safety.service.*;
import com.huafon.safety.service.remote.DeptRemoteService;
import com.huafon.safety.service.remote.UserRemoteService;
import com.huafon.safety.utils.RandomStrUtil;
import com.huafon.safety.utils.TransferUtils;
import com.huafon.support.config.UserContext;
import com.huafon.support.exceptions.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
* 技术交底清单
* <AUTHOR>
* @since 2023-08-03 20:09
*/
@Service
@Slf4j
@Transactional
public class TechnologyInformServiceImpl implements TechnologyInformService {
    @Autowired
    private TechnologyInformMapper technologyInformMapper;
    @Autowired
    private UserRemoteService userRemoteService;
    @Autowired
    private DeptRemoteService deptRemoteService;
    @Autowired
    private TechnologyInformSignatureService technologyInformSignatureService;
    @Autowired
    private TechnologyInformContentService technologyInformContentService;
    @Autowired
    private TechnologyInformStaffService technologyInformStaffService;
    @Autowired
    private TechnologyInformUserService technologyInformUserService;
    @Autowired
    private ContractorProjectMapper contractorProjectMapper;

    @Autowired
    private TechnologyInformTemplateMapper templateMapper;

    @Override
    public void createTechnologyInform(TechnologyInformPostDTO create, TechnologyInformStateEnum state) {
        TechnologyInform target = create.transferToEntity(null);
        target.setInformTime(convertInformTime(create));
        target.setCreate(UserContext.getId());
        target.setTenantId(TenantContext.getOne());
        target.setState(state.getCode());
        target.setVersion(0);

        checkTemplateInfo(target);
        technologyInformMapper.insert(target);
        target.setInformNo(RandomStrUtil.generate(INFORM_NO_PREFIX, target.getId()));
        technologyInformMapper.updateById(target);
        technologyInformUserService.createTechnologyInformUser(target.getId(), create.getInformUsers());
        technologyInformSignatureService.createTechnologyInformSignature(target.getId(), create.getTechnologyInformSignatures());
        technologyInformContentService.createTechnologyInformContent(target.getId(), create.getTechnologyInformContents());
        technologyInformStaffService.createTechnologyInformStaff(target.getId(), create.getTechnologyInformStaffs());

    }

    private void checkTemplateInfo(TechnologyInform source) {
        if (source != null && Objects.equals(source.getState(), TechnologyInformStateEnum.SUBMIT.getCode())) {
            Integer templateId = source.getTemplateId();
            TechnologyInformTemplate templateInfo = templateMapper.selectById(templateId);
            boolean attachmentRequired = false;
            if (Objects.nonNull(templateInfo)) {
                attachmentRequired = templateInfo.isAttachmentRequired();
            }
            if (attachmentRequired && CollectionUtils.isEmpty(source.getAttachments())) {
                throw new ServiceException("附件信息必填");
            }
        }
    }

    private Date convertInformTime(TechnologyInformPostDTO source) {
        if (Objects.isNull(source)) {
            return null;
        }
        List<TechnologyInformStaffVo> technologyInformStaffs = source.getTechnologyInformStaffs();
        Optional<Date> maxInformStaffSignature = technologyInformStaffs.stream()
                .flatMap(e -> e.getInformUserSignatures().stream())
                .map(SignatureInfoUserDTO::getTime)
                .filter(Objects::nonNull)
                .max(Date::compareTo);
        return maxInformStaffSignature.orElse(source.getInformTime());
    }

    @Override
    public void temporaryTechnologyInform(TechnologyInformPostDTO source) {
        if (Objects.isNull(source.getId())) {
            createTechnologyInform(source, TechnologyInformStateEnum.TEMPORARY);
        } else {
            updateTechnologyInform(source, TechnologyInformStateEnum.TEMPORARY);
        }
    }

    @Override
    public void updateTechnologyInform(TechnologyInformPostDTO update, TechnologyInformStateEnum state) {
        TechnologyInform technologyInform = technologyInformMapper.selectById(update.getId());
        if (Objects.isNull(technologyInform)) {
            throw new ServiceException("更新失败，技术交底清单不存在");
        } else if(!Objects.equals(technologyInform.getVersion(), update.getVersion())) {
            throw new ServiceException("当前数据已被更新，请刷新后重试！");
        }
        if (Objects.equals(state, TechnologyInformStateEnum.SUBMIT)) {
            Integer projectId = technologyInform.getInformContractorProjectId();
            ContractorProject contractorProject = contractorProjectMapper.selectByPrimaryKey(Optional.ofNullable(projectId).map(Long::valueOf).orElse(0L));
            if (Objects.nonNull(contractorProject)) {
                Integer projectState = contractorProject.getState();
                if (Objects.equals(projectState, ProjectConstants.STATE_FIVE)) {
                    throw new ServiceException("该项目已完结");
                } else if (Objects.equals(projectState, ProjectConstants.STATE_SEVEN)) {
                    throw new ServiceException("该项目已暂停施工，请联系管理员");
                }
            }
        }

        TechnologyInform target = update.transferToEntity(technologyInform);
        target.setInformTime(convertInformTime(update));
        target.setVersion(target.getVersion() + 1);
        target.setModify(UserContext.getId());
        if (TechnologyInformStateEnum.SUBMIT.equals(state)) {
            target.setState(state.getCode());
        }
        checkTemplateInfo(target);
        technologyInformMapper.updateById(target);
        technologyInformUserService.updateTechnologyInformUser(target.getId(), update.getInformUsers());
        technologyInformSignatureService.updateTechnologyInformSignature(target.getId(), update.getTechnologyInformSignatures());
        technologyInformContentService.updateTechnologyInformContent(target.getId(), update.getTechnologyInformContents());
        technologyInformStaffService.updateTechnologyInformStaff(target.getId(), update.getTechnologyInformStaffs());
    }

    @Override
    public void deleteTechnologyInformById(Integer id) {
        TechnologyInform technologyInform = technologyInformMapper.selectById(id);
        if (Objects.isNull(technologyInform)) {
            throw new ServiceException("删除失败，技术交底清单不存在");
        }
        technologyInformMapper.deleteById(id);
        technologyInformUserService.deleteTechnologyInformUserByInformId(id);
        technologyInformSignatureService.deleteTechnologyInformSignatureByInformId(technologyInform.getId());
        technologyInformContentService.deleteTechnologyInformContentByInformId(technologyInform.getId());
        technologyInformStaffService.deleteTechnologyInformStaffByInformId(technologyInform.getId());
    }

    @Override
    public TechnologyInformVo findTechnologyInformById(Integer id) {
        TechnologyInform technologyInform = technologyInformMapper.selectById(id);
        if (Objects.isNull(technologyInform)) {
            throw new ServiceException("技术交底清单不存在");
        }
        TechnologyInformVo target = BeanUtils.convert(technologyInform, TechnologyInformVo.class);
        target.setInformUsers(technologyInformUserService.queryTechnologyInformUserByInformId(id));
        target.setTechnologyInformSignatures(technologyInformSignatureService.queryTechnologyInformSignatureByInformId(target.getId()));
        target.setTechnologyInformContents(technologyInformContentService.queryTechnologyInformContentByInformId(target.getId()));
        target.setTechnologyInformStaffs(technologyInformStaffService.queryTechnologyInformStaffByInformId(target.getId()));

        TechnologyInformTemplate templateInfo = templateMapper.selectById(technologyInform.getTemplateId());
        target.setAttachmentRequired(Optional.ofNullable(templateInfo).map(TechnologyInformTemplate::isAttachmentRequired).orElse(false));

        Integer userId = Optional.ofNullable(technologyInform.getModifyBy()).map(Long::intValue).orElse(-1);
        UserDto userDto = userRemoteService.queryByUserId(userId);
        if (Objects.nonNull(userDto)) {
            target.setModifyBy(userId);
            target.setModifyName(userDto.getName());
        }
        return target;
    }

    @Override
    public CommonPage<TechnologyInformCommonVo> commonPage(TechnologyInformPageQuery query) {
        query.setTenantId(TenantContext.getOne());
        if (Objects.nonNull(query.getInformDeptId())) {
            query.setFilterInformDeptIds(deptRemoteService.getChildNodeIdsById(query.getInformDeptId()));
        }
        if (Objects.nonNull(query.getConstructDeptId())) {
            query.setFilterConstructDeptIds(deptRemoteService.getChildNodeIdsById(query.getConstructDeptId()));
        }
        if (Strings.isNotBlank(query.getSearchKey())) {
            List<TechnologyInformUser> informUsers = technologyInformUserService.searchByInformUserName(query.getSearchKey());
            List<Integer> informIds = TransferUtils.transfer(informUsers, TechnologyInformUser::getInformId);
            query.setFilterInformIds(informIds);
        }
        Page<TechnologyInformCommonVo> page = new Page<>(query.getPageNo(), query.getPageSize());
        IPage<TechnologyInformCommonVo> pageResult = technologyInformMapper.queryByPage(page, query);
        if (!CollectionUtils.isEmpty(pageResult.getRecords())) {
            List<Integer> allInformIds = TransferUtils.transfer(pageResult.getRecords(), TechnologyInformCommonVo::getId);
            Map<Integer, List<TechnologyInformUser>> informUserMappings = technologyInformUserService.queryTechnologyInformUserByInformId(allInformIds);

            for (TechnologyInformCommonVo item : pageResult.getRecords()) {
                List<TechnologyInformUser> informUserInfos = informUserMappings.getOrDefault(item.getId(), Collections.emptyList());
                List<IdAndNameDTO> targetInformUsers = informUserInfos.stream().map(e -> new IdAndNameDTO(e.getInformUserId(), e.getInformUserName())).collect(Collectors.toList());
                item.setInformUsers(targetInformUsers);
            }

        }
        return new CommonPage<>(pageResult);
    }

    @Override
    public CommonPage<TechnologyInformCommonVo> commonMyPage(TechnologyInformPageQuery query) {
        query.setTenantId(TenantContext.getOne());
        query.setUserId(UserContext.getId());

        if (Objects.nonNull(query.getInformDeptId())) {
            query.setFilterInformDeptIds(deptRemoteService.getChildNodeIdsById(query.getInformDeptId()));
        }
        if (Objects.nonNull(query.getConstructDeptId())) {
            query.setFilterConstructDeptIds(deptRemoteService.getChildNodeIdsById(query.getConstructDeptId()));
        }
        Page<TechnologyInformCommonVo> page = new Page<>(query.getPageNo(), query.getPageSize());
        IPage<TechnologyInformCommonVo> pageResult = technologyInformMapper.queryByMyPage(page, query);
        return new CommonPage<>(pageResult);
    }

    @Override
    public TechnologyCountVo h5Count() {
        TechnologyCountVo result = new TechnologyCountVo();
        Long userId = UserContext.getId();
        List<TechnologyInform> allList = technologyInformMapper.selectList(technologyInformMapper.lambdaQuery());
        result.setAll(allList.size());
        List<TechnologyInform> fromMy = allList.stream().filter(e -> Objects.equals(e.getCreateBy(), userId)).collect(Collectors.toList());
        result.setMy(fromMy.size());

        return result;
    }

    @Override
    public void syncInformUserInfo() {
        List<TechnologyInform> allTechnologyInforms = technologyInformMapper.selectList(new LambdaQueryWrapper<>());
        if (!CollectionUtils.isEmpty(allTechnologyInforms)) {
            List<TechnologyInformUser> informUsers = new ArrayList<>();
            for (TechnologyInform informInfo : allTechnologyInforms) {
                Integer informUserId = informInfo.getInformUserId();
                if (Objects.isNull(informUserId)) {
                    log.info("安全技术交底：交底人信息为空 {}", informInfo.getId());
                    continue;
                }
                TechnologyInformUser informUser = new TechnologyInformUser();
                informUser.setInformId(informInfo.getId());
                informUser.setInformUserId(informInfo.getInformUserId());
                informUser.setInformUserName(informInfo.getInformUserName());
                informUser.setTenantId(informInfo.getTenantId());
                informUser.setCreateBy(UserContext.getId());

                informUsers.add(informUser);
            }

            if (!CollectionUtils.isEmpty(informUsers)) {
                technologyInformUserService.saveBatch(informUsers);
            }
        }
    }

    @Override
    public List<TechnologyInformDownloadDTO> download(TechnologyInformPageQuery query) {
        CommonPage<TechnologyInformCommonVo> pageResult = this.commonPage(query);
        List<TechnologyInformCommonVo> list = pageResult.getList();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<TechnologyInformDownloadDTO> result = new ArrayList<>();
        for (TechnologyInformCommonVo origin : list) {
            TechnologyInformDownloadDTO target = BeanUtils.convert(origin, TechnologyInformDownloadDTO.class);
            List<IdAndNameDTO> informUsers = Optional.ofNullable(origin.getInformUsers()).orElse(Collections.emptyList());
            String informUserInfos = informUsers.stream().map(IdAndNameDTO::getName).collect(Collectors.joining("、"));
            target.setInformUsers(informUserInfos);
            result.add(target);
        }

        return result;
    }
}
