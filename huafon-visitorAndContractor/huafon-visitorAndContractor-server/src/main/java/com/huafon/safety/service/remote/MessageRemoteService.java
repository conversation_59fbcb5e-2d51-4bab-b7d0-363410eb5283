package com.huafon.safety.service.remote;

import com.huafon.api.dto.MessageSendDTO;
import com.huafon.api.dto.blend.MsgAddRpcDTO;
import com.huafon.api.dto.sms.SmsSendRpcDTO;
import com.huafon.api.vo.MessageConfigInfoVo;
import com.huafon.api.vo.MessageConfigRecipientInfoVo;
import com.huafon.api.vo.MessageConfigRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-04-21 15:32
 **/
public interface MessageRemoteService {

	/**
	 * 发送系统消息
	 * @param message
	 */
	boolean sendSystemMessage(MsgAddRpcDTO message);

	/**
	 * 批量发送系统消息
	 * @param messages
	 */
	boolean sendBatchSystemMessage(List<MsgAddRpcDTO> messages);

	/**
	 * 发送短信息
	 * @param phone
	 * @param message
	 * @return
	 */
	boolean sendSMSMessage(String phone, String message);

	/**
	 * 发送短信息
	 * @return
	 */
	boolean sendSMSMessage(SmsSendRpcDTO source);

	/**
	 * 查询消息配置
	 * @param query
	 * @return
	 */
	List<MessageConfigInfoVo> query(MessageConfigRequest.Query query);

	/**
	 * 添加消息配置
	 * @param info
	 * @return
	 */
	MessageConfigInfoVo add(MessageConfigInfoVo info);

	/**
	 * 发送消息
	 * @param messages
	 * @return
	 */
	boolean batchSend(List<MessageSendDTO> messages);

	/**
	 * 编辑消息
	 * @param infoVo
	 * @return
	 */
	MessageConfigInfoVo modify(MessageConfigInfoVo infoVo);

	/**
	 * 获取接收对象
	 * @param configId
	 * @return
	 */
	List<MessageConfigRecipientInfoVo> getRecipientById(Integer configId);
}
