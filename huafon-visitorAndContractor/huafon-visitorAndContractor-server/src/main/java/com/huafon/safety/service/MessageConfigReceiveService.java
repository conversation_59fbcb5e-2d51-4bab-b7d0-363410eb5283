package com.huafon.safety.service;


import com.huafon.safety.models.dto.MessageConfigReceiveDTO;
import com.huafon.safety.models.dto.ProjectMessageDTO;
import com.huafon.safety.models.dto.UserInfoDTO;
import com.huafon.safety.models.vo.MessageConfigReceiveVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
* 消息通知配置-接收人
* <AUTHOR>
* @since 2023-04-20 16:46
*/
public interface MessageConfigReceiveService {

    /**
    * 创建消息通知配置-接收人
    * @param create
    */
    void createMessageConfigReceive(Integer configId, List<MessageConfigReceiveDTO> create);

    /**
    * 更新消息通知配置-接收人
    * @param update
    */
    void updateMessageConfigReceive(Integer configId, List<MessageConfigReceiveDTO> update);

    /**
    * 删除消息通知配置-接收人
    * @param configId
    */
    void deleteMessageConfigReceiveByConfigId(Integer configId);

    /**
    * 查询详情
    * @param configId
    * @return
    */
    List<MessageConfigReceiveVo> queryMessageConfigReceiveByConfigId(Integer configId);

    /**
    * 查询映射：key:id value: entity
    * @param configIds
    * @return
    */
    Map<Integer, List<MessageConfigReceiveVo>> queryMessageConfigReceiveByConfigId(Collection<Integer> configIds);

    /**
     * 获取通知的用户信息
     * @param configId
     * @return
     */
    List<UserInfoDTO> queryUserInfos(Integer configId, Integer tenantId, ProjectMessageDTO project, Integer userId);

}
