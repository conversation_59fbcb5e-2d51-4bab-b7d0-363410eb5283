package com.huafon.safety.models.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
* <AUTHOR>
* @since 2023-08-02 11:00
*/
@Data
@ApiModel(value = "技术交底模版-适用部门")
public class TechnologyInformTemplateDeptVo implements Serializable {

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "模版ID")
    private Integer templateId;

    @ApiModelProperty(value = "部门ID")
    private Integer departmentId;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    @ApiModelProperty(value = "租户ID")
    private Integer tenantId;


}
