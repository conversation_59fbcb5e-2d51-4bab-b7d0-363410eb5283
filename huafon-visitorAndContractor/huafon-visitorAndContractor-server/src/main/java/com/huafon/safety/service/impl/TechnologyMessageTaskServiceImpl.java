package com.huafon.safety.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.google.common.base.Joiner;
import com.huafon.api.dto.MessageSendDTO;
import com.huafon.api.enums.MessageConfigSubsectionType;
import com.huafon.api.vo.MessageConfigInfoVo;
import com.huafon.api.vo.MessageConfigRequest;
import com.huafon.common.config.TenantContext;
import com.huafon.contractor.models.entity.ContractorProject;
import com.huafon.contractor.models.entity.ContractorStaff;
import com.huafon.contractor.utils.TenantUtil;
import com.huafon.safety.dao.mapper.MessageConfigMapper;
import com.huafon.safety.dao.mapper.TechnologyMessageTaskMapper;
import com.huafon.safety.models.dto.IdAndNameDTO;
import com.huafon.safety.models.dto.ProjectMessageDTO;
import com.huafon.safety.models.dto.UserInfoDTO;
import com.huafon.safety.models.dto.message.MessageContentDTO;
import com.huafon.safety.models.dto.message.TaskOtherPropertyDTO;
import com.huafon.safety.models.entity.TechnologyEnable;
import com.huafon.safety.models.entity.TechnologyMessageTask;
import com.huafon.safety.service.MessageConfigReceiveService;
import com.huafon.safety.service.TechnologyEnableService;
import com.huafon.safety.service.TechnologyMessageTaskService;
import com.huafon.safety.service.remote.MessageRemoteService;
import com.huafon.safety.utils.TransferUtils;
import com.huafon.support.config.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 职业健康：劳防用品发放消息通知配置-接收人
 *
 * <AUTHOR>
 * @since 2023-08-07 17:32
 */
@Service
@Slf4j
@Transactional
@RefreshScope
public class TechnologyMessageTaskServiceImpl implements TechnologyMessageTaskService {

	@Autowired
	private TechnologyEnableService technologyEnableService;
	@Autowired
	private TechnologyMessageTaskMapper technologyMessageTaskMapper;
	@Autowired
	private MessageConfigMapper messageConfigMapper;
	@Autowired
	private MessageConfigReceiveService messageConfigReceiveService;
	@Autowired
	private MessageRemoteService messageRemoteService;
	@Autowired
	private TenantUtil tenantUtil;

	@Value("${safety.technology.message.interval:15}")
	private Integer INTERVAL_MINUTES;

	@Value("${contractor.staff.create.message.interval:30}")
	private Integer CONTRACTOR_INTERVAL_MINUTES;

	@Override
	public void addTask(ContractorProject project, ContractorStaff staff, MessageConfigSubsectionType type, TaskOtherPropertyDTO otherProperty) {
		if (Objects.isNull(project) || Objects.isNull(staff)) {
			log.warn("添加承包商管理/安全技术交底消息任务失败：承包商项目或承包商人员信息为Null: {}, {}", project, staff);
			return;
		}
		if (Objects.equals(type, MessageConfigSubsectionType.SAFETY_TECHNOLOGY)) {
			TechnologyEnable technologyEnable = technologyEnableService.findByTenant(TenantContext.getOne());
			if (Objects.isNull(technologyEnable) || !technologyEnable.getEnable()) {
				log.warn("添加承包商管理/安全技术交底消息任务失败：技术交底开启配置未配置或未开启");
				return;
			}
		}

		MessageConfigInfoVo messageConfig = getMessageConfigInfo(TenantContext.getOne(), null, type);
		if (Objects.isNull(messageConfig)) {
			log.warn("添加承包商管理/安全技术交底消息任务失败:未配置消息配置，{}, type {}", TenantContext.getOne(), type);
			return;
		} else if (!messageConfig.getEnable()) {
			log.warn("添加承包商管理/安全技术交底消息任务失败:未开启，{}, type {}", TenantContext.getOne(), type);
			return;
		}

		if (Objects.equals(type, MessageConfigSubsectionType.SAFETY_TECHNOLOGY) ||
				Objects.equals(type, MessageConfigSubsectionType.CONTRACTOR_STAFF_CREATE)) {
			TechnologyMessageTask task = technologyMessageTaskMapper.queryTask(project.getContractorProjectId(), type);
			if (Objects.isNull(task)) {
				task = new TechnologyMessageTask();
				task.setConfigId(messageConfig.getId());
				task.setProjectId(project.getContractorProjectId());
				task.setProjectInfo(new ProjectMessageDTO(project));
				task.setRelationStaff(Collections.singletonList(new IdAndNameDTO(Optional.ofNullable(staff.getContractorStaffId()).orElse(0L).intValue(), staff.getName())));
				task.setExecutionTime(LocalDateTime.now().plusMinutes(Objects.equals(type, MessageConfigSubsectionType.SAFETY_TECHNOLOGY) ? INTERVAL_MINUTES : CONTRACTOR_INTERVAL_MINUTES));
				task.setState(false);
				task.setTenantId(TenantContext.getOne());
				task.setCreate(UserContext.getId());
				task.setType(type.toString());
				task.setUniqueKey(type + "#" + project.getContractorProjectId());
				technologyMessageTaskMapper.insert(task);
			} else {
				task.setConfigId(messageConfig.getId());
				List<IdAndNameDTO> relationStaff = task.getRelationStaff();
				relationStaff.add(new IdAndNameDTO(Optional.ofNullable(staff.getContractorStaffId()).orElse(0L).intValue(), staff.getName()));
				List<IdAndNameDTO> targets = relationStaff.stream().filter(TransferUtils.distinctByKey(IdAndNameDTO::getId)).collect(Collectors.toList());
				task.setRelationStaff(targets);
				task.setModify(UserContext.getId());
				technologyMessageTaskMapper.updateById(task);
			}
		}  else if (Objects.equals(type, MessageConfigSubsectionType.CONTRACTOR_STAFF_COMPLETE)) {
			TechnologyMessageTask task = new TechnologyMessageTask();
			task.setConfigId(messageConfig.getId());
			task.setProjectId(project.getContractorProjectId());
			task.setProjectInfo(new ProjectMessageDTO(project));
			task.setRelationStaff(Collections.singletonList(new IdAndNameDTO(staff.getUserId(), staff.getName())));
			task.setExecutionTime(LocalDateTime.now());
			task.setState(false);
			task.setTenantId(TenantContext.getOne());
			task.setCreate(UserContext.getId());
			task.setType(type.toString());
			task.setUniqueKey(type + "#" + project.getContractorProjectId() + "#" + staff.getContractorStaffId());
			technologyMessageTaskMapper.insert(task);
		} else if (Objects.equals(type, MessageConfigSubsectionType.CONTRACTOR_STAFF_LICENSE) ||
				Objects.equals(type, MessageConfigSubsectionType.CONTRACTOR_STAFF_INSURANCE)) {
			String uniqueKey = type + "#" + project.getContractorProjectId() + "#" + staff.getUserId() + "#" + otherProperty.getUnique();

			TechnologyMessageTask uniqueTask = technologyMessageTaskMapper.queryTaskUnique(uniqueKey);
			if (Objects.isNull(uniqueTask)) {
				TechnologyMessageTask task = new TechnologyMessageTask();
				task.setConfigId(messageConfig.getId());
				task.setProjectId(project.getContractorProjectId());
				task.setProjectInfo(new ProjectMessageDTO(project));
				task.setRelationStaff(Collections.singletonList(new IdAndNameDTO(staff.getUserId(), staff.getName())));
				task.setExecutionTime(LocalDateTime.of(LocalDate.now(), LocalTime.of(8, 0, 0)));
				task.setState(false);
				task.setTenantId(TenantContext.getOne());
				task.setCreate(UserContext.getId());
				task.setType(type.toString());
				task.setUniqueKey(uniqueKey);
				task.setOtherProperty(otherProperty);
				technologyMessageTaskMapper.insert(task);
			}
		} else if (Objects.equals(type, MessageConfigSubsectionType.CONTRACTOR_PROJECT_QUALITY_EXAMINE)) {
			TechnologyMessageTask task = new TechnologyMessageTask();
			task.setConfigId(messageConfig.getId());
			task.setProjectId(project.getContractorProjectId());
			task.setProjectInfo(new ProjectMessageDTO(project));
			task.setExecutionTime(LocalDateTime.now());
			task.setState(false);
			task.setTenantId(TenantContext.getOne());
			task.setCreate(UserContext.getId());
			task.setType(type.toString());
			task.setOtherProperty(otherProperty);
			task.setUniqueKey(type + "#" + project.getContractorProjectId() + "#" + otherProperty.getUnique());
			technologyMessageTaskMapper.insert(task);
		}
	}

	@Override
	public MessageConfigInfoVo getMessageConfigInfo(Integer tenantId, Integer configId, MessageConfigSubsectionType type) {
		MessageConfigRequest.Query.QueryBuilder builder = MessageConfigRequest.Query.builder();
		if (Objects.nonNull(configId)) {
			builder.ids(Collections.singletonList(configId));
		} else {
			if (Objects.nonNull(tenantId)) {
				builder.tenantId(tenantId);
			}
			if (Objects.nonNull(type)) {
				builder.types(Collections.singletonList(type));
			}
		}
		List<MessageConfigInfoVo> messageConfigs = messageRemoteService.query(builder.build());
		if (CollectionUtils.isEmpty(messageConfigs)) {
			log.warn("承包商管理/安全技术交底未配置消息配置: {}, type {}", TenantContext.getOne(), type);
			return null;
		}

		return messageConfigs.get(0);
	}

	@Override
	public void execute() {
		List<TechnologyMessageTask> tasks = technologyMessageTaskMapper.queryToBeExecuteTaskList();
		if (CollectionUtils.isEmpty(tasks)) {
			log.info("承包商管理/安全技术交底消息通知任务为空: {}", LocalDateTime.now());
			return;
		}

		Map<Integer, List<TechnologyMessageTask>> configMappings = tasks.stream().collect(Collectors.groupingBy(TechnologyMessageTask::getConfigId));

		List<MessageSendDTO> messageSend = new ArrayList<>();
		Map<Integer, String> tenantMappings = tenantUtil.getTenantMappings();
		for (Map.Entry<Integer, List<TechnologyMessageTask>> configMapping : configMappings.entrySet()) {
			MessageConfigInfoVo messageConfig = getMessageConfigInfo(null, configMapping.getKey(), null);
			if (Objects.isNull(messageConfig)) {
				log.info("承包商管理/安全技术交底消息通知:消息配置不存在 configId {}", configMapping.getKey());
				continue;
			}
			Integer tenantId = messageConfig.getTenantId();

			for (TechnologyMessageTask task : configMapping.getValue()) {
				MessageConfigSubsectionType type = messageConfig.getType();
				String messageType = null;
				String messageContent = null;
				Long contentId = 0L;
				List<IdAndNameDTO> relationStaff = task.getRelationStaff();
				Integer relationUserId = Optional.ofNullable(relationStaff).map(x -> x.get(0).getId()).orElse(null);
				switch (type) {
					case SAFETY_TECHNOLOGY:
						messageType = "safetyTechnologyInform";
						messageContent = transferMessageForSafety(messageConfig.getContextTemplate(), task.getProjectInfo(), task.getRelationStaff(), tenantMappings.getOrDefault(tenantId, "华峰集团"));
						break;
					case CONTRACTOR_STAFF_CREATE:
						messageType = "contractorProjectMessage";
						messageContent = transferMessageForStaffCreate(messageConfig.getContextTemplate(), task.getProjectInfo(), task.getRelationStaff(), tenantMappings.getOrDefault(tenantId, "华峰集团"));

						break;
					case CONTRACTOR_STAFF_COMPLETE:
						messageType = "contractorProjectMessage";
						messageContent = transferMessageForStaffComplete(messageConfig.getContextTemplate(), task.getProjectInfo(), tenantMappings.getOrDefault(tenantId, "华峰集团"));
						break;
					case CONTRACTOR_STAFF_LICENSE:
						messageType = "contractorProjectMessage";
						messageContent = transferMessageForStaffLicense(messageConfig.getContextTemplate(), task.getProjectInfo(), task.getRelationStaff(), (TaskOtherPropertyDTO.StaffLicenseDTO) task.getOtherProperty(), tenantMappings.getOrDefault(tenantId, "华峰集团"));
						break;
					case CONTRACTOR_STAFF_INSURANCE:
						messageType = "contractorProjectMessage";
						messageContent = transferMessageForStaffInsurance(messageConfig.getContextTemplate(), task.getProjectInfo(), task.getRelationStaff(), (TaskOtherPropertyDTO.StaffInsuranceDTO) task.getOtherProperty(), tenantMappings.getOrDefault(tenantId, "华峰集团"));
						break;
					case CONTRACTOR_PROJECT_QUALITY_EXAMINE:
						messageType = "contractorProjectQualityExamine";
						TaskOtherPropertyDTO.ProjectQualityExamineDTO otherProperty = (TaskOtherPropertyDTO.ProjectQualityExamineDTO) task.getOtherProperty();
						relationUserId = otherProperty.getExamineUserId();
						contentId = Optional.ofNullable(otherProperty.getTaskId()).map(Integer::longValue).orElse(0L);
						messageContent = transferMessageForProjectExamine(messageConfig.getContextTemplate(), task.getProjectInfo(), task.getRelationStaff(), otherProperty, tenantMappings.getOrDefault(tenantId, "华峰集团"));
						break;
					default:
				}
				if (messageType == null) {
					log.warn("不支持的消息类型");
					continue;
				}

				List<UserInfoDTO> userInfoDTOS = messageConfigReceiveService.queryUserInfos(messageConfig.getId(), tenantId, task.getProjectInfo(), relationUserId);

				if (!CollectionUtils.isEmpty(userInfoDTOS)) {
					List<Long> userIds = userInfoDTOS.stream().filter(Objects::nonNull).map(UserInfoDTO::getUserId).filter(Objects::nonNull).map(Integer::longValue).distinct().collect(Collectors.toList());
					MessageSendDTO messageItem = MessageSendDTO.builder()
							.type(messageType)
							.tenantId(tenantId)
							.configId(messageConfig.getId())
							.subject(messageConfig.getName())
							.contentId(contentId)
							.userIds(userIds)
							.content(JSONArray.toJSONString(Collections.singletonList(MessageContentDTO.builder().content(messageContent).build())))
							.build();
					messageSend.add(messageItem);
				}
				//更新发送状态
				task.setState(true);
				task.setModify(UserContext.getId());
				technologyMessageTaskMapper.updateById(task);
			}
		}

		if (!CollectionUtils.isEmpty(messageSend)) {
			boolean sendResult = messageRemoteService.batchSend(messageSend);
			if (sendResult) log.info("生成承包商管理/安全技术交底消息通知: 发送消息成功 {} 条", messageSend.size());
		}
	}



	private String transferMessageForSafety(String content, ProjectMessageDTO project, List<IdAndNameDTO> userInfo, String tenantName) {
		String staffInfo = Joiner.on("、").skipNulls().join(userInfo.stream().filter(Objects::nonNull).map(IdAndNameDTO::getName).toArray(String[]::new));
		return String.format(content, tenantName, project.getName(), staffInfo, project.getConstructArea());
	}

	private String transferMessageForStaffCreate(String content, ProjectMessageDTO project, List<IdAndNameDTO> userInfo, String tenantName) {
		String staffInfo = Joiner.on("、").skipNulls().join(userInfo.stream().filter(Objects::nonNull).map(IdAndNameDTO::getName).toArray(String[]::new));
		return String.format(content, tenantName, project.getContractorName(), project.getName(), staffInfo);
	}

	private String transferMessageForStaffComplete(String content, ProjectMessageDTO project, String tenantName) {
		return String.format(content, tenantName, project.getContractorName(), project.getName());
	}

	private String transferMessageForStaffLicense(String content, ProjectMessageDTO project, List<IdAndNameDTO> userInfo, TaskOtherPropertyDTO.StaffLicenseDTO otherProperty, String tenantName) {
		return String.format(content, tenantName, project.getContractorName(), project.getName(), userInfo.get(0).getName(), otherProperty.getLicenseTypeName(), new SimpleDateFormat("yyyy-MM-dd").format(otherProperty.getExpireDate()));
	}

	private String transferMessageForStaffInsurance(String content, ProjectMessageDTO project, List<IdAndNameDTO> userInfo, TaskOtherPropertyDTO.StaffInsuranceDTO otherProperty, String tenantName) {
		return String.format(content, tenantName, project.getContractorName(), project.getName(), userInfo.get(0).getName(),  new SimpleDateFormat("yyyy-MM-dd").format(otherProperty.getExpiredDate()));
	}

	private String transferMessageForProjectExamine(String content, ProjectMessageDTO project, List<IdAndNameDTO> userInfo, TaskOtherPropertyDTO.ProjectQualityExamineDTO otherProperty, String tenantName) {
		return String.format(content, tenantName, project.getName());
	}
}
