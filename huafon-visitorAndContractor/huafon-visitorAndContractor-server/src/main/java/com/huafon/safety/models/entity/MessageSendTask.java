package com.huafon.safety.models.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.huafon.common.message.dto.MessageTaskPropertyDTO;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import lombok.Data;

import java.time.LocalDateTime;

/**
* 推送消息任务
* <AUTHOR>
* @since 2024-09-03 13:39
*/
@Data
@TableName(value = "hf_message_send_task", autoResultMap = true)
public class MessageSendTask extends BaseEntity{

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
    * 消息配置ID
    */
    @TableField(value = "config_id")
    private Integer configId;

    /**
    * 主类型
    */
    @TableField(value = "category")
    private String category;

    /**
    * 子类型
    */
    @TableField(value = "sub_category")
    private String subCategory;

    /**
    * 业务ID
    */
    @TableField(value = "business_id")
    private Integer businessId;

    /**
    * 唯一Key
    */
    @TableField(value = "unique_key")
    private String uniqueKey;

    /**
     * 升级通知：等级
     */
    @TableField(value = "level")
    private String level;

    /**
     * 是否是升级通知：true 是
     */
    @TableField(value = "is_upgrade")
    private Boolean isUpgrade;

    /**
    * 任务参数
    */
    @TableField(value = "property", typeHandler = MessageTaskPropertyDTO.MessageTaskPropertyDTOHandler.class)
    private MessageTaskPropertyDTO property;

    /**
    * 执行时间
    */
    @TableField(value = "executor_time")
    private LocalDateTime executorTime;

    /**
    * 0: 未处理；1已发送; -1取消
    */
    @TableField(value = "state")
    private Integer state;

    /**
    * 租户ID
    */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER)
    private Integer tenantId;


}
