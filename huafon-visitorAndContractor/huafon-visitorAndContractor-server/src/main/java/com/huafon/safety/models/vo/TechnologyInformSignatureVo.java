package com.huafon.safety.models.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* @since 2023-08-03 20:09
*/
@Data
@ApiModel(value = "技术交底清单-签字信息")
public class TechnologyInformSignatureVo implements Serializable {

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "清单ID", hidden = true)
    private Integer informId;

    @ApiModelProperty(value = "人员类型")
    @NotEmpty(message = "人员类型不能为空")
    private String personType;

    @ApiModelProperty(value = "审核意见")
    @NotEmpty(message = "审核意见不能为空")
    private String auditOpinion;

    @ApiModelProperty(value = "签字URL")
    @NotEmpty(message = "签字URL不能为空")
    private String signatureUrl;

    @ApiModelProperty(value = "签字时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "签字时间不能为空")
    private Date signatureTime;

    @ApiModelProperty(value = "租户ID", hidden = true)
    private Integer tenantId;

}
