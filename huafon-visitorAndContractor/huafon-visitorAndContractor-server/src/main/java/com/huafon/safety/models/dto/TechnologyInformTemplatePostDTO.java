package com.huafon.safety.models.dto;

import com.huafon.safety.models.entity.TechnologyInformTemplate;
import com.huafon.safety.models.vo.TechnologyInformTemplateContentVo;
import com.huafon.safety.models.vo.TechnologyInformTemplateSignatureVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-08-02 11:00
 */
@Data
@ApiModel(value = "技术交底模版提交")
public class TechnologyInformTemplatePostDTO implements Serializable {

	@ApiModelProperty(value = "")
	private Integer id;

	@ApiModelProperty(value = "标题")
	@NotEmpty(message = "标题不能为空")
	private String title;

	@ApiModelProperty(value = "附件是否必填：true必填；false非必填")
	private boolean attachmentRequired;

	@ApiModelProperty(value = "技术交底模版-适用部门")
	@NotEmpty(message = "适用部门不能为空")
	List<Integer> technologyInformTemplateDeptIds;

	@ApiModelProperty(value = "技术交底模版-签字信息")
	List<TechnologyInformTemplateSignatureVo> technologyInformTemplateSignatures;

	@ApiModelProperty(value = "技术交底模版-交底信息")
	List<TechnologyInformTemplateContentVo> technologyInformTemplateContents;

	public TechnologyInformTemplate transferToEntity(TechnologyInformTemplate target) {
		if (target == null) {
			target = new TechnologyInformTemplate();
		}
		target.setId(this.getId());
		target.setTitle(this.getTitle());//标题
		target.setAttachmentRequired(this.isAttachmentRequired());
		return target;
	}
}
