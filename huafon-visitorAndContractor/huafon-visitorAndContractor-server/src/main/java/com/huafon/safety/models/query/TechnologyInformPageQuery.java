package com.huafon.safety.models.query;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.huafon.framework.mybatis.pojo.PageRequest;
import com.huafon.safety.service.support.json.EndOfDayDeserializer;
import com.huafon.safety.service.support.json.StartOfDayDeserializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collection;
import java.util.Date;

@Data
@ApiModel(value = "技术交底清单列表查询")
public class TechnologyInformPageQuery extends PageRequest {

    @ApiModelProperty(value = "模糊搜索")
    private String searchKey;

    @ApiModelProperty(value = "过滤：模糊搜索", hidden = true)
    private Collection<Integer> filterInformIds;

    @ApiModelProperty(value = "租户ID", hidden = true)
    private Integer tenantId;

    @ApiModelProperty(value = "交底时间开始时间")
    @JsonDeserialize(using = StartOfDayDeserializer.class)
    private Date informTimeStart;

    @ApiModelProperty(value = "交底时间结束时间")
    @JsonDeserialize(using = EndOfDayDeserializer.class)
    private Date informTimeEnd;

    @ApiModelProperty(value = "交底部门ID")
    private Integer informDeptId;

    @ApiModelProperty(value = "交底部门过滤", hidden = true)
    private Collection<Integer> filterInformDeptIds;

    @ApiModelProperty(value = "施工时间开始时间")
    @JsonDeserialize(using = StartOfDayDeserializer.class)
    private Date constructTimeStart;

    @ApiModelProperty(value = "施工时间结束时间")
    @JsonDeserialize(using = EndOfDayDeserializer.class)
    private Date constructTimeEnd;

    @ApiModelProperty(value = "施工所在区域部门ID")
    private Integer constructDeptId;

    @ApiModelProperty(value = "施工所在区域部门ID过滤", hidden = true)
    private Collection<Integer> filterConstructDeptIds;

    @ApiModelProperty(value = "过滤创建人", hidden = true)
    private Long userId;

}
