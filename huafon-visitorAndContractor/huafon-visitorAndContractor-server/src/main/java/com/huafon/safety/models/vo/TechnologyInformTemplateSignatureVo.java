package com.huafon.safety.models.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
* <AUTHOR>
* @since 2023-08-02 11:00
*/
@Data
@ApiModel(value = "技术交底模版-签字信息")
public class TechnologyInformTemplateSignatureVo implements Serializable {

    @ApiModelProperty(value = "ID存在则更新，不存在则新增")
    private Integer id;

    @ApiModelProperty(value = "模版ID", hidden = true)
    private Integer templateId;

    @ApiModelProperty(value = "人员类型")
    @NotEmpty(message = "人员类型不能为空")
    private String personType;

    @ApiModelProperty(value = "审核意见")
    @NotEmpty(message = "审核意见不能为空")
    private String auditOpinion;

    @ApiModelProperty(value = "排序：升序")
    @NotNull(message = "排序值不能为空")
    private Integer sort;

    @ApiModelProperty(value = "租户ID", hidden = true)
    private Integer tenantId;
}
