package com.huafon.safety.service.impl;

import com.huafon.common.config.TenantContext;
import com.huafon.common.utils.bean.BeanUtils;
import com.huafon.framework.mybatis.service.MybatisServiceImpl;
import com.huafon.safety.dao.mapper.TechnologyInformUserMapper;
import com.huafon.safety.models.entity.TechnologyInformUser;
import com.huafon.safety.models.vo.TechnologyInformUserVo;
import com.huafon.safety.service.TechnologyInformUserService;
import com.huafon.support.config.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* 技术交底清单-签字信息
* <AUTHOR>
* @since 2024-07-29 17:28
*/
@Service
@Slf4j
@Transactional
public class TechnologyInformUserServiceImpl extends MybatisServiceImpl<TechnologyInformUserMapper, TechnologyInformUser> implements TechnologyInformUserService {
    @Override
    public void createTechnologyInformUser(Integer informId, List<TechnologyInformUserVo> source) {
        if (CollectionUtils.isEmpty(source)) {
            return;
        }

        this.saveBatch(process(informId, source));
    }

    private List<TechnologyInformUser> process(Integer informId, List<TechnologyInformUserVo> source) {
        return source.stream()
                        .map(origin -> {
                            TechnologyInformUser target = BeanUtils.convert(origin, TechnologyInformUser.class);
                            target.setInformId(informId);
                            target.setTenantId(TenantContext.getOne());
                            target.setCreate(UserContext.getId());
                            return target;
                        }).collect(Collectors.toList());
    }
    @Override
    public void updateTechnologyInformUser(Integer informId, List<TechnologyInformUserVo> source) {
        if (source == null) {
            source = new ArrayList<>();
        }

        List<TechnologyInformUser> newest = process(informId, source);
        List<TechnologyInformUser> oldest = this.lambdaQuery().eq(TechnologyInformUser::getInformId, informId).list();

        Map<Integer, TechnologyInformUser> mappings = oldest.stream().collect(Collectors.toMap(TechnologyInformUser::getId, Function.identity(), (a, b) -> a));

        List<TechnologyInformUser> add = new ArrayList<>();
        Set<Integer> newestIds = new HashSet<>();
        for (TechnologyInformUser current : newest) {
            Integer currentId = current.getId();
            TechnologyInformUser item = mappings.get(currentId);
            if (Objects.isNull(item)) {
                add.add(current);
            } else {
                item.setInformUserId(current.getInformUserId());
                item.setInformUserName(current.getInformUserName());
                item.setModify(UserContext.getId());
                this.updateById(item);
            }
            newestIds.add(currentId);
        }

        List<Integer> deleteIds = oldest.stream()
                .map(TechnologyInformUser::getId)
                .filter(o -> !newestIds.contains(o))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(deleteIds)) {
            this.removeByIds(deleteIds);
        }

        if (!CollectionUtils.isEmpty(add)) {
            this.saveBatch(add);
        }
    }

    @Override
    public void deleteTechnologyInformUserByInformId(Integer informId) {
        if (Objects.isNull(informId)) {
            return;
        }
        this.lambdaUpdate().eq(TechnologyInformUser::getInformId, informId).remove();
    }

    @Override
    public List<TechnologyInformUserVo> queryTechnologyInformUserByInformId(Integer informId) {
        return BeanUtils.convert(TechnologyInformUserVo.class, this.lambdaQuery().eq(TechnologyInformUser::getInformId, informId).list());
    }

    @Override
    public Map<Integer, List<TechnologyInformUser>> queryTechnologyInformUserByInformId(Collection<Integer> informIds) {
        if (CollectionUtils.isEmpty(informIds)) {
            return Collections.emptyMap();
        }

        List<TechnologyInformUser> source = this.lambdaQuery().in(TechnologyInformUser::getInformId, informIds).list();

        return source.stream().collect(Collectors.groupingBy(TechnologyInformUser::getInformId));
    }

    @Override
    public List<TechnologyInformUser> searchByInformUserName(String searchKey) {
        if (Strings.isBlank(searchKey)) {
            return Collections.emptyList();
        }

        return this.lambdaQuery().like(TechnologyInformUser::getInformUserName, searchKey).list();
    }
}
