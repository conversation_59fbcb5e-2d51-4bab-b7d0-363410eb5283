package com.huafon.common.message.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.core.type.TypeReference;
import com.huafon.common.handler.JsonbTypeHandler;
import com.huafon.visitor.models.dto.ProjectQualityExamineDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024-09-03 13:43
 **/
@Data
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME)
@JsonSubTypes({
		@JsonSubTypes.Type(value = ProjectQualityExamineDTO.class),
})
public abstract class MessageTaskPropertyDTO implements Serializable {

	@JsonIgnore
	public String getUniqueKey() {
		return "";
	}


	public static class MessageTaskPropertyDTOHandler extends JsonbTypeHandler<MessageTaskPropertyDTO> {

		@Override
		protected TypeReference<MessageTaskPropertyDTO> typeReference() {
			return new TypeReference<MessageTaskPropertyDTO>() {
			};
		}
	}
}
