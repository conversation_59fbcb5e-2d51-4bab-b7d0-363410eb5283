package com.huafon.visitor.controller;

import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.support.core.pojo.R;
import com.huafon.visitor.models.vo.req.reservation.*;
import com.huafon.visitor.models.vo.resp.WorkflowListCountRespVO;
import com.huafon.visitor.models.vo.resp.reservation.ReservationListRespVO;
import com.huafon.visitor.models.vo.resp.reservation.ReservationQrCodeRespVO;
import com.huafon.visitor.service.ReservationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/19 15:27
 */

@Api(tags = "访客预约管理")
@RestController
@RequestMapping("/reservation")
public class ReservationController {

    private final ReservationService reservationService;

    @Autowired
    public ReservationController(ReservationService reservationService) {
        this.reservationService = reservationService;
    }

    @PostMapping("/qrCode/add")
    @ApiOperation(value = "二维码新增")
    public R<Integer> addQrCode() {
        return R.ok(reservationService.addQrCode());
    }

    @PostMapping("/qrCode/edit")
    @ApiOperation(value = "二维码编辑")
    public R editQrCode(@RequestBody @Valid ReservationQrCodeEditReqVO reservationQrCodeEditReqVO) {
        reservationService.editQrCode(reservationQrCodeEditReqVO);
        return R.ok();
    }

    @PostMapping("/qrCode/getList")
    @ApiOperation(value = "二维码列表")
    public R<List<ReservationQrCodeRespVO>> getQrCodeList(@RequestBody QueryQrCodeListReqVO queryQrCodeListReqVO) {
        return R.ok(reservationService.getQrCodeList(queryQrCodeListReqVO));
    }

    @GetMapping("/qrCode/getOne/{id}")
    @ApiOperation(value = "二维码详情")
    public R<ReservationQrCodeRespVO> getQrCodeOne(@PathVariable("id") @Valid @NotBlank(message = "不能为空") Integer id) {
        return R.ok(reservationService.getQrCodeOne(id));
    }


    @PostMapping("/add")
    @ApiOperation(value = "访客预约新增")
    public R add(@RequestBody @Valid ReservationReqVO reservationReqVO) {
        reservationService.add(reservationReqVO);
        return R.ok();
    }

    @PostMapping("/staging/{id}")
    @ApiOperation(value = "访客预约暂存")
    public R staging(@PathVariable("id") Long id, @RequestBody @Valid ReservationEditReqVO reservationEditReqVO) {
        reservationService.staging(id, reservationEditReqVO);
        return R.ok();
    }

    @PostMapping("/queryListByPage")
    @ApiOperation(value = "访客预约分页列表")
    public R<CommonPage<ReservationListRespVO>> queryListByPage(@RequestBody QueryListByPageReqVO queryListByPageReqVO) {
        return R.ok(reservationService.queryListByPage(queryListByPageReqVO));
    }

    @PostMapping("/listCount")
    @ApiOperation(value = "访客预约列表数量")
    public R<WorkflowListCountRespVO> listCount() {
        return R.ok(reservationService.listCount());
    }

    @PostMapping("/check/{id}")
    @ApiOperation(value = "访客预约-节点审批")
    public R check(@PathVariable("id") @Valid @NotBlank(message = "不能为空") Long id, @RequestBody @Valid ReservationCheckReqVO reservationCheckReqVO) {
        reservationService.check(id, reservationCheckReqVO);
        return R.ok();
    }

    @PostMapping("/refuse")
    @ApiOperation(value = "访客预约-拒绝")
    public R refuse(@RequestBody @Valid ReservationRefuseReqVO reservationRefuseReqVO) {
        reservationService.refuse(reservationRefuseReqVO);
        return R.ok();
    }

    @PostMapping("/del")
    @ApiOperation(value = "访客预约-批量删除")
    public R del(@RequestBody @Valid @NotEmpty(message = "id不能为空") List<Long> ids) {
        reservationService.del(ids);
        return R.ok();
    }
}
