package com.huafon.visitor.models.vo.req.excel;

import com.huafon.visitor.models.vo.req.InviteListReqVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2024/4/15 16:03
 */
@Data
public class VisitorInviteExcelQueryReq {

    @ApiModelProperty(value = "查询条件")
    @NotNull(message = "导出查询条件不能为空")
    private InviteListReqVO inviteListReqVO;

    @ApiModelProperty(value = "导出列表")
    @NotEmpty(message = "导出列表头信息不能为空")
    private Set<String> headList;
}
