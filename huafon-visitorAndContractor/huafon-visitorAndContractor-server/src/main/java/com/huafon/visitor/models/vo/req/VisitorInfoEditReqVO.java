package com.huafon.visitor.models.vo.req;

import com.huafon.visitor.models.entity.VisitorInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR> zhang
 * @program: huafon-base
 * @description: 编辑来访人信息前端封装请求体对象
 * @date 2022-04-14 14:16:32
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "VisitorInfoEditReqVO", description = "编辑来访人信息前端封装请求体对象")
public class VisitorInfoEditReqVO {

    @ApiModelProperty(value = "编辑来访人信息的唯一id")
    @NotNull(message = "唯一id不能为空")
    private Long id;

    @ApiModelProperty(value = "访客姓名")
    @NotBlank(message = "访客姓名不能为空")
    private String name;

    @ApiModelProperty(value = "访客人性别 1男 2女")
    @NotNull(message = "访客人性别类型不能为空")
    @Range(min = 1, max = 2, message = "不合法的数字")
    private Integer sex;

    @ApiModelProperty(value = "电话号码")
    @NotBlank(message = "联系人电话不能为空")
    @Pattern(regexp = "1[0-9][0-9]\\d{8}", message = "手机号码格式异常，请输入正确")
    private String phone;

    @ApiModelProperty(value = "证件号码")
    @NotBlank(message = "证件号码不能为空")
    private String idCard;

    @ApiModelProperty(value = "证件类型名称")
    private String idTypeName;

    @ApiModelProperty(value = "证件类型code")
    private String idTypeCode;

    @ApiModelProperty(value = "来访人的工作单位")
    @NotBlank(message = "工作单位不能为空")
    private String organization;

    @ApiModelProperty(value = "车牌牌照")
    @NotBlank(message = "车牌牌照")
    private String licensePlate;

    @ApiModelProperty(value = "来访人照片")
    @NotBlank(message = "来访人照片不能为空")
    private String photo;

    @ApiModelProperty(value = "健康码")
    private String healthCode;

    @ApiModelProperty(value = "行程卡")
    private String travelHistoryCode;

    @ApiModelProperty(value = "核酸报告")
    private String nucleicAcidReport;

    @ApiModelProperty(value = "是否进入厂区")
    private Integer inProductionArea;

    @ApiModelProperty(value = "证件正面")
    private String idCardPhotoFront;

    @ApiModelProperty(value = "证件反面")
    private String idCardPhotoReverse;

    public static VisitorInfo convertToVisitorInfo(VisitorInfoEditReqVO item) {
        if (item == null) {
            return null;
        }
        VisitorInfo result = new VisitorInfo();
        result.setId(item.getId());
        result.setName(item.getName());
        result.setSex(item.getSex());
        result.setIdCard(item.getIdCard().toUpperCase());
        result.setPhone(item.getPhone());
        result.setPhoto(item.getPhoto());
        result.setLicensePlate(item.getLicensePlate());
        result.setOrganization(item.getOrganization());
        result.setHealthCode(item.getHealthCode());
        result.setTravelHistoryCode(item.getTravelHistoryCode());
        result.setNucleicAcidReport(item.getNucleicAcidReport());
        result.setIdCardPhotoFront(item.getIdCardPhotoFront());
        result.setIdCardPhotoReverse(item.getIdCardPhotoReverse());
        result.setInProductionArea(item.getInProductionArea());
        result.setIdTypeName(item.getIdTypeName());
        result.setIdTypeCode(item.getIdTypeCode());
        return result;
    }
}