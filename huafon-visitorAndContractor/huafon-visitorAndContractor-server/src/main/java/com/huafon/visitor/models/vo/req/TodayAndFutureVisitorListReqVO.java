package com.huafon.visitor.models.vo.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.huafon.framework.mybatis.pojo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;


/**
 * <AUTHOR> zhang
 * @program: huafon-base
 * @description: 今日访客和未来访客列表
 * @date 2022-04-26 16:33:50
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "TodayAndFutureVisitorListReqVO", description = "今日访客和未来访客列表")
public class TodayAndFutureVisitorListReqVO extends PageRequest {

    @ApiModelProperty(value = "租户id 预留字段")
    private List<Integer> tenantIds;

    @ApiModelProperty(value = "租户id 预留字段")
    @JsonIgnore
    private Integer tenantId;

    @ApiModelProperty(value = "邀约单创建人")
    private Integer createBy;
}