package com.huafon.api.service;

import com.huafon.api.dto.FileSignatureRequest;
import com.huafon.api.dto.FileSignatureResponse;

/**
 * <AUTHOR>
 * @since 2023-12-27 16:36
 **/
public interface FileOperateRpcService {

	/**
	 * 签字
	 * @param request
	 * @return
	 */
	FileSignatureResponse fileSignature(FileSignatureRequest request);


	void fileSignatureCallback(FileSignatureRequest request, FileSignatureCallbackListener listener);

}
