package com.huafon.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.huafon.models.entity.FileInfo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface FileInfoMapper extends BaseMapper<FileInfo> {
    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int deleteByPrimaryKey(Long id);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int insert(FileInfo row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int insertSelective(FileInfo row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    FileInfo selectByPrimaryKey(Long id);

    List<FileInfo> selectByUploadTime(String uploadTime);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByPrimaryKeySelective(FileInfo row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByPrimaryKey(FileInfo row);
}