package com.huafon.service.impl;

import com.huafon.mapper.FileViewMapper;
import com.huafon.models.entity.FileView;
import com.huafon.service.FileViewService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @program: huafon-base
 * @description:
 * @date 2023-12-25 14:14:03
 */
@Service
public class FileViewServiceImpl implements FileViewService {

    private final FileViewMapper fileViewMapper;

    public FileViewServiceImpl(FileViewMapper fileViewMapper) {
        this.fileViewMapper = fileViewMapper;
    }

    @Override
    public void save(FileView fileView) {
        fileViewMapper.insert(fileView);
    }

    @Override
    public FileView getOneByEncodeUrl(String encodeUrl) {
        return fileViewMapper.selectByEncodeUrl(encodeUrl);
    }
}