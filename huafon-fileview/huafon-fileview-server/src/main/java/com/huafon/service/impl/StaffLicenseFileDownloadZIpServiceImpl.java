package com.huafon.service.impl;

import com.google.common.io.Files;
import com.huafon.common.utils.StreamUtils;
import com.huafon.license.apis.dto.staffLicense.StaffLicenseAttachDTO;
import com.huafon.license.apis.remote.StaffLicenseRpcService;
import com.huafon.models.dto.FileDownloadZipQuery;
import com.huafon.models.dto.ZipEntryDTO;
import com.huafon.models.enums.FileDownloadOptEnum;
import com.huafon.portal.api.dto.UserDto;
import com.huafon.portal.api.service.UserRpcV2Service;
import com.huafon.service.AbstractFileDownloadZipService;
import com.huafon.service.IFileInfoService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class StaffLicenseFileDownloadZIpServiceImpl extends AbstractFileDownloadZipService {


    @DubboReference
    private StaffLicenseRpcService staffLicenseService;

    @DubboReference
    private UserRpcV2Service userRpcV2Service;

    public StaffLicenseFileDownloadZIpServiceImpl(IFileInfoService fileInfoService) {
        super(fileInfoService);
    }

    @Override
    public FileDownloadOptEnum optType() {
        return FileDownloadOptEnum.STAFF_LICENSE;
    }

    @Override
    public List<ZipEntryDTO> getEntries(FileDownloadZipQuery query) {
        if (CollectionUtils.isEmpty(query.getIds())) {
            return Collections.emptyList();
        }


        List<StaffLicenseAttachDTO> staffLicenses = staffLicenseService.queryByLicenseIds(query.getIds());
        List<Integer> allUserIds = StreamUtils.propList(staffLicenses, StaffLicenseAttachDTO::getUserId);
        List<UserDto> userInfos = userRpcV2Service.getByUserIds(allUserIds);
        Map<Integer, UserDto> userMap = userInfos.stream().collect(Collectors.toMap(UserDto::getUserId, Function.identity(), (a, b) -> b));


        List<ZipEntryDTO> result = new ArrayList<>();
        for (StaffLicenseAttachDTO license : staffLicenses) {
            List<String> attachUrls = Optional.ofNullable(license.getAttachUrls()).orElse(Collections.emptyList());
            StringBuilder prefix = new StringBuilder();
            UserDto userDto = userMap.get(license.getUserId());
            if (Objects.nonNull(userDto)) {
                prefix.append(userDto.getName()).append("_").append(userDto.getUsername()).append("_").append(license.getLicenseName());
            } else {
                prefix.append(license.getLicenseId()).append("_").append(license.getLicenseName());
            }

            int len = attachUrls.size();
            boolean appendIndex = len > 1;


            String pre = prefix.toString();
            for (int i = 0; i < attachUrls.size(); i++) {
                ZipEntryDTO entry = new ZipEntryDTO();
                entry.setEntryName((appendIndex ? (prefix + "_" + (i + 1)) : pre) + "." + Files.getFileExtension(attachUrls.get(i)));
                entry.setUrl(attachUrls.get(i));
                result.add(entry);
            }
        }

        return result;
    }
}
