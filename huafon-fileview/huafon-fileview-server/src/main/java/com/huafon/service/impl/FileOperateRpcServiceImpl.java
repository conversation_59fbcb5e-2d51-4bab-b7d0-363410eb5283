package com.huafon.service.impl;

import com.alibaba.fastjson.JSON;
import com.huafon.api.dto.FileSignatureRequest;
import com.huafon.api.dto.FileSignatureResponse;
import com.huafon.api.service.FileOperateRpcService;
import com.huafon.api.service.FileSignatureCallbackListener;
import com.huafon.service.AbstractFileSignatureService;
import com.huafon.service.FileSignTaskService;
import com.huafon.service.SignatureServiceSelector;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Argument;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.config.annotation.Method;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023-12-27 16:59
 **/
@DubboService(methods = {
		@Method(name = "fileSignatureCallback",
				arguments = {
					@Argument(
							type = "com.huafon.api.service.FileSignatureCallbackListener",
							callback = true
					)
				})
})
@Slf4j
public class FileOperateRpcServiceImpl implements FileOperateRpcService {

	@Autowired
	private SignatureServiceSelector signatureServiceSelector;
	@Autowired
	private FileSignTaskService fileSignTaskService;

	@Override
	public FileSignatureResponse fileSignature(FileSignatureRequest request) {
		if (request == null) {
			return new FileSignatureResponse();
		}
		log.info("request: {}", JSON.toJSONString(request));
		AbstractFileSignatureService signatureService = signatureServiceSelector.select(request.getType());
		if (Objects.isNull(signatureService)) {
			log.error("签字失败：签字类型不支持 {}", request.getType());
			return FileSignatureResponse.builder().type(request.getType()).businessId(request.getBusinessId()).build();
		}

		FileSignatureResponse response = signatureService.signature(request);
		if (!response.isSuccess()) {
			fileSignTaskService.insertFailureTask(request);
		}

		return response;
	}

	@Override
	public void fileSignatureCallback(FileSignatureRequest request, FileSignatureCallbackListener listener) {
		log.info("request: {}, listener: {}", JSON.toJSONString(request), JSON.toJSONString(listener));
		fileSignTaskService.execute(request, listener);
	}
}
