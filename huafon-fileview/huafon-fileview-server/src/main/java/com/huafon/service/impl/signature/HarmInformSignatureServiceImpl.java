package com.huafon.service.impl.signature;

import com.alibaba.fastjson.JSONObject;
import com.huafon.api.dto.FileSignatureRequest;
import com.huafon.api.dto.FileSignatureResponse;
import com.huafon.api.enums.SignatureTypeEnum;
import com.huafon.service.AbstractFileSignatureService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.*;
import org.apache.xmlbeans.XmlException;
import org.openxmlformats.schemas.drawingml.x2006.main.CTGraphicalObject;
import org.openxmlformats.schemas.drawingml.x2006.wordprocessingDrawing.CTAnchor;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;

/**
 * <AUTHOR>
 * @since 2023-12-29 14:58
 **/
@Component
@Slf4j
public class HarmInformSignatureServiceImpl extends AbstractFileSignatureService {

	private static String DATE_FORMAT = "%s 年 %s 月 %s 日";

	@Override
	public FileSignatureResponse signature(FileSignatureRequest request) {
		FileSignatureResponse response = getDefaultResponse(request);

		try {
			byte[] templateBytes = getTemplateBytes(request.getSourceUrl());
			byte[] signatureBytes = getSignatureBytes(request.getSignatureUrl());
			byte[] sealBytes = getSealBytes(request.getSealUrl());

			String signDocxFileName = request.getFilePrefix() + ".docx";
			XWPFDocument document = signatureFile(
					new XWPFDocument(new ByteArrayInputStream(templateBytes)),
					signatureBytes,
					sealBytes,
					Optional.ofNullable(request.getValueFromDataMap("informOrg")).map(Object::toString).orElse(""),
					Objects.equals(request.getType(), SignatureTypeEnum.HARM_INFORM));
			String signDocxUrl = saveFile(convertDocumentToByteArray(document), signDocxFileName);//合并后的word存在minio
			log.info("危害告知书签署：docx {}", signDocxUrl);
			response.setFileWordUrl(signDocxUrl);
			String signPdfUrl = convertToPdf(signDocxUrl, request.getFilePrefix());
			response.setSuccess(true);
			response.setFileName(request.getFilePrefix() + ".pdf");
			response.setFileUrl(signPdfUrl);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("转换签署文件错误: request: {}", JSONObject.toJSONString(request));
		}

		return response;
	}

	protected static XWPFDocument signatureFile(XWPFDocument document,
											  byte[] signatureBytes,
											  byte[] sealBytes,
											  String informOrg,
											  boolean useSignDate) throws IOException, InvalidFormatException {
		String signatureDate = "XXXX 年 XX 月 XX 日";
		if (useSignDate) {
			LocalDate now = LocalDate.now();
			signatureDate = String.format(DATE_FORMAT, now.getYear(), now.getMonthValue(), now.getDayOfMonth());
		}

		XWPFParagraph emptyParagraph = document.createParagraph();
		XWPFRun emptyRun = emptyParagraph.createRun();
		emptyRun.addBreak();

		CTSectPr sectPr = document.getDocument().getBody().getSectPr();
		CTPageSz pgSz = sectPr.getPgSz();
		BigInteger documentWidth = pgSz.getW();
		CTPageMar pgMar = sectPr.getPgMar();
		BigInteger left = Objects.isNull(pgMar) ? BigInteger.ZERO : pgMar.getLeft();
		BigInteger right = Objects.isNull(pgMar) ? BigInteger.ZERO : pgMar.getRight();
		documentWidth = documentWidth.subtract(left).subtract(right);

		XWPFTable table = document.createTable(2, 4);
		CTTbl ctTbl = table.getCTTbl();
		CTTblGrid tblGrid = ctTbl.getTblGrid();
		if (tblGrid == null) {
			table.getCTTbl().addNewTblGrid();
		}

		CTTblPr tableProperties = table.getCTTbl().getTblPr();
		tableProperties.unsetTblBorders();//无边框

		// 设置表格列宽为等宽
		List<XWPFTableRow> rows = table.getRows();
		BigInteger width = documentWidth.divide(BigInteger.valueOf(10L));
		for (int i = 0; i < rows.size(); i++) {
			XWPFTableRow currentRow = rows.get(i);
			List<XWPFTableCell> tableCells = currentRow.getTableCells();
			setRowHeight(table, i, 700);

			for (int j = 0; j < tableCells.size(); j++) {
				XWPFTableCell cell = tableCells.get(j);

				//宽度
				CTTcPr tcPr = cell.getCTTc().addNewTcPr();
				CTTblWidth widthType = tcPr.addNewTcW();
				widthType.setType(STTblWidth.DXA);
				if (j % 2 == 0) {
					widthType.setW(width);
				} else {
					widthType.setW(width.multiply(BigInteger.valueOf(4)));
				}

				XWPFParagraph paragraph = cell.getParagraphArray(0);
				if (paragraph == null) {
					paragraph = cell.addParagraph();
				}
				paragraph.setAlignment(ParagraphAlignment.CENTER);
				paragraph.setVerticalAlignment(TextAlignment.CENTER);
				paragraph.setFontAlignment(ParagraphAlignment.LEFT.getValue());
				XWPFRun run = paragraph.createRun();
				run.setFontSize(12);
				run.setFontFamily("仿宋_GB2312");
				run.setBold(true);


				if (i == 0) {
					if (j == 0 || j == 2) {
						run.setText(j == 0 ? "甲方:" : "乙方:");
					} else if (j == 1) {
						run.setText(informOrg);

						if (sealBytes != null && sealBytes.length > 0) {
							run.addPicture(new ByteArrayInputStream(sealBytes), XWPFDocument.PICTURE_TYPE_JPEG, "seal.png", Units.toEMU(118), Units.toEMU(118));
							CTDrawing drawing1 = run.getCTR().getDrawingArray(0);
							CTGraphicalObject graphicalobject1 = drawing1.getInlineArray(0).getGraphic();
							Random random = new Random();
							int number = random.nextInt(999) + 1;
							//拿到新插入的图片替换添加CTAnchor 设置浮动属性 删除inline属性
							CTAnchor anchor1 = getAnchorWithGraphic(graphicalobject1, "Seal" + number,
									Units.toEMU(140), Units.toEMU(140),//图片大小
									Units.toEMU(-20), Units.toEMU(-50), true);//相对当前段落位置及偏移
							drawing1.setAnchorArray(new CTAnchor[]{anchor1});//添加浮动属性
							drawing1.removeInline(0);//删除行内属性
						}
					} else {
						// 插入图片
						int format = XWPFDocument.PICTURE_TYPE_PNG;
						run.addPicture(new ByteArrayInputStream(signatureBytes), format, "签字", Units.toEMU(50), Units.toEMU(40));

					}
				} else if (i == 1) {
					if (j == 0 || j == 2) {
						run.setText("日期:");
					} else if (j == 1 || j == 3) {
						run.setText(signatureDate);
					}
				}

			}
		}

		return document;
	}

	private static void setRowHeight(XWPFTable table, int rowIndex, int height) {
		XWPFTableRow row = table.getRow(rowIndex);
		CTTrPr trPr = row.getCtRow().addNewTrPr();
		CTHeight h = trPr.addNewTrHeight();
		h.setVal(BigInteger.valueOf(height));
	}

	public static CTAnchor getAnchorWithGraphic(CTGraphicalObject ctGraphicalObject,
												String deskFileName, int width, int height,
												int leftOffset, int topOffset, boolean behind) {

		String anchorXML =
				"<wp:anchor xmlns:wp=\"http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing\" "
						+ "simplePos=\"0\" relativeHeight=\"0\" behindDoc=\"" + ((behind) ? 1 : 0) + "\" locked=\"0\" layoutInCell=\"1\" allowOverlap=\"1\">"
						+ "<wp:simplePos x=\"0\" y=\"0\"/>"
						+ "<wp:positionH relativeFrom=\"column\">"
						+ "<wp:posOffset>" + leftOffset + "</wp:posOffset>"
						+ "</wp:positionH>"
						+ "<wp:positionV relativeFrom=\"paragraph\">"
						+ "<wp:posOffset>" + topOffset + "</wp:posOffset>" +
						"</wp:positionV>"
						+ "<wp:extent cx=\"" + width + "\" cy=\"" + height + "\"/>"
						+ "<wp:effectExtent l=\"0\" t=\"0\" r=\"0\" b=\"0\"/>"
						+ "<wp:wrapNone/>"
						+ "<wp:docPr id=\"1\" name=\"Drawing 0\" descr=\"" + deskFileName + "\"/><wp:cNvGraphicFramePr/>"
						+ "</wp:anchor>";

		CTDrawing drawing = null;
		try {
			drawing = CTDrawing.Factory.parse(anchorXML);
		} catch (XmlException e) {
			e.printStackTrace();
		}
		CTAnchor anchor = drawing.getAnchorArray(0);
		anchor.setGraphic(ctGraphicalObject);
		return anchor;
	}
}
