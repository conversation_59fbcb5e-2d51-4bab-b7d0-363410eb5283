package com.huafon.service.impl.signature;

import cn.hutool.core.io.resource.ResourceUtil;
import com.alibaba.fastjson.JSONObject;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.PictureType;
import com.deepoove.poi.data.Pictures;
import com.deepoove.poi.xwpf.NiceXWPFDocument;
import com.huafon.api.dto.FileSignatureRequest;
import com.huafon.api.dto.FileSignatureResponse;
import com.huafon.service.AbstractFileSignatureService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-01-03 09:46
 **/
@Component
@Slf4j
public class IntrospectionSignatureServiceImpl extends AbstractFileSignatureService {

	@Override
	public FileSignatureResponse signature(FileSignatureRequest request) {
		FileSignatureResponse response = getDefaultResponse(request);

		try {
			byte[] templateBytes = getTemplateBytes(request.getSourceUrl());
			byte[] signatureBytes = getSignatureBytes(request.getSignatureUrl());
			InputStream signTemplateInputStream = ResourceUtil.getStream("template/introspectionTaskTemplate.docx");
			Map<String, Object> commonDataMap = request.getCommonDataMap();
			commonDataMap.put("signature", Pictures.ofStream(new ByteArrayInputStream(signatureBytes), PictureType.JPEG).size(100, 50).create());

			String signDocxFileName = request.getFilePrefix() + ".docx";
			XWPFTemplate template = XWPFTemplate.compile(signTemplateInputStream);
			template.render(commonDataMap);

			ByteArrayOutputStream renderSignatureResult = new ByteArrayOutputStream();
			template.writeAndClose(renderSignatureResult);
			ZipSecureFile.setMinInflateRatio(-1.0d);
			NiceXWPFDocument main = new NiceXWPFDocument(new ByteArrayInputStream(templateBytes));
			NiceXWPFDocument append = template.getXWPFDocument();

			NiceXWPFDocument document = main.merge(append);
			String signDocxUrl = saveFile(convertDocumentToByteArray(document), signDocxFileName);//合并后的word存在minio
			log.info("自省书签署：docx {}", signDocxUrl);
			response.setFileWordUrl(signDocxUrl);
			String signPdfUrl = convertToPdf(signDocxUrl, request.getFilePrefix());
			response.setSuccess(true);
			response.setFileName(request.getFilePrefix() + ".pdf");
			response.setFileUrl(signPdfUrl);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("转换签署文件错误: request: {}", JSONObject.toJSONString(request));
		}

		return response;
	}
}
