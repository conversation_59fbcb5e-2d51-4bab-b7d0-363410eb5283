package com.huafon.service;

import com.huafon.FileViewApplication;
import com.huafon.task.SignatureRetryTask;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * <AUTHOR>
 * @since 2024-01-04 15:19
 **/
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = FileViewApplication.class)
@Slf4j
public class SignatureRetryTaskTest {

	@Autowired
	private SignatureRetryTask retryTask;


	@Test
	public void test() {
		retryTask.execute();
	}
}
