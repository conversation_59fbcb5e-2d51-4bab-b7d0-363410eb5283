package com.huafon.service;

import cn.hutool.core.io.resource.ResourceUtil;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.PictureType;
import com.deepoove.poi.data.Pictures;
import com.huafon.FileViewApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-01-03 13:47
 **/
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = FileViewApplication.class)
@Slf4j
public class FileSignatureServiceTest {

	@Test
	public void test() throws IOException {
		InputStream signTemplateInputStream = ResourceUtil.getStream("template/responsibilityTemplate.docx");
		XWPFTemplate template = XWPFTemplate.compile(signTemplateInputStream);
		template.render(dataMap());

		template.write(new FileOutputStream("/Users/<USER>/Desktop/test.docx"));
	}


	private Map<String, Object> dataMap() {
		Map<String, Object> commonDataMap  = new HashMap<>();
		commonDataMap.put("year", "XXXX");
		commonDataMap.put("month", "XX");
		commonDataMap.put("day", "XX");
		commonDataMap.put("leaderPost", "领导岗位名称");
		commonDataMap.put("leaderSignature",  Pictures.ofStream(ResourceUtil.getStream("template/commonTemplateSign.jpg"), PictureType.JPEG).size(100, 50).create());

		commonDataMap.put("firstPostName", "责任岗位名称");
		commonDataMap.put("firstSignatureData", Pictures.ofStream(ResourceUtil.getStream("template/commonTemplateSign.jpg"), PictureType.JPEG).size(100, 50).create());
		List<Map<String, Object>> signatoryList = new ArrayList<>();
		for (int i = 0; i < 3; i++) {
			Map<String, Object> clerkSignInfo = new HashMap<>();
			clerkSignInfo.put("postName", "责任岗位名称");
			clerkSignInfo.put("signatureData", Pictures.ofStream(ResourceUtil.getStream("template/commonTemplateSign.jpg"), PictureType.JPEG).size(100, 50).create());
			signatoryList.add(clerkSignInfo);
		}

		commonDataMap.put("signatoryList", signatoryList);

		return commonDataMap;
	}

}
