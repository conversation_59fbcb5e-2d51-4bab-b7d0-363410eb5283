package com.huafon.license.apis.remote;

import com.huafon.license.apis.dto.StaffLicenseCalculateDTO;
import com.huafon.license.apis.dto.staffLicense.StaffLicenseAttachDTO;
import com.huafon.license.apis.dto.staffLicense.UserLicenseDTO;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023-02-27 14:26
 **/
public interface StaffLicenseRpcService {

	/**
	 * 查询用户的证书
	 * @param userIds
	 * @param tenantId 租户ID
	 * @param filterLicenseType
	 * @return
	 */
	List<UserLicenseDTO> queryUserLicense(Collection<Integer> userIds, Integer tenantId, Collection<Integer> filterLicenseType);


	/**
	 * 查询用户的证书
	 * @param userIds
	 * @param tenantId 租户ID
	 * @param filterLicenseType
	 * @return
	 */
	List<UserLicenseDTO> queryUserLicense(Collection<Integer> userIds, Integer tenantId, Collection<Integer> filterLicenseType, boolean ignoreExpired);
	/**
	 * 通过证书类型查询用户ID列表
	 * @param licenseTypeIds
	 * @return userIds
	 */
	List<Integer> queryUserByLicenseTypeId(Collection<Integer> licenseTypeIds);

	/**
	 * 通过证书类型查询用户ID列表
	 * @param licenseTypeIds
	 * @param all true: 全部要
	 * @return userIds
	 */
	List<Integer> queryUserByLicenseTypeId(Collection<Integer> licenseTypeIds, boolean all);

	/**
	 * 查询部门下内部颁发证书到期复审未复审人员
	 * @param query
	 * @return
	 */
	Set<Integer> calculateStaffLicense(StaffLicenseCalculateDTO query);

	/**
	 * 通过证书ID查询证书信息
	 * @param licenseIds
	 * @return
	 */
	List<StaffLicenseAttachDTO> queryByLicenseIds(Collection<Integer> licenseIds);
}
