package com.huafon.license.apis.dto.reminder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 公司岗位证书提醒配置
 * @date 2022/4/12 9:23
 */
@Data
@ApiModel(value="公司岗位证书提醒配置", description="")
public class JobReminderConfigQuery implements Serializable {
    @ApiModelProperty(value = "岗位id")
    private Integer jobId;
}
