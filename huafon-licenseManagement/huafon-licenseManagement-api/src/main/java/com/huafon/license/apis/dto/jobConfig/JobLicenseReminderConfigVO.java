package com.huafon.license.apis.dto.jobConfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 岗位证书关联配置
 * @date 2022/4/6 19:14
 */
@Data
@ApiModel(value = "岗位证书关联配置", description = "")
public class JobLicenseReminderConfigVO implements Serializable {
    private static final long serialVersionUID = -4946397754637622336L;
    @ApiModelProperty(value = "主键")
    private Integer id;

    @ApiModelProperty(value = "岗位配置id")
    private Integer jobReminderId;

    @ApiModelProperty(value = "证书类型id")
    private Integer licenseTypeId;

    @ApiModelProperty(value = "证书名称")
    private String licenseName;
}
