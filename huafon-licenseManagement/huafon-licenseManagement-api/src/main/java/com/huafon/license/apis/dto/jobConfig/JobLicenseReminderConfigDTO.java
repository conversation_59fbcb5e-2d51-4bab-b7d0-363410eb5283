package com.huafon.license.apis.dto.jobConfig;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 岗位证书关联配置
 * @date 2022/4/21 11:08
 */
@Data
public class JobLicenseReminderConfigDTO implements Serializable {
    private static final long serialVersionUID = 7393906271611606667L;

    @ApiModelProperty(value = "证书类型id")
    private Integer licenseTypeId;

    @ApiModelProperty(value = "证书名称")
    private String licenseName;
}
