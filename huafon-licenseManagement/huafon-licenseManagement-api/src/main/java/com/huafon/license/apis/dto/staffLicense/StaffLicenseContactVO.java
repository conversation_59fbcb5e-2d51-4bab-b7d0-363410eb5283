package com.huafon.license.apis.dto.staffLicense;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 联系人
 * @date 2022/4/6 16:10
 */
@Data
@ApiModel(value = "联系人vo", description = "")
public class StaffLicenseContactVO implements Serializable {
    private static final long serialVersionUID = 875662792705287476L;
    @ApiModelProperty(value = "主键")
    private Integer id;

    @ApiModelProperty(value = "联系人")
    private String contactName;

    @ApiModelProperty(value = "联系方式：1，短信，2邮件")
    private Integer contactType;

    @ApiModelProperty(value = "联系地址")
    private String contactAddress;
}
