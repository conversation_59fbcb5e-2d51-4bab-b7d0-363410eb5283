package com.huafon.license.apis.dto.staffLicense;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 证书统计信息
 * <AUTHOR>
 * @since 2022-07-22 15:01
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "员工证书统计")
public class StaffLicenseStatisticVO {
	@ApiModelProperty(value = "缺失数量")
	private Integer missing = 0;

	@ApiModelProperty(value = "已经到期证书")
	private Integer expired = 0;

	@ApiModelProperty(value = "即将到期证书")
	private Integer willExpire = 0;

	@ApiModelProperty(value = "已过复审日期")
	private Integer reviewed = 0;

	@ApiModelProperty(value = "即将复审")
	private Integer willReview = 0;

}
