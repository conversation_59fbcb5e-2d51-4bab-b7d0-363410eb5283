package com.huafon.license.apis.dto.excel;

import com.huafon.license.apis.query.PageSo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2022-10-18 09:26
 **/
@Data
@ApiModel(value = "excel导出")
public class ExcelQuery<T extends PageSo> {

	@ApiModelProperty(value = "查询条件")
	@NotNull(message = "导出查询条件不能为空")
	private T parameter;

	@ApiModelProperty(value = "导出列表")
	@NotEmpty(message = "导出列表头信息不能为空")
	private Set<String> headList;
}
