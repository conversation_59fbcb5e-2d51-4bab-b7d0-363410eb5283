package com.huafon.license.apis.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023-07-11 10:46
 **/
@Getter
public enum EnumLicenseStatusFilter {

	NORMAL(1, "正常"),
	EXPIRING(2, "即将过期/复审"),
	EXPIRED(3,"已过期/需复审"),
	EFFECTIVE(4, "长期有效"),
	INVALIDATION(5, "已失效"),
	;

	private final int code;
	private final String description;

	EnumLicenseStatusFilter(int code, String description) {
		this.code = code;
		this.description = description;
	}

	public static EnumLicenseStatusFilter parseEnumLicenseStatusFilter(int code) {
		for (EnumLicenseStatusFilter type : EnumLicenseStatusFilter.values()) {
			if (type.getCode() == code) {
				return type;
			}
		}
		return null;
	}
}
