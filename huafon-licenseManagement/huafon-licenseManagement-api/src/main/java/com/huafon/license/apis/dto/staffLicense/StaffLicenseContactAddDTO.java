package com.huafon.license.apis.dto.staffLicense;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 员工证书联系人
 * @date 2022/4/6 14:53
 */
@Data
@ApiModel(value = "员工证书新增联系人", description = "")
public class StaffLicenseContactAddDTO implements Serializable {
    private static final long serialVersionUID = -310244572214003336L;

    @ApiModelProperty(value = "联系人")
    @NotNull(message = "联系人不能为空")
    private String contactName;

    @ApiModelProperty(value = "联系方式：1，短信，2邮件")
    @NotNull(message = "联系方式不能为空")
    private Integer contactType;

    @ApiModelProperty(value = "联系地址")
    @NotNull(message = "联系地址不能为空")
    private String contactAddress;
}
