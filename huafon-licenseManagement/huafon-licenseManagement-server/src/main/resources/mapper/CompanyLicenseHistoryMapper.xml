<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huafon.license.mapper.CompanyLicenseHistoryMapper">

  <!--  &lt;!&ndash; 通用查询映射结果 &ndash;&gt;
    <resultMap id="BaseResultMap" type="com.huafon.license.entity.CompanyLicenseHistory">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="license_type_id" property="licenseTypeId" />
        <result column="license_no" property="licenseNo" />
        <result column="issuing_department" property="issuingDepartment" />
        <result column="license_date" property="licenseDate" />
        <result column="remark" property="remark" />
        <result column="expire_date" property="expireDate" />
        <result column="validity_period" property="validityPeriod" />
        <result column="new_license_id" property="newLicenseId" />
        <result column="reason" property="reason" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="modify_by" property="modifyBy" />
        <result column="modify_time" property="modifyTime" />
        <result column="is_del " property="isDel " />
    </resultMap>

    &lt;!&ndash; 通用查询结果列 &ndash;&gt;
    <sql id="Base_Column_List">
        id, company_id, license_type_id, license_no, issuing_department, license_date, remark, expire_date, validity_period, new_license_id, reason, create_by, create_time, modify_by, modify_time, is_del 
    </sql>-->

</mapper>
