<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huafon.license.mapper.CompanyLicenseReminderMapper">

    <!-- 通用查询映射结果 -->
    <!--    <resultMap id="BaseResultMap" type="com.huafon.license.entity.CompanyLicenseReminder">-->
    <!--        <id column="id" property="id" />-->
    <!--        <result column="license_id" property="licenseId" />-->
    <!--        <result column="license_name" property="licenseName" />-->
    <!--        <result column="company_name" property="companyName" />-->
    <!--        <result column="license_date" property="licenseDate" />-->
    <!--        <result column="has_remaind" property="hasRemaind" />-->
    <!--        <result column="validity_period" property="validityPeriod" />-->
    <!--        <result column="expire_date" property="expireDate" />-->
    <!--        <result column="create_by" property="createBy" />-->
    <!--        <result column="create_time" property="createTime" />-->
    <!--        <result column="modify_by" property="modifyBy" />-->
    <!--        <result column="modify_time" property="modifyTime" />-->
    <!--        <result column="is_del " property="isDel " />-->
    <!--    </resultMap>-->

    <!--    &lt;!&ndash; 通用查询结果列 &ndash;&gt;-->
    <!--    <sql id="Base_Column_List">-->
    <!--        id, license_id, license_name, company_name, license_date, has_remaind, validity_period, expire_date, create_by, create_time, modify_by, modify_time, is_del -->
    <!--    </sql>-->

    <select id="selectPage" resultType="com.huafon.license.apis.dto.reminder.CompanyLicenseReminderVO">
        select
        t.name licenseName,
        h.id id,
        h.license_id licenseId,
        h.company_name companyName,
        h.license_date licenseDate,
        h.state state,
        h.expire_date expireDate,
        h.company_id companyId,
        s.license_no licenseNo,
        h.validity_period validityPeriod,
        c.id licenseConfigId,
        c.name licenseConfigName
        from
        hf_company_license_reminder h
        left join
        hf_company_license s
        on h.license_id = s.id
        left join
        hf_license_type t on s.license_type_id = t.id
        LEFT JOIN hf_license_config c ON t.license_config_id = c.id
        where h.is_del=0
        <if test="query.tenantIds != null and query.tenantIds.size()>0">
            and h.tenant_id in
            <foreach collection="query.tenantIds" item="item" index="index"
                     open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.key != null and query.key !=''">
            and (
                t.name like concat('%',#{query.key},'%')
                OR c.name like concat('%',#{query.key},'%')
            )
        </if>
        <if test="query.state != null">
            and h.state=#{query.state}
        </if>
        <if test="query.companyId != null">
            and h.company_id =#{query.companyId}
        </if>
        <choose>
            <when test="query.orders != null and query.orders.size() >0">
                ORDER BY
                <foreach collection="query.orders" item="item" separator=",">
                    h.${item.column} <choose><when test="item.asc">ASC</when><otherwise>DESC</otherwise></choose>
                </foreach>
            </when>
            <otherwise>
                order by h.expire_date ASC, h.id DESC
            </otherwise>
        </choose>
        <!--limit #{pageSize} offset #{start}-->
    </select>


    <!--<select id="getCount" resultType="java.lang.Integer"
            parameterType="com.huafon.license.apis.dto.reminder.CompanyLicenseReminderQuery">
        select
        count(*)
        from
        hf_company_license_reminder h
        left join
        hf_company_license s
        on h.license_id = s.id
        left join
        hf_license_type t
        on s.license_type_id = t.id
        where h.is_del=0
        and h.has_reminder =1
        <if test="tenantIds != null and tenantIds.size()>0">
            and h.tenant_id in
            <foreach collection="tenantIds" item="id" index="index"
                     open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="key != null">
            and t.name like concat('%',#{key},'%')
        </if>
        <if test="state != null">
            and h.state=#{state}
        </if>
        <if test="companyId != null">
            and h.company_id =#{companyId}
        </if>

    </select>-->

</mapper>
