<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huafon.license.mapper.CompanyLicenseMapper">

    <!-- 通用查询映射结果 -->
<!--    <resultMap id="BaseResultMap" type="com.huafon.license.entity.CompanyLicense">-->
<!--        <id column="id" property="id" />-->
<!--        <result column="company_id" property="companyId" />-->
<!--        <result column="license_type_id" property="licenseTypeId" />-->
<!--        <result column="license_no" property="licenseNo" />-->
<!--        <result column="issuing_department" property="issuingDepartment" />-->
<!--        <result column="license_date" property="licenseDate" />-->
<!--        <result column="remark" property="remark" />-->
<!--        <result column="expire_date" property="expireDate" />-->
<!--        <result column="validity_period" property="validityPeriod" />-->
<!--        <result column="create_by" property="createBy" />-->
<!--        <result column="create_time" property="createTime" />-->
<!--        <result column="modify_by" property="modifyBy" />-->
<!--        <result column="modify_time" property="modifyTime" />-->
<!--        <result column="is_del " property="isDel " />-->
<!--    </resultMap>-->

<!--    &lt;!&ndash; 通用查询结果列 &ndash;&gt;-->
<!--    <sql id="Base_Column_List">-->
<!--        id, company_id, license_type_id, license_no, issuing_department, license_date, remark, expire_date, validity_period, create_by, create_time, modify_by, modify_time, is_del -->
<!--    </sql>-->

    <select id="selectPage" resultType="com.huafon.license.apis.dto.companyLicense.CompanyLicenseVO">
        SELECT
        t.name licenseName,
        s.ID ID,
        s.license_type_id licenseTypeId,
        s.issuing_department issuingDepartment,
        s.company_id companyId,
        s.license_no licenseNo,
        s.license_date licenseDate,
        s.expire_date expireDate,
        s.validity_period validityPeriod,
        s.remark remark,
        s.has_permanent hasPermanent,
        t.expiration_reminder_days expirationReminderDays,
        s.company_name companyName,
        c.id AS licenseConfigId,
        c.name AS                  licenseConfigName
        FROM
        hf_company_license s
        LEFT JOIN hf_license_type t ON s.license_type_id = T.ID
        LEFT JOIN hf_license_config c ON t.license_config_id = c.id
        WHERE
        s.is_del =0
        <if test="query.tenantIds != null and query.tenantIds.size()>0">
            and s.tenant_id in
            <foreach collection="query.tenantIds" item="item" index="index"
                     open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.key != null and query.key !=''">
            and (
            t.name like concat('%',#{query.key},'%')
            or s.license_no like concat('%',#{query.key},'%')
            or c.name like concat('%',#{query.key},'%')
            )
        </if>

        <if test="query.companyId != null">
            and s.company_id =#{query.companyId}
        </if>
        <if test="query.startLicenseDate != null and query.endLicenseDate != null">
            AND s.license_date between #{query.startLicenseDate} and #{query.endLicenseDate}
        </if>
        <if test="query.today != null">
            <choose>
                <when test="query.expireFilter != null">
                    <choose>
                        <when test="query.expireFilter == @com.huafon.license.apis.enums.EnumLicenseStatusFilter@EFFECTIVE">
                            AND s.has_permanent = ${@com.huafon.license.apis.constants.YesNoConstant@YES}
                        </when>
                        <otherwise>
                            AND s.has_permanent = ${@com.huafon.license.apis.constants.YesNoConstant@NO}
                            <choose>
                                <when test="query.expireFilter == @com.huafon.license.apis.enums.EnumLicenseStatusFilter@NORMAL">
                                    AND s.expire_date - #{query.today} - 1 &gt; t.expiration_reminder_days
                                </when>
                                <when test="query.expireFilter == @com.huafon.license.apis.enums.EnumLicenseStatusFilter@EXPIRING">
                                    AND s.expire_date - #{query.today} &gt;= 0 AND s.expire_date - #{query.today} - 1 &lt;= t.expiration_reminder_days
                                </when>
                                <when test="query.expireFilter == @com.huafon.license.apis.enums.EnumLicenseStatusFilter@EXPIRED">
                                    AND s.expire_date - #{query.today} &lt; 0
                                </when>
                            </choose>
                        </otherwise>
                    </choose>
                </when>
            </choose>
        </if>
        order by s.id desc
<!--        limit #{pageSize} offset #{start}-->
    </select>


<!--    <select id="getCount" resultType="java.lang.Integer"-->
<!--            parameterType="com.huafon.license.apis.dto.companyLicense.CompanyLicenseQuery">-->
<!--        select-->
<!--        count(*)-->
<!--        from hf_company_license s-->
<!--        left join-->
<!--        hf_license_type t on s.license_type_id = t.id-->
<!--        where s.is_del=0-->
<!--        <if test="tenantIds != null and tenantIds.size()>0">-->
<!--            and s.tenant_id in-->
<!--            <foreach collection="tenantIds" item="id" index="index"-->
<!--                     open="(" close=")" separator=",">-->
<!--                #{id}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        <if test="key != null">-->
<!--            and (-->
<!--            t.name like concat('%',#{key},'%')-->
<!--            or s.license_no like concat('%',#{key},'%')-->
<!--            )-->
<!--        </if>-->

<!--        <if test="companyId != null">-->
<!--            and s.company_id =#{companyId}-->
<!--        </if>-->

<!--    </select>-->

</mapper>
