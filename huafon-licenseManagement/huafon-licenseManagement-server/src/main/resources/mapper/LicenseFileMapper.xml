<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huafon.license.mapper.LicenseFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huafon.license.entity.LicenseFile">
        <id column="id" property="id" />
        <result column="staff_license_id" property="staffLicenseId" />
        <result column="file_url" property="fileUrl" />
        <result column="file_type" property="fileType" />
        <result column="license_type" property="licenseType" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="modify_by" property="modifyBy" />
        <result column="modify_time" property="modifyTime" />
        <result column="is_del " property="isDel " />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, staff_license_id, file_url, file_type, license_type, create_by, create_time, modify_by, modify_time, is_del 
    </sql>

</mapper>
