package com.huafon.license.service;

import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.framework.mybatis.service.MybatisIService;
import com.huafon.license.dto.AttachmentDTO;
import com.huafon.license.dto.StaffLicenseReviewHistoryDownloadDTO;
import com.huafon.license.entity.StaffLicense;
import com.huafon.license.entity.StaffLicenseReviewHistory;
import com.huafon.license.models.query.StaffLicenseReviewHistoryPageQuery;
import com.huafon.license.models.vo.StaffLicenseReviewHistoryCommonVo;
import com.huafon.license.models.vo.StaffLicenseReviewHistoryVo;

import java.util.Date;
import java.util.List;

/**
* 员工证书复审记录
* <AUTHOR>
* @since 2024-03-11 15:52
*/
public interface StaffLicenseReviewHistoryService extends MybatisIService<StaffLicenseReviewHistory> {

    /**
     * 分页
     */
    CommonPage<StaffLicenseReviewHistoryCommonVo> commonPage(StaffLicenseReviewHistoryPageQuery query);

    /**
     * 下载
     * @param query
     * @return
     */
    List<StaffLicenseReviewHistoryDownloadDTO> download(StaffLicenseReviewHistoryPageQuery query);

    StaffLicenseReviewHistoryVo getInfoById(Integer id);

    boolean hasReviewed(Integer licenseId);

    StaffLicenseReviewHistory queryRecentReviewHistory(Integer licenseId);

    void review(StaffLicense license, Date lastReviewDate, List<AttachmentDTO> attachments);

}
