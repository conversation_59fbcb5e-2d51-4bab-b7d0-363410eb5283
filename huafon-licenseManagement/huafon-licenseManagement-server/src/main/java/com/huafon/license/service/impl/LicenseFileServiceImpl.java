package com.huafon.license.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Sets;
import com.huafon.common.config.TenantContext;
import com.huafon.license.apis.enums.EnumFileState;
import com.huafon.license.entity.LicenseFile;
import com.huafon.license.mapper.LicenseFileMapper;
import com.huafon.license.service.LicenseFileService;
import com.huafon.license.utils.ConvertUtils;
import com.huafon.support.config.UserContext;
import com.huafon.support.exceptions.ServiceException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 证书附件 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
@Service
public class LicenseFileServiceImpl extends ServiceImpl<LicenseFileMapper, LicenseFile> implements LicenseFileService {
    /**
     * 根据证书Id删除
     *
     * @param licenseId
     * @param type
     */
    @Override
    @Transactional
    public void deleteByLicenseId(Integer licenseId, int type) {
        QueryWrapper<LicenseFile> wrapper = new QueryWrapper<>();
        wrapper.lambda().
                eq(LicenseFile::getLicenseId, licenseId).
                eq(LicenseFile::getLicenseType, type);
        this.remove(wrapper);
    }

    /**
     * 根据证书id删除
     *
     * @param ids
     * @param type
     */
    @Override
    @Transactional
    public void deleteByLicenseIds(List<Integer> ids, int type) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException("删除失败，批量删除id不能为空。");
        }
        QueryWrapper<LicenseFile> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(LicenseFile::getLicenseId, ids)
                .eq(LicenseFile::getLicenseType, type);
        remove(wrapper);
    }

    /**
     * @param licenseId
     * @param type
     * @param state
     * @return
     */
    @Override
    @Transactional(readOnly = true)
    public List<LicenseFile> getListByLicenseId(Integer licenseId, int type, int state) {
        QueryWrapper<LicenseFile> wrapper = new QueryWrapper<>();
        wrapper.lambda().
                eq(LicenseFile::getLicenseId, licenseId).
                eq(LicenseFile::getLicenseType, type).
                eq(LicenseFile::getState, state);
        return this.list(wrapper);
    }

    @Override
    public List<LicenseFile> getListByLicenseIdByTypeList(Integer licenseId, List<Integer> types, int state) {
        QueryWrapper<LicenseFile> wrapper = new QueryWrapper<>();
        wrapper.lambda().
                eq(LicenseFile::getLicenseId, licenseId)
                .in(LicenseFile::getLicenseType, types)
                .eq(LicenseFile::getState, state);
        return this.list(wrapper);
    }

    @Override
    public List<LicenseFile> getListByLicenseIdByTypeList(List<Integer> licenseIds, List<Integer> types, int state) {
        if (CollectionUtils.isEmpty(licenseIds)) {
            return Collections.emptyList();
        }
        QueryWrapper<LicenseFile> wrapper = new QueryWrapper<>();
        wrapper.lambda().
                in(LicenseFile::getLicenseId, licenseIds)
                .in(LicenseFile::getLicenseType, types)
                .eq(LicenseFile::getState, state);
        return this.list(wrapper);
    }

    /**
     * 过期文件
     *
     * @param licenseId
     * @param type
     */
    @Override
    @Transactional
    public void expireLicenseFile(Integer licenseId, int type, Long userId) {
        QueryWrapper<LicenseFile> wrapper = new QueryWrapper<>();
        wrapper.lambda().
                eq(LicenseFile::getLicenseId, licenseId).
                eq(LicenseFile::getLicenseType, type).
                eq(LicenseFile::getState, EnumFileState.FILE_USE.getCode());
        List<LicenseFile> files = this.list(wrapper);
        List<LicenseFile> expireFiles = files.stream().map(e -> {
            e.setState(EnumFileState.FILE_EXPIRED.getCode());
            e.setModify(userId);
            return e;
        }).collect(Collectors.toList());
        updateBatchById(expireFiles);
    }

    /**
     * 新增文件
     *
     * @param licenseId
     * @param type
     * @param files
     */
    @Override
    @Transactional
    public void batchSave(Integer licenseId, int type, List<LicenseFile> files, Integer tenantId) {
        if (CollectionUtils.isEmpty(files)) {
            return;
        }
        Long userId = UserContext.getId();
        List<LicenseFile> licenseFiles = files.stream().map(e -> {
            LicenseFile file = new LicenseFile();
            ConvertUtils.copy(e, file);
            file.setLicenseId(licenseId);
            file.setCreate(userId);
            file.setLicenseType(type);
            file.setTenantId(tenantId);
            file.setState(EnumFileState.FILE_USE.getCode());
            return file;
        }).collect(Collectors.toList());
        saveBatch(licenseFiles);
    }


    /**
     * 批量处理更新文件
     *
     * @param licenseId
     * @param type
     * @param files
     */
    @Override
    @Transactional
    public void batchUpdate(Integer licenseId, int type, List<LicenseFile> files) {
        //找出新增的   和删除的
        QueryWrapper<LicenseFile> wrapper = new QueryWrapper<>();
        wrapper.lambda().
                eq(LicenseFile::getLicenseId, licenseId).
                eq(LicenseFile::getLicenseType, type).
                eq(LicenseFile::getState, EnumFileState.FILE_USE.getCode());

        List<LicenseFile> filesDb = this.list(wrapper);
        Long userId = UserContext.getId();
        Set<Integer> oIds = filesDb.stream().map(LicenseFile::getId).collect(Collectors.toSet());
        Set<Integer> ids = files.stream().filter(e -> Objects.nonNull(e.getId())).map(LicenseFile::getId).collect(Collectors.toSet());
        Set<Integer> deletes = Sets.difference(oIds, ids);//删除的
        Integer tenantId = TenantContext.getOne();
        List<LicenseFile> saves = files.stream().filter(e -> Objects.isNull(e.getId())).map(e -> {
            e.setCreate(userId);
            e.setLicenseId(licenseId);
            e.setLicenseType(type);
            e.setState(EnumFileState.FILE_USE.getCode());
            e.setTenantId(tenantId);
            return e;
        }).collect(Collectors.toList());//新增
        saveBatch(saves);
        if (!CollectionUtils.isEmpty(deletes)) {
            removeByIds(deletes);
        }
    }
}
