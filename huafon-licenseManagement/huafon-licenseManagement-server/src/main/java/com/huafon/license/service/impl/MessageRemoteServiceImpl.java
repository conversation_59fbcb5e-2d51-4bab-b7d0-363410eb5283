package com.huafon.license.service.impl;

import com.alibaba.fastjson.JSON;
import com.huafon.api.dto.MessageSendDTO;
import com.huafon.api.service.MessageConfigRpcService;
import com.huafon.api.service.MultiPushMsgRpcService;
import com.huafon.api.vo.MessageConfigInfoVo;
import com.huafon.api.vo.MessageConfigRecipientInfoVo;
import com.huafon.api.vo.MessageConfigRequest;
import com.huafon.license.service.MessageRemoteService;
import com.huafon.support.exceptions.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023-09-21 16:24
 **/
@Slf4j
@Component
public class MessageRemoteServiceImpl implements MessageRemoteService {

	public static StackTraceElement getFirstStackInfo(StackTraceElement[] stackTraceElements) {
		if (stackTraceElements == null || stackTraceElements.length == 0) {
			return null;
		}
		return stackTraceElements[0];
	}

	@DubboReference
	private MessageConfigRpcService messageConfigRpcService;

	@DubboReference
	private MultiPushMsgRpcService messagePushRpcService;

	@Override
	public List<MessageConfigInfoVo> query(MessageConfigRequest.Query query) {
		try{
			if (Objects.nonNull(query)) {
				return messageConfigRpcService.query(query);
			}
		} catch(Exception e) {
			e.printStackTrace();
			log.error("RPC查询消息配置错误: message: {}, stackTrace: {}, query: {}", e.getMessage(), getFirstStackInfo(e.getStackTrace()), JSON.toJSONString(query));
		}
		throw new ServiceException("RPC查询消息配置错误");
	}

	@Override
	public MessageConfigInfoVo add(MessageConfigInfoVo info) {
		try{
			if (Objects.nonNull(info)) {
				return messageConfigRpcService.add(info);
			}
		} catch(Exception e) {
			log.error("RPC添加消息配置错误: message: {}, stackTrace: {}, info: {}", e.getMessage(), getFirstStackInfo(e.getStackTrace()), JSON.toJSONString(info));
		}
		return null;
	}

	@Override
	public boolean batchSend(List<MessageSendDTO> messages) {
		try{
			if (!CollectionUtils.isEmpty(messages)) {
				return messagePushRpcService.batchSend(messages);
			}
		} catch(Exception e) {
			log.error("RPC发送消息错误: message: {}, stackTrace: {}", e.getMessage(), getFirstStackInfo(e.getStackTrace()));
			return false;
		}
		return true;
	}


	@Override
	public List<MessageConfigRecipientInfoVo> getRecipientById(Integer configId) {
		try{
			return messageConfigRpcService.getRecipientById(configId);
		} catch(Exception e) {
			log.error("RPC查询接收对象错误: {}, config: {}", e.getMessage(), configId);
		}
		return Collections.emptyList();
	}
}
