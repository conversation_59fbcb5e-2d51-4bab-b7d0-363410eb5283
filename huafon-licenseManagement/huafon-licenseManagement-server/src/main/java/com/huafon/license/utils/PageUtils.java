/**************************************************************************
 * Copyright (c) 2006-2025 ZheJiang Electronic Port, Inc.
 * All rights reserved.
 *
 * 项目名称：世晨2.0
 * 版权说明：本软件属浙江电子口岸有限公司所有，在未获得浙江电子口岸有限公司正式授权
 *           情况下，任何企业和个人，不能获取、阅读、安装、传播本软件涉及的任何受知
 *           识产权保护的内容。
 ***************************************************************************/
package com.huafon.license.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 分页工具类.
 * <AUTHOR>
 * @version $Id: PageUtils.java  2020/10/26 4:15 下午
 * @since 2.0
 */
public class PageUtils {
    /** logger. */
    private static Logger log = LoggerFactory.getLogger(PageUtils.class);


    /**
     * 方便的将分页查询对象从头DO->VO.
     * @param page page 对象
     * @param rClass 需要 转的对象 的 class
     * @param <T> page 的范型参数 对象 --> DO
     * @param <R> 需要替换的范型参数对象 必须要有无参数的构造器 --> VO
     * <AUTHOR>
     * @return IPage
     */
    public static <T, R> IPage<R> pageVO(IPage<T> page, Class<R> rClass) {
        if (rClass == null) {
            return null;
        }
        IPage<R> pageVO = new Page<>(page.getCurrent(), page.getSize());
        List<R> result = new ArrayList<>();
        if(!page.getRecords().isEmpty()) {
            for (T t : page.getRecords()) {
                R r = null;
                try {
                    r = rClass.newInstance();
                } catch (InstantiationException | IllegalAccessException e) {
                    log.error("传入 {} 对象是实例化失败了,异常信息：{}", rClass, e.getMessage());
                    throw new RuntimeException(e);
                }
                BeanUtils.copyProperties(t, r);
                result.add(r);
            }
        }
        pageVO.setTotal(page.getTotal());
        pageVO.setPages(page.getPages());
        pageVO.setRecords(result);
        return pageVO;
    }

}
