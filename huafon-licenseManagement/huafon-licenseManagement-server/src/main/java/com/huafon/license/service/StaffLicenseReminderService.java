package com.huafon.license.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.license.apis.dto.IdDTO;
import com.huafon.license.apis.dto.MapQueryBaseDTO;
import com.huafon.license.apis.dto.reminder.*;
import com.huafon.license.apis.dto.staffLicense.StaffLicenseStatisticVO;
import com.huafon.license.apis.dto.staffLicense.StaffLicenseUpdateDTO;
import com.huafon.license.dto.LicenseRemindDTO;
import com.huafon.license.dto.SendMessageDTO;
import com.huafon.license.entity.StaffLicenseReminder;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 员工证书到期提醒 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
public interface StaffLicenseReminderService extends IService<StaffLicenseReminder> {
    /**
     * 员工证书到期提醒
     *
     * @param query
     * @return
     */
    CommonPage<StaffLicenseReminderVO> getStaffReminderPage(StaffLicenseReminderQuery query);

    /**
     * 员工提醒统计
     * @param query
     * @return
     */
    LicenseReminderStatisticVO getStaffReminderStatistic(StaffLicenseReminderStatisticQuery query);

    /**
     * 取消员工证书到期提醒
     *
     * @param dto
     */
    void cancelStaffReminder(IdDTO dto);

    /**
     * 根据证书ID删除
     *
     * @param licenseId
     */
    void deleteByLicenseId(Integer licenseId, Integer type);

    /**
     * 根据证书ID删除
     *
     * @param licenseIds
     */
    void deleteByLicenseIds(List<Integer> licenseIds);

    /**
     * 根据证书查询
     *
     * @return
     */
    StaffLicenseReminder getByLicenseId(Integer licenseId, Integer type);

    /**
     * 执行过期提醒
     */
    void processStaffLicenseInvalidReminder();

    /**
     * 执行复审提醒
     */
    void processStaffLicenseReviewReminder();

    /**
     * 更新过期状态
     */
    void processState();

    /**
     * 统计员工到期证书数量
     *
     * @param query
     */
    List<Map<String, Object>> countByUserId(CountStaffQuery query);


    /**
     * 新增证书提醒
     * @param dto
     */
    void syncLicense(StaffLicenseUpdateDTO dto);

    /**
     * 获取员工证书统计信息
     */
    StaffLicenseStatisticVO statistic(List<Integer> tenants);

    /**
     * 查询一张图统计信息
     * @param query
     * @return
     */
    StaffLicenseStatisticVO statistic(MapQueryBaseDTO query);

    /**
     * 获取证书提醒数量
     * @param type 1.到期提醒，2待审提醒
     * @param tenants 租户列表
     * @return
     */
    Integer statistic(Integer type, List<Integer> tenants);


    void staffExpiredOrReview(SendMessageDTO messageDTO);


    LicenseRemindDTO expireRemindCount();
}
