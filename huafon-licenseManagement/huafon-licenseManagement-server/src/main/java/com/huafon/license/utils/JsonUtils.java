package com.huafon.license.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * Created by hypnos.xu on 2018/1/22.
 */
@Slf4j
public class JsonUtils {
    final static ObjectMapper mapper = new ObjectMapper();
    private static final String ERROR_CODE = "1036";
    private static final String ERROR_MES ="json解析异常";
    public final static<T> String of (T o, final String defaultvalue){
        try {
            return mapper.writeValueAsString(o);
        }catch (Exception e){
            log.error("Write object to JSON string failed: {}", e);
            throw new RuntimeException(ERROR_MES);
        }

    }

    public final static<T> byte[] of (T o) {
        try {
            return mapper.writeValueAsBytes(o);
        }catch (Exception e){
            log.warn("Write object to JSON bytes failed: {}", e);
            throw new RuntimeException(ERROR_MES);
        }

    }

    /**
     * 反序列化一般对象
     * @param value
     * @param clazz
     * @param <T>
     * @return
     */
    public final static<T> T from (final String value, Class<T> clazz) {
        try {
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            return mapper.readValue(value, clazz);
        } catch (IOException e) {
            log.warn("Read object from string failed: {}/{}/{}", value, clazz, e);
            throw new RuntimeException(ERROR_MES);
        }
    }

    /**
     * 反序列化list
     * @param value
     * @param  bean 泛型类
     * @param <T>
     * @return
     */
    public final static<T> T from2List (final String value, Class bean) {
        try {
            JavaType javaType  = mapper.getTypeFactory().constructParametricType(List.class, bean);
            return  mapper.readValue(value, javaType);
        } catch (IOException e) {
            log.warn("from2List Read object from string failed: {}/{}/{}", value, e);
            throw new RuntimeException(ERROR_MES);
        }
    }

    /**
     * 反序列化Map
     * @param value
     * @param  bean 泛型类
     * @param <T>
     * @return
     */
    public final static<T> T from2Map (final String value, Class bean) {
        try {
            JavaType javaType  = mapper.getTypeFactory().constructParametricType(Map.class, bean);
            return  mapper.readValue(value, javaType);
        } catch (IOException e) {
            log.warn("from2Map Read object from string failed: {}/{}/{}", value, e);
            throw new RuntimeException(ERROR_MES);
        }
    }


    public final static<T> T from (final byte[] bytes, Class<T> clazz) {
        try {
            return mapper.readValue(bytes, clazz);
        } catch (IOException e) {
            log.warn("Read object from bytes failed: {}/{}", clazz, e);
            throw new RuntimeException(ERROR_MES);
        }
    }

    public final static<T> T from (final InputStream is, Class<T> clazz)  {
        try {
            return mapper.readValue(is, clazz);
        } catch (IOException e) {
            log.warn("Read object from bytes failed: {}/{}", clazz, e);
            throw new RuntimeException(ERROR_MES);
        }

    }
    public final static String toJSONString(Object o){
        return JSONObject.toJSONString(o);
    }

    /**
     * 转化json数组
     * @param list
     * @return
     */
    public final static JSONArray parseArray(List list){
       return JSONArray.parseArray(JSON.toJSONString(list));
    }



    public final static StringBuffer e (final StringBuffer buf, final String key, final String value) {
        buf.append("\"").append(key).append("\":");
        if (null == value)
            return buf.append("null");
        else
            return buf.append('\"').append(value).append('\"');
    }

    public final static StringBuffer e (final StringBuffer buf, final String key, final Object value) {
        buf.append("\"").append(key).append("\":");
        if (null == value)
            return buf.append("null");
        else
            return buf.append(value.toString());
    }
}
