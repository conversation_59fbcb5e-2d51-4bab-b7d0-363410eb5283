package com.huafon.license.service;

import com.huafon.portal.api.dto.dept.DeptDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022-11-16 13:52
 **/
public interface DepartmentRemoteService {

	List<Integer> getChildIds(Integer var1);


	Map<Integer, List<Integer>> getChildIds(List<Integer> deptIds);

	/**
	 * 部门汲联
	 * @param var1
	 * @return
	 */
	DeptDto getNodeById(Integer var1);

	/**
	 * 部门汲联
	 */
	Map<Integer, String> getDepartmentMappings(List<Integer> var1, boolean simple);

	/**
	 * 部门名称
	 */
	Map<Integer, String> getDepartmentSimpleNameMappings(List<Integer> var1);

	DeptDto getDepartmentById(Integer departmentId);

	String getDepartmentNameById(Integer departmentId, boolean simple);

	Map<Integer, String> getDepartmentNodeNameMappings(List<Integer> var1);

	/**
	 * 处理子部门问题
	 * @param rootDept
	 * @return
	 */
	List<Integer> processSubDeptIds(Integer rootDept);

	/**
	 * 获取租户下的所有组织机构
	 *
	 * @param tenantId
	 * @return
	 */
	List<DeptDto> getByTenantId(Integer tenantId,Boolean includeTop);
}
