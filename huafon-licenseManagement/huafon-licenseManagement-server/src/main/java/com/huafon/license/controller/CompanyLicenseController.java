package com.huafon.license.controller;

import com.huafon.admin.api.annotation.OperationLog;
import com.huafon.admin.api.enums.OperationLogType;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.license.apis.dto.IdDTO;
import com.huafon.license.apis.dto.companyLicense.CompanyLicenseAddDTO;
import com.huafon.license.apis.dto.companyLicense.CompanyLicenseQuery;
import com.huafon.license.apis.dto.companyLicense.CompanyLicenseUpdateDTO;
import com.huafon.license.apis.dto.companyLicense.CompanyLicenseVO;
import com.huafon.license.apis.dto.companyLicenseHistory.CompanyLicenseHistoryDTO;
import com.huafon.license.config.IgnoreAopLog;
import com.huafon.license.dto.LicenseCountDTO;
import com.huafon.license.service.CompanyLicenseService;
import com.huafon.support.core.pojo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description: 企业证书
 * @date 2022/4/6 15:06
 */
@Api(tags = "企业证书")
@RequestMapping("/company")
@RestController
public class CompanyLicenseController {
    @Autowired
    private CompanyLicenseService companyLicenseService;

    @PostMapping("/add")
    @ApiOperation(value = "新增")
    public R add(@RequestBody @Validated CompanyLicenseAddDTO dto) {
        companyLicenseService.saveCompanyLicense(dto);
        return R.ok();
    }


    @PostMapping("/update")
    @ApiOperation(value = "更新")
    @OperationLog(notes = "企业证书更新", type = OperationLogType.MODITY)
    public R update(@RequestBody @Validated CompanyLicenseUpdateDTO dto) {
        companyLicenseService.updateCompanyLicense(dto);
        return R.ok();
    }

    @PostMapping("/get")
    @ApiOperation(value = "查询")
    public R<CompanyLicenseVO> get(@RequestBody @Validated IdDTO dto) {
        return R.ok(companyLicenseService.getCompanyLicense(dto));
    }

    @PostMapping("/page")
    @ApiOperation(value = "分页")
    @IgnoreAopLog
    public R<CommonPage<CompanyLicenseVO>> getPage(@RequestBody @Validated CompanyLicenseQuery query) {
        return R.ok(companyLicenseService.getPage(query));
    }

    @PostMapping("/change")
    @ApiOperation(value = "换证")
    @OperationLog(notes = "企业证书换证", type = OperationLogType.MODITY)
    public R change(@RequestBody @Validated CompanyLicenseHistoryDTO dto) {
        companyLicenseService.change(dto);
        return R.ok();
    }

    @PostMapping("/invalid")
    @ApiOperation(value = "作废")
    @OperationLog(notes = "企业证书作废", type = OperationLogType.DELETE)
    public R invalid(@RequestBody @Validated IdDTO dto) {
        companyLicenseService.invalid(dto);
        return R.ok();
    }

    @GetMapping("/license/count")
    @ApiOperation(value = "证书统计")
    @IgnoreAopLog
    public R<LicenseCountDTO> licenseCount() {
        return R.ok(companyLicenseService.licenseCount());
    }
}
