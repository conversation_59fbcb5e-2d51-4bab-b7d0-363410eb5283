package com.huafon.license.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.huafon.license.apis.enums.EnumExpireState;
import com.huafon.license.service.support.ExpandAll;
import lombok.Data;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Date;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 员工证书导出
 *
 * <AUTHOR>
 * @since 2022-10-18 09:35
 **/
@Data
public class StaffLicenseDownload {

	@ExcelProperty(value = "部门")
	@ExpandAll(maxWidth = 32, wrapped = true)
	private String department;

	@ExcelProperty(value = "姓名")
	@ExpandAll(maxWidth = 10)
	private String staffName;

	@ExcelProperty(value = "用户名")
	@ExpandAll(maxWidth = 10)
	private String username;

	@ExcelProperty(value = "联系电话")
	@ExpandAll(maxWidth = 12)
	private String phone;

	@ExcelProperty(value = "证书类型名称")
	private String licenseName;

	@ExcelProperty(value = "证书分类")
	private String licenseConfigName;

	@ExcelProperty(value = "证书编号")
	private String licenseNo;

	@ExcelProperty(value = "发证单位")
	private String licenseFrom;

	@ExcelProperty(value = "初领日期")
	@DateTimeFormat("yyyy-MM-dd")
	private Date firstGetLicenseTime;

	@ExcelProperty(value = "取证日期")
	@DateTimeFormat("yyyy-MM-dd")
	private Date licenseDate;

	@ExcelProperty(value = "取证方式")
	private String methods;

	public void setMethods(Integer methods) {
		if (Objects.nonNull(methods)) {
			this.methods = Objects.equals(methods, 1) ? "公司取证" : "个人取证";
		}
	}

	@ExcelProperty(value = "是否长期有效")
	private String hasPermanent;

	public void setHasPermanent(Integer hasPermanent) {
		if (Objects.nonNull(hasPermanent)) {
			this.hasPermanent = Objects.equals(hasPermanent, 0) ? "否" : "是";
		}
	}

	@ExcelProperty(value = "有效期")
	@NumberFormat("#个月")
	private Integer validityPeriod;

	@ExcelProperty(value = "到期日期")
	@DateTimeFormat("yyyy-MM-dd")
	@ExpandAll(minWidth = 12)
	private Date expireDate;

	@ExcelProperty(value = "是否需要复审")
	private String hasReview;

	public void setHasReview(Integer hasReview) {
		if (Objects.nonNull(hasReview)) {
			this.hasReview = Objects.equals(hasReview, 0) ? "否" : "是";
		}
	}

	@ExcelProperty(value = "复审周期")
	@NumberFormat("#个月")
	private Integer reviewPeriod;

	@ExcelProperty(value = "下次复审日期")
	@DateTimeFormat("yyyy-MM-dd")
	@ExpandAll(minWidth = 12)
	private Date nextReviewDate;

	@ExcelProperty(value = "到期状态")
	private String expireState;

	public void setExpireState(Integer expireState) {
		EnumExpireState parse = EnumExpireState.parse(expireState);
		if (Objects.nonNull(parse)) {
			this.expireState = parse.getName();
		}
	}

	@ExcelProperty(value = "复审状态")
	private String reviewState;

	public void setReviewState(Integer reviewState) {
		//使用到期的描述
		EnumExpireState parse = EnumExpireState.parse(reviewState);
		if (Objects.nonNull(parse)) {
			this.reviewState = parse.getName();
		}
	}

	public static void main(String[] args) {
		Field[] fields = StaffLicenseDownload.class.getDeclaredFields();
		for (Field field :fields) {
			ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
			System.out.println(annotation.value()[0] + ":" + field.getName());
		}
		System.out.println(Arrays.stream(fields).map(Field::getName).collect(Collectors.toList()));
	}
}
