package com.huafon.license.service;

import com.huafon.license.apis.enums.EnumReminderType;
import com.huafon.license.entity.CompanyLicenseReminder;
import com.huafon.license.entity.JobMissingLicenseReminder;
import com.huafon.license.entity.StaffLicenseReminder;
import org.apache.logging.log4j.util.Strings;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-09-21 16:04
 **/
public interface MessageConfigService {

	/**
	 * 初始化消息通知
	 * @param tenantIds
	 */
	void syncTenantInit(Collection<Integer> tenantIds, Long userId);

	/**
	 * 人员证书到期/待审消息提醒
	 * @param staffLicenseReminder
	 */
	void staffExpiredOrReviewMessage(StaffLicenseReminder staffLicenseReminder, EnumReminderType reminderType);


	/**
	 * 人员证书岗位缺失
	 * @param jobMissingLicenseReminders
	 */
	void staffMissingMessage(List<JobMissingLicenseReminder> jobMissingLicenseReminders);

	/**
	 * 公司证书到期
	 * @param companyLicenseReminder
	 */
	void companyExpiredMessage(CompanyLicenseReminder companyLicenseReminder);

	default String getContentTemplate(String source) {
		if (Strings.isNotBlank(source)) {
			source = source.replaceAll("\\{\\{([^}]+)\\}\\}", "%s");
		}
		return source;
	}
}
