package com.huafon.license.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.huafon.framework.mybatis.pojo.CommonPage;
import com.huafon.license.apis.dto.IdDTO;
import com.huafon.license.apis.dto.companyLicense.CompanyLicenseUpdateDTO;
import com.huafon.license.apis.dto.reminder.CompanyLicenseReminderQuery;
import com.huafon.license.apis.dto.reminder.CompanyLicenseReminderVO;
import com.huafon.license.apis.dto.reminder.LicenseReminderStatisticVO;
import com.huafon.license.dto.SendMessageDTO;
import com.huafon.license.entity.CompanyLicenseReminder;

/**
 * <p>
 * 公司岗位证书提醒配置服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
public interface CompanyLicenseReminderService extends IService<CompanyLicenseReminder> {
    /**
     * 企业证书到期提醒
     *
     * @param query
     * @return
     */
    CommonPage<CompanyLicenseReminderVO> getCompanyReminderPage(CompanyLicenseReminderQuery query);

    /**
     * 公司提醒统计
     * @return
     */
    LicenseReminderStatisticVO getCompanyReminderStatistic();


    /**
     * 取消企业证书到期提醒
     *
     * @param idDTO
     */
    void cancelCompanyReminder(IdDTO idDTO);

    /**
     * 删除
     *
     * @param companyLicenseId
     */
    void deleteReminder(Integer companyLicenseId);

    /**
     * 执行企业证书提醒
     */
    void processCompanyLicenseReminder();

    /**
     * 更新过期装的
     */
    void processState();

    /**
     * 同步证书时间
     * @param dto
     */
    void syncLicense(CompanyLicenseUpdateDTO dto);

    void companyExpire(SendMessageDTO messageDTO);
}
