package com.huafon.license.service.support;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022-10-20 15:00
 **/
@Data
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME)
@JsonSubTypes({
		@JsonSubTypes.Type(value = StaffLicenseUploadDTO.class)
})
public abstract class CustomRow {
	@JsonIgnore
	private int row;

	@ExcelProperty(value = "错误原因")
	private String errorMessage;
}
