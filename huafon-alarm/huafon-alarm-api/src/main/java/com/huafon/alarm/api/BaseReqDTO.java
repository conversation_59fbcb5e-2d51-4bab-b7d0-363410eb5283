package com.huafon.alarm.api;

import com.huafon.alarm.api.enums.AlarmEventEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: TODO
 * @Date: 2024/10/12 17:29
 * @Author: zyf
 **/
@Data
public class BaseReqDTO implements Serializable {
    private static final long serialVersionUID = 503990351423434871L;

    private Integer tenantId;

    private Date startTime;

    private Date endTime;

    private Boolean deal;

}
