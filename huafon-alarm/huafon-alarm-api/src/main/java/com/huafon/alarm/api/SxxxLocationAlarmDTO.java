package com.huafon.alarm.api;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author:
 * @Date: 2025-04-27 10:48:04
 * @Desc: 人员定位报警
 */
@Data
public class SxxxLocationAlarmDTO implements Serializable {

    /**
     * 租户id
     */
    private Integer tenantId;

    /**
     * 创建时间
     */
    private LocalDateTime createDate;

    /**
     * 创建人名字
     */
    private String createBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateDate;

    /**
     * 更新人姓名
     */
    private String updateBy;

    /**
     * 0正常|1删除
     */
    private Integer deleted;

    /**
     * 公司编号
     */
    private String companyCode;

    /**
     * 报警类型
     * 1 SOS 报警
     * 2 低电量报警
     * 3 闯入报警
     * 4 离岗报警
     * 5 超员报警
     * 6 滞留报警
     * 7 其他
     */

    private String alarmType;
    /**
     * 报警等级 否
     * 1 一级重大
     * 2 二级较大
     * 3 三级一般
     * 4 四级低
     * 5 五级很低
     */

    private String alarmLevel;
    /**
     * 定位卡号
     */
    private String imei;
    /**
     * 人员姓名
     */
    private String employeeName;
    /**
     * 部门
     */
    private String deptName;
    /**
     * 岗位
     */
    private String postName;
    /**
     * 职务
     */
    private String jobName;
    /**
     * 联系电话 否
     */
    private String contactPhone;
    /**
     * 报警时间 yyyyMMddHHmmss
     */
    private String alarmTime;
    /**
     * 报警状态
     * 1 未处理
     * 2 已结束
     * 3 已处理
     * 4 处理中
     */
    private String alarmStatus;
    /**
     * 报警位置
     */
    private String areaName;
    /**
     * 经度 否
     */
    private String lon;
    /**
     * 纬度 否
     */
    private String lat;

    /**
     * 业务id 数据的唯一标识
     */
    private Long bizId;
}
