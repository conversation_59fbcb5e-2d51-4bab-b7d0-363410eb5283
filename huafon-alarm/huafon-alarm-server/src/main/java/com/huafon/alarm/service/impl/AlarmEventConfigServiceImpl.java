package com.huafon.alarm.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.huafon.alarm.mapper.AlarmEventConfigMapper;
import com.huafon.alarm.models.AlarmEventConfig;
import com.huafon.alarm.service.AlarmEventConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huafon.common.config.LanguageContext;
import com.huafon.common.dto.MultiLanguageDTO;
import com.huafon.common.enums.SysLanguage;
import com.huafon.common.utils.StreamUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
@Service
public class AlarmEventConfigServiceImpl extends ServiceImpl<AlarmEventConfigMapper, AlarmEventConfig> implements AlarmEventConfigService {

    @Override
    public List<AlarmEventConfig> findAllList() {
        return this.baseMapper.findAllList();
    }

    @Override
    public Map<String, String> buildLanguageMap(String languageCode) {
        Map<String, String> map = new HashMap<>();
        List<AlarmEventConfig> allList = this.baseMapper.findAllList();

        if (StringUtils.isBlank(languageCode)){
            SysLanguage sysLanguage = LanguageContext.get();
            if (Objects.nonNull(sysLanguage)) {
                languageCode = sysLanguage.getCode();
            }
        }
        if (StringUtils.isNotBlank(languageCode)){
            for (AlarmEventConfig alarmEventConfig : allList) {
                String multiLanguage = alarmEventConfig.getMultiLanguage();
                if (StringUtils.isNotBlank(multiLanguage)){
                    List<MultiLanguageDTO> multiLanguageDTOS = JSONArray.parseArray(multiLanguage, MultiLanguageDTO.class);
                    if (!CollectionUtils.isEmpty(multiLanguageDTOS)){
                        Map<String, String> map1 = StreamUtils.propMap(multiLanguageDTOS, MultiLanguageDTO::getType, MultiLanguageDTO::getName);
                        String s = map1.get(languageCode);
                        if (StringUtils.isNotBlank(s)){
                            map.put(alarmEventConfig.getCode(),s);
                        }
                    }
                }
            }
        }
        return map;
    }

}
