package com.huafon.alarm.vo;

import com.huafon.alarm.enums.AlarmLevelEnum;
import com.huafon.alarm.enums.AlarmReactionSpeedEnum;
import com.huafon.alarm.enums.AlarmTriggerTypeEnum;
import com.huafon.alarm.enums.AlarmTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;
import java.util.List;

public interface AlarmResponse {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("告警响应分页信息VO")
    class PageItemVo {

        @ApiModelProperty("id")
        private Integer id;

        @ApiModelProperty("名称")
        private String name;

        @ApiModelProperty("remark")
        private String remark;

        @ApiModelProperty("是否启用")
        private Boolean enable;

        @ApiModelProperty("类型")
        private AlarmTypeEnum type;
        @ApiModelProperty("告警等级")
        private AlarmLevelEnum level;
        @ApiModelProperty("响应方式")
        private AlarmReactionSpeedEnum reactionSpeed;

        @ApiModelProperty("事件规则")
        private String rule;

        @ApiModelProperty("事件详情")
        private String description;

        @ApiModelProperty("触发类型 dict key :ALARM_TRIGGER_TYPE")
        private AlarmTriggerTypeEnum triggerType;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode
    @ApiModel("告警响应信息VO")
    class InfoVo extends AlarmRequest.BaseVo {
        @ApiModelProperty("id")
        private Integer id;

        @ApiModelProperty("规则列表")
        private List<AlarmRuleRequest.ModifyVo> rules;

        @ApiModelProperty("模板列表")
        private List<AlarmRequest.TemplateVo> templates;

        @ApiModelProperty("最后修改人id")
        private Long modifyBy;
        @ApiModelProperty("最后修改人")
        private String modifyByName;
        @ApiModelProperty("最后修改时间")
        private Date modifyTime;
    }

}