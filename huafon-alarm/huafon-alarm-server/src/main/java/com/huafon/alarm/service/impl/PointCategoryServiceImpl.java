package com.huafon.alarm.service.impl;

import com.huafon.admin.api.dto.SysConfigRpcDTO;
import com.huafon.admin.api.service.ISysConfigRpcService;
import com.huafon.alarm.mapper.PointCategoryMapper;
import com.huafon.alarm.models.PointCategory;
import com.huafon.alarm.models.PointItemView;
import com.huafon.alarm.service.PointCategoryService;
import com.huafon.alarm.service.PointItemService;
import com.huafon.alarm.service.PointItemViewService;
import com.huafon.alarm.vo.PointCategoryRequest;
import com.huafon.alarm.vo.PointCategoryResponse;
import com.huafon.common.config.TenantContext;
import com.huafon.common.utils.bean.BeanUtils;
import com.huafon.common.utils.tree.TreeHelper;
import com.huafon.framework.mybatis.enums.DelFlag;
import com.huafon.framework.mybatis.pojo.BaseEntity;
import com.huafon.framework.mybatis.service.MybatisServiceImpl;
import com.huafon.portal.api.dto.TenantDto;
import com.huafon.portal.api.dto.UserDto;
import com.huafon.portal.api.service.TenantRpcService;
import com.huafon.portal.api.service.UserRpcService;
import com.huafon.support.config.UserContext;
import com.huafon.support.core.pojo.SystemCode;
import com.huafon.support.exceptions.ServiceException;
import com.huafon.support.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * date  : 2022/10/25 9:18
 * description:设备管理分类
 */
@Slf4j
@Service
public class PointCategoryServiceImpl extends MybatisServiceImpl<PointCategoryMapper, PointCategory> implements PointCategoryService {

    private static final String PATROL_PLAN_CATEGORY_UNKOWN_KEY = "PATROL_PLAN_CATEGORY_UNKOWN_KEY";
    private static final Integer STEP_LENGTH = 10;
    private static final Integer DEFAULT_ORDERNUM = 1000;
    @Lazy
    @Autowired
    private PointItemService pointService;
    @Lazy
    @Autowired
    private PointItemViewService viewService;
    @DubboReference
    private TenantRpcService tenantRpcService;
    @DubboReference
    private UserRpcService userRpcService;

    @DubboReference
    private ISysConfigRpcService configRpcService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PointCategory add(PointCategoryRequest.AddVo addVo) {
//        if (Objects.equals(addVo.getParentId(), 0)) {
//            throw new ServiceException("不允许新增顶级节点");
//        }

        Integer sameNameCount = this.lambdaQuery()
                .eq(PointCategory::getTenantId, TenantContext.getOne())
                .eq(PointCategory::getName, addVo.getName())
                .eq(PointCategory::getParentId, addVo.getParentId())
                .count();
        if (sameNameCount >= 1) {
            throw new ServiceException("当前节点下已存在同名分类");
        }

        PointCategory tmp = BeanUtils.convert(addVo, PointCategory.class);
        PointCategory last = this.baseMapper.getLast(addVo.getParentId(), TenantContext.getOne());

        tmp.setOrderNum(Optional.ofNullable(last).map(x -> last.getOrderNum() + STEP_LENGTH).orElse(DEFAULT_ORDERNUM));
        tmp.setTenantId(TenantContext.getOneOrElseThrow());
        tmp.setCreate(UserContext.getOrElseThrow().getUserId());
        this.save(tmp);
        return tmp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PointCategory modify(PointCategory tmp, PointCategoryRequest.ModifyVo modifyVo) {
        Integer sameNameCount = this.lambdaQuery()
                .eq(PointCategory::getTenantId, TenantContext.getOne())
                .eq(PointCategory::getName, modifyVo.getName())
                .ne(PointCategory::getId, tmp.getId())
                .eq(PointCategory::getParentId, modifyVo.getParentId())
                .count();
        if (!tmp.getCanModify()) {
            throw new ServiceException("当前节点不允许修改");
        }
        if (sameNameCount >= 1) {
            throw new ServiceException("当前节点下已存在同名分类");
        }
        BeanUtils.copyProperties(modifyVo, tmp, "id");
        tmp.setModify(UserContext.getOrElseThrow().getUserId());
        this.updateById(tmp);
        return tmp;
    }

    @Override
    public PointCategoryResponse.InfoVo seleteById(Integer id) {
        return build(findById(id));
    }

    private PointCategoryResponse.InfoVo build(PointCategory tmp) {
        PointCategoryResponse.InfoVo info = BeanUtils.convert(tmp, PointCategoryResponse.InfoVo.class);
        info.setParentName(Optional.ofNullable(this.getById(tmp.getParentId()))
                .map(PointCategory::getName)
                .orElse(null));

        if (Objects.nonNull(tmp.getModifyBy())) {
            UserDto user = userRpcService.getById(tmp.getModifyBy().intValue());
            info.setModifier(Optional.ofNullable(user).map(UserDto::getName).orElse(null));
            info.setModifyDept(Optional.ofNullable(user)
                    .map(x -> Objects.nonNull(x.getDeptDtoList()) && x.getDeptDtoList().isEmpty() ? x.getDeptDtoList()
                            .iterator()
                            .next()
                            .getName() : null)
                    .orElse(null));
        }
        return info;
    }

    @Override
    public PointCategory findById(Integer id) {
        PointCategory tmp;
        if (id == 0) {
            tmp = new PointCategory();
            tmp.setId(0);
            tmp.setParentId(-2);
            tmp.setName("全部数据");
            tmp.setOrderNum(1);
            tmp.setCanModify(Boolean.FALSE);
        } else if (id == -1) {
            tmp = new PointCategory();
            tmp.setId(-1);
            tmp.setParentId(0);
            tmp.setName("待分类");
            tmp.setOrderNum(1);
            tmp.setCanModify(Boolean.FALSE);
        } else {
            tmp = Optional.ofNullable(this.lambdaQuery().eq(PointCategory::getId, id).one())
                    .orElseThrow(() -> new ServiceException(SystemCode.DATA_NOT_EXIST));
        }
        return tmp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Integer id) {
        PointCategory tmp = this.findById(id);
        if (!tmp.getCanModify()) {
            throw new ServiceException("当前节点不允许修改");
        }

        Set<Integer> ids = getChildIdByParent(Collections.singleton(id), TenantContext.getOne());
        if (viewService.lambdaQuery().in(PointItemView::getCategoryId, ids).count() > 0) {
            throw new ServiceException("当前节点下存在测点,不允许删除.");
        }
        Set<Integer> childCategories = this.getChildIdByParent(Collections.singleton(id), TenantContext.getOne());
        Set<Integer> categories = new HashSet<>();
        categories.add(id);
        if (!childCategories.isEmpty()) {
            categories.addAll(childCategories);
        }
        this.lambdaUpdate()
                .set(BaseEntity::getModifyTime, new Date())
                .set(PointCategory::getModifyBy, UserContext.getOrElseThrow().getUserId())
                .set(BaseEntity::getIsDel, DelFlag.DELELTED.getValue())
                .eq(PointCategory::getId, id)
                .update();
//        planService.move2Unkown(categories);
    }

    private void adjustPosition(PointCategory above, PointCategory tmp, PointCategory below) {
        tmp.setOrderNum(above.getOrderNum() + STEP_LENGTH);
        below.setOrderNum(tmp.getOrderNum() + STEP_LENGTH);
        PointCategory belowTmp = this.baseMapper.getBelow(tmp.getParentId(), TenantContext.getOne(), tmp.getId(), tmp.getOrderNum());
        this.updateById(tmp);
        this.updateById(below);
        if (Objects.nonNull(belowTmp) && (below.getOrderNum() < belowTmp.getOrderNum() || belowTmp.getOrderNum() < below.getOrderNum())) {
//      当存在上下位皆有数据 并排序值不符合规则时 需要调整位置
            this.adjustPosition(tmp, below, belowTmp);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void move(Integer id, Integer newId, Integer aboveId) {
        if (Objects.equals(newId, 0)) {
            throw new ServiceException("不允许移动为顶级节点");
        }
        PointCategory tmp = this.findById(id);
        PointCategory above;
        PointCategory below;
        if (aboveId == 0) {
            PointCategory first = this.baseMapper.getFirst(newId, TenantContext.getOne());
            above = null;
            below = null;
            tmp.setOrderNum(Optional.ofNullable(first)
                    .map(x -> first.getOrderNum() - STEP_LENGTH)
                    .orElse(DEFAULT_ORDERNUM));
        } else {
            above = this.findById(aboveId);
            below = this.baseMapper.getBelow(newId, TenantContext.getOne(), above.getId(), above.getOrderNum());
            tmp.setOrderNum(Optional.ofNullable(above)
                    .map(x -> above.getOrderNum() + STEP_LENGTH)
                    .orElse(DEFAULT_ORDERNUM));
        }

        tmp.setParentId(newId);
        tmp.setModify(UserContext.getId());
        this.updateById(tmp);
        if (Objects.nonNull(above) && Objects.nonNull(below) && (tmp.getOrderNum() <= above.getOrderNum() || below.getOrderNum() <= tmp.getOrderNum())) {
//      当存在上下位皆有数据 并排序值不符合规则时 需要调整位置
            this.adjustPosition(above, tmp, below);
        }
    }

    @Override
    public PointCategoryResponse.InfoTreeVo tree(Boolean showUnkown) {
        List<PointCategoryResponse.InfoTreeVo> treeVos = this.baseMapper.getChildByParentId(Collections.singleton(0), TenantContext.getOne());
//        SysConfigRpcDTO config = configRpcService.getByKey(PATROL_PLAN_CATEGORY_UNKOWN_KEY + TenantContext.getOne());
        PointCategoryResponse.InfoTreeVo root = new PointCategoryResponse.InfoTreeVo();
        root.setId(0);
        root.setParentId(-2);
        root.setName("全部数据");
        root.setOrderNum(1);
        root.setCanModify(Boolean.FALSE);
        treeVos.add(root);
        if (showUnkown) {
            PointCategoryResponse.InfoTreeVo unkown = new PointCategoryResponse.InfoTreeVo();
            root.setId(-1);
            root.setParentId(0);
            root.setName("待分类");
            root.setOrderNum(1);
            root.setCanModify(Boolean.FALSE);
            treeVos.add(unkown);
        }
        Collection<PointCategoryResponse.InfoTreeVo> tree = new TreeHelper().build(treeVos, -2);

        return tree.isEmpty() ? null : new ArrayList<>(tree).get(0);
    }


    @Override
    public Set<Integer> getChildIdByParent(Collection<Integer> categoryIds, Integer tenantId) {
        Set<Integer> ids = new HashSet<>();
        if (categoryIds.isEmpty()) {
            return Collections.emptySet();
        }
        List<PointCategoryResponse.InfoTreeVo> treeVos = this.baseMapper.getChildByParentId(categoryIds, tenantId);
        ids.addAll(treeVos.stream().map(PointCategoryResponse.InfoTreeVo::getId).collect(Collectors.toList()));
        ids.addAll(categoryIds);
        if (categoryIds.stream().anyMatch(x -> x == 0)) {
            ids.add(-1);
        }
        return ids;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteById(List<Integer> ids) {
        this.lambdaUpdate()
                .set(BaseEntity::getModifyTime, new Date())
                .set(PointCategory::getModifyBy, UserContext.getOrElseThrow().getUserId())
                .set(BaseEntity::getIsDel, DelFlag.DELELTED.getValue())
                .in(PointCategory::getId, ids)
                .update();
    }

    @Override
    public String getFullName(Integer category) {
        if (category == -1) {
            return "待分类";
        }
        if (category == 0) {
            return "全部数据";
        }
        List<PointCategoryResponse.InfoTreeVo> trees = this.baseMapper.getParentByIdDel(category);
        PointCategoryResponse.InfoTreeVo root = new PointCategoryResponse.InfoTreeVo();
        root.setId(0);
        root.setParentId(-2);
        root.setName("全部数据");
        root.setOrderNum(1);
        root.setCanModify(Boolean.FALSE);
        trees.add(root);

        return getChildName(-2, trees);
    }

    @Override
    public void init(Integer tenantId) {
        String name = "巡检路线";
        PointCategory allTmpH = PointCategory.builder()
                .tenantId(tenantId)
                .name(name)
                .canModify(Boolean.FALSE)
                .parentId(0)
                .orderNum(1)
                .build();
        allTmpH.setCreate(1L);
        this.save(allTmpH);
        PointCategory unkownTmpH = PointCategory.builder()
                .tenantId(tenantId)
                .name("待分类")
                .canModify(Boolean.FALSE)
                .parentId(allTmpH.getId())
                .orderNum(100000)
                .build();
        unkownTmpH.setCreate(1L);
        this.save(unkownTmpH);

        configRpcService.add(SysConfigRpcDTO.builder()
                .name(name + "待分类id" + tenantId)
                .key("PATROL_PLAN_CATEGORY_UNKOWN_KEY" + tenantId)
                .type("N")
                .value(unkownTmpH.getId().toString())
                .build());
    }

    public String getChildName(Integer pid, List<PointCategoryResponse.InfoTreeVo> trees) {
        PointCategoryResponse.InfoTreeVo tree = trees.stream()
                .filter(x -> Objects.equals(x.getParentId(), pid))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(tree)) {
            return "";
        }
        String childName = getChildName(tree.getId(), trees);
        if (pid == -2) {
            return childName;
        } else {
            if (StringUtils.isEmpty(childName)) {
                return tree.getName();
            }
            return tree.getName() + "|" + childName;
        }
    }


    @Override
    public Map<Integer, String> getFullNames(List<Integer> categorys) {
        List<PointCategoryResponse.InfoTreeVo> trees = this.baseMapper.getParentByIdsDel(categorys);
        Map<Integer, String> result = new HashMap<>();
        Map<Integer, PointCategoryResponse.InfoTreeVo> treeMap = new HashMap<>();

        if (!trees.isEmpty()) {
            for (PointCategoryResponse.InfoTreeVo tree : trees) {
                treeMap.put(tree.getId(), tree);
            }
            for (PointCategoryResponse.InfoTreeVo tree : trees) {
                result.put(tree.getId(), this.getParentName(tree, treeMap));
            }
        }
        return result;
    }

    public String getParentName(PointCategoryResponse.InfoTreeVo leaf, Map<Integer, PointCategoryResponse.InfoTreeVo> treeMap) {
        PointCategoryResponse.InfoTreeVo parent = treeMap.get(leaf.getParentId());
        if (leaf.getParentId() == 0) {
            return "";
        }
        if (Objects.isNull(parent)) {
            return leaf.getName();
        }
        String parentName = getParentName(parent, treeMap);
        if (StringUtils.isEmpty(parentName)) {
            return leaf.getName();
        }
        return parentName + "|" + leaf.getName();
    }

    @Override
    public void initCategory() {
        List<TenantDto> tenants = tenantRpcService.queryAllTenant();
//        for (TenantDto tenant : tenants) {
//            List<PointCategory> categorys = this.lambdaQuery()
//                    .eq(PointCategory::getTenantId, tenant.getTenantId())
//                    .list();
//            if (!categorys.isEmpty()) {
//                List<Integer> categoryIds = categorys.stream()
//                        .map(PointCategory::getId)
//                        .collect(Collectors.toList());
//                Map<Integer, String> categoryNameMap = this.getFullNames(categoryIds);
//
//                for (PointCategory category : categorys) {
//                    Integer categoryId = category.getId();
//                    String categoryName = categoryNameMap.get(categoryId);
//                    if (StringUtils.isEmpty(categoryName)) {
//                        categoryName = this.getFullName(categoryId);
//                    }
////                    categoryName = categoryName.split("全部数据");
//                    categoryNameMap.put(categoryId, categoryName);
//                    category.setNameStructure(categoryName);
//                }
//                this.updateBatchById(categorys);
//            }
//        }
//
    }

    @Override
    public Integer findAndAddByNodeName(String category) {
        Integer tenantId = TenantContext.getOne();
//        PointCategory categoryTmp = this.lambdaQuery()
//                .eq(PointCategory::getParentId, 0)
//                .eq(PointCategory::getTenantId, tenantId)
//                .one();

        String[] categorys = category.split("\\|");
        Integer pid = 0;
        for (int i = 0; i < categorys.length; i++) {
            String child = categorys[i];
            PointCategory tmp = this.lambdaQuery()
                    .eq(PointCategory::getParentId, pid)
                    .eq(PointCategory::getTenantId, tenantId)
                    .eq(PointCategory::getName, child)
                    .one();
            if (Objects.isNull(tmp)) {
                tmp = new PointCategory();
                tmp.setName(child);
                tmp.setCanModify(Boolean.TRUE);
                tmp.setParentId(pid);
                tmp.setOrderNum(DEFAULT_ORDERNUM);
                tmp.setTenantId(TenantContext.getOneOrElseThrow());
                tmp.setCreate(UserContext.getOrElseThrow().getUserId());
                this.save(tmp);
            }
            if (i == categorys.length - 1 && Objects.nonNull(tmp)) {
                return tmp.getId();
            } else if (Objects.nonNull(tmp)) {
                pid = tmp.getId();
            } else {
                return null;
            }
        }
        return null;
    }

}