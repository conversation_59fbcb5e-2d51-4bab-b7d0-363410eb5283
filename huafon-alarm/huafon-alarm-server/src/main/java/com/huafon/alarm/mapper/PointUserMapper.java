package com.huafon.alarm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huafon.alarm.models.PointUser;
import com.huafon.alarm.vo.page.PointUserPageReqVO;
import com.huafon.alarm.vo.page.PointUserPageRespVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 用户测点关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Mapper
public interface PointUserMapper extends BaseMapper<PointUser> {

    List<PointUser> findByUserIdAndTenantId(@Param("userId") Integer userId,@Param("tenantId") Integer tenantId);

    //软删
    void updateDelFlag(@Param("list")List<Integer> idList, @Param("userId")Long userId, @Param("date") Date date);

    IPage<PointUserPageRespVO> getPage(Page<PointUserPageRespVO> page,@Param("reqVO") PointUserPageReqVO reqVO);

    List<PointUser> findAllPoint(@Param("userId") Integer userId,@Param("tenantId") Integer tenantId);

    List<PointUser> findByUserIdAndPointId(@Param("userId") Integer userId,@Param("propList") List<Integer> propList);

    void updateDelByUserIdAndPointId(@Param("pointId")Integer pointId,@Param("userId")Long userId, @Param("date") Date date);
}
