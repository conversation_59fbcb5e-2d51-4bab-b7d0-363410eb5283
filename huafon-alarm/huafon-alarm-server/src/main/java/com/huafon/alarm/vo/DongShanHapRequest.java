package com.huafon.alarm.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

public interface DongShanHapRequest {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class ApplyPermissionVo {

        private String accountCode;
        private String sign;
        private String timestamp;
        private List<ApplyPermissionItemVo> pointInfos;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class ApplyPermissionItemVo {
        private String pointName;
        private String pointCode;
        private String pointAddress;
        private String pointDataType;


        public ApplyPermissionItemVo(String pointName, String pointCode, String pointDataType) {
            this.pointName = pointName;
            this.pointCode = pointCode;
            this.pointAddress = null;
            this.pointDataType = pointDataType;
        }

        public ApplyPermissionItemVo(String pointName, String pointCode) {
            this.pointName = pointName;
            this.pointCode = pointCode;
            this.pointAddress = null;
            this.pointDataType = "double";
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class GetRealDataVo {
        private Set<String> pointCodes;
        private String accountCode;
        private String sign;
        private String timestamp;
    }

}
