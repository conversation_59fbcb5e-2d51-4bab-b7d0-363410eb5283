package com.huafon.alarm.utils.factory;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.huafon.alarm.enums.AlarmNoticeRecipientLevelEnum;
import com.huafon.alarm.models.AlarmNoticeRecipient;
import com.huafon.alarm.models.AlarmTemplate;
import com.huafon.portal.api.dto.ApprovalUserDTO;
import com.huafon.portal.api.dto.UserDto;
import com.huafon.portal.api.service.ApprovalService;
import com.huafon.portal.api.service.UserRpcService;
import com.huafon.portal.api.service.UserRpcV2Service;
import com.huafon.support.exceptions.ServiceException;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Service
public class PatrolTaskRecipientFactory extends AbstractRecipientFactory {

    @DubboReference
    private ApprovalService approvalService;
    private UserRpcService userRpcService;
    @DubboReference
    private UserRpcV2Service userService;
    @Override
    public List<AlarmNoticeRecipient> createRecipient(Integer tenantId, List<AlarmTemplate> templates, JSONObject snapshot) {
        List<AlarmNoticeRecipient> recipients = new ArrayList<>();
        templates.forEach(template -> {
            Set<AlarmNoticeRecipient> recipient = null;
            if (Objects.nonNull(template) && Objects.nonNull(template.getCategory())) {
                Integer targetId;
                switch (template.getCategory()) {
                    case USER:
                        recipient = Collections.singleton(this.getUser(template));
                        break;
                    case ROLE:
                        recipient = this.getUserByRole(template);
                    case TRIGGER:
                        recipient = this.getTrigger(tenantId, template.getType(), snapshot, template.getNoticeLevel(), template.getLevels());
                        break;
                    case CREATER_DEPT:
                        targetId = snapshot.getInteger("creater");
                        recipient = this.getTriggerDept(tenantId, targetId, template.getType(), snapshot, template.getNoticeLevel(), template.getLevels());
                        break;
                    case EXECUTOR_DEPT:
                        JSONArray targetIds = snapshot.getJSONArray("executors");
                        recipient = new HashSet<>();
                        for (int i = 0; i < targetIds.size(); i++) {
                            targetId = targetIds.getInteger(i);
                            recipient.addAll(this.getTriggerDept(tenantId, targetId, template.getType(), snapshot, template.getNoticeLevel(), template.getLevels()));
                        }
                        break;
                    default:
                        throw new ServiceException("对应接收人不符合约束");
                }

                if (!CollectionUtils.isEmpty(recipient)) {
                    for (AlarmNoticeRecipient x : recipient) {
                        x.setShowMap(template.getShowMap());
                        x.setChannel(template.getChannel());
                    }
                    recipients.addAll(recipient);
                }

            }else {
                AlarmNoticeRecipient alarmNoticeRecipient = new AlarmNoticeRecipient();
                alarmNoticeRecipient.setShowMap(template.getShowMap());
                alarmNoticeRecipient.setChannel(template.getChannel());
                alarmNoticeRecipient.setUserId(template.getUserId());
                recipients.add(alarmNoticeRecipient);
            }

        });

        return recipients;
    }

    private Set<AlarmNoticeRecipient> getTriggerDept(Integer tenantId, Integer userId, String type, JSONObject snapshot, Integer noticeLevel, AlarmNoticeRecipientLevelEnum level) {
        Set<AlarmNoticeRecipient> recipients = new HashSet<>();
        UserDto user = userService.getById(userId, tenantId);
        Map<Integer, ApprovalUserDTO> approvalMap = approvalService.queryDepartmentApprovalUserMappings(user.getDepartmentIds());
        for (Integer deptId : user.getDepartmentIds()) {
            ApprovalUserDTO approval = approvalMap.get(deptId);

            List<Integer> userMobileDtos = null;
            if (Objects.equals(type, "1")) {
//                告警触发部门-六个责任部门 部门负责人 1 部门HSE管理员 2 部门分管领导 3 财务负责人 4 装置工段负责人 5 班组负责人 6
                userMobileDtos = approval.getPrincipalUserIds();
            } else if (Objects.equals(type, "2")) {
                userMobileDtos = approval.getHsePrincipalUserIds();
            } else if (Objects.equals(type, "3")) {
                userMobileDtos = approval.getLeaderUserIds();
            } else if (Objects.equals(type, "4")) {
                userMobileDtos = approval.getFinancePrincipalUserIds();
            } else if (Objects.equals(type, "5")) {
                userMobileDtos = approval.getDeviceSectionPrincipalUserIds();
            } else if (Objects.equals(type, "6")) {
                userMobileDtos = approval.getTeamPrincipalUserIds();
            }
            Set<UserDto> userDtos = new HashSet<>();
            for (Integer approvalId : userMobileDtos) {
                Set<UserDto> userDtoLevels = userService.getByLevels(approvalId, tenantId, Collections.singletonList(level.getValue()));
                if (Objects.nonNull(userDtoLevels)) {
                    userDtos.addAll(userDtoLevels);
                }
            }
            for (UserDto tmp : userDtos) {
                AlarmNoticeRecipient recipient = new AlarmNoticeRecipient();
                recipient.setUserId(tmp.getUserId());
                recipient.setUsername(tmp.getName());
                recipient.setEmail(tmp.getEmail());
                recipient.setNoticeLevel(noticeLevel);
                recipient.setContact(tmp.getMobile());
                recipients.add(recipient);
            }
        }
        return recipients;
    }

    private Set<AlarmNoticeRecipient> getTrigger(Integer tenantId, String type, JSONObject snapshot, Integer noticeLevel, AlarmNoticeRecipientLevelEnum level) {

        Set<AlarmNoticeRecipient> recipients = new HashSet<>();

        if (Objects.equals(type, "CREATER")) {
            Integer targetId = snapshot.getInteger("creater");

            Set<UserDto> userDtos = userService.getByLevels(targetId, tenantId, Collections.singletonList(level.getValue()));
            for (UserDto tmp : userDtos) {
                AlarmNoticeRecipient recipient = new AlarmNoticeRecipient();
                recipient.setUserId(tmp.getUserId());
                recipient.setUsername(tmp.getName());
                recipient.setEmail(tmp.getEmail());
                recipient.setNoticeLevel(noticeLevel);
                recipient.setContact(tmp.getMobile());
                recipients.add(recipient);
            }
        } else if (Objects.equals(type, "EXECUTOR")) {
            JSONArray targetIds = snapshot.getJSONArray("executors");
            for (int i = 0; i < targetIds.size(); i++) {
                Integer targetId = targetIds.getInteger(i);
                Set<UserDto> userDtos = userService.getByLevels(targetId, tenantId, Collections.singletonList(level.getValue()));
                for (UserDto tmp : userDtos) {
                    AlarmNoticeRecipient recipient = new AlarmNoticeRecipient();
                    recipient.setUserId(tmp.getUserId());
                    recipient.setUsername(tmp.getName());
                    recipient.setEmail(tmp.getEmail());
                    recipient.setNoticeLevel(noticeLevel);
                    recipient.setContact(tmp.getMobile());
                    recipients.add(recipient);
                }
            }
        }
        return recipients;
    }


}