package com.huafon.alarm.mapper;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.huafon.alarm.api.enums.AlarmEventEnum;
import com.huafon.alarm.models.AlarmNotice;
import com.huafon.alarm.vo.AlarmNoticeResponse;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface AlarmNoticeMapper extends BaseMapper<AlarmNotice> {


    @Select("select * " +
            "from hf_alarm_notice " +
            "where frequency != 'ONCE' " +
            "  and deal = false " +
            "  and deal_state = 0 " +
            "  and status = 'ALARM' " +
            "  and is_del = 0 " +
            "  and frequency_mins <= EXTRACT(EPOCH FROM (#{now} - next_alarm_time)) / 60;")
    List<AlarmNotice> findNeedAlarm(@Param("now") LocalDateTime now);

    @Select("select * " +
            "from hf_alarm_notice " +
            "where " +
            "  deal = false " +
            "  and deal_state = 1 " +
            "  and status = 'ALARM' " +
            "  and is_del = 0 " +
            "  and #{now} >= refresh_time")
    List<AlarmNotice> findRefreshAlarm(@Param("now") LocalDateTime now);

    @Select("<script>" +
            "select tenant_id, count(*) as num " +
            "from hf_alarm_notice " +
            "where create_time >= #{time} " +
            "  and is_del = 0 " +
            "group by tenant_id " +
            "</script>")
    List<AlarmNoticeResponse.TenantCountResultVO> countByTenant(@Param("time") LocalDateTime time);


    @Select("<script>" +
            " select distinct han.* " +
            " from hf_alarm_notice han " +
            "         join hf_alarm_notice_recipient hanr on han.id = hanr.notice_id and han.notice_level  <![CDATA[ >= hanr.notice_level  ]]> " +
            " ${ew.customSqlSegment} " +
            "</script>")
    IPage<AlarmNotice> selectPageVo(IPage<AlarmNotice> query, @Param(Constants.WRAPPER) Wrapper<AlarmNotice> wrapper);


    @Select("<script>" +
            " select distinct han.id " +
            " from hf_alarm_notice han " +
            "         join hf_alarm_notice_recipient hanr on han.id = hanr.notice_id and han.notice_level  <![CDATA[ >= hanr.notice_level  ]]> " +
            " ${ew.customSqlSegment} " +
            "</script>")
    List<Integer> tabCount(@Param(Constants.WRAPPER) Wrapper<AlarmNotice> wrapper);

    @Select("<script>" +
            " select event, count(*) as count from hf_alarm_notice " +
            " ${ew.customSqlSegment} " +
            " group by event; " +
            "</script>")
    @MapKey("event")
    Map<AlarmEventEnum, Map<String, Object>> statisticsByEvent(@Param(Constants.WRAPPER) Wrapper<AlarmNotice> wrapper);

    void batchDelById(@Param("list") List<Integer> delList,@Param("userId") Long id,@Param("date") Date date);

    void refreshDealState(@Param("list")List<AlarmNotice> notices);

}

