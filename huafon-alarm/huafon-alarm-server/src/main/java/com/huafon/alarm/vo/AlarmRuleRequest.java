package com.huafon.alarm.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.huafon.alarm.enums.AlarmFrequencyEnum;
import com.huafon.alarm.enums.AlarmLevelEnum;
import com.huafon.alarm.enums.AlarmRuleMethod;
import com.huafon.alarm.enums.AlarmTypeEnum;
import com.huafon.framework.mybatis.pojo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

public interface AlarmRuleRequest {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("告警规则基础信息VO")
    class BaseVo {

        @NotNull
        @ApiModelProperty("规则code， 探头code 事件code ")
        private String targetCode;

        @NotNull
        @ApiModelProperty("规则类型 EVENT VALUE POINT")
        private AlarmTypeEnum targetType;

        //        @Size(max = 63)
//        @NotEmpty
        @ApiModelProperty("规则检测点名， 探头名 事件名 ")
        private String name;
        @ApiModelProperty("tenantId")
        private Integer tenantId;

        //        @NotNull
        @ApiModelProperty("时间粒度")
        private Integer timeParticle;

        //        @NotNull
        @ApiModelProperty("校验方法 dict key ：ALARM_RULE_METHOD")
        private AlarmRuleMethod method;

        //        @NotNull
        @TableField(value = "值")
        private BigDecimal value;

        @ApiModelProperty("等级")
        private AlarmLevelEnum level;

        //        @NotEmpty
        @TableField(value = "单位 探头会有")
        private String unit;

        //        @NotNull
        @TableField(value = "持续次数")
        private Integer duration;

        //        @NotNull
        @TableField(value = "报警频率 dict key ALARM_FREQUENCY")
        private AlarmFrequencyEnum frequency;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @ApiModel("告警规则更新VO")
    class ModifyVo extends BaseVo {
        @ApiModelProperty("id")
        private Integer id;
    }

    @ApiModel("告警规则新建VO")
    class AddVo extends BaseVo {
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @ApiModel("告警规则分页查询Qo")
    class PageQuery extends PageRequest {
        @ApiModelProperty("查询关键字 现支持 - name")
        private String searchKey;

    }
}
