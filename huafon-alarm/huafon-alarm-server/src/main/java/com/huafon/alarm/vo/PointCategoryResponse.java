package com.huafon.alarm.vo;

import com.huafon.common.utils.tree.TreeVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Collection;
import java.util.Date;
import java.util.Optional;

public interface PointCategoryResponse {

    @Data
    @ApiModel("测点分类信息VO")
    @EqualsAndHashCode(callSuper = true)
    class InfoVo extends PointCategoryRequest.BaseVo {
        @ApiModelProperty("id")
        private Integer id;

        @ApiModelProperty("父节点名称")
        private String parentName;

        @ApiModelProperty("编辑人")
        private String modifier;

        @ApiModelProperty("编辑部门")
        private String modifyDept;

        @ApiModelProperty("编辑时间")
        private Date modifyTime;

    }

//    @Data
//    @Schema(name = "测点分类全局分类树VO")
//    class InfoAllTreeVo {
//        @ApiModelProperty(name = "消防点位分类树列表")
//        private List<PointCategoryResponse.TenantTreeVo> trees;
//    }

    @Data
    @Schema(name = "测点分类树VO")
    @EqualsAndHashCode(callSuper = true)
    class InfoTreeVo extends TreeVo<InfoTreeVo> {
        @ApiModelProperty(name = "id")
        private Integer id;

        @ApiModelProperty(name = "name")
        private String name;

        @ApiModelProperty(name = "parentId")
        private Integer parentId;

        @ApiModelProperty(name = "orderNum")
        private Integer orderNum;

        @ApiModelProperty(value = "canModify")
        private Boolean canModify;

        @ApiModelProperty(name = "tenantId")
        private Integer tenantId;

        @ApiModelProperty(name = "icon")
        private String icon;

        @ApiModelProperty(name = "子路由")
        private Collection<InfoTreeVo> children;

        @Override
        public void setId(Integer id) {
            this.id = id;
        }

        @Override
        public void setParentId(Integer parentId) {
            this.parentId = parentId;
        }

        @Override
        public void setChildren(Collection<InfoTreeVo> children) {
            this.children = children;
        }

        @Override
        public void setOrderNum(Integer orderNum) {
            this.orderNum = orderNum;
        }

        @Override
        public int compareTo(InfoTreeVo infoTreeVo) {
            Integer i1 = Optional.ofNullable(infoTreeVo.getOrderNum()).orElse(0);
            Integer i2 = Optional.ofNullable(this.orderNum).orElse(0);
            return i2.equals(i1) ? 1 : i2.compareTo(i1);
        }
    }

}