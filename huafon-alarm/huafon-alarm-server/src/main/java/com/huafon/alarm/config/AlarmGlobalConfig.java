package com.huafon.alarm.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @since 2023-05-23 13:55
 **/
@RefreshScope
@Component
@Getter
public class AlarmGlobalConfig {

	/**
	 * 告警半径范围
	 */
	@Value("${alarm.global.radius}")
	private String radius;


}
