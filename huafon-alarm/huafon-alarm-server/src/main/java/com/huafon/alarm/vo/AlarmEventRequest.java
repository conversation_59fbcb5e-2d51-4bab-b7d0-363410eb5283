package com.huafon.alarm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface AlarmEventRequest {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel( "告警事件基础信息VO")
    class BaseVo {

        @ApiModelProperty("id")
        private Integer id;

        @ApiModelProperty("所属服务")
        private String service;

        @ApiModelProperty("code")
        private String code;

        @ApiModelProperty("名称")
        private String name;
        @ApiModelProperty("公司")
        private String company;
    }

}
