<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huafon.alarm.mapper.AlarmEventConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huafon.alarm.models.AlarmEventConfig">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="multi_language" property="multiLanguage" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="modify_by" property="modifyBy" />
        <result column="modify_time" property="modifyTime" />
        <result column="is_del" property="isDel" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, name, multi_language, create_by, create_time, modify_by, modify_time, is_del
    </sql>

    <select id="findAllList" resultType="com.huafon.alarm.models.AlarmEventConfig">
        SELECT
            t1."id",
            t1.code,
            t1."name",
            t1.multi_language
        FROM
            hf_alarm_event_config AS t1
        WHERE
            t1.is_del = 0
    </select>

</mapper>
